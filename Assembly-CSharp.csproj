<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>latest</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{A7E1687A-77C6-3F61-91A0-0D276FF44E76}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE;UNITY_2019_4_10;UNITY_2019_4;UNITY_2019;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNET;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_MONO_BDWGC;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;PLATFORM_WEBGL;UNITY_WEBGL;UNITY_WEBGL_API;UNITY_DISABLE_WEB_VERIFICATION;UNITY_GFX_USE_PLATFORM_VSYNC;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_SPATIALTRACKING;ENABLE_MONO;NET_4_6;ENABLE_PROFILER;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;UNITY_PRO_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;OPPO;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>D:\unity\2019.4.10f1\Editor\Data\Managed/UnityEngine/UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>D:\unity\2019.4.10f1\Editor\Data\Managed/UnityEditor.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
     <Compile Include="Assets\0_GAME\005_CNT\CLT_CoudMover.cs" />
     <Compile Include="Assets\0_GAME\005_CNT\CNT_GoldPig_FusionSystem.cs" />
     <Compile Include="Assets\0_GAME\005_CNT\CNT_GridConnon_PropPickUp_Panel.cs" />
     <Compile Include="Assets\0_GAME\005_CNT\CNT_KeyTreasure_FusionSystem.cs" />
     <Compile Include="Assets\0_GAME\005_CNT\CNT_Shooting_System.cs" />
     <Compile Include="Assets\0_GAME\005_CNT\CNT_TileCube_ClearColor_Panel.cs" />
     <Compile Include="Assets\0_GAME\005_CNT\Game_CNT_Game.cs" />
     <Compile Include="Assets\0_GAME\005_CNT\Game_CNT_Start.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_BasePanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_BtnCoinAdd.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_BtnMenuLock.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_BtnNoADS.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_BtnPrivate.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_BtnProp.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_BtnSetting.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_BtnUserAgree.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_Btn_Common.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_Controller.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_Data.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_GamePanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_GetPropPanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_GuidePanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_HardLevelPanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_LoadingPanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_LosePanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_MenuBtnPanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_NoAdsPanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_PanelAnimator.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_PropIconRes.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_ReadPanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_RegamePanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_SettingPanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_ShopPack.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_ShopPanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_StartPanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_UnlockVacantPanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_UserData_Manager.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_WinPanel.cs" />
     <Compile Include="Assets\0_GAME\Frame_MatchUI\Scripts\MatchUI_imgCoinBar.cs" />
     <Compile Include="Assets\0_GAME\Moduler_Base\Game_Base_Game.cs" />
     <Compile Include="Assets\0_GAME\Moduler_Base\Game_Base_Start.cs" />
     <Compile Include="Assets\0_GAME\Moduler_Common_ReserveGrid\Common_ReserveGrid.cs" />
     <Compile Include="Assets\0_GAME\Moduler_Common_ReserveGrid\Common_ReserveGridController.cs" />
     <Compile Include="Assets\0_GAME\Moduler_Common_ReserveGrid\Common_ReserveGridManager.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Data\GridConnon_Data_Cannon.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Data\GridConnon_Data_Level.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\EffectManager.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\GridConnon_InputManager.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\LevelCSV_Creator\GridConnon_LevelConverter.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Navigation\GridConnon_Navigation.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Navigation\GridConnon_NavigationData.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Navigation\GridConnon_PathFinder.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Scripts\GridConnon_Bullet.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Scripts\GridConnon_Bullet_CollisionBody.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Scripts\GridConnon_Cannon.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Scripts\GridConnon_Const_Data.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Scripts\GridConnon_Controller.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Scripts\GridConnon_LinkStick.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Scripts\GridConnon_LinkSystem.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Scripts\GridConnon_Object.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Scripts\GridConnon_ShootingSynthesisSystem.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Scripts\GridConnon_ShotGrid_Controller.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\Scripts\GridConnon_ShotGrid_Grid.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\GridConnon_CannonStateBase.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\GridConnon_CannonStateManager.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\GridConnon_CannonState_BackRow.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\GridConnon_CannonState_Destroying.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\GridConnon_CannonState_FirstRow.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\GridConnon_CannonState_Moving.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\GridConnon_CannonState_Prop_PickOut.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\GridConnon_CannonState_Prop_Random.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\GridConnon_CannonState_Shooting.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\GridConnon_CannonState_ShootingSynthesis.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\GridConnon_CannonState_ShootingSynthesisFinish.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\GridConnon_StateFactoryProvider.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\IGridConnon_StateFactory.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\QuestionCannonStates\GridConnon_CannonState_BackRow_Question.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\QuestionCannonStates\GridConnon_CannonState_Destroying_Question.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\QuestionCannonStates\GridConnon_CannonState_FirstRow_Question.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\QuestionCannonStates\GridConnon_CannonState_Moving_Question.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\QuestionCannonStates\GridConnon_CannonState_Prop_PickOut_Question.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\QuestionCannonStates\GridConnon_CannonState_Prop_Random_Question.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\QuestionCannonStates\GridConnon_CannonState_ShootingSynthesisFinish_Question.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\QuestionCannonStates\GridConnon_CannonState_ShootingSynthesis_Question.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\QuestionCannonStates\GridConnon_CannonState_Shooting_Question.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\StateFactories\GridConnon_NormalCannonStateFactory.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\StateFactories\GridConnon_QuestionCannonStateFactory.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\StateFactories\GridConnon_QuestionTriangleCannonStateFactory.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\StateFactories\GridConnon_TreasureCannonStateFactory.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\StateFactories\GridConnon_TriangleCannonStateFactory.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\TreasureCannonStates\GridConnon_CannonState_BackRow_Treasure.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\TreasureCannonStates\GridConnon_CannonState_Destroying_Treasure.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\TreasureCannonStates\GridConnon_CannonState_FirstRow_Treasure.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\TreasureCannonStates\GridConnon_CannonState_Moving_Treasure.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\TreasureCannonStates\GridConnon_CannonState_Prop_PickOut_Treasure.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\TreasureCannonStates\GridConnon_CannonState_Prop_Random_Treasure.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\TreasureCannonStates\GridConnon_CannonState_ShootingSynthesisFinish_Treasure.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\TreasureCannonStates\GridConnon_CannonState_ShootingSynthesis_Treasure.cs" />
     <Compile Include="Assets\0_GAME\Moduler_GridConnon\States\TreasureCannonStates\GridConnon_CannonState_Shooting_Treasure.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\LevelCSV_Creator\TileCube_LevelConverter.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\Objects\TileCube_Barrier.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\Objects\TileCube_GoldPig.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\Objects\TileCube_Key.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\Objects\TileCube_LargeBlock.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\Objects\TileCube_SingleBlock.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\Objects\TileCube_Spawner.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\Objects\TileCube_Spawner_Func.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\Objects\Tool_PreSpawn_Manager.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\TileCube_Const_Data.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\TileCube_Controller.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\TileCube_InputManager.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\TileCube_Manager.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\TileCube_Map.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\TileCube_Object.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\TileCube_SceneColorer.cs" />
     <Compile Include="Assets\0_GAME\Moduler_TileCube\Tool_Demo_RayDestroyer.cs" />
     <Compile Include="Assets\0_GAME\RES Emojis 45\Scripts\ParentFitter.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Demo\Scripts\ETFXButtonScript.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Demo\Scripts\ETFXFireProjectile.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Demo\Scripts\ETFXLoopScript.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Demo\Scripts\ETFXMouseOrbit.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Demo\Scripts\ETFXProjectileScript.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Demo\Scripts\ETFXSceneManager.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Demo\Scripts\ETFXTarget.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Demo\Scripts\VFX Library\PEButtonScript.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Demo\Scripts\VFX Library\ParticleEffectsLibrary.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Demo\Scripts\VFX Library\UICanvasManager.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Scripts\ETFXLightFade.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Scripts\ETFXPitchRandomizer.cs" />
     <Compile Include="Assets\0_GAME\RES Epic Toon FX\Scripts\ETFXRotation.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_AutoStopLoopedEffect.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo_New.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo_RandomDir.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo_RandomDirectionTranslate.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo_RotateCamera.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo_Translate.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Scripts\CFX_AutoDestructShuriken.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Scripts\CFX_AutoRotate.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Scripts\CFX_AutodestructWhenNoChildren.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Scripts\CFX_InspectorHelp.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Scripts\CFX_LightIntensityFade.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Scripts\CFX_ShurikenThreadFix.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Spawn System\CFX_SpawnSystem.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Demo\Scripts\PolygonBeamScript.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Demo\Scripts\PolygonButtonScript.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Demo\Scripts\PolygonFireProjectile.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Demo\Scripts\PolygonLoopScript.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Demo\Scripts\PolygonOrbit.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Demo\Scripts\PolygonProjectileScript.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Demo\Scripts\PolygonSceneSelect.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Scripts\PolygonLightFade.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Scripts\PolygonLightFlicker.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Scripts\PolygonRotation.cs" />
     <Compile Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Scripts\PolygonSoundSpawn.cs" />
     <Compile Include="Assets\0_GAME\RES_通用格斗动作\Shinabro\Platform_Animation\Scripts\PlayerController_Platform.cs" />
     <Compile Include="Assets\0_GAME\RES_通用格斗动作\Shinabro\Platform_Animation\Scripts\TargetCamera_Platform.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\Code4927.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\EffectController.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\SDK_Common_AD.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\SDK_Common_COSData.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\SDK_Common_Main.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\SDK_Common_Playerprefs.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\SDK_Common_Sound.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\SDK_Common_ToolData.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\SDK_Common_Vib.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\SelfDestroy.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\Tool_MessageTip.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\Tool_MessageTip_Item.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\UI_Ani_Scale.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Comman\Scripts\XLUA_FuncFix.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\Frame\AudMgr.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\Frame\ButtonClickScale.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\Frame\Eight_ADBanner.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\Frame\FrameLuaManager.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\Frame\LuaBehaviourHttp.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\Frame\LuaBehaviourHttpAwake.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\Frame\LuaCallCSTool.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\Frame\LuaTimerTicker.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\Frame\WXModels.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\Frame\WXTouchInputOverride.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\Frame\XluaFixer.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\PLAT_OV\PLAT_OV_GamingController.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\PLAT_OV\PLAT_OV_MainController.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\PLAT_OV\PLAT_OV_Start_Panel.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\PLAT_OV\PLAT_OV_Touch_Box_Panel.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\PLAT_OV\PLAT_OV_Touch_Canvas.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\UI\UIEndPanel.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\UI\UIEndPanelFix.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\UI\UIManager.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\UI\UIStartPanel.cs" />
     <Compile Include="Assets\1_FRAME\0_SDK_Frame\Scripts\Csharp\UI\UITrapPanel.cs" />
     <Compile Include="Assets\2_MXR_BRIGE\MXR_BRIGE.cs" />
     <Compile Include="Assets\2_MXR_BRIGE\Scripts\BRIGE_MessageTip.cs" />
     <Compile Include="Assets\2_MXR_BRIGE\Scripts\BRIGE_MessageTip_Item.cs" />
     <Compile Include="Assets\Tool\3DHpBar\ThreeDHPBar.cs" />
     <Compile Include="Assets\Tool\3DHpBar\Tool_ThreeDHPbar_Manager.cs" />
     <Compile Include="Assets\Tool\BazierMove_Tool.cs" />
     <Compile Include="Assets\Tool\ChineseTextExtractor.cs" />
     <Compile Include="Assets\Tool\ChineseTextExtractorConsole.cs" />
     <Compile Include="Assets\Tool\SYS\Tool_SYS_Vib.cs" />
     <Compile Include="Assets\Tool\TextFly\TextFlyTool.cs" />
     <Compile Include="Assets\Tool\Tool_Animator_StateEnter.cs" />
     <Compile Include="Assets\Tool\Tool_BSAI_Base.cs" />
     <Compile Include="Assets\Tool\Tool_BoomEffect\Tool_Boom_Effect.cs" />
     <Compile Include="Assets\Tool\Tool_BrokeComponent.cs" />
     <Compile Include="Assets\Tool\Tool_BtnClickScale.cs" />
     <Compile Include="Assets\Tool\Tool_Bullet\Tool_Bullet_Group.cs" />
     <Compile Include="Assets\Tool\Tool_Bullet\Tool_Bullet_Manager.cs" />
     <Compile Include="Assets\Tool\Tool_Bullet\Tool_Bullet_Object.cs" />
     <Compile Include="Assets\Tool\Tool_BulletCounter_Data.cs" />
     <Compile Include="Assets\Tool\Tool_CameraFollow_ThirdRole.cs" />
     <Compile Include="Assets\Tool\Tool_CheckTrigger\Tool_CheckTrigger.cs" />
     <Compile Include="Assets\Tool\Tool_Code4927.cs" />
     <Compile Include="Assets\Tool\Tool_ColCheck.cs" />
     <Compile Include="Assets\Tool\Tool_ComEventSys.cs" />
     <Compile Include="Assets\Tool\Tool_ComboText\Tool_ComboText.cs" />
     <Compile Include="Assets\Tool\Tool_CountlySendEvent\MatchUI_CountlySendEvent\Tool_CountlySendEvent_MatchUI_Record.cs" />
     <Compile Include="Assets\Tool\Tool_CountlySendEvent\Tool_CountlySendEvent.cs" />
     <Compile Include="Assets\Tool\Tool_CountlySendEvent\Tool_CountlySendEvent_ConstData.cs" />
     <Compile Include="Assets\Tool\Tool_CueText\Tool_CueText.cs" />
     <Compile Include="Assets\Tool\Tool_CutomSelcter.cs" />
     <Compile Include="Assets\Tool\Tool_DayCheck.cs" />
     <Compile Include="Assets\Tool\Tool_DestroySelfByTime.cs" />
     <Compile Include="Assets\Tool\Tool_DissolveEffect\Tool_DissolveEffect.cs" />
     <Compile Include="Assets\Tool\Tool_DissolveEffect\Tool_DissolveEffect2D.cs" />
     <Compile Include="Assets\Tool\Tool_Editor_GetChineseText.cs" />
     <Compile Include="Assets\Tool\Tool_Editor_ResourcesFloderChange.cs" />
     <Compile Include="Assets\Tool\Tool_Effect_Controller.cs" />
     <Compile Include="Assets\Tool\Tool_ElasticScaler.cs" />
     <Compile Include="Assets\Tool\Tool_Enum_PercentSelector.cs" />
     <Compile Include="Assets\Tool\Tool_EquipTakePhoto\Tool_EquipTakePhoto.cs" />
     <Compile Include="Assets\Tool\Tool_FindObjectInParent.cs" />
     <Compile Include="Assets\Tool\Tool_FlattenCapCollider.cs" />
     <Compile Include="Assets\Tool\Tool_FrameRateSet.cs" />
     <Compile Include="Assets\Tool\Tool_GetAllFloderMatOut.cs" />
     <Compile Include="Assets\Tool\Tool_GetOBJ.cs" />
     <Compile Include="Assets\Tool\Tool_GetOrAddComponent.cs" />
     <Compile Include="Assets\Tool\Tool_GetScreenCenterToPoint.cs" />
     <Compile Include="Assets\Tool\Tool_Grenade\Tool_Grenade_Manager.cs" />
     <Compile Include="Assets\Tool\Tool_Grenade\Tool_Grenade_Object.cs" />
     <Compile Include="Assets\Tool\Tool_InputManager.cs" />
     <Compile Include="Assets\Tool\Tool_InvokeAction.cs" />
     <Compile Include="Assets\Tool\Tool_IsPointerOverUI.cs" />
     <Compile Include="Assets\Tool\Tool_JRJELLY\Tool_JRJELLY_BulletTest.cs" />
     <Compile Include="Assets\Tool\Tool_JRJELLY\Tool_JRJELLY_BulletTestDemo.cs" />
     <Compile Include="Assets\Tool\Tool_JRJELLY\Tool_JRJELLY_Controller.cs" />
     <Compile Include="Assets\Tool\Tool_JRJELLY\Tool_JRJELLY_Demo.cs" />
     <Compile Include="Assets\Tool\Tool_JRJELLY\Tool_JRJELLY_Settings.cs" />
     <Compile Include="Assets\Tool\Tool_JRJELLY\Tool_JRJELLY_Simple.cs" />
     <Compile Include="Assets\Tool\Tool_JRJELLY\Tool_JRJELLY_TestFix.cs" />
     <Compile Include="Assets\Tool\Tool_JRJELLY\Tool_OutlineController.cs" />
     <Compile Include="Assets\Tool\Tool_JRJELLY\Tool_OutlineDebug.cs" />
     <Compile Include="Assets\Tool\Tool_JRJELLY\Tool_OutlineExample.cs" />
     <Compile Include="Assets\Tool\Tool_LanguageManager\Tool_LanguageManager.cs" />
     <Compile Include="Assets\Tool\Tool_List_RandPercent.cs" />
     <Compile Include="Assets\Tool\Tool_LoadingScreen\Tool_LoadingScreen.cs" />
     <Compile Include="Assets\Tool\Tool_LoadingScreen\Tool_LoadingScreen_RotateCicle.cs" />
     <Compile Include="Assets\Tool\Tool_MatShakeColor.cs" />
     <Compile Include="Assets\Tool\Tool_MaterialBatchOptimizer.cs" />
     <Compile Include="Assets\Tool\Tool_MeshOutline.cs" />
     <Compile Include="Assets\Tool\Tool_NavMeshPath.cs" />
     <Compile Include="Assets\Tool\Tool_OpenURLOnBrowser.cs" />
     <Compile Include="Assets\Tool\Tool_OriginPointEdit\Tool_Editor_OriginPointEdit.cs" />
     <Compile Include="Assets\Tool\Tool_OriginPointEdit\Tool_OriginPointEdit.cs" />
     <Compile Include="Assets\Tool\Tool_OriginPointEdit\Tool_OriginPointEdit_Example.cs" />
     <Compile Include="Assets\Tool\Tool_PlayerHitEffect\Tool_PlayerHitEffect.cs" />
     <Compile Include="Assets\Tool\Tool_REPACKIMG.cs" />
     <Compile Include="Assets\Tool\Tool_ReplaceMeshByUnNullMesh.cs" />
     <Compile Include="Assets\Tool\Tool_ReplaceMeshByUnNullMesh_Cut.cs" />
     <Compile Include="Assets\Tool\Tool_RotateEulerAngVec.cs" />
     <Compile Include="Assets\Tool\Tool_SaveData_Base.cs" />
     <Compile Include="Assets\Tool\Tool_Scene_EatObjPoint\Tool_Scene_EatObjPoint_Creater.cs" />
     <Compile Include="Assets\Tool\Tool_Scene_EatObjPoint\Tool_Scene_EatObjPoint_Obj.cs" />
     <Compile Include="Assets\Tool\Tool_ShakeTrans.cs" />
     <Compile Include="Assets\Tool\Tool_ShotRect.cs" />
     <Compile Include="Assets\Tool\Tool_ShuffleAndSort.cs" />
     <Compile Include="Assets\Tool\Tool_StartCountText\Tool_StartCountText.cs" />
     <Compile Include="Assets\Tool\Tool_TestLineRen\Tool_TestLineRen.cs" />
     <Compile Include="Assets\Tool\Tool_TileGrid\Tool_TileGrid.cs" />
     <Compile Include="Assets\Tool\Tool_TimeFormat.cs" />
     <Compile Include="Assets\Tool\Tool_UITextJelly.cs" />
     <Compile Include="Assets\Tool\Tool_UITextRhym.cs" />
     <Compile Include="Assets\Tool\Tool_WorldPos_To_UIPos.cs" />
     <Compile Include="Assets\Tool_Editor_SwichOV.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\AssemblyInfo.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\BZip2\BZip2.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\BZip2\BZip2Constants.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\BZip2\BZip2Exception.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\BZip2\BZip2InputStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\BZip2\BZip2OutputStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Checksum\Adler32.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Checksum\BZip2Crc.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Checksum\Crc32.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Checksum\IChecksum.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Core\FileSystemScanner.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Core\INameTransform.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Core\IScanFilter.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Core\NameFilter.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Core\PathFilter.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Core\StreamUtils.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Core\WindowsPathUtils.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Encryption\PkzipClassic.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Encryption\ZipAESStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Encryption\ZipAESTransform.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\GZip\GZip.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\GZip\GZipConstants.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\GZip\GZipException.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\GZip\GZipFile.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\GZip\GzipInputStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\GZip\GzipOutputStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Lzw\LzwConstants.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Lzw\LzwException.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Lzw\LzwInputStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\SharpZipBaseException.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Tar\InvalidHeaderException.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Tar\TarArchive.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Tar\TarBuffer.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Tar\TarEntry.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Tar\TarException.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Tar\TarHeader.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Tar\TarInputStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Tar\TarOutputStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\Deflater.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\DeflaterConstants.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\DeflaterEngine.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\DeflaterHuffman.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\DeflaterPending.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\Inflater.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\InflaterDynHeader.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\InflaterHuffmanTree.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\PendingBuffer.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\Streams\DeflaterOutputStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\Streams\InflaterInputStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\Streams\OutputWindow.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\Compression\Streams\StreamManipulator.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\FastZip.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\IEntryFactory.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\WindowsNameTransform.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\ZipConstants.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\ZipEntry.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\ZipEntryFactory.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\ZipException.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\ZipExtraData.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\ZipFile.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\ZipHelperStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\ZipInputStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\ZipNameTransform.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\Zip\ZipOutputStream.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\obj\Debug\TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\obj\Debug\TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs" />
     <Compile Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\obj\Debug\TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\OVManager.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QG.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QGBannerAd.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QGBaseAd.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QGBoxBannerAd.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QGBoxPortalAd.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QGCallBackManager.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QGCustomAd.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QGInterstitialAd.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QGLog.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QGMiniGameManager.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QGModel.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QGNativeAd.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\QGRewardedVideoAd.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\UIPrivatePanel.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\UIPrivateTextPanel.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\UIbtnPrivate.cs" />
     <Compile Include="Assets\VIVO-OPPO-GAME-SDK\UIbtnVib.cs" />
     <Compile Include="Assets\XLua\Examples\01_Helloworld\Helloworld.cs" />
     <Compile Include="Assets\XLua\Examples\02_U3DScripting\LuaBehaviour.cs" />
     <Compile Include="Assets\XLua\Examples\04_LuaObjectOrented\InvokeLua.cs" />
     <Compile Include="Assets\XLua\Examples\05_NoGc\NoGc.cs" />
     <Compile Include="Assets\XLua\Examples\06_Coroutine\CoroutineTest.cs" />
     <Compile Include="Assets\XLua\Examples\06_Coroutine\Coroutine_Runner.cs" />
     <Compile Include="Assets\XLua\Examples\07_AsyncTest\AsyncTest.cs" />
     <Compile Include="Assets\XLua\Examples\07_AsyncTest\MessageBox.cs" />
     <Compile Include="Assets\XLua\Examples\08_Hotfix\HotfixTest.cs" />
     <Compile Include="Assets\XLua\Examples\08_Hotfix\HotfixTest2.cs" />
     <Compile Include="Assets\XLua\Examples\08_Hotfix\StatefullTest.cs" />
     <Compile Include="Assets\XLua\Examples\09_GenericMethod\Foo.cs" />
     <Compile Include="Assets\XLua\Examples\09_GenericMethod\GenericMethodExample.cs" />
     <Compile Include="Assets\XLua\Examples\11_RawObject\RawObjectTest.cs" />
     <Compile Include="Assets\XLua\Examples\12_ReImplementInLua\ReImplementInLua.cs" />
     <Compile Include="Assets\XLua\Examples\ExampleGenConfig.cs" />
     <Compile Include="Assets\XLua\Src\CodeEmit.cs" />
     <Compile Include="Assets\XLua\Src\CopyByValue.cs" />
     <Compile Include="Assets\XLua\Src\DelegateBridge.cs" />
     <Compile Include="Assets\XLua\Src\GenAttributes.cs" />
     <Compile Include="Assets\XLua\Src\GenericDelegateBridge.cs" />
     <Compile Include="Assets\XLua\Src\InternalGlobals.cs" />
     <Compile Include="Assets\XLua\Src\LuaBase.cs" />
     <Compile Include="Assets\XLua\Src\LuaDLL.cs" />
     <Compile Include="Assets\XLua\Src\LuaEnv.cs" />
     <Compile Include="Assets\XLua\Src\LuaException.cs" />
     <Compile Include="Assets\XLua\Src\LuaFunction.cs" />
     <Compile Include="Assets\XLua\Src\LuaTable.cs" />
     <Compile Include="Assets\XLua\Src\MethodWarpsCache.cs" />
     <Compile Include="Assets\XLua\Src\ObjectCasters.cs" />
     <Compile Include="Assets\XLua\Src\ObjectPool.cs" />
     <Compile Include="Assets\XLua\Src\ObjectTranslator.cs" />
     <Compile Include="Assets\XLua\Src\ObjectTranslatorPool.cs" />
     <Compile Include="Assets\XLua\Src\RawObject.cs" />
     <Compile Include="Assets\XLua\Src\SignatureLoader.cs" />
     <Compile Include="Assets\XLua\Src\StaticLuaCallbacks.cs" />
     <Compile Include="Assets\XLua\Src\TemplateEngine\TemplateEngine.cs" />
     <Compile Include="Assets\XLua\Src\TypeExtensions.cs" />
     <Compile Include="Assets\XLua\Src\Utils.cs" />
     <Compile Include="Assets\XLua\Tutorial\CSharpCallLua\CSCallLua.cs" />
     <Compile Include="Assets\XLua\Tutorial\LoadLuaScript\ByFile\ByFile.cs" />
     <Compile Include="Assets\XLua\Tutorial\LoadLuaScript\ByString\ByString.cs" />
     <Compile Include="Assets\XLua\Tutorial\LoadLuaScript\Loader\CustomLoader.cs" />
     <Compile Include="Assets\XLua\Tutorial\LuaCallCSharp\LuaCallCs.cs" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors\!ToonyColors Readme.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Legacy\TCP2_OutlineOnly Blended.shader" />
     <None Include="Assets\XLua\Examples\07_AsyncTest\Resources\async_test.lua.txt" />
     <None Include="Assets\XLua\Tutorial\LoadLuaScript\ByFile\Resources\byfile.lua.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\SG2\Hologram.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Material Layers Demo Assets\Shaders\Snow Y Position Based.shader" />
     <None Include="Assets\0_GAME\RES Emojis 45\Demo\unity-chan!\Unity-chan! Model\Art\UnityChanShader\Shader\Unitychan_chara_hada_blend.shader" />
     <None Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Shaders\Standard\PolyRimLightSolid.shader" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
     <None Include="Assets\Tool\OutlineGuide.shader" />
     <None Include="Assets\Tool\Tool_DissolveEffect\Resources\Tool_DissolveEffect\DissolveEffect2D.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\SG2\Stylized Water.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Water\Poison.shader" />
     <None Include="Assets\Tool\Tool_JRJELLY\JellyEffect_ShaderModule.cginc" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Legacy\TCP2_OutlineOnly SM2.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Rim Lighting.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Ramp Shading LWRP.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Standard PBS\TCP2_PBS_Core.cginc" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_MatCap.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Terrain.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Vertex Colors Specular.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Material Layers Demo Assets\Shaders\Snow Normal Based.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Cat\Style 1.shader" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Shaders\CFXM_MobileParticleAdd_Alpha8.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Sketch.txt" />
     <None Include="Assets\0_GAME\RES Emojis 45\Demo\unity-chan!\Unity-chan! Model\Art\UnityChanShader\Shader\Unitychan_chara_eye_blend.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Cat\Style 6.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_ShadowHSV.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\SG2\Detail Texture Simple.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\UnityChan\Style 5.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\UnityChan\Style 4.shader" />
     <None Include="Assets\Tool\Tool_LanguageManager\Resources\Tool_LanguageManager\LocLanguage\Chinese.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Standard PBS\TCP2_PBS_ShadowMeta.shader" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\CFXR Assets\Shaders\CFXR Particle Distortion.shader" />
     <None Include="Assets\0_GAME\RES Emojis 45\Demo\unity-chan!\Unity-chan! Model\Art\UnityChanShader\Shader\Unitychan_chara_akarami_blend.shader" />
     <None Include="Assets\0_GAME\RES Emojis 45\Demo\unity-chan!\Unity-chan! Model\Art\UnityChanShader\Shader\Unitychan_chara_fuku.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\PBS\Sketch.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\UnityChan\Style 4 Skin.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\PBS\Outline Behind.shader" />
     <None Include="Assets\Tool\Tool_DissolveEffect\Resources\Tool_DissolveEffect\DissolveEffect.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Standard PBS\TCP2_Standard PBS Outline Blended.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Legacy\Include\TCP2_Outline_SM2.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\UnityChan\Style 1 Skin.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Specular.txt" />
     <None Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Shaders\Standard\PolyRimLightSelective.shader" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\!JMO Assets Readme.txt" />
     <None Include="Assets\Tool\Tool_JRJELLY\Tool_JRJELLY_Shader_Fixed_Outline BLT_NoOutLineAndOverlay.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Standard PBS\TCP2_Standard PBS (Specular).shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\SG2\Detail Texture.shader" />
     <None Include="Assets\2_MXR_BRIGE\版本.txt" />
     <None Include="Assets\0_GAME\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
     <None Include="Assets\XLua\Examples\06_Coroutine\Resources\coruntine_test.lua.txt" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Shaders\CFXM_MobileParticleAB_Alpha8.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Standard PBS\TCP2_Standard PBS Outline.shader" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\CFXR Assets\Shaders\CFXR_SETTINGS.cginc" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\CFXR Assets\Shaders\CFXR.cginc" />
     <None Include="Assets\0_GAME\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Dithering.txt" />
     <None Include="Assets\XLua\Resources\xlua\util.lua.txt" />
     <None Include="Assets\0_GAME\RES Emojis 45\Demo\unity-chan!\Unity-chan! Model\Art\UnityChanShader\Shader\Unitychan_chara_hada.shader" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_SDF-Mobile Masking.shader" />
     <None Include="Assets\0_GAME\RES Emojis 45\Demo\unity-chan!\Unity-chan! Model\Art\UnityChanShader\Shader\Unitychan_chara_hair_ds.shader" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo_WaterWiggle.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\UnityChan\Style 3.shader" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMPro_Properties.cginc" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Hybrid 2\TCP2 Hybrid 2 Include.cginc" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\CFXR Assets\Shaders\CFXR Particle Ubershader.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Default\Old School.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\SG2_Template_Default.txt" />
     <None Include="Assets\0_GAME\Moduler_Common_ReserveGrid\Guide.txt" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_SDF-Mobile.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\UnityChan\Style 5 Skin.shader" />
     <None Include="Assets\0_GAME\Moduler_GridConnon\GridConnon_UnlitColorWithQueue.shader" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMPro.cginc" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Screen Space UV.txt" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_SDF-Surface-Mobile.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Normal Mapping.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_DepthTexture.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Default\Dissolve.shader" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_SDF-Surface.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Outline.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_NdotL Stylization.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Water\Lava Cartoon.shader" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_SDF.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Cat\Style 2.shader" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX (legacy)\CartoonFX Readme.txt" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\UnityChan\Style 3 Skin.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Legacy\Include\TCP2_Outline_Behind.shader" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_Bitmap-Mobile.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Vertical Fog.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Vertex Colors OutLine 2.shader" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\CFXR Assets\Shaders\CFXR Particle Procedural Ring.shader" />
     <None Include="Assets\Tool\Tool_JRJELLY\Tool_JRJELLY_Shader_Fixed_Outline.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_VertExmotion.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_Default.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\PBS\Blended Outline.shader" />
     <None Include="Assets\2_MXR_BRIGE\说明.txt" />
     <None Include="Assets\0_GAME\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\SG2\Water.shader" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMPro_Surface.cginc" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors\Shaders 2.0\Regular.shader" />
     <None Include="Assets\0_GAME\RES_通用格斗动作\Shinabro\Platform_Animation\Readme.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Legacy\TCP2_OutlineOnly Blended SM2.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_CustomAmbient.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Legacy\Include\TCP2_Outline_Stencil.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Triplanar.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Vertex Colors Emissive.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Water.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\UnityChan\Style 1.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Wind.txt" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_SDF-Mobile Overlay.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Cat\Style 3.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Cat\Style 4.shader" />
     <None Include="Assets\0_GAME\RES_Effect_Lana Studio\Hyper Casual FX\Demo\Readme.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Default\Snow Accumulation.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Ground.shader" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_Bitmap.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Water.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Default\Sketch.shader" />
     <None Include="Assets\XLua\Resources\perf\profiler.lua.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\SG2_Template_URP.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Default\HSV.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Legacy\Include\TCP2_Outline_Behind_SM2.shader" />
     <None Include="Assets\Tool\Tool_JRJELLY\Tool_JRJELLY_Shader_Fixed_Outline BLT_Cube.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_NoTile Sampling.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\SG2\Animated Dissolve.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Triplanar Sampling.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Water\Realistic.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Decal.shader" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\Readme Cartoon FX Remaster.txt" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\CFXR Assets\Shaders\CFXR Particle Glow.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_PBS.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Cat\Style 5.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Vertex Colors OutLine.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Legacy\Include\TCP2_Outline_Include.cginc" />
     <None Include="Assets\0_GAME\Moduler_GridConnon\LevelCSV_Creator\规则.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\!ToonyColorsPro Readme.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Vertex Colors Specular Jelly.shader" />
     <None Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Shaders\Standard\PolyRimLightTransparent.shader" />
     <None Include="Assets\0_GAME\RES Emojis 45\Demo\unity-chan!\Unity-chan! Model\Art\UnityChanShader\Shader\Unitychan_chara_fuku_ds.shader" />
     <None Include="Assets\Tool\Tool_JRJELLY\Tool_JRJELLY_Shader_Fixed_Outline BLT_Connon.shader" />
     <None Include="Assets\XLua\Resources\perf\memory.lua.txt" />
     <None Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Shaders\Standard\PolyLitSurface.shader" />
     <None Include="Assets\1_FRAME\1_Res_Project\Resources\Text\FName.txt" />
     <None Include="Assets\0_GAME\RES Epic Toon FX\Prefabs\Interactive\Interactive FX.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors\Shaders 2.0\Rim Outline.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\PBS\Hand Painted.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Material Layers Demo Assets\Shaders\Snow Texture Based.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_VertexDisplacement.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Legacy\TCP2_OutlineOnly.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Vertex Colors.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\UnlitVertexColorsTransparent.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Material Layers Demo Assets\Shaders\Snow Sand Advanced.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\UnityChan\Style 6 Skin.shader" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\CFXR Assets\Shaders\CFXR_PASSES.cginc" />
     <None Include="Assets\XLua\Examples\03_UIEvent\ButtonInteraction.lua.txt" />
     <None Include="Assets\0_GAME\Moduler_GridConnon\LevelCSV_Creator\ColorMap.txt" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
     <None Include="Assets\0_GAME\Moduler_TileCube\LevelCSV_Creator\ColorMap.txt" />
     <None Include="Assets\0_GAME\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
     <None Include="Assets\0_GAME\RES Epic Toon FX\Prefabs\Environment\Environment FX.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_AlbedoHSV.txt" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Easy Editor\CartoonFX Easy Editor Readme.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Hash Functions.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Water\Lava.shader" />
     <None Include="Assets\Fonts\FontText.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Standard PBS\TCP2_PBS_BRDF.cginc" />
     <None Include="Assets\XLua\Examples\02_U3DScripting\LuaTestScript.lua.txt" />
     <None Include="Assets\XLua\Examples\07_AsyncTest\Resources\message_box.lua.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_Water.txt" />
     <None Include="Assets\0_GAME\RES Emojis 45\Read Me.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\SG Demo Assets\_Misc Assets\TCP2_Demo_3DText.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Standard PBS\TCP2_Standard PBS.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_Water_CurvedWorld.txt" />
     <None Include="Assets\0_GAME\RES Emojis 45\Demo\unity-chan!\Unity-chan! Model\Art\UnityChanShader\Shader\Unitychan_chara_eyelash_blend.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\UnityChan\Style 6.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors\Shaders 2.0\Rim Alpha.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_Default_CurvedWorld.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Default\Comic Book.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Reflection.txt" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_SDF Overlay.shader" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\CFXR Prefabs\Texts\_Make your own text effect.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Ramp Shading.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Standard PBS\TCP2_Standard PBS Outline Blended (Specular).shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Legacy\Include\TCP2_Outline.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Water\Water WindWaker.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_Terrain.txt" />
     <None Include="Assets\0_GAME\RES_Effect_Polygon Arsenal\Polygon Arsenal - Readme.txt" />
     <None Include="Assets\0_GAME\RES Emojis 45\Demo\unity-chan!\Unity-chan! Model\Art\Stage\Shader\AlphaMask.shader" />
     <None Include="Assets\0_GAME\RES Emojis 45\Demo\unity-chan!\Unity-chan! Model\Art\UnityChanShader\Shader\Unitychan_chara_hair.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Standard PBS\TCP2_Standard PBS Outline (Specular).shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Aura2.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\SG2\Wind Animation.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_CurvedWorld.txt" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\CFXR Assets\Shaders\CFXR_URP.cginc" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Dissolve.txt" />
     <None Include="Assets\XLua\Resources\tdr\tdr.lua.txt" />
     <None Include="Assets\0_GAME\TextMesh Pro\Shaders\TMP_Sprite.shader" />
     <None Include="Assets\VIVO-GAME-SDK\ICSharpCode.SharpZipLib\obj\Debug\ICSharpCode.SharpZipLib.csproj.FileListAbsolute.txt" />
     <None Include="Assets\XLua\CHANGELOG.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Water\Reflection.shader" />
     <None Include="Assets\Tool\Tool_LanguageManager\Resources\Tool_LanguageManager\LocLanguage\English.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_HSV.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Demo TCP2\Cat Demo Assets\Shaders\Cat Demo\Vertex Colors Trans.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Subsurface Scattering.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Default\Subsurface Scattering.shader" />
     <None Include="Assets\XLua\Examples\06_Coroutine\Resources\cs_coroutine.lua.txt" />
     <None Include="Assets\Tool\Tool_JRJELLY\Tool_JRJELLY_Shader_Fixed_Outline BLT_NoOutLine.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors\Shaders 2.0\Rim Lighting.shader" />
     <None Include="Assets\0_GAME\RES Emojis 45\Demo\unity-chan!\Unity-chan! Model\Art\UnityChanShader\Shader\Unitychan_chara_eye.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\SG2\Vertex Color Albedo.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates 2\Modules\Module_Texture Blending.txt" />
     <None Include="Assets\0_GAME\RES Epic Toon FX\Prefabs\Combat\Combat FX.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders Generated\Examples\Water\Water WindWaker2.shader" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_SurfacePBS.txt" />
     <None Include="Assets\0_GAME\RES CommonCatroonShaderDEL\Toony Colors Pro\Shaders\Standard PBS\TCP2_PBS_Main.cginc" />
 <Reference Include="Unity.Timeline.Editor">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Library/ScriptAssemblies/Unity.Timeline.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.VSCode.Editor">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Library/ScriptAssemblies/Unity.VSCode.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.TextMeshPro.Editor">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UI">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Library/ScriptAssemblies/UnityEngine.UI.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Timeline">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Library/ScriptAssemblies/Unity.Timeline.dll</HintPath>
 </Reference>
 <Reference Include="Unity.CollabProxy.Editor">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Rider.Editor">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Library/ScriptAssemblies/Unity.Rider.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.TextMeshPro">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Library/ScriptAssemblies/Unity.TextMeshPro.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.UI">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Library/ScriptAssemblies/UnityEditor.UI.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AIModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AccessibilityModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AndroidJNIModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AnimationModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AssetBundleModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AudioModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClothModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CoreModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CrashReportingModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.DSPGraphModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.DirectorModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GameCenterModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GridModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.HotReloadModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.IMGUIModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ImageConversionModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.InputModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.InputLegacyModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.JSONSerializeModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.LocalizationModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ParticleSystemModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.PerformanceReportingModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.PhysicsModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.Physics2DModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ProfilerModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ScreenCaptureModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SharedInternalsModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpriteMaskModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpriteShapeModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.StreamingModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SubstanceModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SubsystemsModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TLSModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TerrainModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TerrainPhysicsModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TextCoreModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TextRenderingModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TilemapModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIElementsModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UNETModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UmbraModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityAnalyticsModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityConnectModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityTestProtocolModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestAudioModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestTextureModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestWWWModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VFXModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VRModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VehiclesModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VideoModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.WebGLModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/PlaybackEngines/WebGLSupport/Managed/UnityEngine.WebGLModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.WindModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.XRModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
 </Reference>
 <Reference Include="LitJson">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Assets/Plugins/LitJson.dll</HintPath>
 </Reference>
 <Reference Include="Compress.Info">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Assets/VIVO-GAME-SDK/ICSharpCode.SharpZipLib/Compress.Info.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.iOS.Extensions.Xcode">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.iOS.Extensions.Common">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll</HintPath>
 </Reference>
 <Reference Include="mscorlib">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll</HintPath>
 </Reference>
 <Reference Include="System">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll</HintPath>
 </Reference>
 <Reference Include="System.Core">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.Linq">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics.Vectors">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Http">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.CSharp">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll</HintPath>
 </Reference>
 <Reference Include="System.Data">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.Win32.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="netstandard">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll</HintPath>
 </Reference>
 <Reference Include="System.AppContext">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.Concurrent">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.NonGeneric">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.Specialized">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.Annotations">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.EventBasedAsync">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.TypeConverter">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll</HintPath>
 </Reference>
 <Reference Include="System.Console">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll</HintPath>
 </Reference>
 <Reference Include="System.Data.Common">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Contracts">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Debug">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.FileVersionInfo">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Process">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.StackTrace">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.TextWriterTraceListener">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Tools">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.TraceSource">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll</HintPath>
 </Reference>
 <Reference Include="System.Drawing.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Dynamic.Runtime">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization.Calendars">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization.Extensions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Compression.ZipFile">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll</HintPath>
 </Reference>
 <Reference Include="System.IO">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.DriveInfo">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.Watcher">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.IsolatedStorage">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.MemoryMappedFiles">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Pipes">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.UnmanagedMemoryStream">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Expressions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Parallel">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Queryable">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Http.Rtc">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.NameResolution">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.NetworkInformation">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Ping">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Requests">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Security">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Sockets">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebHeaderCollection">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebSockets.Client">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebSockets">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll</HintPath>
 </Reference>
 <Reference Include="System.ObjectModel">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit.ILGeneration">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit.Lightweight">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Extensions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.Reader">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.ResourceManager">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.Writer">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.CompilerServices.VisualC">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Extensions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Handles">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Numerics">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Formatters">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Json">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Xml">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Claims">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Algorithms">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Csp">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Encoding">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.X509Certificates">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Principal">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.SecureString">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Duplex">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Http">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.NetTcp">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Security">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.Encoding">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.Encoding.Extensions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.RegularExpressions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Overlapped">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks.Parallel">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Thread">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.ThreadPool">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Timer">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll</HintPath>
 </Reference>
 <Reference Include="System.ValueTuple">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.ReaderWriter">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XDocument">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XmlDocument">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XmlSerializer">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XPath">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XPath.XDocument">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll</HintPath>
 </Reference>
 <Reference Include="UnityScript">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/UnityScript.dll</HintPath>
 </Reference>
 <Reference Include="UnityScript.Lang">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/UnityScript.Lang.dll</HintPath>
 </Reference>
 <Reference Include="Boo.Lang">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/Boo.Lang.dll</HintPath>
 </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="KinoBloom.Runtime.csproj">
      <Project>{3F7306CE-2AC1-0089-B33B-4F0622EFBE54}</Project>
      <Name>KinoBloom.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="ToonyColorsPro.Demo.Editor.csproj">
      <Project>{02DFF009-B849-06E1-3C4A-3085B1592A10}</Project>
      <Name>ToonyColorsPro.Demo.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="CFXREditor.csproj">
      <Project>{C7AD3D57-5DD6-3E1B-78F7-0EE6A026E281}</Project>
      <Name>CFXREditor</Name>
    </ProjectReference>
    <ProjectReference Include="CFXRDemo.csproj">
      <Project>{584892AA-CD29-A78B-DA6C-35A8C201A483}</Project>
      <Name>CFXRDemo</Name>
    </ProjectReference>
    <ProjectReference Include="CFXRRuntime.csproj">
      <Project>{0D9F8FE9-B358-5F31-3393-3ECD985102D9}</Project>
      <Name>CFXRRuntime</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
