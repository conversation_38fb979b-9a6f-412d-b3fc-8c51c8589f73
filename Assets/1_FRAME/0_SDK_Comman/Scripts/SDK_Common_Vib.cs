using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SDK_Common_Vib
{

    public static void VirbShort(string heavyvalue = "light")
    {

#if UNITY_EDITOR
        return;
#else

#if Wechat
        SDK_Wechat.VirbShort(heavyvalue);
#endif
#if OPPO
           OVManager.VibShort();
#endif
#if VIVO
      OVManager.VibShort();
#endif

#endif

    }

    public static void VirbLong()
    {


#if UNITY_EDITOR
        return;
#else

#if Wechat
                SDK_Wechat.VirbLong();
#endif
#if OPPO
           OVManager.VibShort();
#endif
#if VIVO
      OVManager.VibShort();
#endif

#endif

    }




}
