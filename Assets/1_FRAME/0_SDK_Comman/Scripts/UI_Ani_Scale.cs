using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

public class UI_Ani_Scale : MonoBehaviour
{
    private RectTransform RectTran
    {
        get
        {
            if (rectTran == null)
                rectTran = GetComponent<RectTransform>();

            return rectTran;
        }
    }

    private RectTransform rectTran;

    public Vector3 OpenSize;

    private Vector3 OpenSizeAdd;

    private float Speed = 20;

    Action openFinshed;
    Action closeFinished;

    Button[] disableButtons;

    int state = 0;

    private void Update()
    {
        switch(state)
        {
            case 1:

                RectTran.localScale = Vector3.Lerp(RectTran.localScale, OpenSizeAdd, Speed * Time.deltaTime);

                if(Vector3.Distance(RectTran.localScale, OpenSizeAdd) < 0.05f)
                    state = 2;

                break;
            case 2:

                RectTran.localScale = Vector3.Lerp(RectTran.localScale, OpenSize, Speed * Time.deltaTime);

                if (Vector3.Distance(RectTran.localScale, OpenSizeAdd) < 0.05f)
                {
                    state = 0;
                    openFinshed?.Invoke();
                }

                break;
            case -1:
                RectTran.localScale = Vector3.Lerp(RectTran.localScale, Vector3.zero, Speed * Time.deltaTime);

                if (Vector3.Distance(RectTran.localScale, Vector3.zero) < 0.05f)
                {
                    state = 0;
                    if (disableButtons != null)
                    {
                        for (var i = 0; i < disableButtons.Length; i++)
                            disableButtons[i].interactable = true;
                    }
                    closeFinished?.Invoke();
                }
                break;
        }
    }


    public void Open(Action fnishedCallBack = null)
    {
        openFinshed = null;
        openFinshed = fnishedCallBack;
        OpenSizeAdd = new Vector3(OpenSize.x + 0.1f, OpenSize.y + 0.1f, OpenSize.z + 0.1f);
        state = 1;
        RectTran.localScale = Vector3.zero;
    }

    public void Close(Action fnishedCallBack= null, Button[] disButtons = null)
    {
        closeFinished = null;
        closeFinished = fnishedCallBack;
        disableButtons = disButtons;
        state = -1;

        if(disableButtons != null)
        {
            for (var i = 0; i < disableButtons.Length; i++)
                disableButtons[i].interactable = false;
        }
    }

}
