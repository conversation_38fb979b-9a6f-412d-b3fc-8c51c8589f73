using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;

public class SDK_Common_ToolData : MonoBehaviour
{
    public static SDK_Common_ToolData Instance
    {
        get
        {
            if (instance == null)
            {
                instance = GameObject.Find("SDK_Common").GetComponent<SDK_Common_ToolData>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }
    private static SDK_Common_ToolData instance;


    private string CheckCityHttpPath;
   // [HideInInspector]
    public string cityStr = "";

    private Action actionFinish = null;


    public void Init(string cityHt, Action finishcall)
    {
        actionFinish = finishcall;
        CheckCityHttpPath = cityHt;
        StartCoroutine("CheckCity");
    }

    IEnumerator CheckCity()
    {


#if UNITY_EDITOR
        WWW wwwServer = new WWW(CheckCityHttpPath);
        yield return wwwServer;
        if (string.IsNullOrEmpty(wwwServer.error))
        {
            cityStr = wwwServer.text;
        }
#else

#if Wechat
        WWW wwwServer = new WWW(CheckCityHttpPath);
            yield return wwwServer;
            if (string.IsNullOrEmpty(wwwServer.error))
            {
                cityStr = wwwServer.text;
            }
#endif
#if OPPO
             Uri uri = new Uri(CheckCityHttpPath);
            UnityWebRequest uwr = UnityWebRequest.Get(uri);
            yield return uwr.SendWebRequest();
            if (uwr.isHttpError || uwr.isNetworkError)
            {
                Debug.Log(uwr.error);
            }
            else //����ɹ�
            {
                cityStr = uwr.downloadHandler.text;
                Debug.Log(cityStr);
            }
#endif
#if VIVO
          Uri uri = new Uri(CheckCityHttpPath);
            UnityWebRequest uwr = UnityWebRequest.Get(uri);
            yield return uwr.SendWebRequest();
            if (uwr.isHttpError || uwr.isNetworkError)
            {
                Debug.Log(uwr.error);
            }
            else //����ɹ�
            {
                cityStr = uwr.downloadHandler.text;
                Debug.Log(cityStr);
            }
#endif

#endif

        actionFinish?.Invoke();
        yield return null;
    }


    public string GetCity()
    {
        return cityStr;
    }

}
