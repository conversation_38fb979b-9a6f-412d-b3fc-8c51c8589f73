using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SDK_Common_AD
{
    
    public static void ShowReward(Action finish,Action close,Action error, int index = 0)
    {

#if UNITY_EDITOR
        finish();
#else

#if Wechat
SDK_Wechat.AD_ShowReward(index, finish, () => {
            Tool_MessageTip.ShowMessageTip("���δ�ۿ���ɣ�");
            close();
        }, error);
#endif
#if OPPO
      OVManager.ShowReward(finish, () => {
            Tool_MessageTip.ShowMessageTip("���δ�ۿ���ɣ�");
            close();
        });
#endif
#if VIVO
      OVManager.ShowReward(finish, () => {
            Tool_MessageTip.ShowMessageTip("���δ�ۿ���ɣ�");
            close();
        });
#endif

#endif

    }

    public static void ShowRewardNoCloseTips(Action finish, Action close, Action error, int index = 0)
    {
    
#if UNITY_EDITOR
        finish();
#else

#if Wechat
 SDK_Wechat.AD_ShowReward(index, finish, close, error);
#endif
#if OPPO
          OVManager.ShowReward(finish,close);
#endif
#if VIVO
      OVManager.ShowReward(finish,close);
#endif

#endif

    }





}
