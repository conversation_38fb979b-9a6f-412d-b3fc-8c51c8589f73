using LitJson;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEngine.Networking;

public class SDK_Common_COSData : MonoBehaviour
{
    public static SDK_Common_COSData Instance
    {
        get
        {
            if (instance == null)
            {
                if (GameObject.Find("SDK_Common"))
                    instance = GameObject.Find("SDK_Common").GetComponent<SDK_Common_COSData>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }

    private static SDK_Common_COSData instance;

    public string basepath = "https://catweuni-1258837015.cos.ap-guangzhou.myqcloud.com/";
    public string WechatAdidpath = "AD_ID/";
    public string OPPOAdidpath = "AD_ID_OV233/OPPO/";
    public string VIVOAdidpath = "AD_ID_OV233/VIVO/";
    public string Two33Adidpath = "AD_ID_OV233/233/";
    public string trapstpath = "Trap_Setting/";
    public string wxid
    {
        get
        {

#if Wechat
   return Wechat_ID;
#endif
#if OPPO
 return  OPPO_ID;
 
#endif
#if VIVO
            return VIVO_ID;

#endif
#if Two233
   return  Two33_ID;

#endif
#if APKNULL
   return  "";

#endif
        }
    }


    public string OPPO_ID = "";
    public string VIVO_ID = "";
    public string Two33_ID = "";
    public string Wechat_ID = "";

    private string GameType = "";

    public string OPPO_GameType = "";
    public string VIVO_GameType = "";
    public string Two33_GameType = "";
    public string Wechat_GameType = "";

    private JsonData ADIDJsonData = null;
    private JsonData TrapSettingJsonData = null;
    [HideInInspector]
    public string ADIDJsonStr;
    [HideInInspector]
    public string TrapSettingJsonStr;

    public string OPPO_Ruanzu_Other_Company = "";
    public string VIVO_Ruanzu_Other_Company = "";

    private Action actionFinish = null;


    public void Load(Action finishcall)
    {
        actionFinish = finishcall;
        StartCoroutine("Download");
    }

    IEnumerator Download()
    {

       
     
     


#if Wechat

        string adpath = basepath + WechatAdidpath + wxid + ".json";
#endif
#if OPPO

        string adpath = basepath + OPPOAdidpath + wxid + ".json";
              GameType = OPPO_GameType;
#endif
#if VIVO
     
        string adpath = basepath + VIVOAdidpath + wxid + ".json";
        GameType = VIVO_GameType;
#endif
#if Two233

        string adpath = basepath + Two33Adidpath + wxid + ".json";
           GameType = Two33_GameType;
#endif
#if APKNULL
 string adpath = "";
#endif

        string trappath = basepath + trapstpath + GameType + ".json";


#if UNITY_EDITOR

        Uri uri = new Uri(adpath);
        UnityWebRequest uwr = UnityWebRequest.Get(uri);
        yield return uwr.SendWebRequest();
        if (uwr.isHttpError || uwr.isNetworkError)
        {
            Debug.LogError("BREAK");
            GameObject.Destroy(GameObject.Find("SDK_Common"));
        }
        else //����ɹ�
        {
            var scode = Code4927.scode(uwr.downloadHandler.text);
            ADIDJsonData = JsonMapper.ToObject(scode);
            ADIDJsonStr = scode;
        }

        Uri uri2 = new Uri(trappath);
        UnityWebRequest uwr2 = UnityWebRequest.Get(uri2);
        yield return uwr2.SendWebRequest();
        if (uwr2.isHttpError || uwr2.isNetworkError)
        {
            Debug.Log(uwr2.error);
        }
        else //����ɹ�
        {
            TrapSettingJsonData = JsonMapper.ToObject(uwr2.downloadHandler.text);
            TrapSettingJsonStr = uwr2.downloadHandler.text;
        }
#else

#if Wechat
        WWW wwwServer = new WWW(adpath);
        yield return wwwServer;
        //code
        if (!string.IsNullOrEmpty(wwwServer.error)){
            Debug.LogError("BREAK");
            GameObject.Destroy(GameObject.Find("SDK_Common"));
            }
        if (string.IsNullOrEmpty(wwwServer.error))
        {
            var scode = Code4927.scode(wwwServer.text);
            ADIDJsonData = JsonMapper.ToObject(scode);
            ADIDJsonStr = scode;
        }


        WWW wwwServer2 = new WWW(trappath);
        yield return wwwServer2;
        if (string.IsNullOrEmpty(wwwServer2.error))
        {
            TrapSettingJsonData = JsonMapper.ToObject(wwwServer2.text);
            TrapSettingJsonStr = wwwServer2.text;
        }

#endif
#if OPPO
            Uri uri = new Uri(adpath);
        UnityWebRequest uwr = UnityWebRequest.Get(uri);
        yield return uwr.SendWebRequest();
        if (uwr.isHttpError || uwr.isNetworkError)
        {
            Debug.LogError("BREAK");
            GameObject.Destroy(GameObject.Find("SDK_Common"));
        }
        else //����ɹ�
        {
            var scode = Code4927.scode(uwr.downloadHandler.text);
            ADIDJsonData = JsonMapper.ToObject(scode);
            ADIDJsonStr = scode;
        }

        Uri uri2 = new Uri(trappath);
        UnityWebRequest uwr2 = UnityWebRequest.Get(uri2);
        yield return uwr2.SendWebRequest();
        if (uwr2.isHttpError || uwr2.isNetworkError)
        {
            Debug.Log(uwr2.error);
        }
        else //����ɹ�
        {
            TrapSettingJsonData = JsonMapper.ToObject(uwr2.downloadHandler.text);
            TrapSettingJsonStr = uwr2.downloadHandler.text;
        }
#endif
#if VIVO
            Uri uri = new Uri(adpath);
        UnityWebRequest uwr = UnityWebRequest.Get(uri);
        yield return uwr.SendWebRequest();
        if (uwr.isHttpError || uwr.isNetworkError)
        {
            Debug.LogError("BREAK");
            GameObject.Destroy(GameObject.Find("SDK_Common"));
        }
        else //����ɹ�
        {
            var scode = Code4927.scode(uwr.downloadHandler.text);
            ADIDJsonData = JsonMapper.ToObject(scode);
            ADIDJsonStr = scode;
        }

        Uri uri2 = new Uri(trappath);
        UnityWebRequest uwr2 = UnityWebRequest.Get(uri2);
        yield return uwr2.SendWebRequest();
        if (uwr2.isHttpError || uwr2.isNetworkError)
        {
            Debug.Log(uwr2.error);
        }
        else //����ɹ�
        {
            TrapSettingJsonData = JsonMapper.ToObject(uwr2.downloadHandler.text);
            TrapSettingJsonStr = uwr2.downloadHandler.text;
        }
      
#endif

#endif

        actionFinish?.Invoke();
        yield return null;
    }


    public object GetADIDData(string key)
    {
        JsonData rob = null;

        if (ADIDJsonData != null)
            if (ADIDJsonData.ContainsKey(key))
                rob = ADIDJsonData[key];


        if (rob != null)
            return CovertJsonData(rob);


        return rob;
    }

    public object GetTrapData(string key)
    {
        JsonData rob = null;

        if (TrapSettingJsonData != null)
            if (TrapSettingJsonData.ContainsKey(key))
                rob = TrapSettingJsonData[key];

        if (rob != null)
            return CovertJsonData(rob);

        return rob;
    }

    public T GetTrapDataToObject<T>(string key)
    {
        JsonData rob = null;

        if (TrapSettingJsonData != null)
            if (TrapSettingJsonData.ContainsKey(key))
                rob = TrapSettingJsonData[key];

        return JsonMapper.ToObject<T>(rob.ToJson());
    }


    public   object GetADIDDataSe(string key, string key2)
    {
        JsonData rob = null;

        if (ADIDJsonData != null)
            if (ADIDJsonData.ContainsKey(key))
                rob = ADIDJsonData[key][key2];


        if (rob != null)
            return CovertJsonData(rob);

        return rob;
    }

    public  object GetTrapDataSe(string key, string key2)
    {
        JsonData rob = null;

        if (TrapSettingJsonData != null)
            if (TrapSettingJsonData.ContainsKey(key))
                rob = TrapSettingJsonData[key][key2];



        if (rob != null)
            return CovertJsonData(rob);

        return rob;
    }

    public List<object> GetTrapDataSeAsList(string key, string key2)
    {
        JsonData rob = null;

        if (TrapSettingJsonData != null)
            if (TrapSettingJsonData.ContainsKey(key))
                rob = TrapSettingJsonData[key][key2];



        if (rob != null)
        {
            var arr = CovertJsonData(rob) as Array;
            List<object> objList = new List<object>();
            for (var i = 0; i < arr.Length; i++)
                objList.Add(arr.GetValue(i));

            return objList;

        }
         
        return null;
    }

    public int GetTrapDataSe_ArrayRandIndex(string key,string key2)
    {
        JsonData rob = null;

        if (TrapSettingJsonData != null)
            if (TrapSettingJsonData.ContainsKey(key))
                rob = TrapSettingJsonData[key][key2];



        if (rob != null)
        {
            var arr = CovertJsonData(rob) as Array;

            return UnityEngine.Random.Range(0, arr.Length);

        }

        return -1;
    }

    private  object CovertJsonDataArray(JsonData data)
    {
        object[] objs = new object[data.Count];

        for (var i = 0; i < data.Count; i++)
        {
            objs[i] = CovertJsonData(data[i]);
        }

        return objs;
    }

    private  object CovertJsonData(JsonData data)
    {
        switch (data.GetJsonType())
        {
            case JsonType.Array:
                return CovertJsonDataArray(data);
            case JsonType.Boolean:
                return Boolean.Parse(data.ToString());
            case JsonType.Double:
                return Double.Parse(data.ToString());
            case JsonType.Int:
                return int.Parse(data.ToString());
            case JsonType.Long:
                return long.Parse(data.ToString());
            case JsonType.String:
                return data.ToString();
        }
        return null;
    }


    public  void PrintObjToJson(object obj)
    {
        var s = JsonMapper.ToJson(obj).ToString();
        Regex reg = new Regex(@"(?i)\\[uU]([0-9a-f]{4})");
        s = reg.Replace(s, delegate (Match m) { return ((char)Convert.ToInt32(m.Groups[1].Value, 16)).ToString(); });
        Debug.LogError("--------------\n" + s + "\n--------------");
    }


}
