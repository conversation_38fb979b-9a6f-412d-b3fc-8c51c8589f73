using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class EffectController : MonoBeh<PERSON>our
{
    public static EffectController Instance
    {
        get
        {
            if (instance == null)
            {
                if (GameObject.Find("EffectController"))
                    instance = GameObject.Find("EffectController").GetComponent<EffectController>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }
    private static EffectController instance;

    public void CreateEffect(string name,Vector3 pos ,Vector3 angle,float timer)
    {
        GameObject effobj = Instantiate(Resources.Load<GameObject>("Effect/"+name));
        effobj.transform.position = pos;

        effobj.transform.eulerAngles = angle;


    }

}
