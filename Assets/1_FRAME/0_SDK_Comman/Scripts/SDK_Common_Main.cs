using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.SceneManagement;

public class SDK_Common_Main : MonoBehaviour
{
    public static SDK_Common_Main Instance
    {
        get
        {
            if (instance == null)
            {
                instance = GameObject.Find("SDK_Common").GetComponent<SDK_Common_Main>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }

    private static SDK_Common_Main instance;

    public static Action<int> OnLoadPorgress_Action;

    public bool isVertical = true;
    public static bool isNET = true;
    
    public static Action On_LoadCosFinished;

    private void Awake()
    {
#if MXR_BRIGE
        SceneManager.LoadScene(1);
        return;
#endif

        StartCoroutine("NetCheck_IE");


    }

 
    IEnumerator NetCheck_IE()
    {
        Uri uri = new Uri("http://catlord.infinitus-test.cn/NETCHECK.json");

        UnityWebRequest uwr = UnityWebRequest.Get(uri);
        uwr.timeout = 3000;
        yield return uwr.SendWebRequest();
        if(uwr.error == null)
        {
           // Debug.LogError("����������");
            //RIGHT
            DontDestroyOnLoad(this);

            OnLoadPorgress_Action?.Invoke(50);

            SetLoadingProgrees(0);


#if OPPO || VIVO
            Instantiate(Resources.Load<GameObject>("PLAT_OV_Prefab/PLAT_OV_MainController")).name = "PLAT_OV_MainController";
            PLAT_OV_MainController.CreateLoadUI();
            OVManager.PushLoadingUI(SDK_Common_COSData.Instance.wxid);

            SDK_Common_COSData.Instance.Load(LoadCosFinish);
#else
           SDK_Common_COSData.Instance.Load(LoadCosFinish);
#endif
        }
        else
        {
          //  Debug.LogError("û����������");
            isNET = false;
#if OPPO || VIVO
            PLAT_OV_MainController.CreateLoadUI();
            OVManager.PushLoadingUI(SDK_Common_COSData.Instance.wxid);
            StartCoroutine("DelayLoadScene");
#else
            SceneManager.LoadScene(1);
#endif
        }

    }

    IEnumerator DelayLoadScene()
    {
        yield return new WaitForSeconds(0.5f);
        SceneManager.LoadScene(1);
    }

    public void LoadLuaFinish()
    {
        Debug.Log("SDK_Common : Load Lua Finished");


        //TOOLData Init
        SDK_Common_ToolData.Instance.Init(SDK_Common_COSData.Instance.GetTrapData("cos_CityHt").ToString(), LoadCityFinish);



    }

    void LoadCityFinish()
    {
        Debug.Log("SDK_Common : Load City Finished");
#if OPPO || VIVO
        LuaTimerTicker.Instance.Init();

        PLAT_OV_MainController.Instance.Init();
#endif



        SetLoadingProgrees(50);

        OnLoadPorgress_Action?.Invoke(100);



        //SOUND Init
        SDK_Common_Sound.Instance.Init(float.Parse(SDK_Common_COSData.Instance.GetTrapDataSe("SoundSetting", "BGMVolume").ToString()), float.Parse(SDK_Common_COSData.Instance.GetTrapDataSe("SoundSetting", "EFVolume").ToString()), float.Parse(SDK_Common_COSData.Instance.GetTrapDataSe("SoundSetting", "EFXTime").ToString()));
        if (bool.Parse(SDK_Common_COSData.Instance.GetTrapDataSe("SoundSetting", "OpenBgm").ToString()))
            SDK_Common_Sound.Instance.PlayBg("BGM");


        //SDK Init
#if UNITY_EDITOR

        InitAllFinished(0);
#else

#if Wechat
           SDK_Wechat.Init(InitAllFinished);
#endif
#if OPPO
              OVManager.Init();
        InitAllFinished(0);
#endif
#if VIVO
                  OVManager.Init();
        InitAllFinished(0);
#endif

#endif

    }

    void LoadCosFinish()
    {

        Debug.Log("SDK_Common : LoadCosFinish");
        
        On_LoadCosFinished?.Invoke();

        FrameLuaManager.Instance.Load(LoadLuaFinish);
    }

    void InitAllFinished(int i)
    {
        Debug.Log("SDK_Common : AllInitFinish");


        SetLoadingProgrees(100);
        SceneManager.LoadScene(1);


        
    }

    public void SetLoadingProgrees(int progress)
    {

#if UNITY_EDITOR

#else

#if Wechat
   
#endif
#if OPPO
          OVManager.SetLoadingProgress(progress);
#endif
#if VIVO
        OVManager.SetLoadingProgress(progress);
#endif

#endif



    }


}
