using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class Tool_MessageTip_Item : MonoBehaviour
{
    public CanvasRenderer cRender;
    public CanvasRenderer txtcRender;
    public Text txtMessage;
    public RectTransform rtrans;

    private float currentSpeed = 0;
    private float stopTimer = 0;

    private float currentAlpha = 0;
    private float currentAlphaSpeed = 0;

    public float currentDestroyY;
    // Update is called once per frame
    private void Awake()
    {

        txtcRender.SetAlpha(0);
        cRender.SetAlpha(0);
    }

    void Update()
    {
        // Debug.LogError()
        if (rtrans.localPosition.y < currentDestroyY)
        {
            rtrans.localPosition += new Vector3(0, Time.deltaTime * currentSpeed, 0);
            if (currentSpeed < Tool_MessageTip.MoveSpeed)
                currentSpeed += Tool_MessageTip.MoveAddingSpeed;

            if (currentAlpha < 1)
                currentAlpha += Time.deltaTime * currentAlphaSpeed;
            if (currentAlphaSpeed < Tool_MessageTip.AlphaSpeed)
                currentAlphaSpeed += Tool_MessageTip.AplhaAddingSpeed;

            txtcRender.SetAlpha(currentAlpha);
            cRender.SetAlpha(currentAlpha);
        }
        else
        {
            stopTimer += Time.deltaTime;
            if(stopTimer > Tool_MessageTip.StopTime)
            {
                if (currentAlpha > 0)
                    currentAlpha -= Time.deltaTime * currentAlphaSpeed;
                else
                {
                    Destroy(this.gameObject);
                }


                txtcRender.SetAlpha(currentAlpha);
                cRender.SetAlpha(currentAlpha);


      
            }
            else
            {
                stopTimer += Time.deltaTime;
            }
             
        }
    }
}
