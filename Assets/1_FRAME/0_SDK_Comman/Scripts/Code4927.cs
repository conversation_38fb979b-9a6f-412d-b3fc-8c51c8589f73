using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Code4927
{


    public static string scode(string str)
    {
        string[] ncode = new string[] { "fxk4x0", "fxt4x0", "fx35x0", "fxa6x0", "fx480x0", "fxa4x0", "fx55x0", "fxio3x0", "fx9ax0", "fxzx0" };
        int[] nback = new int[] { 8, 7, 6, 1, 3, 2, 0, 4, 5, 9 };

        string[] ncode2 = new string[] {"fx39x0","fxa28x0", "fxa4tx0", "fxt9x0","fxi3x0","fx0aax0","fx5ccx0","fxtyx0","fxk0x0","fxc7x0","fx80x0","fx60x0", "fx60mx0", "fxt0x0","fxi9x0", "fx9pax0", "fx3czx0", "fx7x0", "fx9sax0", "fx3x0","fxi5x0","fx99x0","fx3cx0","fxi4x0","fxy9x0","fx3ex0"};
        char[] nback2 = new char[] { 'a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z'};

        if (str.IndexOf("fx") != -1 && str.IndexOf("x0") != -1)
        {
            for (var i = 0; i < ncode.Length; i++)
            {
                str = str.Replace(ncode[i], nback[i].ToString());
            }

      
        }

        if (str.IndexOf("fx") != -1 && str.IndexOf("x0") != -1)
        {
            for (var i = 0; i < ncode2.Length; i++)
            {
                str = str.Replace(ncode2[i], nback2[i].ToString());
            }
        }

        return str;
    }



}
