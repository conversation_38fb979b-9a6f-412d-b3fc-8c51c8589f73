#if OPPO || VIVO
using QGMiniGame;
#endif

using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SDK_Common_Playerprefs
{
    public static void SetInt(string key, int value)
    {

#if UNITY_EDITOR
        UnityEngine.PlayerPrefs.SetInt(key, value);
#else

#if Wechat
      PlayerPrefs.SetInt(key, value);
#endif
#if OPPO
                QG.StorageSetIntSync(key, value);
#endif
#if VIVO
        QG.StorageSetIntSync(key, value);
#endif

#endif

    }

    public static int GetInt(string key, int value)
    {


#if UNITY_EDITOR
        return UnityEngine.PlayerPrefs.GetInt(key, value);
#else

#if Wechat
    return PlayerPrefs.GetInt(key, value);
#endif
#if OPPO
  return QG.StorageGetIntSync(key, value);
#endif
#if VIVO
 return QG.StorageGetIntSync(key, value);
#endif

#endif
        return UnityEngine.PlayerPrefs.GetInt(key, value);
    }

    public static void SetString(string key, string value)
    {

#if UNITY_EDITOR
        UnityEngine.PlayerPrefs.SetString(key, value);
#else

#if Wechat
      PlayerPrefs.SetString(key, value);
#endif
#if OPPO
        QG.StorageSetStringSync(key, value);
#endif
#if VIVO
        QG.StorageSetStringSync(key, value);
#endif

#endif
    }

    public static string GetString(string key, string value)
    {


#if UNITY_EDITOR
        return UnityEngine.PlayerPrefs.GetString(key, value);
#else

#if Wechat
    return PlayerPrefs.GetString(key, value);
#endif
#if OPPO
  return QG.StorageGetStringSync(key, value);
#endif
#if VIVO
 return QG.StorageGetStringSync(key, value);
#endif

#endif
        return UnityEngine.PlayerPrefs.GetString(key, value);
    }




}
