using System.Collections;
using System.Collections.Generic;
#if OPPO
using System.Runtime.InteropServices;
#endif
using UnityEngine;

public class SDK_Common_Sound : MonoBehaviour
{

    public static SDK_Common_Sound Instance
    {
        get
        {
            if (instance == null)
            {
                if (GameObject.Find("SDK_Common"))
                    instance = GameObject.Find("SDK_Common").GetComponent<SDK_Common_Sound>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }

    private static SDK_Common_Sound instance;
    [HideInInspector]
    public string ResourceDir = "Sound";
    AudioSource m_bgSound;
    AudioSource[] m_effectSound;

    public int effectCount = 10;
    private int currentEffectIndex = 0;


    private List<string> EFXTimerName = new List<string>();
    private List<float> EFXTimer = new List<float>();
    private float EFXTime = 0.2f;

    public bool isOpen = true;

    public void Init(float bgvalue, float efvalue, float EFXTime)
    {

        if (!isOpen)
            return;


        m_bgSound = gameObject.AddComponent<AudioSource>();
        m_bgSound.playOnAwake = false;
        m_bgSound.loop = true;

        m_effectSound = new AudioSource[effectCount];

        for (var i = 0; i < m_effectSound.Length; i++)
        {
            m_effectSound[i] = gameObject.AddComponent<AudioSource>();
            m_effectSound[i].volume = efvalue;
        }

        m_bgSound.volume = bgvalue;

        this.EFXTime = EFXTime;
    }

    private void Update()
    {
        if (!isOpen)
            return;

        for (var i = 0; i < EFXTimerName.Count; i++)
        {
            if (EFXTimer[i] > 0)
                EFXTimer[i] -= Time.deltaTime;
        }

    }


    public void PlayBg(string audioName)
    {
#if UNITY_EDITOR
        PlayBGNormal(audioName);
#else

#if Wechat
          PlayBGNormal(audioName);
#endif
#if OPPO
                OPPOPlayBGM(audioName);
#endif
#if VIVO
       PlayBGNormal(audioName);
#endif

#endif

        
    }

    public void PlayBGNormal(string audioName)
    {
        if (!isOpen)
            return;
        string oldName;
        if (m_bgSound.clip == null)
            oldName = "";
        else
            oldName = m_bgSound.clip.name;

        if (oldName != audioName)
        {
            string path;

            path = "Sound" + "/" + audioName;



            AudioClip clip = Resources.Load<AudioClip>(path);

            if (clip != null)
            {
                m_bgSound.clip = clip;
                m_bgSound.Play();
            }

        }
    }

    //public void StopBg()
    //{
    //    if (!isOpen)
    //        return;
    //    m_bgSound.Stop();
    //    m_bgSound.clip = null;
    //}

    public void PlayEffect(string audioName)
    {
        if (!isOpen)
            return;

        if (!CheckEFX(audioName))
            return;

#if UNITY_EDITOR
        PlayEffectNormal(audioName);
#else

#if Wechat
         PlayEffectNormal(audioName);
#endif
#if OPPO
           OPPOPlayAudioEffect(audioName);
#endif
#if VIVO
     PlayEffectNormal(audioName);
#endif

#endif
        
    }

    public bool CheckEFX(string audioName)
    {

        if(EFXTime > 0)
        {
            bool exist = false;
            for (var i = 0; i < EFXTimerName.Count; i++)
                if (EFXTimerName[i] == audioName)
                    exist = true;

            if (!exist)
            {
                EFXTimerName.Add(audioName);
                EFXTimer.Add(0);
            }

            if (EFXTimer[EFXTimerName.IndexOf(audioName)] > 0)
                return false;

            EFXTimer[EFXTimerName.IndexOf(audioName)] = EFXTime;

            return true;

        }
        else if (EFXTime < 0)
        {
            audioName = "Common";
            bool exist = false;
            for (var i = 0; i < EFXTimerName.Count; i++)
                if (EFXTimerName[i] == audioName)
                    exist = true;

            if (!exist)
            {
                EFXTimerName.Add(audioName);
                EFXTimer.Add(0);
            }

         

            if (EFXTimer[EFXTimerName.IndexOf(audioName)] > 0)
                return false;

            EFXTimer[EFXTimerName.IndexOf(audioName)] = -EFXTime;

            return true;
        }


        return false;


    }

    public void PlayEffectNormal(string audioName)
    {


        string path;



        path = "Sound" + "/" + audioName;

        AudioClip clip = Resources.Load<AudioClip>(path);


        // m_effectSound.PlayOneShot(clip);

        m_effectSound[currentEffectIndex].clip = clip;
        m_effectSound[currentEffectIndex].Play();

        currentEffectIndex++;
        if (currentEffectIndex == effectCount)
            currentEffectIndex = 0;
    }

#if OPPO
    [DllImport("__Internal")]
    private static extern void OPPOPlayAudioEffect(string audioName);
    [DllImport("__Internal")]
    private static extern void OPPOPlayBGM(string audioName);
#endif

}
