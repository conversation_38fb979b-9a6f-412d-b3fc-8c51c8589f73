using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using XLua;

public class PLAT_OV_GamingController : LuaBehaviourHttp
{

    public static PLAT_OV_GamingController Instance
    {
        get
        {
            if (instance == null)
            {
                instance = GameObject.Find("PLAT_OV_GamingController").GetComponent<PLAT_OV_GamingController>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }

    private static PLAT_OV_GamingController instance;


    private void Awake()
    {
        Init();
    }


    public override void BindFunc()
    {
        var scriptEnv = scenvs[EnvName];
        //wadButtonEventLua = scriptEnv.Get<LuaFunction>("WadButtonEvent");
        //addWadButtonLua = scriptEnv.Get<LuaFunction>("AddWadButton");

    }

    public override void OffFunc()
    {
        //wadButtonEventLua = null;
        //addWadButtonLua = null;
    }



}
