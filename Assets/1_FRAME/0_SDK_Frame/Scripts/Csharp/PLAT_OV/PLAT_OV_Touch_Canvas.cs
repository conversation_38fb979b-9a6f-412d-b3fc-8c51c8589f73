using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using XLua;

public class PLAT_OV_Touch_Canvas : LuaBehaviourHttp
{
    private static PLAT_OV_Touch_Canvas instance = null;

    public static PLAT_OV_Touch_Canvas Instance
    {
        get
        {
            if (instance == null)
            {
                GameObject res = null;

                if (SDK_Common_Main.Instance.isVertical)
                    res = Resources.Load<GameObject>("PLAT_OV_Prefab/4Touch/Ver/PLAT_OV_Touch_Canvas");
                else
                    res = Resources.Load<GameObject>("PLAT_OV_Prefab/4Touch/Hor/PLAT_OV_Touch_Canvas");
                instance = Instantiate(res).GetComponent<PLAT_OV_Touch_Canvas>();
                instance.gameObject.name = res.name;

                DontDestroyOnLoad(instance.gameObject);
            }
            return instance;
        }
    }

    private static LuaFunction Action_OnStartGameButtonClick_EventLua;
    private static LuaFunction Action_OnGamingStartGame_EventLua;
    private static LuaFunction Action_OnReturnMainMenuClick_EventLua;
    private static LuaFunction Action_OnGameOver_EventLua;
    private static LuaFunction Action_OnEnterStartScene_EventLua;
    private static LuaFunction Action_OnEnterGameScene_EventLua;
    private static LuaFunction Action_OnEnterEndScene_EventLua;

    private void Awake()
    {
        Init();
    }


    public override void BindFunc()
    {

        var scriptEnv = scenvs[EnvName];

        Action_OnStartGameButtonClick_EventLua = scriptEnv.Get<LuaFunction>("Action_OnStartGameButtonClick");
        Action_OnGamingStartGame_EventLua = scriptEnv.Get<LuaFunction>("Action_OnGamingStartGame");
        Action_OnReturnMainMenuClick_EventLua = scriptEnv.Get<LuaFunction>("Action_OnReturnMainMenuClick");
        Action_OnGameOver_EventLua = scriptEnv.Get<LuaFunction>("Action_OnGameOver");
        Action_OnEnterStartScene_EventLua = scriptEnv.Get<LuaFunction>("Action_OnEnterStartScene");
        Action_OnEnterGameScene_EventLua = scriptEnv.Get<LuaFunction>("Action_OnEnterGameScene");
        Action_OnEnterEndScene_EventLua = scriptEnv.Get<LuaFunction>("Action_OnEnterEndScene");

#if !MXR_BRIGE
        MXR_BRIGE.act_OnStartGameButtonClick += Action_OnStartGameButtonClick;
        MXR_BRIGE.act_OnGamingStartGame += Action_OnGamingStartGame;
        MXR_BRIGE.act_OnReturnMainMenuClick += Action_OnReturnMainMenuClick;
        MXR_BRIGE.act_OnGameOver += Action_OnGameOver;

        MXR_BRIGE.act_OnEnterStartScene += Action_OnEnterStartScene;
        MXR_BRIGE.act_OnEnterGameScene += Action_OnEnterGameScene;
        MXR_BRIGE.act_OnEnterEndScene += Action_OnEnterEndScene;
#else

#endif
    }

    public override void OffFunc()
    {

    }

    public static void Action_OnStartGameButtonClick(Action callBack)
    {
        Action_OnStartGameButtonClick_EventLua.Action(callBack);
    }
    public static void Action_OnGamingStartGame(Action callBack)
    {
        Action_OnGamingStartGame_EventLua.Action(callBack);
    }
    public static void Action_OnReturnMainMenuClick(Action callBack)
    {
        Action_OnReturnMainMenuClick_EventLua.Action(callBack);
    }
    public static void Action_OnGameOver(Action callBack)
    {
        Action_OnGameOver_EventLua.Action(callBack);
    }
    public static void Action_OnEnterStartScene()
    {
        Action_OnEnterStartScene_EventLua.Action("");
    }
    public static void Action_OnEnterGameScene()
    {
        Action_OnEnterGameScene_EventLua.Action("");
    }
    public static void Action_OnEnterEndScene()
    {
        Action_OnEnterEndScene_EventLua.Action("");
    }

}
