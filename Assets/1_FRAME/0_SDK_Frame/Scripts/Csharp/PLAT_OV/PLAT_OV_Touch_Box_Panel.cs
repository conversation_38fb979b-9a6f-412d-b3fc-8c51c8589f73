using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.PlayerLoop;
using UnityEngine.UI;

public class PLAT_OV_Touch_Box_Panel : MonoBehaviour
{

    public Action Act_CallBack = null;
    public float AddPer;
    public float GetPer;
    public float CutPerSpeed;
    public float DelayDestroyTime;

    public Image imgBox;
    public Image imgBoxOpen;
    public Image imgProgress;
    public Button btnOpen;

    public Animator imgBoxClickAni;

    public int ClickSoundIndex;

    public void Init(Action callBack,float addPer, float getPer, float cutPerSpeed, float delayDestroyTime)
    {
        imgProgress.fillAmount = 0;
        Act_CallBack = callBack;
        AddPer = addPer;
        GetPer = getPer;
        CutPerSpeed = cutPerSpeed;
        DelayDestroyTime = delayDestroyTime;

    }

    void Update()
    {
        if (imgProgress.fillAmount > 0)
            imgProgress.fillAmount -= Time.deltaTime * CutPerSpeed;
    }

    public void OnBtnOpenClick()
    {
        imgProgress.fillAmount += AddPer;
        BtnClick();
        if (imgProgress.fillAmount >= GetPer)
        {
            btnOpen.gameObject.SetActive(false);
            imgBox.gameObject.SetActive(false);
            imgBoxOpen.gameObject.SetActive(true);
            StartCoroutine("DelayDestroy");
        }
    }

    IEnumerator DelayDestroy()
    {
        Act_CallBack?.Invoke();
        yield return new WaitForSeconds(DelayDestroyTime);
        GameObject.Destroy(this.gameObject);
    }

    private void BtnClick()
    {
        SDK_Common_Sound.Instance.PlayEffect("OV_TouchClick"+ ClickSoundIndex);
        imgBoxClickAni.Play("ClickShake");
    }

}
