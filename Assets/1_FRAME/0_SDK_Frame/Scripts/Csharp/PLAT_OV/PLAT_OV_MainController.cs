using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using XLua;

public class PLAT_OV_MainController : LuaBehaviourHttp
{
    public static PLAT_OV_MainController Instance
    {
        get
        {
            if (instance == null)
            {
                instance = GameObject.Find("PLAT_OV_MainController").GetComponent<PLAT_OV_MainController>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }

    private static PLAT_OV_MainController instance;


    public static bool isInitedFinished;

    public bool CanAllTouch;
    public bool CanOpenTouch;
    public bool CanCityTouch;
    public bool CanTimerTouch;


    private static LuaFunction Action_OnStartGameButtonClick_EventLua;
    private static LuaFunction Action_OnGamingStartGame_EventLua;
    private static LuaFunction Action_OnReturnMainMenuClick_EventLua;
    private static LuaFunction Action_OnGameOver_EventLua;
    private static LuaFunction Action_OnEnterStartScene_EventLua;
    private static LuaFunction Action_OnEnterGameScene_EventLua;
    private static LuaFunction Action_OnEnterEndScene_EventLua;


    public override void BindFunc()
    {

        DontDestroyOnLoad(this.gameObject);


        var scriptEnv = scenvs[EnvName];

        isInitedFinished = true;

        scriptEnv.Get<LuaFunction>("UpdateTouchState").Action("");

        Action_OnStartGameButtonClick_EventLua = scriptEnv.Get<LuaFunction>("Action_OnStartGameButtonClick");
        Action_OnGamingStartGame_EventLua = scriptEnv.Get<LuaFunction>("Action_OnGamingStartGame");
        Action_OnReturnMainMenuClick_EventLua = scriptEnv.Get<LuaFunction>("Action_OnReturnMainMenuClick");
        Action_OnGameOver_EventLua = scriptEnv.Get<LuaFunction>("Action_OnGameOver");
        Action_OnEnterStartScene_EventLua = scriptEnv.Get<LuaFunction>("Action_OnEnterStartScene");
        Action_OnEnterGameScene_EventLua = scriptEnv.Get<LuaFunction>("Action_OnEnterGameScene");
        Action_OnEnterEndScene_EventLua = scriptEnv.Get<LuaFunction>("Action_OnEnterEndScene");

#if !MXR_BRIGE
        MXR_BRIGE.act_OnStartGameButtonClick += Action_OnStartGameButtonClick;
        MXR_BRIGE.act_OnGamingStartGame += Action_OnGamingStartGame;
        MXR_BRIGE.act_OnReturnMainMenuClick += Action_OnReturnMainMenuClick;
        MXR_BRIGE.act_OnGameOver += Action_OnGameOver;

        MXR_BRIGE.act_OnEnterStartScene += Action_OnEnterStartScene;
        MXR_BRIGE.act_OnEnterGameScene += Action_OnEnterGameScene;
        MXR_BRIGE.act_OnEnterEndScene += Action_OnEnterEndScene;
#else

#endif

    }

    public static void Action_OnStartGameButtonClick(Action callBack)
    {
        Action_OnStartGameButtonClick_EventLua.Action(callBack);
    }
    public static void Action_OnGamingStartGame(Action callBack)
    {
        Action_OnGamingStartGame_EventLua.Action(callBack);
    }
    public static void Action_OnReturnMainMenuClick(Action callBack)
    {
        Action_OnReturnMainMenuClick_EventLua.Action(callBack);
    }
    public static void Action_OnGameOver(Action callBack)
    {
        Action_OnGameOver_EventLua.Action(callBack);
    }
    public static void Action_OnEnterStartScene()
    {
       

        Action_OnEnterStartScene_EventLua.Action("");
    }
    public static void Action_OnEnterGameScene()
    {
        Action_OnEnterGameScene_EventLua.Action("");
    }
    public static void Action_OnEnterEndScene()
    {
        Action_OnEnterEndScene_EventLua.Action("");
    }

    public static void CreateEightEventSys()
    {
        GameObject ago0 = GameObject.Find("EventSystem");
        if (ago0)
            GameObject.Destroy(ago0);

        GameObject ago = GameObject.Find("Eight_EventSystem");
        if (ago)
            GameObject.Destroy(ago);

        GameObject res = Resources.Load<GameObject>("Eight_EventSystem");
        GameObject go = GameObject.Instantiate(res);
        go.name = res.name;
    }

    public static void CreateTouchPanel(int index,Action callback,float addper,float getper,float cutspeed,float delayDesTime)
    {
        GameObject res = null;

        if (SDK_Common_Main.Instance.isVertical)
            res = Resources.Load<GameObject>("PLAT_OV_Prefab/5Touch_Box/Ver/PLAT_OV_Box_Panel"+index);
        else
            res = Resources.Load<GameObject>("PLAT_OV_Prefab/5Touch_Box/Hor/PLAT_OV_Box_Panel" + index);

       var panel = Instantiate(res, PLAT_OV_Touch_Canvas.Instance.transform).GetComponent<PLAT_OV_Touch_Box_Panel>();
        panel.gameObject.name = res.name;
        panel.Init(callback, addper, getper, cutspeed, delayDesTime);

    }

    public static void CreateLoadUI()
    {
        GameObject res = null;

        if (SDK_Common_Main.Instance.isVertical)
            res = Resources.Load<GameObject>("PLAT_OV_Prefab/1Load/Ver/PLAT_OV_Load_Canvas");
        else
            res = Resources.Load<GameObject>("PLAT_OV_Prefab/1Load/Hor/PLAT_OV_Load_Canvas");

        Instantiate(res).name = res.name;
        
    }

    public static void CreateStartUI()
    {
        GameObject res = null;

        if (SDK_Common_Main.Instance.isVertical)
            res = Resources.Load<GameObject>("PLAT_OV_Prefab/2Start/Ver/PLAT_OV_Start_Canvas");
        else
            res = Resources.Load<GameObject>("PLAT_OV_Prefab/2Start/Hor/PLAT_OV_Start_Canvas");
        Instantiate(res).name = res.name;
    }

    public static void CreateGamingController()
    {
        GameObject res = null;

        res = Resources.Load<GameObject>("PLAT_OV_Prefab/PLAT_OV_GamingController");

        Instantiate(res).name = res.name;
    }

    public static void CreateGameBanner()
    {
        GameObject res = null;

        res = Resources.Load<GameObject>("PLAT_OV_Prefab/PLAT_OV_GameBanner");

        Instantiate(res).name = res.name;
    }

}
