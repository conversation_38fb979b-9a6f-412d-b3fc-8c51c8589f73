using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PLAT_OV_Start_Panel : LuaBehaviourHttp
{

    public static PLAT_OV_Start_Panel Instance
    {
        get
        {
            if (instance == null)
            {
                instance = GameObject.Find("PLAT_OV_Start_Panel").GetComponent<PLAT_OV_Start_Panel>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }

    private static PLAT_OV_Start_Panel instance;

    private void Awake()
    {
        Init();
    }


    public override void BindFunc()
    {
    
    }

    public override void OffFunc()
    {
       
    }


}
