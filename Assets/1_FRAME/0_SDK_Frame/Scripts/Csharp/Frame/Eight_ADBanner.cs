using LitJson;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.Networking;

public class Eight_ADBanner : MonoBehaviour
{
    // Start is called before the first frame update
    void Start()
    {


#if UNITY_EDITOR

#else

#if Wechat
               WXManager.Instance.WXADBanner.showBanner();
#endif
#if OPPO
             OVManager.CreateBannerAD();
#endif
#if VIVO
                     OVManager.CreateBannerAD();
#endif

#endif

    }


    private void OnDestroy()
    {



#if UNITY_EDITOR

#else

#if Wechat
              WXManager.Instance.WXADBanner.hideBanner();
#endif
#if OPPO
           OVManager.HideBanner();
#endif
#if VIVO
            OVManager.HideBanner();
#endif

#endif



    }

}
