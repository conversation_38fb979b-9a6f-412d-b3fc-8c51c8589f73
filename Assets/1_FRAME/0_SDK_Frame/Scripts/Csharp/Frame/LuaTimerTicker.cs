using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using XLua;

public class LuaTimerTicker : LuaBehaviourHttp
{

    public static LuaTimerTicker Instance
    {
        get
        {
            if (instance == null)
            {
                instance = GameObject.Find("LuaTimerTicker").GetComponent<LuaTimerTicker>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }

    private static LuaTimerTicker instance;

    private LuaFunction oneceLua;
    private LuaFunction loopLua;
    private LuaFunction removeLua;

    public bool isOpen = true;


    public override void BindFunc()
    {
        DontDestroyOnLoad(this.gameObject);

        var scriptEnv = scenvs[EnvName];

        oneceLua = scriptEnv.Get<LuaFunction>("Once");
        loopLua = scriptEnv.Get<LuaFunction>("Loop");
        removeLua = scriptEnv.Get<LuaFunction>("Remove");

        Debug.Log("LuaTimerTicker Inited");
    }

    public override void OffFunc()
    {
        
    }

    public void onece(float a,Action b)
    {
        if (!isOpen)
            return;
        oneceLua.Action(a, b);
    }

    public void loop(float a, Action b)
    {
        if (!isOpen)
            return;
        loopLua.Action(a, b);
    }

    public void remove(Action b)
    {
        if (!isOpen)
            return;
        removeLua.Action(b);
    }



}
