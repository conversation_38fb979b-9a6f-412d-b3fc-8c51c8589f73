using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class AudMgr : MonoBehaviour
{
    //音乐文件
    public AudioSource music;

    public AudioClip[] audioClips;
    public AudioClip bgm;

    /// <summary>播放放音乐</summary>
    private void playMusic()
    {
        if (music != null)
        {
            music.Play();
        }
    }

    /// <summary>关闭音乐播放</summary>
    private void stopMusic()
    {
        if (music != null)
        {
            music.Stop();
        }
    }

    /// <summary>暂停音乐播放</summary>
    private void pauseMusic()
    {
        if (music != null)
        {
            music.Pause();
        }
    }

    /// <summary>
    /// 设置播放音量
    /// </summary>
    /// <param name="volume"></param>
    private void setMusicVolume(float volume)
    {
        if (music != null)
        {
            music.volume = volume;
        }
    }

}
