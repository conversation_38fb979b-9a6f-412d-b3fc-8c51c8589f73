
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using XLua;
using System;


[LuaCallCSharp]
public class LuaBehaviourHttp : MonoBehaviour
{

   // public bool isInit = true;
    public string LuaName;
    public string EnvName;

    private Action luaUpdate;
    private Action luaOnDestroy;



    // private TextAsset luaScript;

    internal static LuaEnv luaEnv = new LuaEnv(); //all lua behaviour shared one luaenv only!
    //internal static float lastGCTime = 0;
    //internal const float GCInterval = 1;//1 second 

    public static Dictionary<string, LuaTable> scenvs = new Dictionary<string, LuaTable>();
    public static Dictionary<string, TextAsset> luascs = new Dictionary<string, TextAsset>();

    //public void Awake()
    //{
    //    if (!isInit)
    //        BindLua(FrameLuaManager.Instance.GetLua(LuaName));

    //}

    public void Init()
    {
        //if (isInit)
            BindLua(FrameLuaManager.Instance.GetLua(LuaName));
    }


    private void BindLua(TextAsset value)
    {
        LuaTable scriptEnv = null;

        if (EnvName == "")
            EnvName = LuaName;

        if (luascs.ContainsKey(EnvName) == false)
            luascs[EnvName] = value;

        if (scenvs.ContainsKey(EnvName) == false)
        {

            scriptEnv = luaEnv.NewTable();

            // 为每个脚本设置一个独立的环境，可一定程度上防止脚本间全局变量、函数冲突
            LuaTable meta = luaEnv.NewTable();
            meta.Set("__index", luaEnv.Global);
            scriptEnv.SetMetaTable(meta);
            meta.Dispose();

            scenvs.Add(EnvName, scriptEnv);
        }
        else
            scriptEnv = scenvs[EnvName];


        scriptEnv.Set("self", this);
   
        luaEnv.DoString(luascs[EnvName].text, luascs[EnvName].name.Replace(".lua",""), scriptEnv);

        Action luaLoaed = scriptEnv.Get<Action>("loaded");
        scriptEnv.Get("update", out luaUpdate);
        scriptEnv.Get("ondestroy", out luaOnDestroy);


        if (luaLoaed != null)
        {
            luaLoaed();
        }

        BindFunc();
    }

    public virtual void BindFunc()
    {

    }

    public virtual void OffFunc()
    {

    }

    // Update is called once per frame
    void Update()
    {
       
        if (luaUpdate != null)
        {
            luaUpdate();
        }
        //if (Time.time - LuaBehaviourAddres.lastGCTime > GCInterval)
        //{
        //    luaEnv.Tick();
        //    LuaBehaviourAddres.lastGCTime = Time.time;
        //}
    }

  

    void OnDestroy()
    {
        //Debug.LogError("Destroy " + this.name);
        if (luaOnDestroy != null)
        {
            luaOnDestroy();
        }
        luaOnDestroy = null;
        luaUpdate = null;
        OffFunc();
        //if(scriptEnv != null)
        //{
        //   // scriptEnv.Dispose();
           
        //}

    }
}

