using LitJson;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.Networking;

public class FrameLuaManager : MonoBehaviour
{

    public static FrameLuaManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = GameObject.Find("SDK_Common").GetComponent<FrameLuaManager>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }

    private static FrameLuaManager instance;

     string[] LoadLuaNames;

    public string[] OVLuaNames;
    public string[] WechatLuaNames;

    Dictionary<string, TextAsset> Scripts = new Dictionary<string, TextAsset>();

    private string HttpPath;
    [HideInInspector]
    public string LocalPath;
    [HideInInspector]
    public string FloderPath;

    public void Load(Action actionFinish)
    {
        SwitchPlatForm();
        StartCoroutine(LoadLogic(actionFinish));
    }

    private void SwitchPlatForm()
    {
#if UNITY_EDITOR
#if OPPO
      
        LocalPath = SDK_Common_COSData.Instance.GetTrapDataSe("LuxPath", "OPPO_L").ToString();

#endif
#if VIVO
    LocalPath = SDK_Common_COSData.Instance.GetTrapDataSe("LuxPath", "VIVO_L").ToString();
#endif
#else

#if OPPO
     FloderPath = SDK_Common_COSData.Instance.GetTrapDataSe("LuxPath", "OPPO_N").ToString();
#endif
#if VIVO
    FloderPath = SDK_Common_COSData.Instance.GetTrapDataSe("LuxPath", "VIVO_N").ToString();
#endif

#endif

        HttpPath = SDK_Common_COSData.Instance.basepath + FloderPath;
    }

    IEnumerator LoadLogic(Action actionFinish)
    {
#if OPPO
      LoadLuaNames = OVLuaNames;
#endif
#if VIVO
        LoadLuaNames = OVLuaNames;
#endif
#if Wechat
        LoadLuaNames = WechatLuaNames;
#endif

        for (int i = 0; i < LoadLuaNames.Length; i++)
        {
            string Path = "";

#if UNITY_EDITOR
            Path = LocalPath + LoadLuaNames[i] + ".lua.txt";
#else
            Path = HttpPath + LoadLuaNames[i] + ".lua.txt";
#endif


#if UNITY_EDITOR
            WWW wwwServer = new WWW(Path);
            yield return wwwServer;
            if (string.IsNullOrEmpty(wwwServer.error))
            {

                var scode = Code4927.scode(wwwServer.text);


                Scripts[LoadLuaNames[i]] = new TextAsset(scode);
            }
#else

#if Wechat
       WWW wwwServer = new WWW(Path);
                yield return wwwServer;
                if (string.IsNullOrEmpty(wwwServer.error))
                {

                    var scode = Code4927.scode(wwwServer.text);


                    Scripts[LoadLuaNames[i]] = new TextAsset(scode);
                }

#endif
#if OPPO
        Uri uri = new Uri(Path);

                UnityWebRequest uwr = UnityWebRequest.Get(uri);
                yield return uwr.SendWebRequest();
                if (uwr.isHttpError || uwr.isNetworkError)
                {
                    Debug.Log(uwr.error);
                }
                else //请求成功
                {
                    var scode = Code4927.scode(uwr.downloadHandler.text);
                    Scripts[LoadLuaNames[i]] = new TextAsset(scode);
                }
#endif
#if VIVO
         Uri uri = new Uri(Path);

                UnityWebRequest uwr = UnityWebRequest.Get(uri);
                yield return uwr.SendWebRequest();
                if (uwr.isHttpError || uwr.isNetworkError)
                {
                    Debug.Log(uwr.error);
                }
                else //请求成功
                {
                    var scode = Code4927.scode(uwr.downloadHandler.text);
                    Scripts[LoadLuaNames[i]] = new TextAsset(scode);
                }
#endif

#endif

            

        }
        actionFinish();
        yield return null;
    }

    public TextAsset GetLua(string Name)
    {
        return Scripts[Name];
    }


}
