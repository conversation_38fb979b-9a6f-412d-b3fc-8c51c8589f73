
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using XLua;
using System;



[LuaCallCSharp]
public class LuaBehaviourHttpAwake : MonoBehaviour
{
    public string LuaName;

    private TextAsset luaScript;

    internal static LuaEnv luaEnv = new LuaEnv(); //all lua behaviour shared one luaenv only!
    internal static float lastGCTime = 0;
    internal const float GCInterval = 1;//1 second 

    private Action luaUpdate;
    private Action luaOnDestroy;

    public LuaTable scriptEnv;





    private void BindLua(TextAsset value)
    {
        Debug.LogError(luaEnv);

        luaScript = value;

        scriptEnv = luaEnv.NewTable();

        // 为每个脚本设置一个独立的环境，可一定程度上防止脚本间全局变量、函数冲突
        LuaTable meta = luaEnv.NewTable();
        meta.Set("__index", luaEnv.Global);
        scriptEnv.SetMetaTable(meta);
        meta.Dispose();

        scriptEnv.Set("self", this);


        luaEnv.DoString(luaScript.text, luaScript.name.Replace(".lua",""), scriptEnv);

        Action luaLoaed = scriptEnv.Get<Action>("loaded");
        scriptEnv.Get("update", out luaUpdate);
        scriptEnv.Get("ondestroy", out luaOnDestroy);


        if (luaLoaed != null)
        {
            luaLoaed();
        }

        BindFunc();
    }

    public virtual void BindFunc()
    {

    }

    public virtual void OffFunc()
    {

    }

    // Update is called once per frame
    void Update()
    {
        if (luaUpdate != null)
        {
            luaUpdate();
        }

    }

  

    void OnDestroy()
    {
        if (luaOnDestroy != null)
        {
            luaOnDestroy();
        }
        luaOnDestroy = null;
        luaUpdate = null;
        OffFunc();
        if(scriptEnv != null)
        scriptEnv.Dispose();
    }
}

