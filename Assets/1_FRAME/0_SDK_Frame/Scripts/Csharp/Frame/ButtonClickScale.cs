using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class ButtonClickScale : MonoBehaviour
{
    private float startX = 0;
    private float startY = 0;

    RectTransform rectran;

    public float scaleOff = 0.25f;

    private float speed = 0.01f;

    private void Start()
    {
        speed = 0.005f;
        rectran = GetComponent<RectTransform>();
        GetComponent<Button>().onClick.AddListener(this.onClick);
        startX = rectran.localScale.x;
        startY = rectran.localScale.y;


    }

    private void Update()
    {
        if(rectran.localScale.x > startX)
        {
            rectran.localScale = new Vector3(rectran.localScale.x- speed, rectran.localScale.y - speed, rectran.localScale.z);
        }
    }

    void onClick()
    {
        rectran.localScale = new Vector3(startX + scaleOff, startY + scaleOff, rectran.localScale.z);
    }

}
