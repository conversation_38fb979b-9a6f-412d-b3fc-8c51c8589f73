using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class UIManager : MonoBehaviour
{
    //public AssetReference UIStartPanelAddress;
    //public AssetReference UIEndPanelAddress;
    //public AssetReference UITrap1PanelAddress;
    //public AssetReference UITrap2PanelAddress;
    //public AssetReference UITrap3PanelAddress;



    public void ShowPanel(string scenename)
    {
        GameObject canvas = GameObject.Find("Canvas");

        switch (name)
        {
            case "Start":

                //UIStartPanelAddress.Instantiate().Completed += (obj) =>
                //         {
                //             obj.Result.transform.SetParent(canvas.transform);
                //             RectTransform rectform = obj.Result.GetComponent<RectTransform>();
                //             rectform.localPosition = new Vector3(0, 0, 0);
                //             rectform.localScale = new Vector3(1, 1, 1);
                //             rectform.offsetMin = new Vector2(0, 0);
                //             rectform.offsetMax = new Vector2(0, 0);
                //         };

                break;
            case "End":
                //UIEndPanelAddress.Instantiate().Completed += (obj) =>
                //{
                //    obj.Result.transform.SetParent(canvas.transform);
                //    RectTransform endrectform = obj.Result.GetComponent<RectTransform>();
                //    endrectform.localPosition = new Vector3(0, 0, 0);
                //    endrectform.localScale = new Vector3(1, 1, 1);
                //    endrectform.offsetMin = new Vector2(0, 0);
                //    endrectform.offsetMax = new Vector2(0, 0);
                //};
                break;
            case "Trap1":
                break;
            case "Trap2":
                break;
            case "Trap3":
                break;
            case "Game":

                break;
            case "Match":

                break;
        };
    }



}
