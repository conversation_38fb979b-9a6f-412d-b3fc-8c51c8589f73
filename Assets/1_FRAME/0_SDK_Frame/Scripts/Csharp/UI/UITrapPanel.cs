using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using XLua;

public class UITrapPanel : LuaBehaviourHttp
{
    private LuaFunction OpenLua;

    public override void BindFunc()
    {

        var scriptEnv = scenvs[EnvName];

        OpenLua = scriptEnv.Get<LuaFunction>("Open");

    }

    public override void OffFunc()
    {
        OpenLua = null;
    }

    public void Open(Action closeCallBack)
    {
        OpenLua.Action(closeCallBack);
    }




}
