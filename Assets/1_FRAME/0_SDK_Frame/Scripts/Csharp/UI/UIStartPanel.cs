using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class UIStartPanel : LuaBehaviourHttp
{

    public static UIStartPanel Instance
    {
        get
        {
            if (instance == null)
            {
                instance = GameObject.Find("StartPanel").GetComponent<UIStartPanel>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }

    private static UIStartPanel instance;

    private void Awake()
    {
        Init();
    }


    public override void BindFunc()
    {
    
    }

    public override void OffFunc()
    {
       
    }


}
