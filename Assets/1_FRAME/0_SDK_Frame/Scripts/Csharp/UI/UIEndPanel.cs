using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using XLua;

public class UIEndPanel : LuaBehaviourHttp
{

    public static UIEndPanel Instance
    {
        get
        {
            if (instance == null)
            {
                instance = GameObject.Find("EndPanel").GetComponent<UIEndPanel>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }

    private static UIEndPanel instance;

    private LuaFunction OpenshowLua;

    private void Awake()
    {
        Init();
    }


    public override void BindFunc()
    {
        var scriptEnv = scenvs[EnvName];
        OpenshowLua = scriptEnv.Get<LuaFunction>("OpenShow");
    }

    public override void OffFunc()
    {
        OpenshowLua = null;
    }

    public void Openshow(int a = 0)
    {
        OpenshowLua.Action(a);
    }

}
