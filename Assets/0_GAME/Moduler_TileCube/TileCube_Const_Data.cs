using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using System;


public class TileCube_Const_Data
{


    public string LevelJSON_Path;

    // 添加关卡文本数据字段
    public string LevelText = "";

    public Dictionary<string, string> ColorIDMap = new Dictionary<string, string>();
    public Dictionary<string, float[]> ColorDic = new Dictionary<string, float[]>();

    public float[] Parent_Scale = new float[3];

    // 预制体路径配置
    public Dictionary<string, string> PrefabPaths = new Dictionary<string, string>();

    public int Virb_Normal;

    public float TileGrid_PushSpeed;

    public string Level_ConnectRate_Config = "50:100,100:80,200:60,999:40";


    public string Level_HeightConsistencyRate_Config = "30:90,80:70,999:50";

    public string Level_SpawnBoxColorRange_Config = "50:6-20,100:8-25,999:10-30";

    public string Level_GoldPigHitCount_Config = "40:1,100:2,999:3";

    public string Level_ColorPerBarGrid_Config = "30:5,80:6,999:7";


    public string Level_SceneColorChange_Config = "50:Red,999:Blue";

    // 辅助方法：获取Vector3格式的Parent_Scale
    public Vector3 GetParentScale()
    {
        return new Vector3(Parent_Scale[0], Parent_Scale[1], Parent_Scale[2]);
    }

    // 通过ID获取颜色的辅助方法
    public Color GetColorById(int id)
    {
        string idStr = id.ToString();



        if (ColorIDMap.TryGetValue(idStr, out string colorName) && ColorDic.TryGetValue(colorName, out float[] colorValues))
        {
            if (colorValues.Length >= 4)
            {
                return new Color(colorValues[0], colorValues[1], colorValues[2], colorValues[3]);
            }
        }
        return Color.white; // 默认返回白色
    }



}

/// <summary>
/// 关卡配置解析器 - 解析区间段式配置字符串
/// </summary>
public static class LevelConfigParser
{
    /// <summary>
    /// 解析关卡配置字符串获取指定关卡的配置值
    /// </summary>
    /// <param name="configString">配置字符串，格式：关卡数:值,关卡数:值</param>
    /// <param name="levelIndex">关卡索引（从1开始）</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    public static int ParseLevelConfig(string configString, int levelIndex, int defaultValue)
    {
        if (string.IsNullOrEmpty(configString))
            return defaultValue;

        string[] segments = configString.Split(',');
        int lastValue = defaultValue;

        foreach (string segment in segments)
        {
            string[] parts = segment.Split(':');
            if (parts.Length == 2 && int.TryParse(parts[0], out int maxLevel) && int.TryParse(parts[1], out int value))
            {
                lastValue = value;
                if (levelIndex <= maxLevel)
                {
                    return value;
                }
            }
        }

        return lastValue; // 超出所有配置范围时返回最后一个值
    }

    /// <summary>
    /// 解析出块颜色范围配置
    /// </summary>
    /// <param name="configString">配置字符串，格式：关卡数:最小值-最大值,关卡数:最小值-最大值</param>
    /// <param name="levelIndex">关卡索引</param>
    /// <param name="defaultMin">默认最小值</param>
    /// <param name="defaultMax">默认最大值</param>
    /// <returns>颜色范围(最小值, 最大值)</returns>
    public static (int min, int max) ParseSpawnBoxColorRange(string configString, int levelIndex, int defaultMin, int defaultMax)
    {
        if (string.IsNullOrEmpty(configString))
            return (defaultMin, defaultMax);

        string[] segments = configString.Split(',');
        int lastMin = defaultMin, lastMax = defaultMax;

        foreach (string segment in segments)
        {
            string[] levelParts = segment.Split(':');
            if (levelParts.Length == 2 && int.TryParse(levelParts[0], out int maxLevel))
            {
                string[] rangeParts = levelParts[1].Split('-');
                if (rangeParts.Length == 2 && int.TryParse(rangeParts[0], out int min) && int.TryParse(rangeParts[1], out int max))
                {
                    lastMin = min;
                    lastMax = max;
                    if (levelIndex <= maxLevel)
                    {
                        return (min, max);
                    }
                }
            }
        }

        return (lastMin, lastMax);
    }

    /// <summary>
    /// 获取配置的兼容性方法 - 优先使用新配置，如果新配置为空则使用旧配置
    /// </summary>
    /// <param name="newConfigString">新的字符串配置</param>
    /// <param name="oldConfigArray">旧的数组配置</param>
    /// <param name="levelIndex">关卡索引</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    public static int GetLevelConfigWithFallback(string newConfigString, int levelIndex, int defaultValue)
    {
        // 优先使用新配置
        if (!string.IsNullOrEmpty(newConfigString))
        {
            return ParseLevelConfig(newConfigString, levelIndex, defaultValue);
        }


        return defaultValue;
    }

    /// <summary>
    /// 获取出块颜色范围的兼容性方法
    /// </summary>
    public static (int min, int max) GetSpawnBoxColorRangeWithFallback(string newConfigString, int levelIndex, int defaultMin, int defaultMax)
    {
        // 优先使用新配置
        if (!string.IsNullOrEmpty(newConfigString))
        {
            return ParseSpawnBoxColorRange(newConfigString, levelIndex, defaultMin, defaultMax);
        }

        return (defaultMin, defaultMax);
    }

    /// <summary>
    /// 解析场景颜色变化配置
    /// </summary>
    /// <param name="configString">配置字符串，格式：关卡数:颜色名,关卡数:颜色名</param>
    /// <param name="levelIndex">关卡索引（从1开始）</param>
    /// <param name="defaultColor">默认颜色</param>
    /// <returns>颜色名称</returns>
    public static string ParseSceneColorChange(string configString, int levelIndex, string defaultColor)
    {
        if (string.IsNullOrEmpty(configString))
            return defaultColor;

        string[] segments = configString.Split(',');
        string lastColor = defaultColor;

        foreach (string segment in segments)
        {
            string[] parts = segment.Split(':');
            if (parts.Length == 2 && int.TryParse(parts[0], out int maxLevel))
            {
                string colorName = parts[1];
                lastColor = colorName;
                if (levelIndex <= maxLevel)
                {
                    return colorName;
                }
            }
        }

        return lastColor; // 超出所有配置范围时返回最后一个颜色
    }

    /// <summary>
    /// 测试配置解析器功能
    /// </summary>
    public static void TestConfigParser()
    {
        Debug.Log("=== 关卡配置解析器测试 ===");

        // 测试连片率配置
        string connectRateConfig = "50:100,100:80,200:60,999:40";
        Debug.Log($"连片率配置: {connectRateConfig}");
        Debug.Log($"第25关连片率: {ParseLevelConfig(connectRateConfig, 25, 50)}"); // 应该返回100
        Debug.Log($"第75关连片率: {ParseLevelConfig(connectRateConfig, 75, 50)}"); // 应该返回80
        Debug.Log($"第150关连片率: {ParseLevelConfig(connectRateConfig, 150, 50)}"); // 应该返回60
        Debug.Log($"第500关连片率: {ParseLevelConfig(connectRateConfig, 500, 50)}"); // 应该返回40

        // 测试出块颜色范围配置
        string colorRangeConfig = "50:6-20,100:8-25,999:10-30";
        Debug.Log($"\n出块颜色范围配置: {colorRangeConfig}");
        var range1 = ParseSpawnBoxColorRange(colorRangeConfig, 30, 6, 20);
        Debug.Log($"第30关颜色范围: {range1.min}-{range1.max}"); // 应该返回6-20
        var range2 = ParseSpawnBoxColorRange(colorRangeConfig, 80, 6, 20);
        Debug.Log($"第80关颜色范围: {range2.min}-{range2.max}"); // 应该返回8-25
        var range3 = ParseSpawnBoxColorRange(colorRangeConfig, 200, 6, 20);
        Debug.Log($"第200关颜色范围: {range3.min}-{range3.max}"); // 应该返回10-30

        Debug.Log("=== 测试完成 ===");
    }
}

// 关卡数据结构
public class TileCube_Data_Level
{
    public List<TileCube_Data_SingleBlock> SingleBlocks;    // 单一块数据
    public List<TileCube_Data_LargeBlock> LargeBlocks;      // 大型块数据
    public List<TileCube_Data_GoldPig> GoldPigs;            // 金猪数据
    public List<TileCube_Data_Barrier> Barriers;            // 物理阻挡条数据
    public List<TileCube_Data_Key> Keys;                     // 钥匙数据
    public List<TileCube_Data_Spawner> Spawners;            // 出块库数据

    // 构造函数，初始化所有列表
    public TileCube_Data_Level()
    {
        SingleBlocks = new List<TileCube_Data_SingleBlock>();
        LargeBlocks = new List<TileCube_Data_LargeBlock>();
        GoldPigs = new List<TileCube_Data_GoldPig>();
        Barriers = new List<TileCube_Data_Barrier>();
        Keys = new List<TileCube_Data_Key>();
        Spawners = new List<TileCube_Data_Spawner>();
    }
}

// 单一块数据
[System.Serializable]
public class TileCube_Data_SingleBlock
{
    public int GridX;           // 网格X坐标
    public int GridZ;           // 网格Z坐标
    public int Height;          // 高度层
    public int ColorId;         // 颜色ID
}

// 大型块数据
[System.Serializable]
public class TileCube_Data_LargeBlock
{
    public int GridX;           // 网格X坐标（左下角）
    public int GridZ;           // 网格Z坐标（左下角）
    public int ColorId;         // 颜色ID
    public int HitValues;    // 需要击打次数
}

// 金猪数据
[System.Serializable]
public class TileCube_Data_GoldPig
{
    public int GridX;           // 网格X坐标
    public int GridZ;           // 网格Z坐标
    public int Height;          // 高度层

    public int HitValues;    // 需要击打次数
}

// 物理阻挡条数据
[System.Serializable]
public class TileCube_Data_Barrier
{
    public int GridX;           // 根位置网格X坐标
    public int GridZ;           // 根位置网格Z坐标
    public int Direction;       // 伸展方向 (0=上, 1=右, 2=下, 3=左)
    public int GridCount;       // 占用格数
    public int ColorId;         // 颜色ID
    public int HitsPerGrid;     // 每格击打次数
}

// 钥匙数据
[System.Serializable]
public class TileCube_Data_Key
{
    public int GridX;           // 网格X坐标
    public int GridZ;           // 网格Z坐标
    public int Height;          // 高度层
    public int KeyId;           // 钥匙ID（用于匹配锁）
}

// 出块库数据
[System.Serializable]
public class TileCube_Data_Spawner
{
    public int GridX;           // 网格X坐标（左下角）
    public int GridZ;           // 网格Z坐标（左下角）

    public List<int> SpawnColorSingleBlocks;

    // 构造函数，初始化列表
    public TileCube_Data_Spawner()
    {
        SpawnColorSingleBlocks = new List<int>();
    }
}