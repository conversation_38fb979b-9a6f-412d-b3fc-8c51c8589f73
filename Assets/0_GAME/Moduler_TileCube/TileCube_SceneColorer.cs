using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

[Serializable]
public class TileCube_SceneColorConfig
{
    [Header("换色配置标识")]
    public string colorKey = "";

    [Header("四个材质的颜色配置")]
    public Color BG_Ground_Color = Color.white;
    public Color BG_GirdAndLine_Color1 = Color.white;
    public Color BG_GirdAndLine_Color2 = Color.white;
    public Color UP_Ground_Color = Color.white;

    [Header("射击网格颜色配置")]
    public Color ShotGridLines_Color = Color.white;
    public Color ShotGridInsides_Color = Color.white;
}

public class TileCube_SceneColorer : MonoBehaviour
{

    private static TileCube_SceneColorer _instance;

    public static TileCube_SceneColorer Instance
    {
        get
        {
            if (_instance == null)
                _instance = FindObjectOfType<TileCube_SceneColorer>();

            return _instance;
        }
    }



    [Header("场景换色配置数组")]
    public List<TileCube_SceneColorConfig> colorConfigs = new List<TileCube_SceneColorConfig>();

    public MeshRenderer BG_Ground;
    public MeshRenderer BG_GirdAndLine;
    public MeshRenderer UP;

    public MeshRenderer[] ShotGridLines;
    public MeshRenderer[] ShotGridInsides;

    public Material BG_Ground_Mat
    {
        get
        {
            if (bG_Ground_Mat == null)
                bG_Ground_Mat = BG_Ground.material;
            return bG_Ground_Mat;
        }
    }
    public Material[] BG_GirdAndLine_Mats
    {
        get
        {
            if (bG_GirdAndLine_Mats == null)
                bG_GirdAndLine_Mats = BG_GirdAndLine.materials;
            return bG_GirdAndLine_Mats;
        }
    }
    public Material UP_Ground_Mat
    {
        get
        {
            if (uP_Ground_Mat == null)
                uP_Ground_Mat = UP.material;
            return uP_Ground_Mat;
        }
    }

    // 获取射击网格线材质数组
    public Material[] ShotGridLines_Mats
    {
        get
        {
            if (shotGridLines_Mats == null && ShotGridLines != null)
            {
                shotGridLines_Mats = new Material[ShotGridLines.Length];
                for (int i = 0; i < ShotGridLines.Length; i++)
                {
                    if (ShotGridLines[i] != null)
                        shotGridLines_Mats[i] = ShotGridLines[i].material;
                }
            }
            return shotGridLines_Mats;
        }
    }

    // 获取射击网格内部材质数组
    public Material[] ShotGridInsides_Mats
    {
        get
        {
            if (shotGridInsides_Mats == null && ShotGridInsides != null)
            {
                shotGridInsides_Mats = new Material[ShotGridInsides.Length];
                for (int i = 0; i < ShotGridInsides.Length; i++)
                {
                    if (ShotGridInsides[i] != null)
                        shotGridInsides_Mats[i] = ShotGridInsides[i].material;
                }
            }
            return shotGridInsides_Mats;
        }
    }

    Material bG_Ground_Mat;
    Material[] bG_GirdAndLine_Mats;
    Material uP_Ground_Mat;
    Material[] shotGridLines_Mats;  // 射击网格线材质数组
    Material[] shotGridInsides_Mats;  // 射击网格内部材质数组

    /// <summary>
    /// 根据颜色key设置场景材质颜色
    /// </summary>
    /// <param name="colorKey">颜色配置的标识key</param>
    public void SetSceneColor(string colorKey)
    {
        // 查找对应的颜色配置
        TileCube_SceneColorConfig config = colorConfigs.Find(c => c.colorKey == colorKey);

        if (config == null)
        {
            //Debug.LogWarning($"[TileCube_SceneColorer] 未找到颜色配置: {colorKey}");
            return;
        }

        // 设置地面材质颜色
        if (BG_Ground_Mat != null)
        {
            BG_Ground_Mat.color = config.BG_Ground_Color;
        }

        // 设置网格线材质颜色 (两个材质)
        if (BG_GirdAndLine_Mats != null && BG_GirdAndLine_Mats.Length >= 2)
        {
            BG_GirdAndLine_Mats[0].color = config.BG_GirdAndLine_Color1;
            BG_GirdAndLine_Mats[1].color = config.BG_GirdAndLine_Color2;
        }

        // 设置上层地面材质颜色
        if (UP_Ground_Mat != null)
        {
            UP_Ground_Mat.color = config.UP_Ground_Color;
        }

        // 设置射击网格线材质颜色
        if (ShotGridLines_Mats != null)
        {
            for (int i = 0; i < ShotGridLines_Mats.Length; i++)
            {
                if (ShotGridLines_Mats[i] != null)
                    ShotGridLines_Mats[i].color = config.ShotGridLines_Color;
            }
        }

        // 设置射击网格内部材质颜色
        if (ShotGridInsides_Mats != null)
        {
            for (int i = 0; i < ShotGridInsides_Mats.Length; i++)
            {
                if (ShotGridInsides_Mats[i] != null)
                    ShotGridInsides_Mats[i].color = config.ShotGridInsides_Color;
            }
        }

        //Debug.Log($"[TileCube_SceneColorer] 场景换色完成: {colorKey}");
    }

    /// <summary>
    /// 根据关卡索引自动换色（使用配置数据）
    /// </summary>
    /// <param name="levelIndex">关卡索引</param>
    public void SetSceneColorByLevel(int levelIndex)
    {
        // 从配置数据获取关卡换色配置
        string configString = MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data.Level_SceneColorChange_Config;
        string targetColor = LevelConfigParser.ParseSceneColorChange(configString, levelIndex, "Default");

        // 如果找到目标颜色配置，执行换色
        if (targetColor != "Default")
        {
            SetSceneColor(targetColor);
        }
    }


}
