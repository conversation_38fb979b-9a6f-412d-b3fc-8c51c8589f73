using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using UnityEditor;
using UnityEngine;

/// <summary>
/// TileCube 关卡转换编辑器
/// 将CSV格式的关卡数据转换为压缩格式的一行字符串
/// 支持SpawnBox(2x3), LargeBlock(2x2), PhysicBar(直线), SingleBlock, GoldPig, Key等元素
/// </summary>
public class TileCube_LevelEditor : EditorWindow
{
    private string colorMapFilePath = "";
    private string csvFolderPath = "";
    private string outputText = "";
    private Vector2 outputScrollPos;
    private Dictionary<string, int> colorNameToId = new Dictionary<string, int>();

    // 生成参数（现在这些参数在调用GetLevel时直接传入，不再从LevelText读取）
    [Header("生成参数")]
    // [SerializeField] private int connectRate = 50;  // 已弃用：现在在调用GetLevel时直接传入
    // [SerializeField] private int heightConsistencyRate = 60;  // 已弃用：现在在调用GetLevel时直接传入
    // [SerializeField] private int spawnBoxMinColors = 6;  // 已弃用：现在在调用GetLevel时直接传入
    // [SerializeField] private int spawnBoxMaxColors = 20;  // 已弃用：现在在调用GetLevel时直接传入
    // [SerializeField] private int colorPerBarGrid = 5;  // 已弃用：现在在调用GetLevel时直接传入
    [SerializeField] private bool enableLevelEncryption = true;
    [SerializeField] private bool enableRLECompression = true; // RLE压缩开关

    // 记住路径的Key
    private const string COLOR_MAP_PATH_KEY = "TileCube_ColorMapPath";
    private const string CSV_FOLDER_PATH_KEY = "TileCube_CSVFolderPath";

    [MenuItem("TileCube/关卡转换编辑器")]
    public static void ShowWindow()
    {
        var window = GetWindow<TileCube_LevelEditor>("TileCube关卡转换编辑器");
        window.minSize = new Vector2(700, 600);
    }

    private void OnEnable()
    {
        // 加载上次保存的路径
        colorMapFilePath = EditorPrefs.GetString(COLOR_MAP_PATH_KEY, "");
        csvFolderPath = EditorPrefs.GetString(CSV_FOLDER_PATH_KEY, "");

        // 如果颜色映射文件存在，自动加载
        if (!string.IsNullOrEmpty(colorMapFilePath) && File.Exists(colorMapFilePath))
        {
            LoadColorMap();
        }
    }

    private void OnGUI()
    {
        GUILayout.Label("TileCube 关卡转换编辑器", EditorStyles.boldLabel);
        GUILayout.Space(10);

        DrawColorMapSection();
        GUILayout.Space(10);

        DrawCSVFolderSection();
        GUILayout.Space(10);

        DrawGenerationParamsSection();
        GUILayout.Space(10);

        DrawConvertSection();
        GUILayout.Space(10);

        DrawOutputSection();
        GUILayout.Space(10);

    }

    /// <summary>
    /// 绘制颜色对应表部分
    /// </summary>
    private void DrawColorMapSection()
    {
        GUILayout.Label("1. 颜色对应表", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("颜色对应表文件:", GUILayout.Width(120));

        if (GUILayout.Button("选择文件", GUILayout.Width(80)))
        {
            string newPath = EditorUtility.OpenFilePanel("选择颜色对应表文件",
                string.IsNullOrEmpty(colorMapFilePath) ? "" : Path.GetDirectoryName(colorMapFilePath), "txt");
            if (!string.IsNullOrEmpty(newPath))
            {
                colorMapFilePath = newPath;
                EditorPrefs.SetString(COLOR_MAP_PATH_KEY, colorMapFilePath);
                LoadColorMap();
            }
        }

        EditorGUILayout.EndHorizontal();

        if (!string.IsNullOrEmpty(colorMapFilePath))
        {
            EditorGUILayout.LabelField("文件路径:", colorMapFilePath);
        }

        // 拖拽区域
        var dropRect = GUILayoutUtility.GetRect(0, 50, GUILayout.ExpandWidth(true));
        GUI.Box(dropRect, "拖拽颜色对应表文件到这里");

        HandleDragAndDrop(dropRect, ".txt", (path) =>
        {
            colorMapFilePath = path;
            EditorPrefs.SetString(COLOR_MAP_PATH_KEY, colorMapFilePath);
            LoadColorMap();
        });
    }

    /// <summary>
    /// 绘制CSV文件夹部分
    /// </summary>
    private void DrawCSVFolderSection()
    {
        GUILayout.Label("2. CSV关卡文件夹", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("CSV文件夹:", GUILayout.Width(120));

        if (GUILayout.Button("选择文件夹", GUILayout.Width(80)))
        {
            string newPath = EditorUtility.OpenFolderPanel("选择CSV关卡文件夹",
                string.IsNullOrEmpty(csvFolderPath) ? "" : csvFolderPath, "");
            if (!string.IsNullOrEmpty(newPath))
            {
                csvFolderPath = newPath;
                EditorPrefs.SetString(CSV_FOLDER_PATH_KEY, csvFolderPath);
            }
        }

        EditorGUILayout.EndHorizontal();

        if (!string.IsNullOrEmpty(csvFolderPath))
        {
            EditorGUILayout.LabelField("文件夹路径:", csvFolderPath);

            string[] csvFiles = Directory.GetFiles(csvFolderPath, "*.csv");
            if (csvFiles.Length > 0)
            {
                EditorGUILayout.LabelField($"找到 {csvFiles.Length} 个CSV文件");
            }
            else
            {
                EditorGUILayout.HelpBox("文件夹中没有找到CSV文件", MessageType.Warning);
            }
        }

        // 拖拽区域
        var dropRect = GUILayoutUtility.GetRect(0, 50, GUILayout.ExpandWidth(true));
        GUI.Box(dropRect, "拖拽CSV文件夹到这里");

        HandleFolderDragAndDrop(dropRect, (path) =>
        {
            csvFolderPath = path;
            EditorPrefs.SetString(CSV_FOLDER_PATH_KEY, csvFolderPath);
        });
    }

    /// <summary>
    /// 绘制生成参数部分
    /// </summary>
    private void DrawGenerationParamsSection()
    {
        GUILayout.Label("3. 生成参数", EditorStyles.boldLabel);

        EditorGUILayout.BeginVertical("box");

        enableLevelEncryption = EditorGUILayout.Toggle("启用关卡加密打乱", enableLevelEncryption);
        enableRLECompression = EditorGUILayout.Toggle("启用RLE压缩", enableRLECompression);

        EditorGUILayout.HelpBox("同色连片几率、高度一致率、出库块颜色值等参数现在需要在调用GetLevel方法时直接传入，不再从LevelText中读取。", MessageType.Info);

        if (enableRLECompression)
        {
            EditorGUILayout.HelpBox("加密RLE压缩：对连续重复的token使用伪token格式压缩，既减少数据大小又增强安全性。\n例如：A,A,A,A,A,A,A,A,A,A -> AXJ (看起来像AX系列的特殊块)", MessageType.Info);
        }

        EditorGUILayout.EndVertical();
    }

    /// <summary>
    /// 绘制转换部分
    /// </summary>
    private void DrawConvertSection()
    {
        GUILayout.Label("4. 转换操作", EditorStyles.boldLabel);

        bool canConvert = !string.IsNullOrEmpty(colorMapFilePath) &&
                         !string.IsNullOrEmpty(csvFolderPath) &&
                         colorNameToId.Count > 0;

        GUI.enabled = canConvert;
        if (GUILayout.Button("开始转换", GUILayout.Height(30)))
        {
            ConvertCSVToCompressedFormat();
        }
        GUI.enabled = true;

        if (!canConvert)
        {
            EditorGUILayout.HelpBox("请先选择颜色对应表文件和CSV文件夹", MessageType.Info);
        }
    }

    /// <summary>
    /// 绘制输出部分
    /// </summary>
    private void DrawOutputSection()
    {
        GUILayout.Label("5. 转换结果 (一行格式)", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("压缩格式关卡数据 (使用~分隔关卡):");

        GUI.enabled = !string.IsNullOrEmpty(outputText);
        if (GUILayout.Button("复制到剪贴板", GUILayout.Width(120)))
        {
            EditorGUIUtility.systemCopyBuffer = outputText;
            ShowNotification(new GUIContent("已复制到剪贴板!"));
        }
        GUI.enabled = true;

        EditorGUILayout.EndHorizontal();

        outputScrollPos = EditorGUILayout.BeginScrollView(outputScrollPos, GUILayout.Height(200));
        outputText = EditorGUILayout.TextArea(outputText, GUILayout.ExpandHeight(true));
        EditorGUILayout.EndScrollView();
    }


    /// <summary>
    /// 加载颜色对应表
    /// </summary>
    private void LoadColorMap()
    {
        colorNameToId.Clear();

        try
        {
            string[] lines = File.ReadAllLines(colorMapFilePath);
            foreach (string line in lines)
            {
                if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#")) continue;

                string[] parts = line.Split(':');
                if (parts.Length == 2)
                {
                    string colorName = parts[0].Trim();
                    string colorValue = parts[1].Trim();

                    if (int.TryParse(colorValue, out int colorId))
                    {
                        colorNameToId[colorName] = colorId;
                    }
                    else
                    {
                        int hashId = Mathf.Abs(colorValue.GetHashCode() % 100) + 1;
                        colorNameToId[colorName] = hashId;
                    }
                }
            }
        }
        catch (Exception)
        {
        }
    }

    /// <summary>
    /// 转换CSV到压缩格式
    /// </summary>
    private void ConvertCSVToCompressedFormat()
    {
        try
        {
            List<(string levelData, int encryptedId)> levelDataList = new List<(string, int)>();

            string[] csvFiles = Directory.GetFiles(csvFolderPath, "*.csv");
            Array.Sort(csvFiles, (a, b) =>
            {
                string nameA = Path.GetFileNameWithoutExtension(a);
                string nameB = Path.GetFileNameWithoutExtension(b);

                if (nameA.StartsWith("Level") && nameB.StartsWith("Level"))
                {
                    var (levelA, exceedA) = ParseLevelFileName(nameA);
                    var (levelB, exceedB) = ParseLevelFileName(nameB);

                    if (levelA != -1 && levelB != -1)
                    {
                        int levelCompare = levelA.CompareTo(levelB);
                        if (levelCompare != 0) return levelCompare;
                        return exceedA.CompareTo(exceedB);
                    }
                }

                return string.Compare(nameA, nameB, StringComparison.OrdinalIgnoreCase);
            });

            for (int i = 0; i < csvFiles.Length; i++)
            {
                string csvFile = csvFiles[i];
                string fileName = Path.GetFileNameWithoutExtension(csvFile);

                int realLevelId = i + 1;
                int levelExceed = 1;
                string levelIdentifier = "";

                if (fileName.StartsWith("Level"))
                {
                    string levelPart = fileName.Substring(5);

                    if (levelPart.Contains("-"))
                    {
                        string[] parts = levelPart.Split('-');
                        if (parts.Length == 2 &&
                            int.TryParse(parts[0], out int mainLevel) &&
                            int.TryParse(parts[1], out int exceedLevel))
                        {
                            realLevelId = mainLevel;
                            levelExceed = exceedLevel;
                            levelIdentifier = $"{realLevelId}-{levelExceed}";
                        }
                        else
                        {
                            Debug.LogWarning($"无法解析附加关卡文件名: {fileName}，使用默认序号");
                            levelIdentifier = realLevelId.ToString();
                        }
                    }
                    else
                    {
                        if (int.TryParse(levelPart, out int parsedId))
                        {
                            realLevelId = parsedId;
                        }
                        levelIdentifier = realLevelId.ToString();
                    }
                }
                else
                {
                    levelIdentifier = realLevelId.ToString();
                }

                string levelId = enableLevelEncryption ?
                    TileCube_LevelConverter.GetEncryptedLevelId(realLevelId, levelExceed).ToString() : levelIdentifier;

                string compressedLevel = ConvertSingleCSV(csvFile, levelId);
                if (!string.IsNullOrEmpty(compressedLevel))
                {
                    int sortWeight = enableLevelEncryption ?
                        TileCube_LevelConverter.GetEncryptedLevelId(realLevelId, levelExceed) :
                        (realLevelId * 100 + levelExceed);
                    levelDataList.Add((compressedLevel, sortWeight));
                }
            }

            if (enableLevelEncryption)
            {
                levelDataList.Sort((a, b) => a.encryptedId.CompareTo(b.encryptedId));
            }

            List<string> levelStrings = levelDataList.Select(x => x.levelData).ToList();

            outputText = "\"LevelText\": " + "\"" + string.Join("~", levelStrings) + "\"";
            Debug.Log($"TileCube转换完成，共处理 {csvFiles.Length} 个关卡文件");
        }
        catch (Exception e)
        {
            Debug.LogError($"转换失败: {e.Message}");
            EditorUtility.DisplayDialog("转换失败", e.Message, "确定");
        }
    }

    /// <summary>
    /// 转换单个CSV文件（总是启用高级混淆）
    /// </summary>
    private string ConvertSingleCSV(string csvFilePath, string levelId)
    {
        try
        {
            string[] lines = File.ReadAllLines(csvFilePath, Encoding.UTF8);
            if (lines.Length < 10)
            {
                Debug.LogWarning($"CSV文件行数不足: {csvFilePath}");
                return "";
            }

            var csvData = ParseCSVData(lines);

            StringBuilder result = new StringBuilder();
            result.Append($"{levelId}");

            var rowList = new List<string>(10);

            // 列置乱序列（固定10列）
            int seedCol = ComputeDeterministicHash(levelId + "#COL");
            int[] columnPermutation = GeneratePermutation(10, seedCol);

            for (int z = 0; z < 10; z++)
            {
                string compressedRow = CompressLevelRow(csvData, z);

                // 先列置乱（RLE前）
                compressedRow = ApplyColumnPermutationToRowString(compressedRow, columnPermutation);

                if (enableRLECompression)
                {
                    compressedRow = CompressRowWithRLE(compressedRow);
                }

                rowList.Add(compressedRow);
            }

            // 行置乱
            int seedRow = ComputeDeterministicHash(levelId + "#ROW");
            int[] rowPermutation = GeneratePermutation(10, seedRow);
            var permutedRows = ApplyPermutationToList(rowList, rowPermutation);

            // 拼payload
            string payload = string.Join("|", permutedRows);

            // 数字域混淆
            int seedNum = ComputeDeterministicHash(levelId + "#NUM");
            payload = ObfuscateDigits(payload, seedNum, true);

            // XOR + Base64
            int seedXor = ComputeDeterministicHash(levelId + "#XOR");
            string encoded = XorAndBase64Encode(payload, seedXor);

            // 始终写入 E: 格式
            result.Append($"|E:{encoded}");

            return result.ToString();
        }
        catch (Exception e)
        {
            Debug.LogError($"转换CSV文件失败 {csvFilePath}: {e.Message}");
            return "";
        }
    }

    private string[,] ParseCSVData(string[] lines)
    {
        var csvData = new string[10, 10];
        for (int i = 0; i < Math.Min(lines.Length, 10); i++)
        {
            int z = 9 - i;
            string[] cells = lines[i].Split(',');
            for (int x = 0; x < Math.Min(cells.Length, 10); x++)
            {
                csvData[z, x] = cells[x].Trim();
            }
        }
        return csvData;
    }

    private string CompressLevelRow(string[,] csvData, int z)
    {
        var compressedTokens = new List<string>();
        var processedPositions = new HashSet<(int, int)>();

        for (int x = 0; x < 10; x++)
        {
            if (processedPositions.Contains((x, z)))
            {
                compressedTokens.Add("-");
                continue;
            }

            string cell = csvData[z, x];

            if (string.IsNullOrEmpty(cell) || cell == "-")
            {
                compressedTokens.Add("-");
            }
            else if (cell.Contains("s"))
            {
                string compressed = ProcessMultiGridStartPoint(csvData, x, z, cell, processedPositions);
                compressedTokens.Add(compressed);
            }
            else if (cell.Contains("e"))
            {
                compressedTokens.Add("-");
            }
            else
            {
                string compressed = ProcessSingleGridElement(cell);
                compressedTokens.Add(compressed);
            }
        }

        return string.Join(",", compressedTokens);
    }

    // 处理单格元素（钥匙/金猪/普通单块）
    private string ProcessSingleGridElement(string cell)
    {
        // 钥匙
        if (cell == "钥匙")
        {
            return "KE";
        }
        // 金猪（可带高度）
        else if (cell.StartsWith("金猪"))
        {
            string heightStr = cell.Substring(2);
            int height = string.IsNullOrEmpty(heightStr) ? 1 : int.Parse(heightStr);
            return height == 1 ? "GP" : $"GP{height}";
        }
        else
        {
            // 普通单块：颜色+高度
            var parseResult = ParseSingleBlockCell(cell);
            if (parseResult.HasValue)
            {
                char colorChar = GetColorChar(parseResult.Value.colorName);
                return parseResult.Value.height == 1 ? colorChar.ToString() : $"{colorChar}{parseResult.Value.height}";
            }
        }

        return "-";
    }

    private string CompressRowWithRLE(string rowData)
    {
        if (!enableRLECompression || string.IsNullOrEmpty(rowData))
        {
            return rowData;
        }

        string[] tokens = rowData.Split(',');
        var compressedTokens = new List<string>();

        int i = 0;
        while (i < tokens.Length)
        {
            string currentToken = tokens[i];
            int count = 1;
            while (i + count < tokens.Length && tokens[i + count] == currentToken)
            {
                count++;
            }
            if (count > 2)
            {
                string encryptedToken = EncryptRLEToken(currentToken, count);
                compressedTokens.Add(encryptedToken);
            }
            else if (count == 2)
            {
                if (currentToken.Length > 1)
                {
                    string encryptedToken = EncryptRLEToken(currentToken, count);
                    compressedTokens.Add(encryptedToken);
                }
                else
                {
                    compressedTokens.Add(currentToken);
                    compressedTokens.Add(currentToken);
                }
            }
            else
            {
                compressedTokens.Add(currentToken);
            }
            i += count;
        }
        string result = string.Join(",", compressedTokens);
        return result;
    }

    private string EncryptRLEToken(string token, int count)
    {
        char encodedCount = EncodeNumberToChar(count);
        return $"{token}X{encodedCount}";
    }

    private char EncodeNumberToChar(int number)
    {
        if (number >= 1 && number <= 26)
        {
            return (char)('A' + number - 1);
        }
        else
        {
            int modResult = ((number - 1) % 26) + 1;
            return (char)('A' + modResult - 1);
        }
    }

    private string ProcessMultiGridStartPoint(string[,] csvData, int startX, int startZ, string startCell, HashSet<(int, int)> processedPositions)
    {
        Debug.Log($"🔍 处理多格块起点: [{startCell}] 位置({startX},{startZ})");

        if (startCell.Contains("出库"))
        {
            Debug.Log($"  识别为出库起点");
            var endPos = FindSpawnBoxEndPosition(csvData, startCell, startX, startZ, processedPositions);
            if (endPos.HasValue)
            {
                MarkOccupiedArea(processedPositions, startX, startZ, endPos.Value.x, endPos.Value.z);
                System.Random random = new System.Random();
                int spawnColors = random.Next(6 / 2, 20 / 2 + 1) * 2;
                string result = $"SB{spawnColors}";
                Debug.Log($"  ✅ 出库处理成功: {result}");
                return result;
            }
            else
            {
                Debug.LogError($"  ❌ 未找到出库 {startCell} 在位置({startX},{startZ}) 的对应终点");
            }
        }
        else if (startCell.Contains("大"))
        {
            Debug.Log($"  识别为大型块起点");
            var parseResult = ParseLargeBlockCellWithId(startCell);
            Debug.Log($"  解析结果: {(parseResult.HasValue ? $"颜色={parseResult.Value.colorName}, 数值={parseResult.Value.value}, ID={parseResult.Value.id}" : "解析失败")}");

            if (parseResult.HasValue)
            {
                var endPos = FindLargeBlockEndPosition(csvData, startCell, startX, startZ, processedPositions);
                Debug.Log($"  查找终点结果: {(endPos.HasValue ? $"({endPos.Value.x},{endPos.Value.z})" : "未找到")}");

                if (endPos.HasValue)
                {
                    MarkOccupiedArea(processedPositions, startX, startZ, endPos.Value.x, endPos.Value.z);
                    char colorChar = GetColorChar(parseResult.Value.colorName);
                    string result = $"LG{colorChar}{parseResult.Value.value}";
                    Debug.Log($"  ✅ 大型块处理成功: {result}");
                    return result;
                }
                else
                {
                    Debug.LogError($"  ❌ 未找到大型块 {startCell} 在位置({startX},{startZ}) 的对应终点");
                }
            }
            else
            {
                Debug.LogError($"  ❌ 大型块解析失败: {startCell}");
            }
        }
        else if (startCell.Contains("阻"))
        {
            Debug.Log($"  识别为阻挡条起点");
            var barrierInfo = ParseBarrierCellWithId(startCell);
            Debug.Log($"  解析结果: {(barrierInfo.HasValue ? $"颜色={barrierInfo.Value.color}, ID={barrierInfo.Value.id}" : "解析失败")}");

            if (barrierInfo.HasValue)
            {
                var endPos = FindBarrierEndPosition(csvData, startCell, startX, startZ, processedPositions);
                Debug.Log($"  查找终点结果: {(endPos.HasValue ? $"({endPos.Value.x},{endPos.Value.z})" : "未找到")}");

                if (endPos.HasValue)
                {
                    var (direction, length) = CalculateBarrierDirectionAndLength(startX, startZ, endPos.Value.x, endPos.Value.z);
                    MarkBarrierOccupiedPositions(processedPositions, startX, startZ, endPos.Value.x, endPos.Value.z);

                    char colorChar = GetColorChar(barrierInfo.Value.color);
                    string result = $"PH{colorChar}{direction}{length}";
                    Debug.Log($"  ✅ 阻挡条处理成功: {result}");
                    return result;
                }
                else
                {
                    Debug.LogError($"  ❌ 未找到阻挡条 {startCell} 在位置({startX},{startZ}) 的对应终点");
                }
            }
            else
            {
                Debug.LogError($"  ❌ 阻挡条解析失败: {startCell}");
            }
        }

        Debug.LogWarning($"🚫 多格块处理失败，返回 '-': {startCell}");
        return "-";
    }

    private (int x, int z)? FindBarrierEndPosition(string[,] csvData, string startCell, int startX, int startZ, HashSet<(int, int)> processedPositions)
    {
        var barrierInfo = ParseBarrierCellWithId(startCell);
        if (barrierInfo == null)
            return null;

        string targetEndPattern = barrierInfo.Value.id.HasValue ? $"阻{barrierInfo.Value.color}e{barrierInfo.Value.id}" : $"阻{barrierInfo.Value.color}e";

        var candidates = new List<(int x, int z, int distance)>();
        for (int z = 0; z < 10; z++)
        {
            for (int x = 0; x < 10; x++)
            {
                string cellContent = csvData[z, x];
                bool isMatch = cellContent == targetEndPattern;
                if (isMatch && !processedPositions.Contains((x, z)))
                {
                    int distance = Math.Abs(x - startX) + Math.Abs(z - startZ);
                    candidates.Add((x, z, distance));
                }
            }
        }
        if (candidates.Count > 0)
        {
            var closest = candidates.OrderBy(c => c.distance).First();
            return (closest.x, closest.z);
        }
        return null;
    }

    private (int x, int z)? FindSpawnBoxEndPosition(string[,] csvData, string startCell, int startX, int startZ, HashSet<(int, int)> processedPositions)
    {
        var spawnBoxId = ParseSpawnBoxCellWithId(startCell);
        string targetEndPattern = spawnBoxId.HasValue ? $"出库e{spawnBoxId.Value}" : startCell.Replace("s", "e");
        for (int z = 0; z < 10; z++)
        {
            for (int x = 0; x < 10; x++)
            {
                if (csvData[z, x] == targetEndPattern && !processedPositions.Contains((x, z)))
                {
                    return (x, z);
                }
            }
        }
        return null;
    }

    private (int x, int z)? FindLargeBlockEndPosition(string[,] csvData, string startCell, int startX, int startZ, HashSet<(int, int)> processedPositions)
    {
        var largeBlockInfo = ParseLargeBlockCellWithId(startCell);
        if (largeBlockInfo == null)
            return null;

        string targetEndPattern = largeBlockInfo.Value.id.HasValue ? $"大{largeBlockInfo.Value.colorName}{largeBlockInfo.Value.value}e{largeBlockInfo.Value.id}" : startCell.Replace("s", "e");
        for (int z = 0; z < 10; z++)
        {
            for (int x = 0; x < 10; x++)
            {
                if (csvData[z, x] == targetEndPattern && !processedPositions.Contains((x, z)))
                {
                    return (x, z);
                }
            }
        }
        return null;
    }

    private (int x, int z)? FindEndPosition(string[,] csvData, string endPattern, int startX, int startZ)
    {
        for (int z = 0; z < 10; z++)
        {
            for (int x = 0; x < 10; x++)
            {
                if (csvData[z, x] == endPattern)
                {
                    return (x, z);
                }
            }
        }
        return null;
    }

    private void MarkOccupiedArea(HashSet<(int, int)> processedPositions, int startX, int startZ, int endX, int endZ)
    {
        int minX = Math.Min(startX, endX);
        int maxX = Math.Max(startX, endX);
        int minZ = Math.Min(startZ, endZ);
        int maxZ = Math.Max(startZ, endZ);
        for (int z = minZ; z <= maxZ; z++)
        {
            for (int x = minX; x <= maxX; x++)
            {
                processedPositions.Add((x, z));
            }
        }
    }

    private void MarkBarrierOccupiedPositions(HashSet<(int, int)> processedPositions, int startX, int startZ, int endX, int endZ)
    {
        if (startZ == endZ)
        {
            int minX = Math.Min(startX, endX);
            int maxX = Math.Max(startX, endX);
            for (int x = minX; x <= maxX; x++)
            {
                processedPositions.Add((x, startZ));
            }
        }
        else if (startX == endX)
        {
            int minZ = Math.Min(startZ, endZ);
            int maxZ = Math.Max(startZ, endZ);
            for (int z = minZ; z <= maxZ; z++)
            {
                processedPositions.Add((startX, z));
            }
        }
    }

    private (char direction, int length) CalculateBarrierDirectionAndLength(int startX, int startZ, int endX, int endZ)
    {
        Debug.Log($"🧭 计算阻挡条方向: 起点({startX},{startZ}) -> 终点({endX},{endZ})");
        if (startZ == endZ)
        {
            char direction = startX < endX ? 'R' : 'L';
            int length = Math.Abs(endX - startX) + 1;
            Debug.Log($"  水平方向: {direction}, 长度: {length}");
            return (direction, length);
        }
        else if (startX == endX)
        {
            char direction = startZ < endZ ? 'U' : 'D';
            int length = Math.Abs(endZ - startZ) + 1;
            Debug.Log($"  垂直方向: {direction}, 长度: {length}");
            return (direction, length);
        }
        Debug.LogWarning($"  既不水平也不垂直，默认右方向");
        return ('R', 1);
    }

    private int? ParseSpawnBoxCellWithId(string cell)
    {
        if (!cell.StartsWith("出库") || (!cell.Contains("s") && !cell.Contains("e")))
        {
            return null;
        }
        string content = cell.Substring(2);
        int seIndex = content.IndexOf('s');
        if (seIndex == -1) seIndex = content.IndexOf('e');
        if (seIndex == -1)
        {
            return null;
        }
        string seAndIdPart = content.Substring(seIndex);
        if (seAndIdPart.Length > 1)
        {
            string idStr = seAndIdPart.Substring(1);
            if (int.TryParse(idStr, out int parsedId))
            {
                return parsedId;
            }
        }
        return null;
    }

    private (string colorName, int value, int? id)? ParseLargeBlockCellWithId(string cell)
    {
        Debug.Log($"🔍 解析大型块: [{cell}]");
        if (!cell.StartsWith("大") || (!cell.Contains("s") && !cell.Contains("e")))
        {
            Debug.Log($"  ❌ 格式不正确 - 不包含s或e标记");
            return null;
        }
        string content = cell.Substring(1);
        Debug.Log($"  提取内容: [{content}]");
        int seIndex = content.IndexOf('s');
        if (seIndex == -1) seIndex = content.IndexOf('e');
        if (seIndex == -1)
        {
            Debug.Log($"  ❌ 未找到s或e标记");
            return null;
        }
        string colorValuePart = content.Substring(0, seIndex);
        string seAndIdPart = content.Substring(seIndex);
        Debug.Log($"  分离结果: 颜色数值部分=[{colorValuePart}], s/e+序号部分=[{seAndIdPart}]");
        int valueStart = -1;
        for (int i = 0; i < colorValuePart.Length; i++)
        {
            if (char.IsDigit(colorValuePart[i])) { valueStart = i; break; }
        }
        if (valueStart <= 0)
        {
            Debug.Log($"  ❌ 颜色数值部分格式错误");
            return null;
        }
        string colorName = colorValuePart.Substring(0, valueStart);
        string valueStr = colorValuePart.Substring(valueStart);
        Debug.Log($"  颜色=[{colorName}], 数值字符串=[{valueStr}]");
        if (!int.TryParse(valueStr, out int value))
        {
            Debug.Log($"  ❌ 数值解析失败");
            return null;
        }
        int? id = null;
        if (seAndIdPart.Length > 1)
        {
            string idStr = seAndIdPart.Substring(1);
            if (int.TryParse(idStr, out int parsedId))
            {
                id = parsedId;
                Debug.Log($"  序号=[{id}]");
            }
            else
            {
                Debug.Log($"  序号解析失败: [{idStr}]");
            }
        }
        var result = (colorName, value, id);
        Debug.Log($"  ✅ 解析成功: {result}");
        return result;
    }

    private (string colorName, int value)? ParseLargeBlockCell(string cell)
    {
        var result = ParseLargeBlockCellWithId(cell);
        if (result.HasValue)
        {
            return (result.Value.colorName, result.Value.value);
        }
        return null;
    }

    private (string color, int? id)? ParseBarrierCellWithId(string cell)
    {
        Debug.Log($"🔍 解析阻挡条: [{cell}]");
        if (!cell.StartsWith("阻") || (!cell.Contains("s") && !cell.Contains("e")))
        {
            Debug.Log($"  ❌ 格式不正确 - 不包含s或e标记");
            return null;
        }
        string content = cell.Substring(1);
        Debug.Log($"  提取内容: [{content}]");
        int seIndex = content.IndexOf('s');
        if (seIndex == -1) seIndex = content.IndexOf('e');
        if (seIndex == -1)
        {
            Debug.Log($"  ❌ 未找到s或e标记");
            return null;
        }
        string colorPart = content.Substring(0, seIndex);
        string seAndIdPart = content.Substring(seIndex);
        Debug.Log($"  分离结果: 颜色部分=[{colorPart}], s/e+序号部分=[{seAndIdPart}]");
        int? id = null;
        if (seAndIdPart.Length > 1)
        {
            string idStr = seAndIdPart.Substring(1);
            if (int.TryParse(idStr, out int parsedId))
            {
                id = parsedId;
                Debug.Log($"  序号=[{id}]");
            }
            else
            {
                Debug.Log($"  序号解析失败: [{idStr}]");
            }
        }
        var result = (colorPart, id);
        Debug.Log($"  ✅ 解析成功: {result}");
        return result;
    }

    private string ParseBarrierCell(string cell)
    {
        var result = ParseBarrierCellWithId(cell);
        return result?.color ?? "";
    }

    private (string colorName, int height)? ParseSingleBlockCell(string cell)
    {
        int numberStart = -1;
        for (int i = 0; i < cell.Length; i++)
        {
            if (char.IsDigit(cell[i])) { numberStart = i; break; }
        }
        if (numberStart == -1)
        {
            return (cell, 1);
        }
        else
        {
            string colorName = cell.Substring(0, numberStart);
            string heightStr = cell.Substring(numberStart);
            if (int.TryParse(heightStr, out int height))
            {
                return (colorName, height);
            }
        }
        return null;
    }

    private char GetColorChar(string colorName)
    {
        if (colorNameToId.TryGetValue(colorName, out int colorId))
        {
            char result = (char)('A' + colorId - 1);
            Debug.Log($"颜色映射: {colorName} -> {colorId} -> {result}");
            return result;
        }
        Debug.LogWarning($"未找到颜色映射: {colorName}，使用默认颜色A");
        return 'A';
    }

    private void HandleDragAndDrop(Rect dropRect, string extension, System.Action<string> onDrop)
    {
        if (dropRect.Contains(Event.current.mousePosition))
        {
            if (Event.current.type == EventType.DragUpdated)
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                Event.current.Use();
            }
            else if (Event.current.type == EventType.DragPerform)
            {
                DragAndDrop.AcceptDrag();
                if (DragAndDrop.paths.Length > 0 && DragAndDrop.paths[0].EndsWith(extension))
                {
                    onDrop(DragAndDrop.paths[0]);
                }
                Event.current.Use();
            }
        }
    }

    private void HandleFolderDragAndDrop(Rect dropRect, System.Action<string> onDrop)
    {
        if (dropRect.Contains(Event.current.mousePosition))
        {
            if (Event.current.type == EventType.DragUpdated)
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                Event.current.Use();
            }
            else if (Event.current.type == EventType.DragPerform)
            {
                DragAndDrop.AcceptDrag();
                if (DragAndDrop.paths.Length > 0 && Directory.Exists(DragAndDrop.paths[0]))
                {
                    onDrop(DragAndDrop.paths[0]);
                }
                Event.current.Use();
            }
        }
    }

    private (int mainLevel, int levelExceed) ParseLevelFileName(string fileName)
    {
        if (!fileName.StartsWith("Level"))
            return (-1, -1);
        string levelPart = fileName.Substring(5);
        if (levelPart.Contains("-"))
        {
            string[] parts = levelPart.Split('-');
            if (parts.Length == 2 && int.TryParse(parts[0], out int mainLevel) && int.TryParse(parts[1], out int levelExceed))
            {
                return (mainLevel, levelExceed);
            }
        }
        else
        {
            if (int.TryParse(levelPart, out int mainLevel))
            {
                return (mainLevel, 1);
            }
        }
        return (-1, -1);
    }

    // ===== 高级混淆辅助方法 =====
    private int ComputeDeterministicHash(string input)
    {
        unchecked
        {
            const uint fnvOffset = 2166136261;
            const uint fnvPrime = 16777619;
            uint hash = fnvOffset;
            foreach (char c in input)
            {
                hash ^= c;
                hash *= fnvPrime;
            }
            return (int)(hash & 0x7FFFFFFF);
        }
    }

    private int[] GeneratePermutation(int length, int seed)
    {
        var perm = Enumerable.Range(0, length).ToArray();
        uint state = (uint)seed;
        for (int i = length - 1; i > 0; i--)
        {
            state = state * 1664525u + 1013904223u;
            int j = (int)(state % (uint)(i + 1));
            int tmp = perm[i];
            perm[i] = perm[j];
            perm[j] = tmp;
        }
        return perm;
    }

    private List<string> ApplyPermutationToList(List<string> source, int[] permutation)
    {
        var result = new List<string>(source.Count);
        for (int i = 0; i < source.Count; i++) result.Add(source[i]);
        for (int i = 0; i < permutation.Length && i < source.Count; i++)
        {
            result[i] = source[permutation[i]];
        }
        return result;
    }

    private string ApplyColumnPermutationToRowString(string row, int[] columnPermutation)
    {
        if (columnPermutation == null) return row;
        var tokens = row.Split(',');
        if (tokens.Length != 10) return row;
        var reordered = new string[10];
        for (int i = 0; i < 10; i++)
        {
            reordered[i] = tokens[columnPermutation[i]];
        }
        return string.Join(",", reordered);
    }

    private string ObfuscateDigits(string text, int seed, bool encode)
    {
        var map = BuildDigitMap(seed, encode);
        var sb = new StringBuilder(text.Length);
        foreach (char c in text)
        {
            if (c >= '0' && c <= '9') sb.Append(map[c - '0']); else sb.Append(c);
        }
        return sb.ToString();
    }

    private char[] BuildDigitMap(int seed, bool encode)
    {
        var digits = Enumerable.Range(0, 10).ToArray();
        uint state = (uint)seed;
        for (int i = digits.Length - 1; i > 0; i--)
        {
            state = state * 1664525u + 1013904223u;
            int j = (int)(state % (uint)(i + 1));
            int tmp = digits[i];
            digits[i] = digits[j];
            digits[j] = tmp;
        }
        var map = new char[10];
        if (encode)
        {
            for (int d = 0; d < 10; d++) map[d] = (char)('0' + digits[d]);
        }
        else
        {
            var inv = new int[10];
            for (int d = 0; d < 10; d++) inv[digits[d]] = d;
            for (int d = 0; d < 10; d++) map[d] = (char)('0' + inv[d]);
        }
        return map;
    }

    private string XorAndBase64Encode(string plain, int seed)
    {
        var bytes = Encoding.UTF8.GetBytes(plain);
        var key = GenerateXorKey(bytes.Length, seed);
        for (int i = 0; i < bytes.Length; i++) bytes[i] ^= key[i];
        return Convert.ToBase64String(bytes);
    }

    private byte[] GenerateXorKey(int length, int seed)
    {
        var key = new byte[length];
        uint state = (uint)seed;
        for (int i = 0; i < length; i++)
        {
            state = state * 1664525u + 1013904223u;
            key[i] = (byte)((state >> 16) & 0xFF);
        }
        return key;
    }
}