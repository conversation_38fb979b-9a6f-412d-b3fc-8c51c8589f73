using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using System.Text;

/// <summary>
/// TileCube 关卡转换器 - 解析压缩格式的关卡数据并生成后续行
/// 压缩格式说明（一行格式，使用~分隔关卡）：
/// 1|BA-S25ABABAB|B2A2--A2B2A2B2A2B2|...
/// 第一段：关卡ID（已加密）
/// 第二段开始：每一段代表一行数据
/// 字符映射：A=1红, B=2蓝, C=3绿, D=4黄, E=5紫, F=6碧, G=7橙, H=8粉, I=9白, J=10黑, K=11棕, L=12青...
/// 类型：SingleBlock=颜色+数字, PhysicBar=PH+颜色+方向+长度, GoldPig=GP+数字, Key=KE, LargeBlock=LG+颜色+数值, SpawnBox=SB+数值
/// 注意：生成参数（连片率、高度一致率等）现在通过GetLevel方法直接传入，不再存储在LevelText中
/// 支持附加关卡：Level10表示主关卡，Level10-2表示第10关的第2个附加关卡
/// </summary>
public static class TileCube_LevelConverter
{
    // 修改缓存结构，支持(level, levelExceed)组合键 - Unity 2019兼容版本
    private static Dictionary<LevelKey, TileCube_Data_Level> cachedLevels = new Dictionary<LevelKey, TileCube_Data_Level>();
    private static bool isInitialized = false;
    private static Dictionary<string, int> colorNameToId = new Dictionary<string, int>();

    /// <summary>
    /// 关卡ID加密映射表 - (真实关卡序号, 附加关卡序号) -> 加密序号
    /// </summary>
    private static Dictionary<LevelKey, int> levelEncryptionMap = null;

    /// <summary>
    /// 关卡键结构体，用于替代元组语法以兼容Unity 2019
    /// </summary>
    [System.Serializable]
    public struct LevelKey : System.IEquatable<LevelKey>
    {
        public int level;
        public int levelExceed;

        public LevelKey(int level, int levelExceed)
        {
            this.level = level;
            this.levelExceed = levelExceed;
        }

        public bool Equals(LevelKey other)
        {
            return level == other.level && levelExceed == other.levelExceed;
        }

        public override bool Equals(object obj)
        {
            return obj is LevelKey other && Equals(other);
        }

        public override int GetHashCode()
        {
            return level.GetHashCode() ^ (levelExceed.GetHashCode() << 2);
        }

        public override string ToString()
        {
            return $"({level}, {levelExceed})";
        }
    }

    // 读取端解析期间的列置乱（用于高级混淆逆置乱）
    private static int[] currentColumnPermutationForParsing = null;


    /// <summary>
    /// 获取指定关卡数据，支持直接传入生成参数和附加关卡
    /// </summary>
    /// <param name="levelIndex">关卡索引（从1开始）</param>
    /// <param name="totalColorDict">总颜色配置字典（用于生成后续行）</param>
    /// <param name="levelExceed">附加关卡序号（默认为1，表示主关卡，2表示Level10-2）</param>
    /// <returns>关卡数据</returns>
    public static TileCube_Data_Level GetLevel(int levelIndex, Dictionary<int, int> totalColorDict, int levelExceed = 1)
    {
        InitializeIfNeeded();

        TileCube_Data_Level level = null;

        // 尝试从缓存获取基础关卡数据（使用组合键）
        var cacheKey = new LevelKey(levelIndex, levelExceed);
        if (cachedLevels.TryGetValue(cacheKey, out level))
        {
            // 复制一份关卡数据，避免修改缓存的原始数据
            level = CloneLevelData(level);
        }
        else
        {
            ////Debug.LogWarning($"关卡 {levelIndex}-{levelExceed} 不存在，创建空关卡");
            level = new TileCube_Data_Level();
        }

        // 从TileCube_Const_Data获取关卡配置参数
        var constData = MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data;

        // 使用新的配置解析器获取关卡参数（支持向后兼容）
        int connectRate = LevelConfigParser.GetLevelConfigWithFallback(
            constData?.Level_ConnectRate_Config, levelIndex, 50);



        int heightConsistencyRate = LevelConfigParser.GetLevelConfigWithFallback(
            constData?.Level_HeightConsistencyRate_Config, levelIndex, 60);

        // Debug.LogError(levelIndex + " " + "链接率： " + connectRate + " 高度率： " + heightConsistencyRate);

        int goldPigHitCount = LevelConfigParser.GetLevelConfigWithFallback(
            constData?.Level_GoldPigHitCount_Config, levelIndex, 1);

        int colorPerBarGrid = LevelConfigParser.GetLevelConfigWithFallback(
            constData?.Level_ColorPerBarGrid_Config, levelIndex, 5);

        // 获取出库块颜色范围配置（支持向后兼容）
        var spawnBoxColorRange = LevelConfigParser.GetSpawnBoxColorRangeWithFallback(
            constData?.Level_SpawnBoxColorRange_Config, levelIndex, 6, 20);
        int spawnBoxColorRangeMin = spawnBoxColorRange.Item1;
        int spawnBoxColorRangeMax = spawnBoxColorRange.Item2;

        // 确保出库块颜色范围为偶数（与ParseGenerationParams逻辑保持一致）
        if (spawnBoxColorRangeMin % 2 != 0) spawnBoxColorRangeMin++;
        if (spawnBoxColorRangeMax % 2 != 0) spawnBoxColorRangeMax--;

        // 创建自定义生成参数
        var customParams = new GenerationParams
        {
            MaxHeight = 3, // 这个值会在生成时根据前十行的最高高度动态设置
            ConnectRate = connectRate,
            HeightConsistencyRate = heightConsistencyRate,
            SpawnBoxColorRange = new System.Tuple<int, int>(spawnBoxColorRangeMin, spawnBoxColorRangeMax),
            ColorPerBarGrid = colorPerBarGrid,
            GoldPigHitCount = goldPigHitCount
        };

        // 设置关卡参数（包含levelExceed信息）
        level.SetLevelId(levelIndex);
        level.SetLevelExceed(levelExceed);
        level.SetGenerationParams(customParams);

        // 更新所有金猪的击打次数以应用从配置中获取的GoldPigHitCount参数
        // 注意：缓存的关卡数据中金猪的HitValues默认为1，这里根据配置重新设置
        foreach (var goldPig in level.GoldPigs)
        {
            goldPig.HitValues = goldPigHitCount;
        }

        // 更新所有阻挡条的每格击打次数以应用从配置中获取的Level_Barrier_OneGrid_HitCount参数
        // 注意：缓存的关卡数据中阻挡条的HitsPerGrid默认为5，这里根据配置重新设置
        foreach (var barrier in level.Barriers)
        {
            barrier.HitsPerGrid = colorPerBarGrid;
        }

        // 生成后续行
        if (totalColorDict != null)
        {
            GenerateAdditionalRows(level, totalColorDict, customParams);
        }

        return level;
    }


    /// <summary>
    /// 复制关卡数据
    /// </summary>
    private static TileCube_Data_Level CloneLevelData(TileCube_Data_Level original)
    {
        var clone = new TileCube_Data_Level();

        // 复制 SingleBlocks
        foreach (var block in original.SingleBlocks)
        {
            clone.SingleBlocks.Add(new TileCube_Data_SingleBlock
            {
                GridX = block.GridX,
                GridZ = block.GridZ,
                Height = block.Height,
                ColorId = block.ColorId
            });
        }

        // 复制 LargeBlocks
        foreach (var largeBlock in original.LargeBlocks)
        {
            clone.LargeBlocks.Add(new TileCube_Data_LargeBlock
            {
                GridX = largeBlock.GridX,
                GridZ = largeBlock.GridZ,
                ColorId = largeBlock.ColorId,
                HitValues = largeBlock.HitValues
            });
        }

        // 复制 GoldPigs
        foreach (var goldPig in original.GoldPigs)
        {
            clone.GoldPigs.Add(new TileCube_Data_GoldPig
            {
                GridX = goldPig.GridX,
                GridZ = goldPig.GridZ,
                Height = goldPig.Height,
                HitValues = goldPig.HitValues
            });
        }

        // 复制 Barriers
        foreach (var barrier in original.Barriers)
        {
            clone.Barriers.Add(new TileCube_Data_Barrier
            {
                GridX = barrier.GridX,
                GridZ = barrier.GridZ,
                Direction = barrier.Direction,
                GridCount = barrier.GridCount,
                ColorId = barrier.ColorId,
                HitsPerGrid = barrier.HitsPerGrid
            });
        }

        // 复制 Keys
        foreach (var key in original.Keys)
        {
            clone.Keys.Add(new TileCube_Data_Key
            {
                GridX = key.GridX,
                GridZ = key.GridZ,
                Height = key.Height,
                KeyId = key.KeyId
            });
        }

        // 复制 Spawners
        foreach (var spawner in original.Spawners)
        {
            clone.Spawners.Add(new TileCube_Data_Spawner
            {
                GridX = spawner.GridX,
                GridZ = spawner.GridZ,
                SpawnColorSingleBlocks = new List<int>(spawner.SpawnColorSingleBlocks)
            });
        }

        // 复制关卡ID和附加关卡序号
        clone.SetLevelId(original.GetLevelId());
        clone.SetLevelExceed(original.GetLevelExceed());

        return clone;
    }

    /// <summary>
    /// 重新生成SpawnBox的颜色库存（使用新的参数范围） - 使用确定性算法
    /// </summary>
    private static void RegenerateSpawnerColors(TileCube_Data_Level level, System.Tuple<int, int> spawnBoxColorRange)
    {
        // 使用关卡ID作为种子，确保每关的SpawnBox颜色分配都是固定的
        int levelSeed = level.GetLevelId() * 1000 + level.GetLevelExceed();

        int spawnerIndex = 0;
        foreach (var spawner in level.Spawners)
        {
            // 使用确定性算法生成颜色数量：基于关卡ID、附加关卡序号和SpawnBox位置
            int positionSeed = levelSeed + spawner.GridX * 100 + spawner.GridZ * 10 + spawnerIndex;
            int spawnColors = DeterministicValueInRange(positionSeed, spawnBoxColorRange.Item1, spawnBoxColorRange.Item2);

            // 确保是偶数
            if (spawnColors % 2 != 0) spawnColors++;

            // 重新生成颜色库存
            spawner.SpawnColorSingleBlocks = GenerateSpawnColors(spawnColors);

            ////Debug.Log($"🎯 重新生成SpawnBox颜色库存: 位置({spawner.GridX},{spawner.GridZ}) -> {spawnColors}个颜色块");

            spawnerIndex++;
        }
    }

    /// <summary>
    /// 确定性算法：基于种子在指定范围内生成固定值
    /// </summary>
    private static int DeterministicValueInRange(int seed, int min, int max)
    {
        if (min > max) return min;
        if (min == max) return min;

        // 使用简单的线性同余生成器算法生成伪随机数
        int range = max - min + 1;
        int hash = seed;
        hash = ((hash * 1103515245) + 12345) & 0x7FFFFFFF; // LCG算法
        return min + (hash % range);
    }

    /// <summary>
    /// 从剩余颜色中重新分配SpawnBox的颜色库存 - 使用确定性算法
    /// </summary>
    private static void RegenerateSpawnerColorsFromRemaining(TileCube_Data_Level level, Dictionary<int, int> remainingColors, System.Tuple<int, int> spawnBoxColorRange)
    {
        // 使用关卡ID作为种子，确保每关的SpawnBox颜色分配都是固定的
        int levelSeed = level.GetLevelId() * 1000 + level.GetLevelExceed();

        int spawnerIndex = 0;
        foreach (var spawner in level.Spawners)
        {
            // 使用确定性算法生成颜色数量：基于关卡ID、附加关卡序号和SpawnBox位置
            int positionSeed = levelSeed + spawner.GridX * 100 + spawner.GridZ * 10 + spawnerIndex;
            int spawnColors = DeterministicValueInRange(positionSeed, spawnBoxColorRange.Item1, spawnBoxColorRange.Item2);

            // 确保是偶数
            if (spawnColors % 2 != 0) spawnColors++;

            // 从剩余颜色中分配（使用确定性方法）
            spawner.SpawnColorSingleBlocks = GenerateSpawnColorsFromRemainingDeterministic(spawnColors, remainingColors, positionSeed);

            ////Debug.Log($"🎯 从剩余颜色重新分配SpawnBox: 位置({spawner.GridX},{spawner.GridZ}) -> {spawnColors}个颜色块");

            spawnerIndex++;
        }
    }

    /// <summary>
    /// 从剩余颜色中生成SpawnBox的颜色库存（保留用于向后兼容）
    /// </summary>
    private static List<int> GenerateSpawnColorsFromRemaining(int totalColors, Dictionary<int, int> remainingColors)
    {
        return GenerateSpawnColorsFromRemainingDeterministic(totalColors, remainingColors, 0);
    }

    /// <summary>
    /// 从剩余颜色中确定性生成SpawnBox的颜色库存
    /// </summary>
    private static List<int> GenerateSpawnColorsFromRemainingDeterministic(int totalColors, Dictionary<int, int> remainingColors, int seed)
    {
        var spawnColors = new List<int>();
        var availableColors = remainingColors.Where(kv => kv.Value > 0).OrderBy(kv => kv.Key).ToList(); // 按颜色ID排序确保顺序固定

        if (availableColors.Count == 0)
        {
            ////Debug.LogWarning("没有剩余颜色可用于SpawnBox分配，使用默认颜色");
            return GenerateSpawnColors(totalColors); // 回退到原来的方法
        }

        // 平均分配给可用的颜色
        int colorsPerType = Math.Max(1, totalColors / availableColors.Count);
        int remainingToAssign = totalColors;

        foreach (var colorKv in availableColors)
        {
            if (remainingToAssign <= 0) break;

            int colorId = colorKv.Key;
            int availableCount = Math.Min(colorsPerType, colorKv.Value);
            int assignCount = Math.Min(availableCount, remainingToAssign);

            for (int i = 0; i < assignCount; i++)
            {
                spawnColors.Add(colorId);
            }

            remainingToAssign -= assignCount;

            ////Debug.Log($"  分配颜色{colorId}: {assignCount}个");
        }

        // 如果还有剩余需要分配的，确定性分配给有库存的颜色
        int additionalIndex = 0;
        while (remainingToAssign > 0 && availableColors.Count > 0)
        {
            // 使用确定性算法选择颜色
            int colorIndex = DeterministicValueInRange(seed + additionalIndex, 0, availableColors.Count - 1);
            var selectedColor = availableColors[colorIndex];

            if (selectedColor.Value > 0)
            {
                spawnColors.Add(selectedColor.Key);
                remainingToAssign--;
            }

            additionalIndex++;

            // 防止无限循环
            if (additionalIndex > 1000) break;
        }

        return spawnColors;
    }

    /// <summary>
    /// 计算前10行已使用的颜色（不包括SpawnBox，因为要重新分配）
    /// </summary>
    private static Dictionary<int, int> CalculateUsedColorsExcludingSpawners(TileCube_Data_Level level, int colorPerBarGrid)
    {
        var usedColors = new Dictionary<int, int>();

        ////Debug.Log($"📊 开始计算前10行已使用颜色（不包括SpawnBox）...");

        // SingleBlock颜色消耗 - 每个SingleBlock消耗1个颜色
        foreach (var block in level.SingleBlocks)
        {
            int currentCount = usedColors.ContainsKey(block.ColorId) ? usedColors[block.ColorId] : 0;
            usedColors[block.ColorId] = currentCount + 1;
        }

        ////Debug.Log($"SingleBlock统计: 总共{level.SingleBlocks.Count}个块");
        foreach (var kv in usedColors.OrderBy(x => x.Key))
        {
            ////Debug.Log($"  颜色{kv.Key}: {kv.Value}个SingleBlock");
        }

        // PhysicBar颜色消耗
        foreach (var barrier in level.Barriers)
        {
            int barColorCost = barrier.GridCount * colorPerBarGrid;
            int currentCount = usedColors.ContainsKey(barrier.ColorId) ? usedColors[barrier.ColorId] : 0;
            usedColors[barrier.ColorId] = currentCount + barColorCost;
        }

        // LargeBlock颜色消耗
        foreach (var largeBlock in level.LargeBlocks)
        {
            int currentCount = usedColors.ContainsKey(largeBlock.ColorId) ? usedColors[largeBlock.ColorId] : 0;
            usedColors[largeBlock.ColorId] = currentCount + largeBlock.HitValues;
        }

        // 注意：不计算SpawnBox的颜色消耗，因为要重新分配

        return usedColors;
    }

    /// <summary>
    /// 优先为SpawnBox分配颜色 - 使用确定性算法确保每关固定
    /// </summary>
    private static void AllocateColorsToSpawnBoxes(TileCube_Data_Level level, Dictionary<int, int> availableColors, System.Tuple<int, int> spawnBoxColorRange)
    {
        // 使用关卡ID作为种子，确保每关的SpawnBox颜色分配都是固定的
        int levelSeed = level.GetLevelId() * 1000 + level.GetLevelExceed();

        //    Debug.Log($"🎯 开始优先为SpawnBox分配颜色...");

        int spawnerIndex = 0;
        foreach (var spawner in level.Spawners)
        {
            // 使用确定性算法生成颜色数量：基于关卡ID、附加关卡序号和SpawnBox位置
            int positionSeed = levelSeed + spawner.GridX * 100 + spawner.GridZ * 10 + spawnerIndex;
            int spawnColors = DeterministicValueInRange(positionSeed, spawnBoxColorRange.Item1, spawnBoxColorRange.Item2);

            // 确保是偶数
            if (spawnColors % 2 != 0) spawnColors++;

            // 从可用颜色中分配（使用确定性方法）
            spawner.SpawnColorSingleBlocks = AllocateColorsFromAvailableDeterministic(spawnColors, availableColors, positionSeed);

            //            Debug.Log($"🎯 SpawnBox分配完成: 位置({spawner.GridX},{spawner.GridZ}) -> {spawnColors}个颜色块");
            //   Debug.Log($"  分配的颜色: {string.Join(",", spawner.SpawnColorSingleBlocks)}");

            spawnerIndex++;
        }
    }

    /// <summary>
    /// 从可用颜色中随机分配指定数量的颜色（保留用于向后兼容）
    /// </summary>
    private static List<int> AllocateColorsFromAvailable(int totalColors, Dictionary<int, int> availableColors)
    {
        return AllocateColorsFromAvailableDeterministic(totalColors, availableColors, 0);
    }

    /// <summary>
    /// 从可用颜色中确定性分配指定数量的颜色
    /// </summary>
    private static List<int> AllocateColorsFromAvailableDeterministic(int totalColors, Dictionary<int, int> availableColors, int seed)
    {
        var allocatedColors = new List<int>();
        var availableList = availableColors.Where(kv => kv.Value > 0).OrderBy(kv => kv.Key).ToList(); // 按颜色ID排序确保顺序固定

        if (availableList.Count == 0)
        {
            ////Debug.LogWarning("没有可用颜色为SpawnBox分配，使用默认颜色");
            return GenerateSpawnColors(totalColors); // 回退到原来的方法
        }

        // 创建颜色池（根据每种颜色的可用数量）
        var colorPool = new List<int>();
        foreach (var colorKv in availableList)
        {
            int colorId = colorKv.Key;
            int availableCount = colorKv.Value;

            // 将每种颜色按可用数量添加到池中
            for (int i = 0; i < availableCount; i++)
            {
                colorPool.Add(colorId);
            }
        }

        ////Debug.Log($"  颜色池总数: {colorPool.Count}个颜色块可供确定性分配");

        // 确定性抽取指定数量的颜色
        int actualAllocateCount = Math.Min(totalColors, colorPool.Count);
        var colorCountMap = new Dictionary<int, int>();

        for (int i = 0; i < actualAllocateCount; i++)
        {
            // 确定性选择颜色：基于种子和索引计算确定的位置
            int deterministicIndex = DeterministicValueInRange(seed + i, 0, colorPool.Count - 1);
            int selectedColorId = colorPool[deterministicIndex];

            // 添加到分配列表
            allocatedColors.Add(selectedColorId);

            // 统计每种颜色的分配数量（用于调试）
            int currentCount = colorCountMap.ContainsKey(selectedColorId) ? colorCountMap[selectedColorId] : 0;
            colorCountMap[selectedColorId] = currentCount + 1;

            // 从池中移除已选择的颜色
            colorPool.RemoveAt(deterministicIndex);
        }

        // 从可用颜色中扣除已分配的数量
        foreach (var kvp in colorCountMap)
        {
            int colorId = kvp.Key;
            int allocatedCount = kvp.Value;

            availableColors[colorId] -= allocatedCount;

            ////Debug.Log($"  确定性分配颜色{colorId}: {allocatedCount}个，剩余可用: {availableColors[colorId]}个");
        }

        ////Debug.Log($"  确定性分配结果: {string.Join(",", allocatedColors)}");

        return allocatedColors;
    }

    /// <summary>
    /// 获取所有可用的关卡索引（主关卡）
    /// </summary>
    public static List<int> GetAvailableLevels()
    {
        InitializeIfNeeded();
        return new List<int>(cachedLevels.Keys.Select(k => k.level).Distinct());
    }

    /// <summary>
    /// 获取所有可用的关卡信息（包含附加关卡）
    /// </summary>
    /// <returns>关卡信息列表，格式：LevelKey</returns>
    public static List<LevelKey> GetAvailableLevelDetails()
    {
        InitializeIfNeeded();
        return new List<LevelKey>(cachedLevels.Keys);
    }

    /// <summary>
    /// 获取总关卡数（只统计主关卡数量，不重复计算附加关卡）
    /// </summary>
    /// <returns>主关卡总数量</returns>
    public static int GetTotalLevelCount()
    {
        InitializeIfNeeded();

        // 只统计不同的主关卡号，不重复计算附加关卡
        var uniqueLevels = new HashSet<int>();
        foreach (var key in cachedLevels.Keys)
        {
            uniqueLevels.Add(key.level);
        }

        return uniqueLevels.Count;
    }

    /// <summary>
    /// 获取所有关卡变体的总数量（包括主关卡和所有附加关卡）
    /// </summary>
    /// <returns>所有关卡变体的总数量</returns>
    public static int GetTotalLevelVariantCount()
    {
        InitializeIfNeeded();
        return cachedLevels.Count;
    }

    /// <summary>
    /// 获取目标关卡的关卡总数（包括主关卡和所有附加关卡）
    /// </summary>
    /// <param name="targetLevel">目标关卡号</param>
    /// <returns>该关卡的总数量（主关卡+附加关卡）</returns>
    public static int GetTargetLevelCount(int targetLevel)
    {
        InitializeIfNeeded();

        // 统计指定关卡号的所有变体（主关卡和附加关卡）
        int count = 0;
        foreach (var key in cachedLevels.Keys)
        {
            if (key.level == targetLevel)
            {
                count++;
            }
        }

        return count;
    }

    /// <summary>
    /// 强制重新初始化（用于调试）
    /// </summary>
    public static void ForceReinitialize()
    {
        isInitialized = false;
        cachedLevels.Clear();
        levelEncryptionMap = null;
        InitializeIfNeeded();
    }

    /// <summary>
    /// 获取真实关卡组合对应的加密序号
    /// </summary>
    public static int GetEncryptedLevelId(int realLevelId, int levelExceed = 1)
    {
        InitializeLevelEncryptionMap();
        var key = new LevelKey(realLevelId, levelExceed);
        int result;
        if (levelEncryptionMap.TryGetValue(key, out result))
        {
            return result;
        }
        return realLevelId;
    }

    /// <summary>
    /// 获取加密序号对应的真实关卡组合
    /// </summary>
    public static LevelKey GetRealLevelId(int encryptedLevelId)
    {
        InitializeLevelEncryptionMap();

        foreach (var kvp in levelEncryptionMap)
        {
            if (kvp.Value == encryptedLevelId)
            {
                return kvp.Key;
            }
        }

        return new LevelKey(-1, -1);
    }

    private static void InitializeIfNeeded()
    {
        if (!isInitialized)
        {
            InitializeColorMap();
            ParseLevelText();
            isInitialized = true;
        }
    }

    private static void InitializeLevelEncryptionMap()
    {
        if (levelEncryptionMap != null) return;

        levelEncryptionMap = new Dictionary<LevelKey, int>();
        System.Random random = new System.Random(12345);

        // 创建更大的加密序号池，支持主关卡和附加关卡
        List<int> encryptedIds = new List<int>();
        for (int i = 1; i <= 2000; i++) // 增加到2000以支持更多组合
        {
            encryptedIds.Add(i);
        }

        // 打乱加密序号池
        for (int i = encryptedIds.Count - 1; i > 0; i--)
        {
            int randomIndex = random.Next(i + 1);
            int temp = encryptedIds[i];
            encryptedIds[i] = encryptedIds[randomIndex];
            encryptedIds[randomIndex] = temp;
        }

        // 建立映射：主关卡 + 附加关卡
        int encryptedIndex = 0;

        // 为每个主关卡分配映射（1-500，每个关卡最多支持10个附加关卡）
        for (int realLevel = 1; realLevel <= 500; realLevel++)
        {
            for (int levelExceed = 1; levelExceed <= 10; levelExceed++)
            {
                if (encryptedIndex < encryptedIds.Count)
                {
                    levelEncryptionMap[new LevelKey(realLevel, levelExceed)] = encryptedIds[encryptedIndex];
                    encryptedIndex++;
                }
            }
        }

        ////Debug.Log($"TileCube关卡加密映射表初始化完成，支持500关×10个附加关卡");
    }

    private static void InitializeColorMap()
    {
        colorNameToId.Clear();

        var constData = MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data;
        if (constData?.ColorIDMap != null)
        {
            foreach (var kvp in constData.ColorIDMap)
            {
                if (int.TryParse(kvp.Key, out int colorId))
                {
                    colorNameToId[kvp.Value] = colorId;
                    ////Debug.Log($"TileCube颜色映射: {kvp.Value} -> {colorId}");
                }
            }
        }

        // 默认映射
        if (colorNameToId.Count == 0)
        {
            ////Debug.LogWarning("TileCube_Const_Data.ColorIDMap为空，使用默认颜色映射");
            colorNameToId["红"] = 1;
            colorNameToId["黄"] = 2;
            colorNameToId["蓝"] = 3;
            colorNameToId["绿"] = 4;
            colorNameToId["紫"] = 5;
            colorNameToId["橙"] = 6;
        }

        ////Debug.Log($"TileCube初始化颜色映射完成，共{colorNameToId.Count}个颜色");
    }

    private static void ParseLevelText()
    {
        cachedLevels.Clear();

        var constData = MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data;
        if (constData == null || string.IsNullOrEmpty(constData.LevelText))
        {
            ////Debug.LogWarning("TileCube_Const_Data.LevelText为空，无法加载关卡数据");
            return;
        }

        string levelText = constData.LevelText;

        if (string.IsNullOrEmpty(levelText))
        {
            ////Debug.LogWarning("TileCube关卡文本为空");
            return;
        }

        string[] levels = levelText.Split(new char[] { '~' }, StringSplitOptions.RemoveEmptyEntries);

        foreach (string levelLine in levels)
        {
            if (string.IsNullOrEmpty(levelLine.Trim())) continue;

            try
            {
                TileCube_Data_Level level = ParseSingleLevel(levelLine.Trim());
                if (level != null)
                {
                    // 缓存时使用组合键
                    cachedLevels[new LevelKey(level.GetLevelId(), level.GetLevelExceed())] = level;
                }
            }
            catch (Exception e)
            {
                ////Debug.LogError($"解析TileCube关卡数据失败: {levelLine}\n错误: {e.Message}");
            }
        }

        ////Debug.Log($"成功加载 {cachedLevels.Count} 个TileCube关卡");
    }

    private static TileCube_Data_Level ParseSingleLevel(string levelLine)
    {
        string[] parts = levelLine.Split('|');
        if (parts.Length < 2)
        {
            ////Debug.LogError($"TileCube关卡数据格式错误: {levelLine}");
            return null;
        }

        // 解析关卡ID（支持Level10-2格式）
        string idPart = parts[0];
        int realLevelId;
        int levelExceed = 1; // 默认为主关卡

        // 检查是否包含附加关卡标识符（例如：10-2）
        if (idPart.Contains("-"))
        {
            string[] idSegments = idPart.Split('-');
            if (idSegments.Length == 2 &&
                int.TryParse(idSegments[0], out int mainLevel) &&
                int.TryParse(idSegments[1], out int exceedLevel))
            {
                realLevelId = mainLevel;
                levelExceed = exceedLevel;
                ////Debug.Log($"解析附加关卡: Level{realLevelId}-{levelExceed}");
            }
            else
            {
                ////Debug.LogError($"TileCube附加关卡ID格式错误: {idPart}");
                return null;
            }
        }
        else
        {
            // 传统的单一关卡ID处理
            if (!int.TryParse(idPart, out int encryptedLevelId))
            {
                ////Debug.LogError($"TileCube关卡ID格式错误: {idPart}");
                return null;
            }

            // 转换为真实关卡ID
            // 检查是否使用加密系统：如果LevelText中的ID是连续的1,2,3...则说明没有加密

            // 简单检测：如果encryptedLevelId <= 10，很可能是未加密的数据
            if (encryptedLevelId <= 10)
            {
                // 直接使用原始ID，不进行加密转换
                realLevelId = encryptedLevelId;
                ////Debug.Log($"检测到未加密关卡ID {encryptedLevelId}，直接使用");
            }
            else
            {
                // 使用加密映射转换
                var realLevelInfo = GetRealLevelId(encryptedLevelId);
                if (realLevelInfo.level == -1)
                {
                    // 没有找到加密映射，回退到使用原始值
                    realLevelId = encryptedLevelId;
                    levelExceed = 1;
                    ////Debug.Log($"关卡ID {encryptedLevelId} 加密映射失败，使用原始值");
                }
                else
                {
                    realLevelId = realLevelInfo.level;
                    levelExceed = realLevelInfo.levelExceed;
                    ////Debug.Log($"关卡ID {encryptedLevelId} 解密为真实关卡 {realLevelId}-{levelExceed}");
                }
            }
        }

        // 强制要求新格式：第二段必须以 E: 开头
        if (!(parts.Length >= 2 && parts[1].StartsWith("E:")))
        {
            return null;
        }

        string[] partsForParsing;
        try
        {
            string encodedPayload = parts[1].Substring(2);

            // XOR+Base64 解码（先Base64解码，再XOR）
            int seedXor = ComputeDeterministicHash(idPart + "#XOR");
            string payload = Base64AndXorDecode(encodedPayload, seedXor);

            // 数字域反混淆
            int seedNum = ComputeDeterministicHash(idPart + "#NUM");
            payload = ObfuscateDigits(payload, seedNum, false);

            // 拆成10行（当前顺序为行置乱后的 new[i]）
            var rows = payload.Split('|');
            if (rows.Length < 10)
            {
                return null;
            }

            // 行逆置乱：new[i] = old[perm[i]]  -> old[k] = new[invPerm[k]]
            int seedRow = ComputeDeterministicHash(idPart + "#ROW");
            int[] rowPerm = GeneratePermutation(10, seedRow);
            int[] invRow = InvertPermutation(rowPerm);
            var originalRows = new string[10];
            for (int k = 0; k < 10; k++)
            {
                originalRows[k] = rows[invRow[k]];
            }

            // 准备列逆置乱的置乱表（在 ParseLevelRow 中使用）
            int seedCol = ComputeDeterministicHash(idPart + "#COL");
            currentColumnPermutationForParsing = GeneratePermutation(10, seedCol);

            // 组装新的 partsForParsing：id + 10 行
            partsForParsing = new string[11];
            partsForParsing[0] = idPart;
            for (int idx = 0; idx < 10; idx++)
            {
                partsForParsing[idx + 1] = originalRows[idx];
            }
        }
        catch (Exception)
        {
            return null;
        }

        var generationParams = new GenerationParams
        {
            MaxHeight = 3,
            ConnectRate = 50,
            HeightConsistencyRate = 60,
            SpawnBoxColorRange = new System.Tuple<int, int>(6, 20),
            ColorPerBarGrid = 5
        };

        var level = new TileCube_Data_Level();

        for (int rowIndex = 1; rowIndex < Math.Min(partsForParsing.Length, 11); rowIndex++)
        {
            int zPos = rowIndex - 1; // Z坐标从0开始
            ParseLevelRow(level, partsForParsing[rowIndex], zPos, 1, 5);
        }

        SetLevelIdAndParams(level, realLevelId, levelExceed, generationParams);

        currentColumnPermutationForParsing = null;

        return level;
    }

    private static GenerationParams ParseGenerationParams(string paramString)
    {
        var parameters = new GenerationParams
        {
            MaxHeight = 3, // 这个值会在生成时根据前十行的最高高度动态设置
            ConnectRate = 50,
            HeightConsistencyRate = 60,
            SpawnBoxColorRange = new System.Tuple<int, int>(6, 20),
            ColorPerBarGrid = 5
        };

        // 解析格式: C50H70R6-20B5 (去掉了M参数)
        var matches = System.Text.RegularExpressions.Regex.Matches(paramString, @"([CHRBP])(\d+(?:-\d+)?)");

        foreach (System.Text.RegularExpressions.Match match in matches)
        {
            string type = match.Groups[1].Value;
            string value = match.Groups[2].Value;

            switch (type)
            {
                case "C":
                    parameters.ConnectRate = int.Parse(value);
                    break;
                case "H":
                    parameters.HeightConsistencyRate = int.Parse(value);
                    break;
                case "R":
                    if (value.Contains("-"))
                    {
                        var parts = value.Split('-');
                        int min = int.Parse(parts[0]);
                        int max = int.Parse(parts[1]);
                        // 确保是偶数
                        if (min % 2 != 0) min++;
                        if (max % 2 != 0) max--;
                        parameters.SpawnBoxColorRange = new System.Tuple<int, int>(min, max);
                    }
                    break;
                case "B":
                    parameters.ColorPerBarGrid = int.Parse(value);
                    break;
            }
        }

        return parameters;
    }

    private static void ParseLevelRow(TileCube_Data_Level level, string rowData, int zPos, int goldPigHitCount = 1, int barrierHitsPerGrid = 5)
    {
        // 先解压RLE压缩的数据
        string expandedRowData = ExpandRLECompression(rowData);

        string[] tokens = expandedRowData.Split(new char[] { ',' }, StringSplitOptions.None);

        // 若启用了高级混淆（读取到列置乱信息），对列进行逆置乱
        if (currentColumnPermutationForParsing != null && tokens.Length == 10)
        {
            int[] invCol = InvertPermutation(currentColumnPermutationForParsing);
            var reordered = new string[10];
            for (int i = 0; i < 10; i++)
            {
                // old[k] = new[inv[k]]
                reordered[i] = tokens[invCol[i]];
            }
            tokens = reordered;
        }

        for (int x = 0; x < tokens.Length; x++)
        {
            string token = tokens[x].Trim();
            if (string.IsNullOrEmpty(token) || token == "-") continue;

            ParseToken(level, token, x, zPos, goldPigHitCount, barrierHitsPerGrid);
        }
    }

    /// <summary>
    /// 展开RLE压缩的行数据（支持加密增强格式）
    /// </summary>
    private static string ExpandRLECompression(string rowData)
    {
        if (string.IsNullOrEmpty(rowData))
        {
            return rowData;
        }

        // 检查是否包含压缩数据（支持新旧两种格式）
        if (!rowData.Contains("*") && !rowData.Contains("X"))
        {
            return rowData; // 没有压缩数据，直接返回
        }

        string[] tokens = rowData.Split(',');
        var expandedTokens = new List<string>();

        foreach (string token in tokens)
        {
            if (token.Contains("X") && IsEncryptedRLEToken(token))
            {
                // 处理加密RLE格式: tokenXencodedCount
                var decryptResult = DecryptRLEToken(token);
                if (decryptResult.HasValue)
                {
                    string originalToken = decryptResult.Value.token;
                    int count = decryptResult.Value.count;

                    // 展开为多个相同的token
                    for (int i = 0; i < count; i++)
                    {
                        expandedTokens.Add(originalToken);
                    }

                    ////Debug.Log($"🔓 加密RLE解压: {token} -> {originalToken} x{count}");
                }
                else
                {
                    // 解密失败，保持原样
                    ////Debug.LogWarning($"⚠️ 加密RLE解析失败: {token}");
                    expandedTokens.Add(token);
                }
            }
            else if (token.Contains("*"))
            {
                // 处理旧的RLE格式: token*count (向后兼容)
                int starIndex = token.LastIndexOf('*');
                if (starIndex > 0 && starIndex < token.Length - 1)
                {
                    string originalToken = token.Substring(0, starIndex);
                    string countStr = token.Substring(starIndex + 1);

                    if (int.TryParse(countStr, out int count) && count > 0)
                    {
                        // 展开为多个相同的token
                        for (int i = 0; i < count; i++)
                        {
                            expandedTokens.Add(originalToken);
                        }

                        ////Debug.Log($"🔓 传统RLE解压: {token} -> {originalToken} x{count}");
                    }
                    else
                    {
                        // 解析失败，保持原样
                        ////Debug.LogWarning($"⚠️ 传统RLE解析失败: {token}");
                        expandedTokens.Add(token);
                    }
                }
                else
                {
                    // 格式错误，保持原样
                    expandedTokens.Add(token);
                }
            }
            else
            {
                // 非压缩token，直接添加
                expandedTokens.Add(token);
            }
        }

        string result = string.Join(",", expandedTokens);

        // 如果有解压缩发生，显示统计信息
        if (result.Length != rowData.Length)
        {
            ////Debug.Log($"📈 行解压效果: {rowData.Length} -> {result.Length} 字符");
        }

        return result;
    }

    /// <summary>
    /// 检查是否为加密RLE token
    /// 格式要求: 必须包含X，且X后面只有一个字符
    /// </summary>
    private static bool IsEncryptedRLEToken(string token)
    {
        int xIndex = token.LastIndexOf('X');
        if (xIndex <= 0 || xIndex != token.Length - 2)
        {
            return false; // X不在倒数第二位
        }

        // 检查X后面的字符是否为A-Z
        char encodedChar = token[token.Length - 1];
        return encodedChar >= 'A' && encodedChar <= 'Z';
    }

    /// <summary>
    /// 解密RLE token
    /// 格式: tokenXencodedCount -> (token, count)
    /// </summary>
    private static (string token, int count)? DecryptRLEToken(string encryptedToken)
    {
        int xIndex = encryptedToken.LastIndexOf('X');
        if (xIndex <= 0 || xIndex >= encryptedToken.Length - 1)
        {
            return null;
        }

        string originalToken = encryptedToken.Substring(0, xIndex);
        char encodedCount = encryptedToken[encryptedToken.Length - 1];

        int count = DecodeCharToNumber(encodedCount);
        if (count <= 0)
        {
            return null;
        }

        return (originalToken, count);
    }

    /// <summary>
    /// 将字符解码为数字
    /// A-Z: 1-26
    /// </summary>
    private static int DecodeCharToNumber(char encodedChar)
    {
        if (encodedChar >= 'A' && encodedChar <= 'Z')
        {
            return encodedChar - 'A' + 1;
        }
        else
        {
            ////Debug.LogWarning($"⚠️ 无效的编码字符: {encodedChar}");
            return -1;
        }
    }

    private static void ParseToken(TileCube_Data_Level level, string token, int x, int z, int goldPigHitCount = 1, int barrierHitsPerGrid = 5)
    {
        if (token.StartsWith("SB"))
        {
            // SpawnBox（新格式）
            int colorCount = int.Parse(token.Substring(2));
            var spawner = new TileCube_Data_Spawner
            {
                GridX = x,
                GridZ = z,
                SpawnColorSingleBlocks = GenerateSpawnColors(colorCount)
            };
            level.Spawners.Add(spawner);
        }
        else if (token.StartsWith("S") && char.IsDigit(token[1]))
        {
            // SpawnBox（旧格式兼容）
            int colorCount = int.Parse(token.Substring(1));
            var spawner = new TileCube_Data_Spawner
            {
                GridX = x,
                GridZ = z,
                SpawnColorSingleBlocks = GenerateSpawnColors(colorCount)
            };
            level.Spawners.Add(spawner);
        }
        else if (token.StartsWith("LG"))
        {
            // LargeBlock: LG + 颜色字符 + 数值
            char colorChar = token[2];
            int colorId = colorChar - 'A' + 1;
            int hitValues = int.Parse(token.Substring(3));

            ////Debug.Log($"🎯 解析大型块token: '{token}' 位置({x},{z}) -> 颜色ID={colorId}, 数值={hitValues}");

            var largeBlock = new TileCube_Data_LargeBlock
            {
                GridX = x,
                GridZ = z,
                ColorId = colorId,
                HitValues = hitValues
            };
            level.LargeBlocks.Add(largeBlock);

            ////Debug.Log($"✅ 大型块已添加: GridX={largeBlock.GridX}, GridZ={largeBlock.GridZ}, ColorId={largeBlock.ColorId}, HitValues={largeBlock.HitValues}");
        }
        else if (token.StartsWith("PH"))
        {
            // 阻挡条：PH + 颜色字符 + 方向 + 长度
            char colorChar = token[2];
            int colorId = colorChar - 'A' + 1;
            char direction = token[3];
            int length = int.Parse(token.Substring(4));

            int directionCode;
            switch (direction)
            {
                case 'R':
                    directionCode = 1; // 右
                    break;
                case 'L':
                    directionCode = 3; // 左
                    break;
                case 'U':
                    directionCode = 0; // 上
                    break;
                case 'D':
                    directionCode = 2; // 下
                    break;
                default:
                    directionCode = 1;
                    break;
            }

            var barrier = new TileCube_Data_Barrier
            {
                GridX = x,
                GridZ = z,
                Direction = directionCode,
                GridCount = length,
                ColorId = colorId,
                HitsPerGrid = barrierHitsPerGrid // 使用传入的每格击打次数参数
            };
            level.Barriers.Add(barrier);
        }
        else if (token.StartsWith("GP"))
        {
            // 金猪：GP + 可选的堆叠高度数字
            int stackHeight = token.Length > 2 ? int.Parse(token.Substring(2)) : 1;

            // 为每个高度层创建一个金猪
            for (int h = 0; h < stackHeight; h++)
            {
                var goldPig = new TileCube_Data_GoldPig
                {
                    GridX = x,
                    GridZ = z,
                    Height = h, // 从0开始的高度层
                    HitValues = goldPigHitCount // 使用传入的金猪击打次数参数
                };
                level.GoldPigs.Add(goldPig);
            }
        }
        else if (token == "KE")
        {
            // Key
            var key = new TileCube_Data_Key
            {
                GridX = x,
                GridZ = z,
                Height = 0, // 钥匙在第1层（高度索引0）
                KeyId = 1 // 默认钥匙ID
            };
            level.Keys.Add(key);
        }
        else if (char.IsLetter(token[0]))
        {
            // SingleBlock: 颜色字符 + 高度数字
            char colorChar = token[0];
            int colorId = colorChar - 'A' + 1;
            int stackHeight = token.Length > 1 ? int.Parse(token.Substring(1)) : 1;

            // 为每个高度层创建一个SingleBlock
            for (int h = 0; h < stackHeight; h++)
            {
                var singleBlock = new TileCube_Data_SingleBlock
                {
                    GridX = x,
                    GridZ = z,
                    Height = h, // 从0开始的高度层索引
                    ColorId = colorId
                };
                level.SingleBlocks.Add(singleBlock);
            }
        }
    }

    private static List<int> GenerateSpawnColors(int totalColors)
    {
        // 简单实现：平均分配到几种颜色
        var spawnColors = new List<int>();
        int colorTypes = Math.Min(4, totalColors / 2); // 最多4种颜色
        int colorsPerType = totalColors / colorTypes;

        for (int i = 1; i <= colorTypes; i++)
        {
            for (int j = 0; j < colorsPerType; j++)
            {
                spawnColors.Add(i);
            }
        }

        return spawnColors;
    }

    private static void GenerateAdditionalRows(TileCube_Data_Level level, Dictionary<int, int> totalColorDict)
    {
        // 获取关卡的生成参数（包含colorPerBarGrid和spawnBoxColorRange）
        var generationParams = GetLevelGenerationParams(level);

        // 调用重载版本
        GenerateAdditionalRows(level, totalColorDict, generationParams);
    }

    /// <summary>
    /// 生成额外行，使用自定义生成参数
    /// </summary>
    private static void GenerateAdditionalRows(TileCube_Data_Level level, Dictionary<int, int> totalColorDict, GenerationParams generationParams)
    {
        // 计算前10行已使用的颜色（不包括SpawnBox，因为要重新分配）
        var usedColors = CalculateUsedColorsExcludingSpawners(level, generationParams.ColorPerBarGrid);

        // 计算可分配颜色（总需求 - 前10行已使用）
        var availableColors = new Dictionary<int, int>();
        ////Debug.Log($"🧮 计算可分配颜色:");
        foreach (var kvp in totalColorDict)
        {
            int used = usedColors.ContainsKey(kvp.Key) ? usedColors[kvp.Key] : 0;
            int available = kvp.Value - used;
            ////Debug.Log($"  颜色{kvp.Key}: 总需求{kvp.Value} - 已使用{used} = 可分配{available}");
            if (available > 0)
            {
                availableColors[kvp.Key] = available;
            }
        }

        // 优先为SpawnBox分配颜色
        AllocateColorsToSpawnBoxes(level, availableColors, generationParams.SpawnBoxColorRange);

        // availableColors 已经在分配过程中被自动扣除了SpawnBox使用的颜色
        ////Debug.Log($"🎲 SpawnBox分配完成后，剩余颜色用于生成SingleBlock: {string.Join(", ", availableColors.Select(kv => $"颜色{kv.Key}={kv.Value}个"))}");

        // 生成第11行及以后的SingleBlock（使用剩余颜色）
        GenerateAdditionalSingleBlocks(level, availableColors, generationParams);
    }

    private static Dictionary<int, int> CalculateUsedColors(TileCube_Data_Level level, int colorPerBarGrid, (int, int) spawnBoxColorRange)
    {
        var usedColors = new Dictionary<int, int>();

        ////Debug.Log($"📊 开始计算前10行已使用颜色...");

        // SingleBlock颜色消耗 - 每个SingleBlock消耗1个颜色
        foreach (var block in level.SingleBlocks)
        {
            int currentCount = usedColors.ContainsKey(block.ColorId) ? usedColors[block.ColorId] : 0;
            usedColors[block.ColorId] = currentCount + 1;
        }

        ////Debug.Log($"SingleBlock统计: 总共{level.SingleBlocks.Count}个块");
        foreach (var kv in usedColors.OrderBy(x => x.Key))
        {
            ////Debug.Log($"  颜色{kv.Key}: {kv.Value}个SingleBlock");
        }

        // PhysicBar颜色消耗
        foreach (var barrier in level.Barriers)
        {
            int barColorCost = barrier.GridCount * colorPerBarGrid;
            int currentCount = usedColors.ContainsKey(barrier.ColorId) ? usedColors[barrier.ColorId] : 0;
            usedColors[barrier.ColorId] = currentCount + barColorCost;
        }

        // LargeBlock颜色消耗
        foreach (var largeBlock in level.LargeBlocks)
        {
            int currentCount = usedColors.ContainsKey(largeBlock.ColorId) ? usedColors[largeBlock.ColorId] : 0;
            usedColors[largeBlock.ColorId] = currentCount + largeBlock.HitValues;
        }

        // SpawnBox颜色消耗（确定性分配）
        int levelSeed = level.GetLevelId() * 1000 + level.GetLevelExceed();
        int spawnerIndex = 0;
        foreach (var spawner in level.Spawners)
        {
            // 使用确定性算法生成颜色数量：基于关卡ID、附加关卡序号和SpawnBox位置
            int positionSeed = levelSeed + spawner.GridX * 100 + spawner.GridZ * 10 + spawnerIndex;
            int spawnColors = DeterministicValueInRange(positionSeed, spawnBoxColorRange.Item1, spawnBoxColorRange.Item2);

            // 确保是偶数
            if (spawnColors % 2 != 0) spawnColors++;

            // 简单分配给前几种颜色
            int colorsPerType = spawnColors / Math.Min(4, spawnColors / 2);
            for (int colorId = 1; colorId <= 4 && spawnColors > 0; colorId++)
            {
                int assignedColors = Math.Min(colorsPerType, spawnColors);
                int currentCount = usedColors.ContainsKey(colorId) ? usedColors[colorId] : 0;
                usedColors[colorId] = currentCount + assignedColors;
                spawnColors -= assignedColors;
            }

            spawnerIndex++;
        }

        return usedColors;
    }

    private static void GenerateAdditionalSingleBlocks(TileCube_Data_Level level, Dictionary<int, int> remainingColors, GenerationParams generationParams)
    {
        ////Debug.Log($"🎲 开始生成额外单块，剩余颜色: {string.Join(", ", remainingColors.Select(kv => $"颜色{kv.Key}={kv.Value}个"))}");

        int levelSeed = level.GetLevelId() * 1000 + level.GetLevelExceed();

        // 分析前10行的完整布局模式（包括颜色分布和高度模式）
        var frontRowsLayoutPattern = AnalyzeFrontRowsLayoutPattern(level);

        // 统计前10行的高度分布，用于参考生成相似的高度样式
        var heightDistribution = AnalyzeFrontRowsHeightPattern(level);

        // 计算总剩余颜色数量
        int totalRemainingColors = remainingColors.Values.Sum();

        ////Debug.Log($"📊 剩余颜色总数: {totalRemainingColors}");

        // 动态生成行数，直到剩余颜色用完
        int z = 10; // 从第11行开始生成
        int maxRowsToGenerate = 999; // 防止无限循环

        while (remainingColors.Values.Sum() > 0 && z < (10 + maxRowsToGenerate))
        {
            // 检查是否还有剩余颜色
            if (remainingColors.Count == 0)
            {
                Debug.Log($"✅ 所有剩余颜色已用完，在第{z}行停止生成");
                break;
            }

            for (int x = 0; x < 10; x++)
            {
                if (remainingColors.Count == 0) break;

                // 根据前10行的布局模式选择颜色（不再使用连片率）
                int selectedColorId = SelectColorBasedOnLayoutPattern(frontRowsLayoutPattern, x, z, remainingColors, levelSeed + x + z);

                // 根据前十行的高度分布选择堆叠高度，优先保证颜色能够完全消耗
                int selectedStackHeight = SelectStackHeightFromPattern(x, z, heightDistribution, remainingColors, selectedColorId, levelSeed + x + z);

                // 确保有足够的颜色生成该高度的堆叠
                int availableColorCount = remainingColors.ContainsKey(selectedColorId) ? remainingColors[selectedColorId] : 0;
                if (availableColorCount >= selectedStackHeight && selectedStackHeight > 0)
                {
                    // 为每个高度层创建一个SingleBlock
                    for (int h = 0; h < selectedStackHeight; h++)
                    {
                        var newBlock = new TileCube_Data_SingleBlock
                        {
                            GridX = x,
                            GridZ = z,
                            Height = h, // 从0开始的高度层索引
                            ColorId = selectedColorId
                        };
                        level.SingleBlocks.Add(newBlock);
                    }

                    // 更新剩余颜色
                    remainingColors[selectedColorId] -= selectedStackHeight;
                    int remainingCount = remainingColors.ContainsKey(selectedColorId) ? remainingColors[selectedColorId] : 0;
                    ////Debug.Log($"  位置({x},{z}) 生成颜色{selectedColorId} {selectedStackHeight}层，剩余: {remainingCount}");

                    if (remainingColors[selectedColorId] <= 0)
                    {
                        remainingColors.Remove(selectedColorId);
                        ////Debug.Log($"  颜色{selectedColorId}已用完，从候选列表移除");
                    }
                }
                else
                {
                    // 如果选择的颜色数量不足，尝试用该颜色的剩余数量生成更低的堆叠
                    int availableCount = remainingColors.ContainsKey(selectedColorId) ? remainingColors[selectedColorId] : 0;
                    if (availableCount > 0)
                    {
                        // 生成能生成的最大高度（不超过剩余颜色数）
                        int actualStackHeight = Math.Min(availableCount, Math.Max(1, selectedStackHeight / 2)); // 至少生成1层

                        for (int h = 0; h < actualStackHeight; h++)
                        {
                            var newBlock = new TileCube_Data_SingleBlock
                            {
                                GridX = x,
                                GridZ = z,
                                Height = h,
                                ColorId = selectedColorId
                            };
                            level.SingleBlocks.Add(newBlock);
                        }

                        remainingColors[selectedColorId] -= actualStackHeight;
                        if (remainingColors[selectedColorId] <= 0)
                        {
                            remainingColors.Remove(selectedColorId);
                        }
                    }
                }
            }

            z++; // 移动到下一行
        }

        // 最后清理：如果还有剩余颜色，强制生成单层块来消耗完
        if (remainingColors.Values.Sum() > 0)
        {
            ////Debug.Log($"⚠️ 强制清理剩余颜色: {string.Join(", ", remainingColors.Select(kv => $"颜色{kv.Key}={kv.Value}个"))}");
            ForceConsumeRemainingColors(level, remainingColors, z);
        }

        // 生成完成后的统计信息
        int finalRemainingColors = remainingColors.Values.Sum();
        ////Debug.Log($"🎯 额外单块生成完成！剩余颜色数量: {finalRemainingColors}");
        if (finalRemainingColors > 0)
        {
            ////Debug.Log($"⚠️ 仍有剩余颜色未用完: {string.Join(", ", remainingColors.Select(kv => $"颜色{kv.Key}={kv.Value}个"))}");
        }
    }

    private static int SelectColorBasedOnConnectivity(TileCube_Data_Level level, int x, int z, Dictionary<int, int> availableColors, int connectRate, int seed)
    {
        // 检查相邻位置的颜色
        var adjacentColors = new List<int>();

        // 检查左、上、右、下四个方向
        int[] dx = { -1, 0, 1, 0 };
        int[] dz = { 0, -1, 0, 1 };

        for (int i = 0; i < 4; i++)
        {
            int adjX = x + dx[i];
            int adjZ = z + dz[i];

            var adjacentBlock = level.SingleBlocks.FirstOrDefault(b => b.GridX == adjX && b.GridZ == adjZ);
            if (adjacentBlock != null && availableColors.ContainsKey(adjacentBlock.ColorId))
            {
                adjacentColors.Add(adjacentBlock.ColorId);
            }
        }

        // 根据连片率决定是否使用相邻颜色（使用确定性算法）
        if (adjacentColors.Count > 0 && DeterministicValueInRange(seed, 0, 99) < connectRate)
        {
            // 选择相邻颜色中最常见的
            var mostCommonColor = adjacentColors.GroupBy(c => c).OrderByDescending(g => g.Count()).First().Key;
            return mostCommonColor;
        }

        // 确定性选择可用颜色
        var colorIds = availableColors.Keys.OrderBy(c => c).ToList(); // 按颜色ID排序确保顺序固定
        int colorIndex = DeterministicValueInRange(seed + 100, 0, colorIds.Count - 1);
        return colorIds[colorIndex];
    }

    /// <summary>
    /// 分析前10行的高度分布模式，生成高度分布统计
    /// </summary>
    private static Dictionary<int, int> AnalyzeFrontRowsHeightPattern(TileCube_Data_Level level)
    {
        var heightDistribution = new Dictionary<int, int>();

        // 统计前10行（Z = 0-9）中每个位置的堆叠层数
        for (int z = 0; z < 10; z++)
        {
            for (int x = 0; x < 10; x++)
            {
                var blocksAtPosition = level.SingleBlocks.Where(b => b.GridX == x && b.GridZ == z).ToList();
                int stackHeight = blocksAtPosition.Count;

                if (stackHeight > 0)
                {
                    int currentCount = heightDistribution.ContainsKey(stackHeight) ? heightDistribution[stackHeight] : 0;
                    heightDistribution[stackHeight] = currentCount + 1;
                }
            }
        }

        // 如果没有找到任何堆叠，默认为1层高度
        if (heightDistribution.Count == 0)
        {
            heightDistribution[1] = 100; // 100%的概率选择1层
        }

        ////Debug.Log($"📊 前10行高度分布: {string.Join(", ", heightDistribution.Select(kv => $"{kv.Key}层:{kv.Value}次"))}");

        return heightDistribution;
    }

    /// <summary>
    /// 分析前10行的完整布局模式（包括颜色分布和位置模式）
    /// </summary>
    private static FrontRowsLayoutPattern AnalyzeFrontRowsLayoutPattern(TileCube_Data_Level level)
    {
        var layoutPattern = new FrontRowsLayoutPattern();

        // 统计前10行（Z = 0-9）中每个位置的颜色分布
        for (int z = 0; z < 10; z++)
        {
            for (int x = 0; x < 10; x++)
            {
                var blocksAtPosition = level.SingleBlocks.Where(b => b.GridX == x && b.GridZ == z).ToList();
                if (blocksAtPosition.Count > 0)
                {
                    int colorId = blocksAtPosition.First().ColorId;
                    int stackHeight = blocksAtPosition.Count;

                    // 记录颜色出现频率
                    int currentFreq = layoutPattern.ColorFrequency.ContainsKey(colorId) ? layoutPattern.ColorFrequency[colorId] : 0;
                    layoutPattern.ColorFrequency[colorId] = currentFreq + 1;

                    // 记录该位置的颜色和高度信息
                    layoutPattern.PositionData[x, z] = new PositionInfo { ColorId = colorId, StackHeight = stackHeight };

                    // 统计每种颜色的平均高度
                    if (!layoutPattern.ColorAverageHeight.ContainsKey(colorId))
                    {
                        layoutPattern.ColorAverageHeight[colorId] = new List<int>();
                    }
                    layoutPattern.ColorAverageHeight[colorId].Add(stackHeight);
                }
                else
                {
                    // 空位置
                    layoutPattern.PositionData[x, z] = new PositionInfo { ColorId = 0, StackHeight = 0 };
                }
            }
        }

        // 计算每种颜色的平均高度
        foreach (var kvp in layoutPattern.ColorAverageHeight.ToList())
        {
            layoutPattern.ColorAverageHeight[kvp.Key] = new List<int> { (int)kvp.Value.Average() };
        }

        ////Debug.Log($"📊 前10行布局模式分析完成: {layoutPattern.ColorFrequency.Count}种颜色");

        return layoutPattern;
    }

    /// <summary>
    /// 根据前十行的布局模式选择颜色（分析相邻行的模式和颜色分布）
    /// </summary>
    private static int SelectColorBasedOnLayoutPattern(FrontRowsLayoutPattern frontRowsLayoutPattern, int x, int z, Dictionary<int, int> remainingColors, int seed)
    {
        // 寻找前10行中对应位置的颜色模式
        int patternZ = (z - 10) % 10; // 将Z坐标映射到前10行的模式中（循环模式）
        var referencePositionInfo = frontRowsLayoutPattern.PositionData[x, patternZ];

        // 如果参考位置有颜色且该颜色有剩余库存，优先使用该颜色
        if (referencePositionInfo.ColorId > 0 && remainingColors.ContainsKey(referencePositionInfo.ColorId) && remainingColors[referencePositionInfo.ColorId] > 0)
        {
            return referencePositionInfo.ColorId;
        }

        // 查找附近位置的颜色模式
        var nearbyColors = new List<int>();
        int[] dx = { -1, 0, 1, 0, -1, 1, -1, 1 }; // 包括对角线的8个方向
        int[] dz = { 0, -1, 0, 1, -1, -1, 1, 1 };

        for (int i = 0; i < dx.Length; i++)
        {
            int nearX = x + dx[i];
            int nearZ = patternZ + dz[i];

            if (nearX >= 0 && nearX < 10 && nearZ >= 0 && nearZ < 10)
            {
                var nearbyInfo = frontRowsLayoutPattern.PositionData[nearX, nearZ];
                if (nearbyInfo.ColorId > 0 && remainingColors.ContainsKey(nearbyInfo.ColorId) && remainingColors[nearbyInfo.ColorId] > 0)
                {
                    nearbyColors.Add(nearbyInfo.ColorId);
                }
            }
        }

        // 如果找到附近颜色，确定性选择一个
        if (nearbyColors.Count > 0)
        {
            var colorGroups = nearbyColors.GroupBy(c => c).OrderByDescending(g => g.Count()).ThenBy(g => g.Key);
            return colorGroups.First().Key;
        }

        // 根据前10行的颜色频率确定性选择颜色
        var availableColorsByFrequency = frontRowsLayoutPattern.ColorFrequency
            .Where(kv => remainingColors.ContainsKey(kv.Key) && remainingColors[kv.Key] > 0)
            .OrderByDescending(kv => kv.Value) // 按出现频率降序
            .ThenBy(kv => kv.Key) // 颜色ID升序作为次要排序
            .ToList();

        if (availableColorsByFrequency.Count > 0)
        {
            // 确定性选择：根据seed和位置选择
            int index = DeterministicValueInRange(seed, 0, availableColorsByFrequency.Count - 1);
            return availableColorsByFrequency[index].Key;
        }

        // 最后备选：选择任意剩余颜色
        var colorIds = remainingColors.Keys.OrderBy(c => c).ToList();
        if (colorIds.Count > 0)
        {
            int colorIndex = DeterministicValueInRange(seed + 100, 0, colorIds.Count - 1);
            return colorIds[colorIndex];
        }

        return 1; // 默认颜色
    }

    /// <summary>
    /// 强制消耗剩余颜色，生成单层块
    /// </summary>
    private static void ForceConsumeRemainingColors(TileCube_Data_Level level, Dictionary<int, int> remainingColors, int startZ)
    {
        int x = 0;
        int z = startZ;

        // 将所有剩余颜色按颜色ID排序，确保确定性
        var sortedRemainingColors = remainingColors.OrderBy(kv => kv.Key).ToList();

        foreach (var colorKv in sortedRemainingColors)
        {
            int colorId = colorKv.Key;
            int count = colorKv.Value;

            for (int i = 0; i < count; i++)
            {
                // 如果当前行已满，移动到下一行
                if (x >= 10)
                {
                    x = 0;
                    z++;
                }

                // 生成单层块
                var newBlock = new TileCube_Data_SingleBlock
                {
                    GridX = x,
                    GridZ = z,
                    Height = 0, // 只生成底层
                    ColorId = colorId
                };
                level.SingleBlocks.Add(newBlock);

                x++;
            }
        }

        // 清空剩余颜色
        remainingColors.Clear();
        ////Debug.Log($"🧹 强制清理完成，最终行数: {z + 1}");
    }

    private static int SelectStackHeightBasedOnConsistency(TileCube_Data_Level level, int x, int z, int maxStackHeight, int consistencyRate, int seed)
    {
        // 检查相邻位置的堆叠高度
        var adjacentStackHeights = new List<int>();

        int[] dx = { -1, 0, 1, 0 };
        int[] dz = { 0, -1, 0, 1 };

        for (int i = 0; i < 4; i++)
        {
            int adjX = x + dx[i];
            int adjZ = z + dz[i];

            // 统计该位置的堆叠层数
            var blocksAtPosition = level.SingleBlocks.Where(b => b.GridX == adjX && b.GridZ == adjZ).ToList();
            if (blocksAtPosition.Count > 0)
            {
                adjacentStackHeights.Add(blocksAtPosition.Count);
            }
        }

        // 根据一致率决定是否使用相邻堆叠高度（使用确定性算法）
        if (adjacentStackHeights.Count > 0 && DeterministicValueInRange(seed, 0, 99) < consistencyRate)
        {
            int heightIndex = DeterministicValueInRange(seed + 200, 0, adjacentStackHeights.Count - 1);
            return adjacentStackHeights[heightIndex];
        }

        // 确定性选择堆叠高度
        return DeterministicValueInRange(seed + 300, 1, maxStackHeight);
    }

    // 辅助方法和数据结构
    private static void SetLevelIdAndParams(TileCube_Data_Level level, int levelId, int levelExceed, GenerationParams parameters)
    {
        level.SetLevelId(levelId);
        level.SetLevelExceed(levelExceed);
        level.SetGenerationParams(parameters);
    }

    private static GenerationParams GetLevelGenerationParams(TileCube_Data_Level level)
    {
        return level.GetGenerationParams();
    }

    /// <summary>
    /// 根据前十行的高度分布模式选择堆叠高度，优先保证颜色能够完全消耗
    /// </summary>
    private static int SelectStackHeightFromPattern(int x, int z, Dictionary<int, int> heightDistribution, Dictionary<int, int> remainingColors, int selectedColorId, int seed)
    {
        // 获取该颜色的剩余数量
        int availableCount = remainingColors.ContainsKey(selectedColorId) ? remainingColors[selectedColorId] : 0;
        if (availableCount <= 0) return 0;

        // 根据高度分布的权重来选择高度（确定性算法）
        var sortedHeights = heightDistribution.OrderBy(kv => kv.Key).ToList();

        // 计算总权重
        int totalWeight = heightDistribution.Values.Sum();

        // 确定性选择高度
        int randomValue = DeterministicValueInRange(seed, 0, totalWeight - 1);
        int currentWeight = 0;

        foreach (var heightKv in sortedHeights)
        {
            currentWeight += heightKv.Value;
            if (randomValue < currentWeight)
            {
                // 确保选择的高度不超过剩余颜色数量
                int selectedHeight = Math.Min(heightKv.Key, availableCount);
                return Math.Max(1, selectedHeight); // 至少返回1层
            }
        }

        // 备选：返回最小高度但不超过剩余颜色
        int minHeight = sortedHeights.Count > 0 ? sortedHeights.First().Key : 1;
        return Math.Min(minHeight, availableCount);
    }

    // 生成参数数据结构
    public class GenerationParams
    {
        public int MaxHeight { get; set; }
        public int ConnectRate { get; set; }
        public int HeightConsistencyRate { get; set; }
        public System.Tuple<int, int> SpawnBoxColorRange { get; set; }
        public int ColorPerBarGrid { get; set; }
        public int GoldPigHitCount { get; set; }
    }

    // ===== 高级混淆读取端辅助方法 =====

    private static int ComputeDeterministicHash(string input)
    {
        unchecked
        {
            const uint fnvOffset = 2166136261;
            const uint fnvPrime = 16777619;
            uint hash = fnvOffset;
            foreach (char c in input)
            {
                hash ^= c;
                hash *= fnvPrime;
            }
            return (int)(hash & 0x7FFFFFFF);
        }
    }

    private static int[] GeneratePermutation(int length, int seed)
    {
        var perm = Enumerable.Range(0, length).ToArray();
        uint state = (uint)seed;
        for (int i = length - 1; i > 0; i--)
        {
            state = state * 1664525u + 1013904223u;
            int j = (int)(state % (uint)(i + 1));
            int tmp = perm[i];
            perm[i] = perm[j];
            perm[j] = tmp;
        }
        return perm;
    }

    private static int[] InvertPermutation(int[] perm)
    {
        var inv = new int[perm.Length];
        for (int i = 0; i < perm.Length; i++)
        {
            inv[perm[i]] = i;
        }
        return inv;
    }

    private static string Base64AndXorDecode(string encoded, int seed)
    {
        var bytes = Convert.FromBase64String(encoded);
        var key = GenerateXorKey(bytes.Length, seed);
        for (int i = 0; i < bytes.Length; i++) bytes[i] ^= key[i];
        return Encoding.UTF8.GetString(bytes);
    }

    private static byte[] GenerateXorKey(int length, int seed)
    {
        var key = new byte[length];
        uint state = (uint)seed;
        for (int i = 0; i < length; i++)
        {
            state = state * 1664525u + 1013904223u;
            key[i] = (byte)((state >> 16) & 0xFF);
        }
        return key;
    }

    private static string ObfuscateDigits(string text, int seed, bool encode)
    {
        var map = BuildDigitMap(seed, encode);
        var sb = new StringBuilder(text.Length);
        foreach (char c in text)
        {
            if (c >= '0' && c <= '9')
            {
                sb.Append(map[c - '0']);
            }
            else
            {
                sb.Append(c);
            }
        }
        return sb.ToString();
    }

    private static char[] BuildDigitMap(int seed, bool encode)
    {
        var digits = Enumerable.Range(0, 10).ToArray();
        uint state = (uint)seed;
        for (int i = digits.Length - 1; i > 0; i--)
        {
            state = state * 1664525u + 1013904223u;
            int j = (int)(state % (uint)(i + 1));
            int tmp = digits[i];
            digits[i] = digits[j];
            digits[j] = tmp;
        }
        var map = new char[10];
        if (encode)
        {
            for (int d = 0; d < 10; d++) map[d] = (char)('0' + digits[d]);
        }
        else
        {
            var inv = new int[10];
            for (int d = 0; d < 10; d++) inv[digits[d]] = d;
            for (int d = 0; d < 10; d++) map[d] = (char)('0' + inv[d]);
        }
        return map;
    }
}

// 扩展方法
public static class TileCube_Data_Level_Extensions
{
    private static Dictionary<TileCube_Data_Level, int> levelIds = new Dictionary<TileCube_Data_Level, int>();
    private static Dictionary<TileCube_Data_Level, int> levelExceeds = new Dictionary<TileCube_Data_Level, int>();
    private static Dictionary<TileCube_Data_Level, TileCube_LevelConverter.GenerationParams> levelParams = new Dictionary<TileCube_Data_Level, TileCube_LevelConverter.GenerationParams>();

    public static int GetLevelId(this TileCube_Data_Level level)
    {
        return levelIds.ContainsKey(level) ? levelIds[level] : 1;
    }

    public static void SetLevelId(this TileCube_Data_Level level, int levelId)
    {
        levelIds[level] = levelId;
    }

    public static int GetLevelExceed(this TileCube_Data_Level level)
    {
        return levelExceeds.ContainsKey(level) ? levelExceeds[level] : 1;
    }

    public static void SetLevelExceed(this TileCube_Data_Level level, int levelExceed)
    {
        levelExceeds[level] = levelExceed;
    }

    public static TileCube_LevelConverter.GenerationParams GetGenerationParams(this TileCube_Data_Level level)
    {
        return levelParams.ContainsKey(level) ? levelParams[level] : new TileCube_LevelConverter.GenerationParams();
    }

    public static void SetGenerationParams(this TileCube_Data_Level level, TileCube_LevelConverter.GenerationParams parameters)
    {
        levelParams[level] = parameters;
    }
}

// 位置信息数据结构
public class PositionInfo
{
    public int ColorId { get; set; }
    public int StackHeight { get; set; }
}

// 前10行布局模式数据结构
public class FrontRowsLayoutPattern
{
    public Dictionary<int, int> ColorFrequency { get; set; } = new Dictionary<int, int>();
    public PositionInfo[,] PositionData { get; set; } = new PositionInfo[10, 10];
    public Dictionary<int, List<int>> ColorAverageHeight { get; set; } = new Dictionary<int, List<int>>();
}