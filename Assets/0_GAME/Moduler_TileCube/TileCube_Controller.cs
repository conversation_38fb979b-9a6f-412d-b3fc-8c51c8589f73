using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 瓦片方块控制器 - 管理各种类型的方块物体
/// 参考CupQueue架构模式，集成Tool_TileGrid网格系统
/// </summary>
public class TileCube_Controller : MonoBehaviour
{
    // 关卡创建完成事件
    public static event Action OnLevelCreationCompleted;

    private static TileCube_Controller _instance;
    private static bool _isQuitting = false; // 标记是否正在退出，防止在销毁时重新创建实例

    public static TileCube_Controller Instance
    {
        get
        {
            // 如果正在退出或销毁，不创建新实例
            if (_isQuitting || _instance == null)
            {
                if (_isQuitting) return null;

                _instance = FindObjectOfType<TileCube_Controller>();
                if (_instance == null && !_isQuitting)
                {
                    GameObject go = new GameObject(typeof(TileCube_Controller).Name);
                    _instance = go.AddComponent<TileCube_Controller>();
                }
            }
            return _instance;
        }
    }

    public Tool_TileGrid TileGrid
    {
        get
        {
            if (tileGrid == null)
            {
                tileGrid = FindObjectOfType<Tool_TileGrid>();
            }

            // 确保网格起始点始终为TileCube_Parent的位置
            if (tileGrid != null)
            {
                tileGrid.gridOrigin = TileCube_Parent.transform.position;
            }

            return tileGrid;
        }
    }

    public Tool_TileGrid tileGrid;

    // [Header("父级对象")]
    public GameObject TileCube_Parent
    {
        get
        {
            if (tileCube_Parent == null)
            {
                tileCube_Parent = GameObject.Find("TileCube_Parent");
                if (tileCube_Parent == null)
                {
                    tileCube_Parent = new GameObject("TileCube_Parent");
                }
                tileCube_Parent.transform.localScale = MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data.GetParentScale();
            }
            return tileCube_Parent;
        }
    }

    GameObject tileCube_Parent;

    #region 高度压缩配置

    [Header("SingleBlock高度压缩配置")]
    [Tooltip("Z坐标阈值，超过此值时触发压缩")]
    public int HeightProp_ClearColorGrid_compressionZThreshold = 8;

    [Tooltip("开始压缩的高度层")]
    public int HeightProp_ClearColorGrid_compressionHeightA = 2;

    [Tooltip("压缩后的Y缩放值")]
    [Range(0.1f, 0.9f)]
    public float HeightProp_ClearColorGrid_compressedScale = 0.5f;

    [Tooltip("最后一层恢复时的弹跳缩放值")]
    [Range(0.05f, 0.3f)]
    public float HeightProp_ClearColorGrid_bounceScale = 0.1f;

    [Tooltip("压缩/恢复动画持续时间")]
    [Range(0.1f, 1f)]
    public float HeightProp_ClearColorGrid_compressionAnimationDuration = 0.3f;

    /// <summary>
    /// 开始隐藏的高度层（自动计算为compressionHeightA + 1）
    /// </summary>
    public int HeightProp_ClearColorGrid_compressionHeightB => HeightProp_ClearColorGrid_compressionHeightA + 1;

    [Header("GoldPig高度压缩配置")]
    [Tooltip("GoldPig Z坐标阈值，超过此值时触发压缩")]
    public int HeightProp_ClearColorGrid_goldPigCompressionZThreshold = 6;

    [Tooltip("GoldPig开始压缩的高度层")]
    public int HeightProp_ClearColorGrid_goldPigCompressionHeightA = 1;

    [Tooltip("GoldPig压缩后的Y缩放值")]
    [Range(0.1f, 0.9f)]
    public float HeightProp_ClearColorGrid_goldPigCompressedScale = 0.3f;

    [Tooltip("GoldPig最后一层恢复时的弹跳缩放值")]
    [Range(0.05f, 0.3f)]
    public float HeightProp_ClearColorGrid_goldPigBounceScale = 0.15f;

    [Tooltip("GoldPig压缩/恢复动画持续时间")]
    [Range(0.1f, 1f)]
    public float HeightProp_ClearColorGrid_goldPigCompressionAnimationDuration = 0.4f;

    /// <summary>
    /// GoldPig开始隐藏的高度层（自动计算为goldPigCompressionHeightA + 1）
    /// </summary>
    public int HeightProp_ClearColorGrid_goldPigCompressionHeightB => HeightProp_ClearColorGrid_goldPigCompressionHeightA + 1;

    #endregion

    #region 清除颜色道具配置

    [Header("清除颜色道具配置")]
    [Tooltip("可消除的网格Z行数（从网格Z=0开始，包含指定行数）")]
    public int Prop_ClearColorGrid_clearableZRows = 1;

    [Tooltip("闪光过渡速度（每次过渡的持续时间）")]
    [Range(0.1f, 2f)]
    public float Prop_ClearColorGrid_flashSpeed = 0.3f;

    [Tooltip("透明强度值")]
    [Range(0.1f, 1f)]
    public float Prop_ClearColorGrid_overlayAlpha = 0.7f;

    [Tooltip("等待点击前的延迟时间（秒）")]
    [Range(0f, 3f)]
    public float Prop_ClearColorGrid_clickDelayTime = 0.5f;

    [Header("碰撞器配置")]
    [Tooltip("是否为目标SingleBlock添加BoxCollider")]
    public bool Prop_ClearColorGrid_enableBoxCollider = true;

    [Tooltip("BoxCollider的Center偏移")]
    public Vector3 Prop_ClearColorGrid_colliderCenter = Vector3.zero;

    [Tooltip("BoxCollider的Size大小")]
    public Vector3 Prop_ClearColorGrid_colliderSize = new Vector3(1f, 1f, 1f);

    [Tooltip("BoxCollider是否为触发器")]
    public bool Prop_ClearColorGrid_colliderIsTrigger = false;

    #endregion

    #region Z行隐藏优化配置

    [Header("Z行隐藏优化配置")]
    [Tooltip("Z坐标阈值，大于等于此值时隐藏SingleBlock的meshRenderer")]
    public int optimizeZ = 8;

    [Header("Prop清除颜色")]
    public GameObject prefab_ClearColorHamer;

    [Header("清除颜色锤子特效配置")]
    [Tooltip("点击偏移位置")]
    public Vector3 Prop_ClearColorHammer_clickOffset = new Vector3(0, 2, -1);

    [Tooltip("出现时的旋转Z角度")]
    public float Prop_ClearColorHammer_appearRotationZ = -30f;

    [Tooltip("出现时的目标缩放")]
    public float Prop_ClearColorHammer_appearScale = 1f;

    [Tooltip("出现缩放动画时间")]
    public float Prop_ClearColorHammer_appearScaleTime = 0.3f;

    [Tooltip("出现后停顿时间")]
    public float Prop_ClearColorHammer_appearPauseTime = 0.2f;

    [Tooltip("旋转砸击时间")]
    public float Prop_ClearColorHammer_hitRotationTime = 0.2f;

    [Tooltip("旋转砸击角度Z")]
    public float Prop_ClearColorHammer_hitRotationZ = -20f;

    [Tooltip("销毁子物体特效时间")]
    public float Prop_ClearColorHammer_effectDestroyTime = 1f;

    [Tooltip("返回角度1")]
    public float Prop_ClearColorHammer_returnRotation1 = 10f;

    [Tooltip("返回角度2")]
    public float Prop_ClearColorHammer_returnRotation2 = 30f;

    [Tooltip("返回时间1")]
    public float Prop_ClearColorHammer_returnTime1 = 0.15f;

    [Tooltip("返回时间2")]
    public float Prop_ClearColorHammer_returnTime2 = 0.15f;

    [Tooltip("消失缩小时间")]
    public float Prop_ClearColorHammer_disappearTime = 0.3f;

    #endregion

    #region 箭矢道具配置

    [Header("箭矢道具配置")]
    [Tooltip("箭矢预制体")]
    public GameObject prefab_Arrow;

    [Tooltip("箭矢飞行速度 m/s")]
    public float Prop_Arrow_Speed = 15f;

    [Tooltip("到达目标的判定距离阈值")]
    public float Prop_Arrow_ArrivalTolerance = 0.05f;

    [Tooltip("箭矢出生世界坐标")]
    public Vector3 Prop_Arrow_SpawnWorldPosition = Vector3.zero;

    [Tooltip("箭矢目标世界坐标")]
    public Vector3 Prop_Arrow_TargetWorldPosition = Vector3.zero;

    [Tooltip("命中特效子物体销毁时间")]
    public float Prop_Arrow_effectDestroyTime = 1f;

    [Tooltip("发射前等待秒数（缓解与子弹锁定置位的竞态），0为不等待")]
    public float Prop_Arrow_FireDelay = 1f;

    [Tooltip("结束退出状态等待时间")]
    public float Prop_Arrow_ExitDelay = 1f;

    #endregion

    #region 当前数据

    [Header("当前数据")]
    public List<TileCube_Object> Current_Objects = new List<TileCube_Object>();

    // 进度清除缓存数据
    private int totalProgressValue = 0;        // 总目标值（关卡开始时计算一次）
    private int currentProgressValue = 0;      // 当前剩余值（递减更新）

    #endregion

    #region 状态管理

    /// <summary>
    /// 控制器状态枚举
    /// </summary>
    public enum ControllerState
    {
        Normal,                 // 正常状态
        Prop_ClearColorGrid,    // 清除颜色道具状态
        Prop_Arrow              // 箭矢道具状态
    }

    public ControllerState currentState = ControllerState.Normal;

    // 道具状态相关变量
    private Dictionary<int, List<TileCube_SingleBlock>> Prop_ClearColorGrid_colorGroups = new Dictionary<int, List<TileCube_SingleBlock>>();
    private Coroutine Prop_ClearColorGrid_flashCoroutine;
    private bool Prop_ClearColorGrid_isWaitingForClick = false;
    // 添加锤子动画执行状态控制变量
    private bool Prop_ClearColorGrid_isHammerAnimating = false;
    // 用于存储为SingleBlock添加的BoxCollider的字典
    private Dictionary<TileCube_SingleBlock, BoxCollider> Prop_ClearColorGrid_addedColliders = new Dictionary<TileCube_SingleBlock, BoxCollider>();

    // 箭矢道具运行时变量
    private bool Prop_Arrow_isAnimating = false;
    private Coroutine Prop_Arrow_coroutine;
    private GameObject Prop_Arrow_instance;

    #endregion

    #region 事件
    /// <summary>钥匙到达最前方位置时触发</summary>
    public static Action<TileCube_Key> OnKeyReachedFront;

    /// <summary>钥匙开始开锁时触发</summary>
    public static Action<TileCube_Key, Vector3> OnKeyStartUnlock;

    /// <summary>物理阻挡条被击打时触发</summary>
    public static Action<TileCube_Barrier, int> OnBarrierHit;





    /// <summary>出块库吐出物体时触发</summary>
    public static Action<TileCube_Spawner, TileCube_Object> OnSpawnerEject;

    public static Action<TileCube_Spawner, TileCube_SingleBlock> OnSpanwer_BlockLand;


    public static Action<TileCube_GoldPig> OnPigBroke;

    /// <summary>播放音效</summary>
    public static Action<string> On_PlaySound;

    /// <summary>播放振动</summary>
    public static Action<int> On_PlayVib;


    public static Action On_Enter_PropClearColor_State;

    // public static Action On_Hamer
    public static Action On_Exit_PropClearColor_State;

    /// <summary>道具清除颜色使用成功事件</summary>
    public static Action OnPropClearColorUsedSuccessfully;

    public static Action<int, int> OnPropClearColor_ClearTargetColor;

    /// <summary>进度清除事件 - 传出当前剩余值和总目标值</summary>
    public static Action<int, int> On_Progress_Clear;


    #endregion

    private void Awake()
    {



        // 重置退出标志，确保场景重载时可以正常创建实例
        _isQuitting = false;

        // 确保单例模式正确设置
        if (_instance == null)
        {
            _instance = this;
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
            return;
        }

        // 初始化时设置网格起始点
        Tool_UpdateGridOrigin();

        // 监听推进完成事件
        Tool_TileGrid.OnPushCompleted += HandlePushCompleted;


        tileGrid.pushSpeed = MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data.TileGrid_PushSpeed;
    }

    #region 关卡创建功能

    /// <summary>
    /// 创建关卡
    /// </summary>
    /// <param name="level">关卡编号</param>
    /// <param name="CreateMapOnly">是否只创建地图</param>
    public void CreateLevel(int level, bool CreateMapOnly = false)
    {
        // 确保TileGrid存在并设置其起始点为TileCube_Parent的位置
        if (TileGrid != null)
        {
            TileGrid.gridOrigin = TileCube_Parent.transform.position;
        }

        // 加载关卡数据
        TileCube_Data_Level levelData = TileCube_Manager.LoadLevelData(level);

        if (!CreateMapOnly && levelData != null)
        {
            // 创建各种物体
            CreateObjectsFromLevelData(levelData);
        }

    }

    /// <summary>
    /// 根据关卡数据创建物体
    /// </summary>
    public void CreateObjectsFromLevelData(TileCube_Data_Level levelData)
    {
        // 清空现有物体
        ClearAllObjects();

        // 创建单一块
        CreateSingleBlocks(levelData.SingleBlocks);

        // 创建大型块
        CreateLargeBlocks(levelData.LargeBlocks);

        // 创建金猪
        CreateGoldPigs(levelData.GoldPigs);

        // 创建物理阻挡条
        CreateBarriers(levelData.Barriers);

        // 创建钥匙
        CreateKeys(levelData.Keys);

        // 创建出块库
        CreateSpawners(levelData.Spawners);

        // 初始化进度系统
        InitializeProgressSystem(levelData);

        // 关卡创建完成后，根据关卡配置进行场景换色
        HandleLevelCreationCompleted();
    }

    /// <summary>
    /// 创建单一块
    /// </summary>
    private void CreateSingleBlocks(List<TileCube_Data_SingleBlock> blocks)
    {
        if (blocks == null) return;

        foreach (var blockData in blocks)
        {
            GameObject prefab = TileCube_Manager.GetSingleBlockPrefab(blockData.ColorId);
            if (prefab != null)
            {
                GameObject obj = Instantiate(prefab, TileCube_Parent.transform);
                TileCube_SingleBlock singleBlock = obj.GetComponent<TileCube_SingleBlock>();
                if (singleBlock != null)
                {
                    singleBlock.Init(blockData);
                    PlaceObjectOnGrid(singleBlock, blockData.GridX, blockData.GridZ, blockData.Height, 1, 1);
                    Current_Objects.Add(singleBlock);

                    // 应用Z行隐藏优化
                    ApplyZRowOptimizationToSingleBlock(singleBlock);
                }
            }
        }
    }

    /// <summary>
    /// 创建大型块
    /// </summary>
    private void CreateLargeBlocks(List<TileCube_Data_LargeBlock> blocks)
    {
        if (blocks == null) return;

        foreach (var blockData in blocks)
        {
            GameObject prefab = TileCube_Manager.GetLargeBlockPrefab(blockData.ColorId);
            if (prefab != null)
            {
                GameObject obj = Instantiate(prefab, TileCube_Parent.transform);
                TileCube_LargeBlock largeBlock = obj.GetComponent<TileCube_LargeBlock>();
                if (largeBlock != null)
                {
                    largeBlock.Init(blockData);
                    PlaceObjectOnGrid(largeBlock, blockData.GridX, blockData.GridZ, 0, 2, 2, false); // 大型块占2x2，只在第一层，可以移动
                    Current_Objects.Add(largeBlock);

                    // 初始化虚拟生命值
                    CNT_Shooting_System_BulletTracker.ResetVirtualHealth(largeBlock.gameObject, largeBlock);
                }
            }
        }
    }

    /// <summary>
    /// 创建金猪
    /// </summary>
    private void CreateGoldPigs(List<TileCube_Data_GoldPig> pigs)
    {
        if (pigs == null) return;

        foreach (var pigData in pigs)
        {
            GameObject prefab = TileCube_Manager.GetGoldPigPrefab();
            if (prefab != null)
            {
                GameObject obj = Instantiate(prefab, TileCube_Parent.transform);
                TileCube_GoldPig goldPig = obj.GetComponent<TileCube_GoldPig>();
                if (goldPig != null)
                {
                    goldPig.Init(pigData);
                    PlaceObjectOnGrid(goldPig, pigData.GridX, pigData.GridZ, pigData.Height, 1, 1);
                    Current_Objects.Add(goldPig);

                    // 初始化虚拟生命值
                    CNT_Shooting_System_BulletTracker.ResetVirtualHealth(goldPig.gameObject, goldPig);
                }
            }
        }
    }

    /// <summary>
    /// 创建物理阻挡条
    /// </summary>
    private void CreateBarriers(List<TileCube_Data_Barrier> barriers)
    {
        if (barriers == null) return;

        foreach (var barrierData in barriers)
        {
            GameObject prefab = TileCube_Manager.GetBarrierPrefab(barrierData.ColorId);
            if (prefab != null)
            {
                GameObject obj = Instantiate(prefab, TileCube_Parent.transform);
                TileCube_Barrier barrier = obj.GetComponent<TileCube_Barrier>();
                if (barrier != null)
                {
                    barrier.Init(barrierData);
                    // 物理阻挡条根位置占1格，只在第一层，不可移动且上面不能叠加
                    PlaceObjectOnGrid(barrier, barrierData.GridX, barrierData.GridZ, 0, 1, 1, true);
                    barrier.SetupGridOccupancy(TileGrid);
                    Current_Objects.Add(barrier);

                    // 初始化虚拟生命值
                    CNT_Shooting_System_BulletTracker.ResetVirtualHealth(barrier.gameObject, barrier);
                }
            }
        }
    }

    /// <summary>
    /// 创建钥匙
    /// </summary>
    private void CreateKeys(List<TileCube_Data_Key> keys)
    {
        if (keys == null) return;

        foreach (var keyData in keys)
        {
            GameObject prefab = TileCube_Manager.GetKeyPrefab();
            if (prefab != null)
            {
                GameObject obj = Instantiate(prefab, TileCube_Parent.transform);
                TileCube_Key key = obj.GetComponent<TileCube_Key>();
                if (key != null)
                {
                    key.Init(keyData);
                    // 钥匙只能在第一层（高度0），可以移动
                    PlaceObjectOnGrid(key, keyData.GridX, keyData.GridZ, 0, 1, 1, false);
                    Current_Objects.Add(key);
                }
            }
        }
    }

    /// <summary>
    /// 创建出块库
    /// </summary>
    private void CreateSpawners(List<TileCube_Data_Spawner> spawners)
    {
        if (spawners == null) return;

        foreach (var spawnerData in spawners)
        {
            GameObject prefab = TileCube_Manager.GetSpawnerPrefab();
            if (prefab != null)
            {
                GameObject obj = Instantiate(prefab, TileCube_Parent.transform);
                TileCube_Spawner spawner = obj.GetComponent<TileCube_Spawner>();
                if (spawner != null)
                {
                    spawner.Init(spawnerData);
                    PlaceObjectOnGrid(spawner, spawnerData.GridX, spawnerData.GridZ, 0, 2, 3, true); // 出块库不可移动，占2x3
                    Current_Objects.Add(spawner);
                }
            }
        }
    }

    /// <summary>
    /// 将物体放置到网格上
    /// </summary>
    private void PlaceObjectOnGrid(TileCube_Object obj, int gridX, int gridZ, int height, int sizeX, int sizeZ, bool isImmovable = false)
    {
        if (TileGrid != null)
        {
            var gridInfo = TileGrid.PlaceObject(obj.gameObject, gridX, gridZ, height, sizeX, sizeZ, isImmovable);
            if (gridInfo != null)
            {
                obj.SetGridInfo(gridInfo);
            }
            else
            {
                //Debug.LogError($"无法将物体 {obj.name} 放置到网格位置 ({gridX}, {gridZ}, {height})");
            }
        }
    }

    /// <summary>
    /// 清空所有物体
    /// </summary>
    private void ClearAllObjects()
    {
        foreach (var obj in Current_Objects)
        {
            if (obj != null && obj.gameObject != null)
            {
                if (TileGrid != null)
                {
                    TileGrid.RemoveObject(obj.gameObject);
                }
                Destroy(obj.gameObject);
            }
        }
        Current_Objects.Clear();


    }


    #endregion

    #region 事件处理

    /// <summary>
    /// 处理推进完成事件 - 检查SingleBlock并执行Rebound效果
    /// </summary>
    /// <param name="stoppedTileObject">停下的格子对象</param>
    private void HandlePushCompleted(GridTileObject stoppedTileObject)
    {
        if (stoppedTileObject?.gameObject == null) return;

        // 检查物体是否为SingleBlock
        TileCube_Object tobj = stoppedTileObject.gameObject.GetComponent<TileCube_Object>();
        if (tobj != null)
        {
            // 执行Rebound效果
            tobj.OnRebound();
            return;
        }




    }

    #endregion

    #region 工具方法和生命周期

    /// <summary>
    /// 工具方法：更新网格起始点
    /// </summary>
    public void Tool_UpdateGridOrigin()
    {
        // 通过属性获取TileGrid，这会自动设置gridOrigin
        var grid = TileGrid;

        if (grid != null)
        {
            //Debug.Log($"网格起始点已更新为TileCube_Parent位置: {TileCube_Parent.transform.position}");
        }
        else
        {
            //Debug.LogWarning("TileGrid未找到，将在下次访问时自动设置网格起始点");
        }
    }

    private void OnApplicationQuit()
    {
        // 应用程序退出时设置标志
        _isQuitting = true;
    }

    private void OnDestroy()
    {
        // 取消事件监听，防止内存泄漏
        Tool_TileGrid.OnPushCompleted -= HandlePushCompleted;

        // 清理道具状态
        /*     if (currentState == ControllerState.Prop_ClearColorGrid)
			{
				Prop_ClearColorGrid_StopFlashEffect();
				Prop_ClearColorGrid_RemoveBoxCollidersFromTargets();
				Prop_ClearColorGrid_colorGroups.Clear();
			} */

        // 清理箭矢状态（防止残留协程或实例）
        if (currentState == ControllerState.Prop_Arrow)
        {
            if (Prop_Arrow_coroutine != null)
            {
                StopCoroutine(Prop_Arrow_coroutine);
                Prop_Arrow_coroutine = null;
            }
            if (Prop_Arrow_instance != null)
            {
                DestroyImmediate(Prop_Arrow_instance);
                Prop_Arrow_instance = null;
            }
            Prop_Arrow_isAnimating = false;
        }

        if (_instance == this)
        {
            _instance = null;
            _isQuitting = true; // 设置退出标志，防止其他物体在销毁时重新创建实例
        }
    }



    /// <summary>
    /// 处理物理阻挡条被击打的逻辑
    /// </summary>
    public void HandleBarrierHit(TileCube_Barrier barrier, int damage)
    {
        OnBarrierHit?.Invoke(barrier, damage);
    }

    /// <summary>
    /// 处理出块库吐出物体的逻辑
    /// </summary>
    public void HandleSpawnerEject(TileCube_Spawner spawner, TileCube_Object ejectedObject)
    {
        OnSpawnerEject?.Invoke(spawner, ejectedObject);
    }

    #endregion

    #region 子弹检测触发函数接口 - 供外部子弹系统调用

    /// <summary>
    /// 触发物体被子弹锁定 - 供外部子弹系统调用
    /// </summary>
    /// <param name="targetObject">被锁定的物体</param>
    /// <param name="bullet">子弹对象</param>
    /// <param name="bulletData">子弹数据</param>
    public void TriggerObjectTargeted(TileCube_Object targetObject, GameObject bullet, object bulletData = null)
    {
        if (targetObject != null)
        {
            targetObject.OnBulletTargeted(bullet, bulletData);
        }
    }

    /// <summary>
    /// 触发物体被子弹穿过 - 供外部子弹系统调用
    /// </summary>
    /// <param name="penetratedObject">被穿过的物体</param>
    /// <param name="bullet">子弹对象</param>
    /// <param name="penetratePosition">穿过位置</param>
    /// <param name="bulletData">子弹数据</param>
    public void TriggerObjectPenetrated(TileCube_Object penetratedObject, GameObject bullet, object bulletData = null)
    {
        if (penetratedObject != null)
        {
            penetratedObject.OnBulletPenetrate(bullet, bulletData);
        }
    }

    /// <summary>
    /// 触发物体被子弹击中 - 供外部子弹系统调用
    /// </summary>
    /// <param name="hitObject">被击中的物体</param>
    /// <param name="bullet">子弹对象</param>
    /// <param name="hitPosition">击中位置</param>
    /// <param name="bulletData">子弹数据</param>
    /// <returns>是否应该销毁子弹</returns>
    public bool TriggerObjectHit(TileCube_Object hitObject, GameObject bullet, object bulletData = null)
    {
        if (hitObject != null)
        {
            return hitObject.OnBulletHit(bullet, bulletData);
        }
        return true; // 默认销毁子弹
    }

    /// <summary>
    /// 根据网格位置获取物体 - 供外部子弹系统进行碰撞检测
    /// </summary>
    /// <param name="gridX">网格X坐标</param>
    /// <param name="gridZ">网格Z坐标</param>
    /// <param name="height">高度层级（-1表示任意高度）</param>
    /// <returns>找到的瓦片方块物体</returns>
    public TileCube_Object GetObjectAtGridPosition(int gridX, int gridZ, int height = -1)
    {
        if (TileGrid == null) return null;

        if (height == -1)
        {
            // 查找任意高度的物体，优先返回最高层的
            for (int h = 10; h >= 0; h--) // 假设最大高度为10
            {
                var gridObj = TileGrid.GetObjectAtPosition(gridX, gridZ, h);
                if (gridObj?.gameObject != null)
                {
                    var tileCubeObj = gridObj.gameObject.GetComponent<TileCube_Object>();
                    if (tileCubeObj != null) return tileCubeObj;
                }
            }
        }
        else
        {
            var gridObj = TileGrid.GetObjectAtPosition(gridX, gridZ, height);
            if (gridObj?.gameObject != null)
            {
                return gridObj.gameObject.GetComponent<TileCube_Object>();
            }
        }

        return null;
    }

    /// <summary>
    /// 根据世界坐标获取物体 - 供外部子弹系统进行碰撞检测
    /// </summary>
    /// <param name="worldPosition">世界坐标</param>
    /// <returns>找到的瓦片方块物体</returns>
    public TileCube_Object GetObjectAtWorldPosition(Vector3 worldPosition)
    {
        if (TileGrid == null) return null;

        // 将世界坐标转换为网格坐标
        var gridPos2D = TileGrid.WorldToGridPosition(worldPosition);
        // 计算高度层（基于Y坐标）
        int height = Mathf.FloorToInt((worldPosition.y - TileGrid.gridOrigin.y) / TileGrid.cellSize.y);
        return GetObjectAtGridPosition(gridPos2D.x, gridPos2D.y, height);
    }

    /// <summary>
    /// 获取所有当前物体列表 - 供外部子弹系统查询目标
    /// </summary>
    /// <returns>当前所有瓦片方块物体</returns>
    public List<TileCube_Object> GetAllObjects()
    {
        return new List<TileCube_Object>(Current_Objects);
    }

    #endregion

    #region 道具状态管理功能

    /// <summary>
    /// 获取当前状态
    /// </summary>
    public ControllerState GetCurrentState()
    {
        return currentState;
    }

    /// <summary>
    /// 进入清除颜色道具状态
    /// </summary>
    public bool PropClearColor_EnterGridState()
    {
        if (currentState != ControllerState.Normal) return false;

        //Debug.Log("进入清除颜色道具状态");
        currentState = ControllerState.Prop_ClearColorGrid;

        On_Enter_PropClearColor_State?.Invoke();

        // 查找并分组指定Z行范围内所有高度层的SingleBlock
        Prop_ClearColorGrid_FindAndGroupSingleBlocks();

        // 为目标SingleBlock添加BoxCollider
        Prop_ClearColorGrid_AddBoxCollidersToTargets();

        // 延迟启用点击监听
        StartCoroutine(Prop_ClearColorGrid_DelayedEnableClick());

        return true;
    }

    /// <summary>
    /// 退出清除颜色道具状态
    /// </summary>
    public void ExitPropClearColorGridState()
    {
        if (currentState != ControllerState.Prop_ClearColorGrid) return;

        //Debug.Log("退出清除颜色道具状态");

        // 停止闪光效果
        Prop_ClearColorGrid_StopFlashEffect();

        // 重置所有SingleBlock的透明度
        Prop_ClearColorGrid_ResetAllSingleBlocksOverlay();

        // 移除为SingleBlock添加的BoxCollider
        Prop_ClearColorGrid_RemoveBoxCollidersFromTargets();

        // 清理数据
        Prop_ClearColorGrid_colorGroups.Clear();
        Prop_ClearColorGrid_isWaitingForClick = false;
        // 重置锤子动画状态
        Prop_ClearColorGrid_isHammerAnimating = false;

        currentState = ControllerState.Normal;

        On_Exit_PropClearColor_State?.Invoke();
    }

    /// <summary>
    /// 查找并分组指定Z行范围内所有高度层的SingleBlock
    /// </summary>
    private void Prop_ClearColorGrid_FindAndGroupSingleBlocks()
    {
        Prop_ClearColorGrid_colorGroups.Clear();

        foreach (var obj in Current_Objects)
        {
            if (obj is TileCube_SingleBlock singleBlock)
            {
                var gridPos = singleBlock.GetGridPosition();

                var sgobj = obj as TileCube_SingleBlock;
                bool cansel = !sgobj.isByBulletLocked && !sgobj.isByBulletHited && !sgobj.IsPreSpawnState;

                // 检查是否在可清除的网格Z行范围内（gridPos.y对应网格Z坐标）
                if (gridPos.y <= Prop_ClearColorGrid_clearableZRows && cansel)
                {
                    int colorId = singleBlock.ColorId;

                    if (!Prop_ClearColorGrid_colorGroups.ContainsKey(colorId))
                    {
                        Prop_ClearColorGrid_colorGroups[colorId] = new List<TileCube_SingleBlock>();
                    }

                    Prop_ClearColorGrid_colorGroups[colorId].Add(singleBlock);
                }
            }
        }

        //Debug.Log($"找到 {Prop_ClearColorGrid_colorGroups.Count} 个颜色组，总共 {Prop_ClearColorGrid_colorGroups.Values.Sum(g => g.Count)} 个SingleBlock");

        // 输出每个颜色组的详细信息
        foreach (var kvp in Prop_ClearColorGrid_colorGroups)
        {
            //Debug.Log($"颜色ID {kvp.Key}: {kvp.Value.Count} 个SingleBlock，网格Z <= {Prop_ClearColorGrid_clearableZRows}");
        }
    }

    /// <summary>
    /// 开始闪光效果
    /// </summary>
    private void Prop_ClearColorGrid_StartFlashEffect()
    {
        if (Prop_ClearColorGrid_flashCoroutine != null)
        {
            StopCoroutine(Prop_ClearColorGrid_flashCoroutine);
        }

        Prop_ClearColorGrid_flashCoroutine = StartCoroutine(Prop_ClearColorGrid_FlashEffectCoroutine());
    }

    /// <summary>
    /// 停止闪光效果
    /// </summary>
    private void Prop_ClearColorGrid_StopFlashEffect()
    {
        if (Prop_ClearColorGrid_flashCoroutine != null)
        {
            StopCoroutine(Prop_ClearColorGrid_flashCoroutine);
            Prop_ClearColorGrid_flashCoroutine = null;
        }
    }

    /// <summary>
    /// 闪光效果协程
    /// </summary>
    private IEnumerator Prop_ClearColorGrid_FlashEffectCoroutine()
    {
        while (Prop_ClearColorGrid_isWaitingForClick && !Prop_ClearColorGrid_isHammerAnimating && Prop_ClearColorGrid_colorGroups.Count > 0)
        {
            // 每次循环都重新获取有效的颜色键，防止中途有颜色组被销毁
            var colorKeys = Prop_ClearColorGrid_colorGroups.Keys.ToList();

            // 循环遍历所有颜色组
            for (int i = 0; i < colorKeys.Count && Prop_ClearColorGrid_isWaitingForClick && !Prop_ClearColorGrid_isHammerAnimating; i++)
            {
                int currentColorId = colorKeys[i];

                // 检查颜色组是否还存在
                if (!Prop_ClearColorGrid_colorGroups.ContainsKey(currentColorId))
                    continue;

                // 过渡到当前组的目标透明度
                yield return StartCoroutine(Prop_ClearColorGrid_TransitionGroupAlpha(currentColorId, 0f, Prop_ClearColorGrid_overlayAlpha, Prop_ClearColorGrid_flashSpeed));

                // 如果还在等待点击且锤子未开始动画，过渡回0透明度
                if (Prop_ClearColorGrid_isWaitingForClick && !Prop_ClearColorGrid_isHammerAnimating && Prop_ClearColorGrid_colorGroups.ContainsKey(currentColorId))
                {
                    yield return StartCoroutine(Prop_ClearColorGrid_TransitionGroupAlpha(currentColorId, Prop_ClearColorGrid_overlayAlpha, 0f, Prop_ClearColorGrid_flashSpeed));
                }
            }
            // 循环完成后立即开始下一轮，没有停顿
        }
    }

    /// <summary>
    /// 过渡指定颜色组的透明度
    /// </summary>
    private IEnumerator Prop_ClearColorGrid_TransitionGroupAlpha(int colorId, float fromAlpha, float toAlpha, float duration)
    {
        if (!Prop_ClearColorGrid_colorGroups.ContainsKey(colorId)) yield break;

        float elapsed = 0f;

        while (elapsed < duration)
        {
            // 检查是否还在道具状态、等待点击状态，以及锤子是否正在动画中
            if (currentState != ControllerState.Prop_ClearColorGrid || !Prop_ClearColorGrid_isWaitingForClick || Prop_ClearColorGrid_isHammerAnimating)
                yield break;

            // 检查颜色组是否还存在
            if (!Prop_ClearColorGrid_colorGroups.ContainsKey(colorId))
                yield break;

            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            float currentAlpha = Mathf.Lerp(fromAlpha, toAlpha, progress);

            // 应用到该颜色组的所有块（过滤掉null的块）
            var validBlocks = Prop_ClearColorGrid_colorGroups[colorId].Where(block => block != null).ToList();
            foreach (var singleBlock in validBlocks)
            {
                singleBlock.SetOverlayAlpha(currentAlpha);
            }

            yield return null;
        }

        // 最终检查并设置最终值
        if (currentState == ControllerState.Prop_ClearColorGrid && Prop_ClearColorGrid_isWaitingForClick && !Prop_ClearColorGrid_isHammerAnimating && Prop_ClearColorGrid_colorGroups.ContainsKey(colorId))
        {
            var validBlocks = Prop_ClearColorGrid_colorGroups[colorId].Where(block => block != null).ToList();
            foreach (var singleBlock in validBlocks)
            {
                singleBlock.SetOverlayAlpha(toAlpha);
            }
        }
    }

    /// <summary>
    /// 重置所有SingleBlock的透明度
    /// </summary>
    private void Prop_ClearColorGrid_ResetAllSingleBlocksOverlay()
    {
        foreach (var group in Prop_ClearColorGrid_colorGroups.Values)
        {
            foreach (var singleBlock in group)
            {
                if (singleBlock != null)
                {
                    singleBlock.SetOverlayAlpha(0f);
                }
            }
        }
    }

    /// <summary>
    /// 处理SingleBlock点击（在道具状态下）
    /// </summary>
    public void Prop_ClearColorGrid_HandleSingleBlockClick(TileCube_SingleBlock clickedBlock)
    {
        // 检查是否在道具状态、等待点击状态，以及锤子是否正在动画中
        if (currentState != ControllerState.Prop_ClearColorGrid || !Prop_ClearColorGrid_isWaitingForClick || Prop_ClearColorGrid_isHammerAnimating)
            return;

        int clickedColorId = clickedBlock.ColorId;

        // 检查点击的块是否在可清除范围内
        if (Prop_ClearColorGrid_colorGroups.ContainsKey(clickedColorId))
        {
            //Debug.Log($"点击了颜色ID {clickedColorId} 的块，执行该颜色组销毁");

            // 立即设置锤子动画状态为执行中，防止重复点击
            Prop_ClearColorGrid_isHammerAnimating = true;

            // 创建锤子特效并启动动画
            Vector3 clickPosition = clickedBlock.transform.position;
            StartCoroutine(Prop_ClearColorHammer_CreateAndAnimate(clickPosition, clickedColorId));
        }
    }

    /// <summary>
    /// 执行指定颜色组的销毁
    /// </summary>
    private void Prop_ClearColorGrid_ExecuteGroupDestroy(int colorId)
    {
        if (Prop_ClearColorGrid_colorGroups.ContainsKey(colorId))
        {
            // 获取销毁的方块数量
            int destroyedCount = Prop_ClearColorGrid_colorGroups[colorId].Count;

            foreach (var singleBlock in Prop_ClearColorGrid_colorGroups[colorId])
            {
                if (singleBlock != null)
                {
                    // 调用SingleBlock的缩小销毁方法
                    singleBlock.TriggerShrinkDestroy();
                }
            }

            // 触发清除目标颜色事件，传递颜色ID和数量
            OnPropClearColor_ClearTargetColor?.Invoke(colorId, destroyedCount);

            // 从颜色组字典中移除该组，防止后续访问
            Prop_ClearColorGrid_colorGroups.Remove(colorId);
            //Debug.Log($"颜色组 {colorId} 已销毁并从字典中移除，数量: {destroyedCount}");
        }
    }

    /// <summary>
    /// 对指定颜色组应用透明效果（立即设置）
    /// </summary>
    private void Prop_ClearColorGrid_ApplyOverlayToGroup(int colorId, float alpha)
    {
        if (Prop_ClearColorGrid_colorGroups.ContainsKey(colorId))
        {
            foreach (var singleBlock in Prop_ClearColorGrid_colorGroups[colorId])
            {
                if (singleBlock != null)
                {
                    singleBlock.SetOverlayAlpha(alpha);
                }
            }
        }
    }

    /// <summary>
    /// 为目标SingleBlock添加BoxCollider
    /// </summary>
    private void Prop_ClearColorGrid_AddBoxCollidersToTargets()
    {
        if (!Prop_ClearColorGrid_enableBoxCollider) return;

        // 清理旧的碰撞器记录
        Prop_ClearColorGrid_addedColliders.Clear();

        foreach (var colorGroup in Prop_ClearColorGrid_colorGroups.Values)
        {
            foreach (var singleBlock in colorGroup)
            {
                if (singleBlock != null)
                {
                    // 检查是否已经有BoxCollider
                    BoxCollider existingCollider = singleBlock.GetComponent<BoxCollider>();
                    if (existingCollider == null)
                    {
                        // 添加新的BoxCollider
                        BoxCollider newCollider = singleBlock.gameObject.AddComponent<BoxCollider>();
                        newCollider.center = Prop_ClearColorGrid_colliderCenter;
                        newCollider.size = Prop_ClearColorGrid_colliderSize;
                        newCollider.isTrigger = Prop_ClearColorGrid_colliderIsTrigger;

                        // 记录添加的碰撞器
                        Prop_ClearColorGrid_addedColliders[singleBlock] = newCollider;
                        //Debug.Log($"为SingleBlock {singleBlock.name} 添加了BoxCollider");
                    }
                }
            }
        }

        //Debug.Log($"共为 {Prop_ClearColorGrid_addedColliders.Count} 个SingleBlock添加了BoxCollider");
    }

    /// <summary>
    /// 移除为SingleBlock添加的BoxCollider
    /// </summary>
    private void Prop_ClearColorGrid_RemoveBoxCollidersFromTargets()
    {
        int removedCount = 0;

        foreach (var kvp in Prop_ClearColorGrid_addedColliders)
        {
            var singleBlock = kvp.Key;
            var collider = kvp.Value;

            if (singleBlock != null && collider != null)
            {
                // 销毁添加的BoxCollider
                DestroyImmediate(collider);
                removedCount++;
                //Debug.Log($"移除了SingleBlock {singleBlock.name} 的BoxCollider");
            }
        }

        // 清理记录
        Prop_ClearColorGrid_addedColliders.Clear();
        //Debug.Log($"共移除了 {removedCount} 个BoxCollider");
    }

    /// <summary>
    /// 延迟启用点击监听的协程
    /// </summary>
    private IEnumerator Prop_ClearColorGrid_DelayedEnableClick()
    {
        // 等待指定的延迟时间
        yield return new WaitForSeconds(Prop_ClearColorGrid_clickDelayTime);

        // 检查是否仍在道具状态中，如果是则启用点击监听
        if (currentState == ControllerState.Prop_ClearColorGrid)
        {
            Prop_ClearColorGrid_isWaitingForClick = true;
            // 开始闪光效果
            Prop_ClearColorGrid_StartFlashEffect();
        }
    }

    #endregion

    #region 清除颜色锤子特效功能

    /// <summary>
    /// 创建锤子特效并执行完整动画序列
    /// </summary>
    /// <param name="clickPosition">点击位置</param>
    /// <param name="colorId">要销毁的颜色组ID</param>
    private IEnumerator Prop_ClearColorHammer_CreateAndAnimate(Vector3 clickPosition, int colorId)
    {
        if (prefab_ClearColorHamer == null)
        {
            Debug.LogError("prefab_ClearColorHamer 为空，无法创建锤子特效");
            // 异常退出时重置锤子动画状态
            Prop_ClearColorGrid_isHammerAnimating = false;
            yield break;
        }

        // 立即停止闪光效果并重置所有SingleBlock的透明度
        Prop_ClearColorGrid_StopFlashEffect();
        Prop_ClearColorGrid_ResetAllSingleBlocksOverlay();

        // 道具使用成功，立即触发事件（锤子出现时就算使用成功）
        OnPropClearColorUsedSuccessfully?.Invoke();

        // 1. 创建锤子实例并设置位置
        Vector3 hammerPosition = clickPosition + Prop_ClearColorHammer_clickOffset;
        GameObject hammer = Instantiate(prefab_ClearColorHamer);
        hammer.transform.position = hammerPosition;

        // 2. 计算朝向 - 让X轴朝向点击位置
        Vector3 directionToClick = (clickPosition - hammerPosition).normalized;
        // 使用LookAt让Z轴朝向目标，然后旋转90度让X轴朝向目标
        hammer.transform.LookAt(clickPosition);
        hammer.transform.Rotate(0, -90, 0); // 让X轴朝向目标

        // 3. 设置初始角度（Z轴旋转）
        Vector3 initialRotation = hammer.transform.eulerAngles;
        initialRotation.z = Prop_ClearColorHammer_appearRotationZ;
        hammer.transform.eulerAngles = initialRotation;

        // 4. 设置初始缩放为0
        hammer.transform.localScale = Vector3.zero;


        TileCube_Controller.On_PlaySound?.Invoke("CNT_道具锤子使用");
        // 5. 执行出现缩放动画
        yield return StartCoroutine(Prop_ClearColorHammer_ScaleAnimation(hammer, Vector3.zero, Vector3.one * Prop_ClearColorHammer_appearScale, Prop_ClearColorHammer_appearScaleTime));



        // 6. 出现后停顿
        yield return new WaitForSeconds(Prop_ClearColorHammer_appearPauseTime);

        // 7. 执行砸击旋转动画
        yield return StartCoroutine(Prop_ClearColorHammer_RotationAnimation(hammer, Prop_ClearColorHammer_appearRotationZ, Prop_ClearColorHammer_hitRotationZ, Prop_ClearColorHammer_hitRotationTime));

        // 8. 砸击到达最低点，触发颜色组销毁
        Prop_ClearColorGrid_ExecuteGroupDestroy(colorId);

        TileCube_Controller.On_PlaySound?.Invoke("CNT_道具锤子砸下");

        TileCube_Controller.On_PlayVib?.Invoke(MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data.Virb_Normal);


        // 9. 处理子物体特效
        Transform firstChild = hammer.transform.childCount > 0 ? hammer.transform.GetChild(0) : null;
        if (firstChild != null)
        {
            // 保持局部缩放，移到世界根目录
            Vector3 worldScale = firstChild.lossyScale;
            firstChild.SetParent(null);
            firstChild.localScale = worldScale;

            firstChild.gameObject.SetActive(true);

            // 在指定时间后销毁子物体
            Destroy(firstChild.gameObject, Prop_ClearColorHammer_effectDestroyTime);
        }

        // 10. 执行返回动画 - 第一段
        yield return StartCoroutine(Prop_ClearColorHammer_RotationAnimation(hammer, Prop_ClearColorHammer_hitRotationZ, Prop_ClearColorHammer_returnRotation1, Prop_ClearColorHammer_returnTime1));

        // 11. 执行返回动画 - 第二段
        yield return StartCoroutine(Prop_ClearColorHammer_RotationAnimation(hammer, Prop_ClearColorHammer_returnRotation1, Prop_ClearColorHammer_returnRotation2, Prop_ClearColorHammer_returnTime2));

        // 12. 执行消失缩放动画
        yield return StartCoroutine(Prop_ClearColorHammer_ScaleAnimation(hammer, hammer.transform.localScale, Vector3.zero, Prop_ClearColorHammer_disappearTime));

        // 13. 销毁锤子对象
        if (hammer != null)
        {
            Destroy(hammer);
        }

        //退出道具状态（这里会重置Prop_ClearColorGrid_isHammerAnimating状态）
        ExitPropClearColorGridState();
    }

    /// <summary>
    /// 缩放动画协程
    /// </summary>
    /// <param name="target">目标对象</param>
    /// <param name="fromScale">起始缩放</param>
    /// <param name="toScale">目标缩放</param>
    /// <param name="duration">动画时间</param>
    private IEnumerator Prop_ClearColorHammer_ScaleAnimation(GameObject target, Vector3 fromScale, Vector3 toScale, float duration)
    {
        if (target == null) yield break;

        float elapsed = 0f;
        while (elapsed < duration)
        {
            if (target == null) yield break;

            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            target.transform.localScale = Vector3.Lerp(fromScale, toScale, progress);

            yield return null;
        }

        // 确保最终值正确
        if (target != null)
        {
            target.transform.localScale = toScale;
        }
    }

    /// <summary>
    /// Z轴旋转动画协程
    /// </summary>
    /// <param name="target">目标对象</param>
    /// <param name="fromRotationZ">起始Z轴角度</param>
    /// <param name="toRotationZ">目标Z轴角度</param>
    /// <param name="duration">动画时间</param>
    private IEnumerator Prop_ClearColorHammer_RotationAnimation(GameObject target, float fromRotationZ, float toRotationZ, float duration)
    {
        if (target == null) yield break;

        float elapsed = 0f;
        Vector3 currentRotation = target.transform.eulerAngles;

        while (elapsed < duration)
        {
            if (target == null) yield break;

            elapsed += Time.deltaTime;
            float progress = elapsed / duration;
            float currentZ = Mathf.Lerp(fromRotationZ, toRotationZ, progress);

            currentRotation = target.transform.eulerAngles;
            currentRotation.z = currentZ;
            target.transform.eulerAngles = currentRotation;

            yield return null;
        }

        // 确保最终值正确
        if (target != null)
        {
            currentRotation = target.transform.eulerAngles;
            currentRotation.z = toRotationZ;
            target.transform.eulerAngles = currentRotation;
        }
    }

    #endregion

    #region 箭矢道具功能

    /// <summary>
    /// 进入箭矢道具状态（无需点击）
    /// </summary>
    /// <param name="spawnWorldPosition">箭矢实例化的世界坐标</param>
    /// <param name="targetWorldPosition">箭矢飞行的目标世界坐标</param>
    /// <returns>是否成功进入状态</returns>
    public bool PropArrow_Enter(Vector3 spawnWorldPosition, Vector3 targetWorldPosition)
    {
        // 仅在正常状态且未在执行中时可进入
        if (currentState != ControllerState.Normal || Prop_Arrow_isAnimating) return false;
        if (prefab_Arrow == null) return false;

        currentState = ControllerState.Prop_Arrow;
        Prop_Arrow_isAnimating = true;

        // 可选：发射前等待一小段时间，缓解与子弹锁定置位的竞态
        if (Prop_Arrow_FireDelay > 0f)
        {
            StartCoroutine(Prop_Arrow_DelayedFire(spawnWorldPosition, targetWorldPosition));
        }
        else
        {
            Prop_Arrow_FireNow(spawnWorldPosition, targetWorldPosition);
        }

        return true;
    }

    /// <summary>
    /// 进入箭矢道具状态（使用面板配置的出生与目标世界坐标）
    /// </summary>
    public bool PropArrow_Enter()
    {
        return PropArrow_Enter(Prop_Arrow_SpawnWorldPosition, Prop_Arrow_TargetWorldPosition);
    }

    /// <summary>
    /// 箭矢飞行并在命中后执行清除第一行
    /// </summary>
    private IEnumerator Prop_Arrow_FlyAndExecute(Vector3 targetWorldPosition)
    {
        if (Prop_Arrow_instance == null)
        {
            ExitPropArrowState();
            yield break;
        }

        Transform t = Prop_Arrow_instance.transform;
        while (Prop_Arrow_instance != null)
        {
            Vector3 toTarget = targetWorldPosition - t.position;
            float distance = toTarget.magnitude;
            if (distance <= Prop_Arrow_ArrivalTolerance)
            {
                break;
            }

            // 移动步进
            Vector3 step = toTarget.normalized * Prop_Arrow_Speed * Time.deltaTime;
            if (step.sqrMagnitude >= toTarget.sqrMagnitude)
            {
                t.position = targetWorldPosition;
            }
            else
            {
                t.position += step;
            }

            // 朝向目标方向
            if (toTarget.sqrMagnitude > 1e-8f)
            {
                t.rotation = Quaternion.LookRotation(toTarget.normalized, Vector3.up);
            }

            yield return null;
        }

        // 命中：清除第一行的所有SingleBlock
        Prop_Arrow_DestroyFirstRow();

        // 命中音效与震动（可选事件实现）
        On_PlaySound?.Invoke("CNT_道具箭矢命中");
        On_PlayVib?.Invoke(MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data.Virb_Normal);

        // 处理命中特效：将第一个子物体拖到世界根并定时销毁
        if (Prop_Arrow_instance != null)
        {
            Transform firstChild = Prop_Arrow_instance.transform.childCount > 0 ? Prop_Arrow_instance.transform.GetChild(0) : null;
            if (firstChild != null)
            {

                firstChild.SetParent(null);

                firstChild.gameObject.SetActive(true);
                Destroy(firstChild.gameObject, Prop_Arrow_effectDestroyTime);
            }
        }

        // 销毁箭矢实例
        if (Prop_Arrow_instance != null)
        {
            Destroy(Prop_Arrow_instance);
            Prop_Arrow_instance = null;
        }

        yield return new WaitForSeconds(Prop_Arrow_ExitDelay);
        // 退出箭矢状态
        ExitPropArrowState();
    }

    /// <summary>
    /// 清除第一行（Z=0）的所有SingleBlock（过滤被子弹占用/预出块），并按颜色汇总触发清除事件
    /// </summary>
    private void Prop_Arrow_DestroyFirstRow()
    {
        List<TileCube_SingleBlock> targets = new List<TileCube_SingleBlock>();
        Dictionary<int, int> colorIdToCount = new Dictionary<int, int>();
        foreach (var obj in Current_Objects)
        {
            var single = obj as TileCube_SingleBlock;
            if (single == null) continue;
            var gridPos = single.GetGridPosition();
            bool canClear = !single.isByBulletLocked && !single.isByBulletHited && !single.IsPreSpawnState;
            if (gridPos.y == 0 && canClear)
            {
                targets.Add(single);
                int colorId = single.ColorId;
                if (!colorIdToCount.ContainsKey(colorId))
                {
                    colorIdToCount[colorId] = 0;
                }
                colorIdToCount[colorId]++;
            }
        }

        for (int i = 0; i < targets.Count; i++)
        {
            if (targets[i] != null)
            {
                targets[i].TriggerShrinkDestroy();
            }
        }

        // 触发与清除颜色道具一致的事件，驱动其他系统（如炮台清除）
        foreach (var kvp in colorIdToCount)
        {
            OnPropClearColor_ClearTargetColor?.Invoke(kvp.Key, kvp.Value);
        }
    }

    /// <summary>
    /// 退出箭矢道具状态并做清理
    /// </summary>
    private void ExitPropArrowState()
    {
        if (Prop_Arrow_coroutine != null)
        {
            StopCoroutine(Prop_Arrow_coroutine);
            Prop_Arrow_coroutine = null;
        }

        if (Prop_Arrow_instance != null)
        {
            Destroy(Prop_Arrow_instance);
            Prop_Arrow_instance = null;
        }

        Prop_Arrow_isAnimating = false;
        currentState = ControllerState.Normal;
    }

    private IEnumerator Prop_Arrow_DelayedFire(Vector3 spawnWorldPosition, Vector3 targetWorldPosition)
    {
        float t = Prop_Arrow_FireDelay;
        while (t > 0f)
        {
            t -= Time.deltaTime;
            yield return null;
        }
        Prop_Arrow_FireNow(spawnWorldPosition, targetWorldPosition);
    }

    private void Prop_Arrow_FireNow(Vector3 spawnWorldPosition, Vector3 targetWorldPosition)
    {
        // 实例化箭矢并设置初始位置与朝向
        Prop_Arrow_instance = Instantiate(prefab_Arrow);
        Prop_Arrow_instance.transform.position = spawnWorldPosition;
        Prop_Arrow_instance.transform.LookAt(targetWorldPosition);

        // 播放发射音效
        On_PlaySound?.Invoke("CNT_道具箭矢发射");

        // 启动飞行协程
        Prop_Arrow_coroutine = StartCoroutine(Prop_Arrow_FlyAndExecute(targetWorldPosition));
    }

    #endregion

    #region Z行隐藏优化功能

    /// <summary>
    /// 对单个SingleBlock应用Z行隐藏优化
    /// </summary>
    /// <param name="singleBlock">要优化的SingleBlock</param>
    public void ApplyZRowOptimizationToSingleBlock(TileCube_SingleBlock singleBlock)
    {
        if (singleBlock == null) return;

        var gridPos = singleBlock.GetGridPosition();
        // 使用gridPos.y来检查Z行坐标，y对应gridInfo.gridZ（网格Z坐标）
        bool shouldBeVisible = gridPos.y < optimizeZ;

        //        Debug.LogError($"SingleBlock在网格Z={gridPos.y}, 优化阈值={optimizeZ}, 是否可见={shouldBeVisible}");

        SetSingleBlockMeshVisibility(singleBlock, shouldBeVisible);
    }

    /// <summary>
    /// 设置SingleBlock的MeshRenderer可见性
    /// </summary>
    /// <param name="singleBlock">SingleBlock对象</param>
    /// <param name="isVisible">是否可见</param>
    private void SetSingleBlockMeshVisibility(TileCube_SingleBlock singleBlock, bool isVisible)
    {
        if (singleBlock == null) return;


        singleBlock.meshRenderer.gameObject.SetActive(isVisible);
    }

    #endregion

    #region 进度清除系统

    /// <summary>
    /// 初始化进度系统
    /// </summary>
    /// <param name="levelData">关卡数据</param>
    private void InitializeProgressSystem(TileCube_Data_Level levelData)
    {
        // 计算总目标值
        totalProgressValue = CalculateTotalProgressValue(levelData);
        currentProgressValue = totalProgressValue;

        // 触发初始进度事件
        On_Progress_Clear?.Invoke(currentProgressValue, totalProgressValue);

        //Debug.Log($"进度系统初始化完成 - 总目标值: {totalProgressValue}");
    }

    /// <summary>
    /// 计算关卡总进度值
    /// </summary>
    /// <param name="levelData">关卡数据</param>
    /// <returns>总进度值</returns>
    private int CalculateTotalProgressValue(TileCube_Data_Level levelData)
    {
        if (levelData == null) return 0;

        int total = 0;

        // SingleBlocks - 每个算1个值
        total += levelData.SingleBlocks?.Count ?? 0;

        // LargeBlocks - 按HitValues击打次数算
        if (levelData.LargeBlocks != null)
        {
            foreach (var largeBlock in levelData.LargeBlocks)
            {
                total += largeBlock.HitValues;
            }
        }

        // Barriers - 按每格击打次数算 (长度 × HitsPerGrid)
        if (levelData.Barriers != null)
        {
            foreach (var barrier in levelData.Barriers)
            {
                total += barrier.GridCount * barrier.HitsPerGrid;
            }
        }

        // GoldPigs - 按HitValues击打次数算
        if (levelData.GoldPigs != null)
        {
            foreach (var goldPig in levelData.GoldPigs)
            {
                total += goldPig.HitValues;
            }
        }

        // Keys - 每个算1个值
        total += levelData.Keys?.Count ?? 0;

        // Spawners - 按内容物数量算
        if (levelData.Spawners != null)
        {
            foreach (var spawner in levelData.Spawners)
            {
                total += spawner.SpawnColorSingleBlocks?.Count ?? 0;
            }
        }

        return total;
    }

    /// <summary>
    /// 更新进度（供各个TileObject调用）
    /// </summary>
    /// <param name="decreaseValue">减少的进度值</param>
    public void UpdateProgress(int decreaseValue)
    {
        if (decreaseValue <= 0) return;

        currentProgressValue -= decreaseValue;
        currentProgressValue = Mathf.Max(0, currentProgressValue); // 防止负数

        // 触发进度更新事件
        On_Progress_Clear?.Invoke(currentProgressValue, totalProgressValue);

        //Debug.Log($"进度更新 - 减少值: {decreaseValue}, 当前剩余: {currentProgressValue}/{totalProgressValue}");
    }

    /// <summary>
    /// 获取当前进度信息（供外部查询使用）
    /// </summary>
    /// <returns>当前剩余值, 总目标值</returns>
    public (int current, int total) GetProgressInfo()
    {
        return (currentProgressValue, totalProgressValue);
    }

    /// <summary>
    /// 重置进度系统（供调试或重新开始关卡时使用）
    /// </summary>
    public void ResetProgressSystem()
    {
        totalProgressValue = 0;
        currentProgressValue = 0;
    }

    #endregion

    #region 场景换色系统

    /// <summary>
    /// 处理关卡创建完成的逻辑
    /// </summary>
    private void HandleLevelCreationCompleted()
    {
        // 获取当前关卡索引
        int currentLevel = MatchUI_UserData_Manager.Instance.Get.Level_Current;

        // 根据关卡配置进行场景换色
        if (TileCube_SceneColorer.Instance != null)
        {
            TileCube_SceneColorer.Instance.SetSceneColorByLevel(currentLevel);
        }

        // 触发关卡创建完成事件
        OnLevelCreationCompleted?.Invoke();
        //        Debug.Log("[TileCube_Controller] 关卡创建完成事件已触发");
    }

    #endregion
}
