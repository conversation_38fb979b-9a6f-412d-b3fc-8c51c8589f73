using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using JRJelly;

/// <summary>
/// 金猪 - 只占一格，可以叠多格高度层，只有一种颜色
/// 需要击打多次才会销毁，支持子弹穿过和击中效果
/// </summary>
public class TileCube_GoldPig : TileCube_Object
{
    [Header("金猪特有属性")]
    public bool CanStack = true;            // 是否可以堆叠
    public int MaxStackHeight = 10;         // 最大堆叠高度
    public int GoldValue = 100;             // 金币价值
    public bool IsSpecialType = true;       // 特殊类型标记

    [Header("击打销毁设置")]
    [Tooltip("需要击打的次数才销毁")]
    public int RequiredHits = 5;            // 需要击打次数
    [Tooltip("当前已被击打次数")]
    public int CurrentHits = 0;             // 当前击打次数

    [Header("渲染组件")]
    public MeshRenderer meshRenderer;       // 渲染器

    [Header("果冻效果设置")]
    [Tooltip("推进刹车效果强度")]
    [Range(0.1f, 2f)]
    public float pushBrakeIntensity = 0.8f;

    [Tooltip("子弹穿过甩动基础强度")]
    [Range(0.1f, 2f)]
    public float bulletPassSwingIntensity = 0.5f;

    [Tooltip("子弹击中甩动强度")]
    [Range(0.1f, 2f)]
    public float bulletHitSwingIntensity = 1.2f;

    [Tooltip("击中后缩小持续时间")]
    [Range(0.2f, 1f)]
    public float hitShrinkDuration = 0.3f;

    [Header("子弹检测参数")]
    [Tooltip("穿过甩动持续时间")]
    [Range(0.1f, 1f)]
    public float bulletPassSwingDuration = 0.6f;

    [Tooltip("击中甩动持续时间")]
    [Range(0.1f, 1f)]
    public float bulletHitSwingDuration = 0.8f;

    [Tooltip("击中回来缩小的时间比例")]
    [Range(0f, 1f)]
    public float bulletHitToScalePerent = 0.6f;

    [Header("倾斜效果参数")]
    [Tooltip("穿过时倾斜强度")]
    [Range(0f, 30f)]
    public float bulletPassTiltIntensity = 8f;

    [Tooltip("击中时倾斜强度")]
    [Range(0f, 30f)]
    public float bulletHitTiltIntensity = 15f;

    [Tooltip("倾斜恢复速度")]
    [Range(1f, 10f)]
    public float tiltRecoverySpeed = 3f;

    [Tooltip("多子弹连续击中时倾斜叠加强度")]
    [Range(0f, 1f)]
    public float multiHitTiltMultiplier = 0.7f;

    [Header("击打变大效果参数")]
    [Tooltip("变大过渡时间")]
    [Range(0.1f, 1f)]
    public float hitScaleTransitionTime = 0.3f;

    [Tooltip("最大缩放限制")]
    [Range(1f, 3f)]
    public float maxScaleLimit = 2f;

    [Header("前方阻挡检测倾倒功能")]
    [Tooltip("起立时的X旋转角度")]
    [Range(-80f, 90f)]
    public float standUpRotationX = 0f;

    [Tooltip("倾倒时的X旋转角度")]
    [Range(-90f, 90f)]
    public float fallDownRotationX = -45f;

    [Tooltip("倾倒状态过渡速度")]
    [Range(1f, 10f)]
    public float fallTransitionSpeed = 3f;

    [Tooltip("前方阻挡检测间隔时间（秒）")]
    [Range(0.1f, 2f)]
    public float obstacleCheckInterval = 0.2f;


    // 私有变量
    private TileCube_Data_GoldPig pigData;
    private MaterialPropertyBlock propertyBlock;
    private bool isByBulletLocked = false;
    private bool isDestroying = false;      // 防止重复销毁

    // 金猪的固定颜色（金色）
    private readonly Color GoldColor = new Color(1f, 0.8f, 0f, 1f); // 金色

    [Header("果冻参数")]
    public Tool_JRJELLY_Simple tool_JRJELLY_Simple;

    [Header("破碎参数")]
    public GameObject broke_Child;

    [Header("破碎效果参数")]
    [Tooltip("破碎块爆炸基础力度")]
    [Range(1f, 20f)]
    public float explosionForce = 8f;

    [Tooltip("破碎块向上爆炸力度")]
    [Range(1f, 15f)]
    public float upwardForce = 5f;

    [Tooltip("击打方向影响力度")]
    [Range(0f, 10f)]
    public float hitDirectionForce = 3f;

    [Tooltip("重力强度")]
    [Range(0.5f, 8f)]
    public float gravityMultiplier = 1f;

    [Tooltip("破碎效果持续时间")]
    [Range(0.1f, 1f)]
    public float breakEffectDuration = 0.3f;

    [Tooltip("清理网格占位时间（释放网格占用）")]
    [Range(0.01f, 1f)]
    public float gridClearTime = 1f;

    [Tooltip("清除物体时间（销毁整个金猪对象）")]
    [Range(0.5f, 4f)]
    public float objectDestroyTime = 5f;

    [Tooltip("是否启用破碎块旋转")]
    public bool enableFragmentRotation = true;

    // 倾斜效果相关变量
    private Vector3 targetTiltRotation = Vector3.zero;    // 目标倾斜角度
    private Vector3 currentTiltRotation = Vector3.zero;   // 当前倾斜角度
    private Vector3 originalRotation = Vector3.zero;     // 原始旋转角度
    private Vector3 originalMeshRotation = Vector3.zero; // mesh原始旋转角度
    private float tiltEndTime = 0f;                      // 倾斜结束时间
    private List<Vector3> activeTiltDirections = new List<Vector3>(); // 活跃的倾斜方向列表
    private List<float> activeTiltEndTimes = new List<float>();       // 对应的结束时间列表

    // 击打变大效果相关变量
    private Vector3 originalMeshScale = Vector3.one;     // mesh原始缩放
    private Vector3 targetMeshScale = Vector3.one;       // mesh目标缩放
    private Vector3 currentMeshScale = Vector3.one;      // mesh当前缩放

    // 前方阻挡检测倾倒功能相关变量
    private float targetMeshRotationX = 0f;              // mesh目标X旋转角度
    private float currentMeshRotationX = 0f;             // mesh当前X旋转角度
    private float lastObstacleCheckTime = 0f;            // 上次检测阻挡的时间
    private bool hasObstacleInFront = true;              // 前方是否有阻挡（默认为true，起立状态）

    #region 高度压缩功能相关变量

    // 高度压缩功能相关变量
    private Vector3 originalLocalScale; // 记录原始的local scale
    private bool isInCompressionZone = false; // 是否在压缩区域
    private bool isCompressing = false; // 是否正在压缩过程中
    private bool isRestoring = false; // 是否正在恢复过程中
    private Coroutine compressionCoroutine; // 压缩协程
    private int lastKnownGridZ = -1; // 上次已知的网格Z坐标

    #endregion

    #region 破碎效果相关变量

    // 破碎块数据结构
    [System.Serializable]
    public class FragmentData
    {
        public Transform transform;           // 破碎块Transform
        public Vector3 velocity;             // 当前速度
        public Vector3 angularVelocity;      // 角速度
        public Vector3 originalLocalPosition; // 原始本地位置
        public Vector3 originalLocalRotation; // 原始本地旋转
        public Vector3 originalLocalScale;    // 原始本地缩放
    }

    // 破碎效果私有变量
    private List<FragmentData> fragments = new List<FragmentData>();
    private bool isFragmentSimulating = false;
    private float fragmentStartTime = 0f;
    private Vector3 lastHitDirection = Vector3.zero;
    private bool hasGridCleared = false;  // 是否已清理网格占位
    private bool hasObjectDestroyed = false;  // 是否已销毁物体

    #endregion



    /// <summary>
    /// 初始化金猪
    /// </summary>
    public void Init(TileCube_Data_GoldPig data)
    {
        pigData = data;
        ObjectType = TileCube_ObjectType.GoldPig;

        // 记录原始旋转角度（用于倾斜效果）
        originalRotation = transform.localEulerAngles;

        // 记录原始缩放（用于击打变大效果）
        originalLocalScale = transform.localScale;
        currentMeshScale = originalLocalScale;
        targetMeshScale = originalLocalScale;

        // 记录mesh原始信息（保留用于其他功能）
        if (meshRenderer != null)
        {
            originalMeshScale = meshRenderer.transform.localScale;
            originalMeshRotation = meshRenderer.transform.localEulerAngles;
        }

        // 初始化倾倒功能 - 一开始设置为起立状态
        targetMeshRotationX = standUpRotationX;
        currentMeshRotationX = standUpRotationX;
        hasObstacleInFront = true; // 默认起立状态

        // 立即设置meshRenderer的X旋转为起立角度
        if (meshRenderer != null)
        {
            Vector3 meshRotation = meshRenderer.transform.localEulerAngles;
            meshRotation.x = standUpRotationX;
            meshRenderer.transform.localEulerAngles = meshRotation;
        }

        // 初始化批处理优化
        InitializeBatchingOptimization();

        // 设置击打次数
        if (data.HitValues > 0)
            RequiredHits = data.HitValues;
        else
            RequiredHits = 5; // 默认5次

        // 应用金色
        ApplyGoldColor();
    }

    /// <summary>
    /// 初始化批处理优化
    /// </summary>
    private void InitializeBatchingOptimization()
    {
        if (meshRenderer == null) return;

        // 只创建一次PropertyBlock
        if (propertyBlock == null)
        {
            propertyBlock = new MaterialPropertyBlock();
            //Debug.Log($"[{gameObject.name}] PropertyBlock创建完成");
        }

        // 获取当前状态
        meshRenderer.GetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 应用金色到材质
    /// </summary>
    protected virtual void ApplyGoldColor()
    {
        if (meshRenderer == null) return;

        // 直接设置金色，无需重复创建PropertyBlock
        Tool_MaterialBatchOptimizer.SetColor(meshRenderer, propertyBlock, GoldColor);
    }

    /// <summary>
    /// 检查是否可以堆叠到指定位置
    /// </summary>
    public bool CanStackAt(int gridX, int gridZ, int height)
    {
        if (!CanStack) return false;
        if (height >= MaxStackHeight) return false;

        // 检查下方是否有支撑
        if (height > 0)
        {
            var tileGrid = TileCube_Controller.Instance?.TileGrid;
            if (tileGrid != null)
            {
                var belowObject = tileGrid.GetObjectAtPosition(gridX, gridZ, height - 1);
                return belowObject != null; // 需要下方有物体支撑
            }
        }

        return true;
    }

    /// <summary>
    /// 尝试堆叠到另一个物体上
    /// </summary>
    public bool TryStackOn(TileCube_Object otherObject)
    {
        if (otherObject == null || !CanStack) return false;

        var otherGridInfo = otherObject.GetGridInfo();
        if (otherGridInfo == null) return false;

        int targetHeight = otherGridInfo.height + 1;

        if (CanStackAt(otherGridInfo.gridX, otherGridInfo.gridZ, targetHeight))
        {
            // 执行堆叠逻辑
            var tileGrid = TileCube_Controller.Instance?.TileGrid;
            if (tileGrid != null)
            {
                // 先从当前位置移除
                tileGrid.RemoveObject(gameObject);

                // 放置到新位置
                var newGridInfo = tileGrid.PlaceObject(gameObject, otherGridInfo.gridX, otherGridInfo.gridZ, targetHeight, 1, 1);
                if (newGridInfo != null)
                {
                    SetGridInfo(newGridInfo);
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 检查是否在最高层
    /// </summary>
    public bool IsAtTopLayer()
    {
        if (gridInfo == null) return true;

        var tileGrid = TileCube_Controller.Instance?.TileGrid;
        if (tileGrid != null)
        {
            // 检查上方是否还有物体
            var aboveObject = tileGrid.GetObjectAtPosition(gridInfo.gridX, gridInfo.gridZ, gridInfo.height + 1);
            return aboveObject == null;
        }

        return true;
    }

    /// <summary>
    /// 获取堆叠高度（从底部开始计算）
    /// </summary>
    public int GetStackHeight()
    {
        if (gridInfo == null) return 0;

        var tileGrid = TileCube_Controller.Instance?.TileGrid;
        if (tileGrid == null) return gridInfo.height;

        int height = 0;
        // 从底部开始计算
        for (int h = 0; h <= gridInfo.height; h++)
        {
            var obj = tileGrid.GetObjectAtPosition(gridInfo.gridX, gridInfo.gridZ, h);
            if (obj != null)
            {
                height++;
            }
        }

        return height;
    }

    #region 网格信息重写

    /// <summary>
    /// 重写设置网格信息，初始化压缩功能相关数据
    /// </summary>
    public override void SetGridInfo(GridTileObject info)
    {
        base.SetGridInfo(info);

        // 初始化lastKnownGridZ并立即检查是否应该在压缩区域
        if (info != null)
        {
            lastKnownGridZ = info.gridZ;

            // 立即检查初始状态是否应该在压缩区域
            var controller = TileCube_Controller.Instance;
            if (controller != null)
            {
                bool shouldBeInCompressionZone = info.gridZ > controller.HeightProp_ClearColorGrid_goldPigCompressionZThreshold;
                //Debug.Log($"[GoldPig {name}] 初始化压缩状态检查 - Z: {info.gridZ}, 阈值: {controller.HeightProp_ClearColorGrid_goldPigCompressionZThreshold}, 应该在压缩区域: {shouldBeInCompressionZone}");

                if (shouldBeInCompressionZone && !isInCompressionZone)
                {
                    //Debug.Log($"[GoldPig {name}] 初始化时进入压缩区域");
                    EnterCompressionZone(controller);
                }
            }
        }
    }

    #endregion

    #region 击打销毁逻辑

    /// <summary>
    /// 物体被击打时的处理逻辑
    /// </summary>
    public virtual void OnHit(int damage = 1)
    {
        if (isDestroying) return;

        CurrentHits += damage;

        // 触发进度更新（金猪每次被击打减对应伤害值）
        TileCube_Controller.Instance?.UpdateProgress(damage);

        //Debug.Log($"金猪 {name} 被击打，当前击打次数: {CurrentHits}/{RequiredHits}");

        // 添加击打变大效果
        AddHitScaleEffect();

        // 可以在这里添加击打效果，比如颜色变化、缩放等
        UpdateVisualBasedOnHits();

        // 检查是否达到销毁条件
        if (CurrentHits >= RequiredHits)
        {
            StartDestroySequence();
        }
    }

    /// <summary>
    /// 根据击打次数更新视觉效果
    /// </summary>
    private void UpdateVisualBasedOnHits()
    {

    }

    /// <summary>
    /// 开始销毁序列
    /// </summary>
    private void StartDestroySequence()
    {
        if (isDestroying) return;
        isDestroying = true;


        StartCoroutine(BrokeAndDestroy());
    }

    /// <summary>
    /// 破碎并销毁的动画（强制执行）
    /// </summary>
    private IEnumerator BrokeAndDestroy()
    {
        // 1. 移动broke_Child到金猪第一层，保持broke_Child原有状态
        if (broke_Child != null)
        {
            broke_Child.transform.SetParent(transform, false);
            // broke_Child保持自己原有的localPosition, localRotation, localScale
        }

        // 2. 隐藏meshRenderer
        if (meshRenderer != null)
        {
            meshRenderer.gameObject.SetActive(false);
        }

        // 3. 激活broke_Child
        if (broke_Child != null)
        {
            broke_Child.SetActive(true);
        }

        // 4. 初始化破碎块数据
        InitializeFragments();

        // 5. 开始破碎模拟
        StartFragmentSimulation();

        yield return null;

        // 不立即销毁，等待破碎模拟完成
    }

    /// <summary>
    /// 通过Controller销毁物体（推荐的销毁方式）
    /// </summary>
    private void DestroyByController()
    {
        // 使用基类的DestroyObject方法，它会正确处理网格移除和Controller列表清理
        DestroyObject();
    }




    public override void OnRebound()
    {

        // 检测网格位置变化，处理高度压缩
        CheckHeightCompression();

        // 推进完成时触发果冻刹车效果
        if (tool_JRJELLY_Simple != null)
        {
            // 检查前方格子是否有物体
            if (HasObjectInFront() || gridInfo.gridZ == 0)
            {
                // 将2D方向转换为3D方向（Y轴设为0，保持在水平面）
                Vector3 brakeDirection = -transform.forward;
                bool success = tool_JRJELLY_Simple.TriggerRebound(brakeDirection, pushBrakeIntensity, 0.3f);


            }

        }
    }

    /// <summary>
    /// 检查前方格子是否有物体
    /// </summary>
    /// <returns>前方是否有物体</returns>
    private bool HasObjectInFront()
    {
        if (gridInfo == null) return false;

        var controller = TileCube_Controller.Instance;
        if (controller?.TileGrid == null) return false;

        // 推进方向是向Z轴负方向，前方位置是gridZ - 1
        int frontGridX = gridInfo.gridX;
        int frontGridZ = gridInfo.gridZ - 1;

        // 检查前方位置的所有高度层是否有物体
        int maxHeight = controller.TileGrid.maxHeightLayers;
        for (int height = 0; height < maxHeight; height++)
        {
            var frontObject = controller.TileGrid.GetObjectAtPosition(frontGridX, frontGridZ, height);
            if (frontObject != null)
            {
                return true; // 找到任意高度层有物体就返回true
            }
        }

        return false; // 所有高度层都没有物体
    }

    #endregion

    #region 子弹检测重写方法

    /// <summary>
    /// 重写基类：被子弹瞄准锁定时触发
    /// </summary>
    public override void OnBulletTargeted(GameObject bullet, object bulletData = null)
    {
        base.OnBulletTargeted(bullet, bulletData);
        isByBulletLocked = true;
    }

    /// <summary>
    /// 重写基类：被子弹穿过时触发
    /// </summary>
    public override void OnBulletPenetrate(GameObject bullet, object bulletData = null)
    {
        base.OnBulletPenetrate(bullet, bulletData);

        if (tool_JRJELLY_Simple == null || isDestroying) return;

        // 如果正在销毁，不再处理穿过效果
        if (isDestroying)
        {
            //Debug.Log($"[{name}] 正在销毁中，忽略子弹穿过效果");
            return;
        }

        // 检查是否正在甩动，如果是则返回
        if (tool_JRJELLY_Simple.HasActiveEffects)
        {
            //Debug.Log($"[{name}] 正在甩动中，忽略子弹穿过");
            return;
        }

        Vector3 bulletPos = bullet.transform.position;

        // 直接使用固定的甩动强度，不计算距离影响
        float finalSwingIntensity = bulletPassSwingIntensity;

        // 触发甩动效果
        bool success = tool_JRJELLY_Simple.TriggerSwing(bulletPos, finalSwingIntensity, bulletPassSwingDuration);

        // 添加倾斜效果
        AddTiltEffect(bulletPos, bulletPassTiltIntensity, bulletPassSwingDuration, false);

        if (success)
        {
            //Debug.Log($"[{name}] 子弹穿过甩动 - 固定强度: {finalSwingIntensity:F2}");
        }
    }

    /// <summary>
    /// 重写基类：被子弹击中时触发
    /// </summary>
    public override bool OnBulletHit(GameObject bullet, object bulletData = null)
    {
        base.OnBulletHit(bullet, bulletData);

        if (isDestroying) return true; // 如果正在销毁，直接销毁子弹
        if (tool_JRJELLY_Simple == null) return false;



        float swingIntensity = bulletHitSwingIntensity;


        // 击中时强制停止所有正在进行的果冻效果，确保击中效果优先执行
        if (!tool_JRJELLY_Simple.HasActiveEffects)
        {

            bool swingSuccess = tool_JRJELLY_Simple.TriggerSwing(bullet.transform.position, swingIntensity, bulletHitSwingDuration);

        }

        // 添加倾斜效果
        AddTiltEffect(bullet.transform.position, bulletHitTiltIntensity, bulletHitSwingDuration, true);


        // 记录击打方向（用于破碎效果）
        Vector3 hitDirection = (transform.position - bullet.transform.position).normalized;
        hitDirection.y = 0; // 只考虑水平方向
        lastHitDirection = hitDirection;

        // 强制触发击打逻辑
        OnHit(1);

        // 金猪被击中后销毁子弹
        return true;
    }

    #endregion

    #region Unity生命周期

    void Update()
    {
        // 破碎模拟
        if (isFragmentSimulating)
        {
            UpdateFragmentSimulation();

            float elapsedTime = Time.time - fragmentStartTime;

            // 检查是否到达清理网格占位时间
            if (!hasGridCleared && elapsedTime >= gridClearTime)
            {
                TileCube_Controller.OnPigBroke?.Invoke(this);
                RemoveFromList();
                hasGridCleared = true;
            }

            // 检查是否到达销毁物体时间
            if (!hasObjectDestroyed && elapsedTime >= objectDestroyTime)
            {
                // 停止所有协程
                StopAllCoroutines();
                DestroyGameObj();
                hasObjectDestroyed = true;
                return;
            }
        }

        // 更新倾斜效果
        UpdateTiltEffect();

        // 更新缩放效果
        UpdateScaleEffect();

        // 检测前方阻挡并更新倾倒状态
        CheckObstacleAndUpdateFallState();

        // 更新mesh倾倒旋转效果
        UpdateMeshFallRotation();
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();

        // 停止压缩协程
        if (compressionCoroutine != null)
        {
            StopCoroutine(compressionCoroutine);
            compressionCoroutine = null;
        }

        // 清理破碎块数据
        if (fragments != null)
        {
            fragments.Clear();
        }

        StopAllCoroutines();
    }

    #endregion

    #region 倾斜效果系统

    /// <summary>
    /// 更新倾斜效果
    /// </summary>
    private void UpdateTiltEffect()
    {
        // 清理已过期的倾斜方向
        float currentTime = Time.time;
        for (int i = activeTiltDirections.Count - 1; i >= 0; i--)
        {
            if (currentTime >= activeTiltEndTimes[i])
            {
                activeTiltDirections.RemoveAt(i);
                activeTiltEndTimes.RemoveAt(i);
            }
        }

        // 计算合成的目标倾斜角度
        Vector3 combinedTiltDirection = Vector3.zero;
        for (int i = 0; i < activeTiltDirections.Count; i++)
        {
            // 后加入的倾斜效果权重更高，用于处理连续击中
            float weight = 1f + (i * multiHitTiltMultiplier);
            combinedTiltDirection += activeTiltDirections[i] * weight;
        }

        // 限制最大倾斜角度
        targetTiltRotation = Vector3.ClampMagnitude(combinedTiltDirection, Mathf.Max(bulletPassTiltIntensity, bulletHitTiltIntensity));

        // 如果没有活跃的倾斜效果，目标角度回归原始角度
        if (activeTiltDirections.Count == 0)
        {
            targetTiltRotation = Vector3.zero;
        }

        // 平滑插值到目标角度，使用更高的插值速度避免闪烁
        currentTiltRotation = Vector3.Lerp(currentTiltRotation, targetTiltRotation, Time.deltaTime * tiltRecoverySpeed);

        // 应用倾斜旋转到父物体transform，基于原始角度
        Vector3 finalRotation = originalRotation + currentTiltRotation;
        transform.localEulerAngles = finalRotation;
    }

    /// <summary>
    /// 更新缩放效果
    /// </summary>
    private void UpdateScaleEffect()
    {
        // 平滑插值到目标缩放，使用更快的插值速度实现平滑过渡
        float lerpSpeed = 1f / hitScaleTransitionTime; // 根据过渡时间计算插值速度
        currentMeshScale = Vector3.Lerp(currentMeshScale, targetMeshScale, Time.deltaTime * lerpSpeed);

        // 应用缩放到父物体transform
        transform.localScale = currentMeshScale;

        // 检查是否接近目标缩放
        if (Vector3.Distance(currentMeshScale, targetMeshScale) < 0.001f)
        {
            currentMeshScale = targetMeshScale;
            transform.localScale = currentMeshScale;
        }
    }

    /// <summary>
    /// 根据子弹位置计算倾斜方向（与子弹位置相反，穿过和击中都影响XZ轴）
    /// </summary>
    /// <param name="bulletPosition">子弹世界坐标</param>
    /// <param name="isHit">是否为击中</param>
    /// <returns>倾斜方向向量</returns>
    private Vector3 CalculateTiltDirection(Vector3 bulletPosition, bool isHit)
    {
        // 计算从子弹到金猪的方向
        Vector3 bulletToGoldPig = transform.position - bulletPosition;
        bulletToGoldPig.y = 0; // 忽略Y轴差异，只考虑水平面

        // 如果距离太近，使用默认倾斜方向
        if (bulletToGoldPig.magnitude < 0.1f)
        {
            return new Vector3(-10f, 0, 10f); // 默认前倾+右倾
        }

        // 归一化方向向量
        bulletToGoldPig = bulletToGoldPig.normalized;

        // 穿过和击中都影响XZ轴（前后+左右倾斜）
        Vector3 tiltDirection = new Vector3(
            -bulletToGoldPig.z,  // 子弹在前方时，金猪向前倾斜（负X轴旋转）
            0,
            bulletToGoldPig.x    // 子弹在右方时，金猪向左倾斜（正Z轴旋转）
        );

        return tiltDirection;
    }

    /// <summary>
    /// 添加倾斜效果
    /// </summary>
    /// <param name="bulletPosition">子弹位置</param>
    /// <param name="intensity">倾斜强度</param>
    /// <param name="duration">持续时间</param>
    /// <param name="isHit">是否为击中</param>
    private void AddTiltEffect(Vector3 bulletPosition, float intensity, float duration, bool isHit)
    {
        if (isDestroying) return;

        // 计算倾斜方向
        Vector3 tiltDirection = CalculateTiltDirection(bulletPosition, isHit) * intensity;

        // 添加到活跃倾斜列表
        activeTiltDirections.Add(tiltDirection);
        activeTiltEndTimes.Add(Time.time + duration);

        //Debug.Log($"[{name}] 添加倾斜效果 - 子弹位置: {bulletPosition}, 倾斜方向: {tiltDirection}, 强度: {intensity}, 持续时间: {duration}, 击中: {isHit}");
    }

    /// <summary>
    /// 添加击打变大效果
    /// </summary>
    private void AddHitScaleEffect()
    {
        // 根据当前击打次数和最大击打次数计算缩放比例
        float hitProgress = (float)CurrentHits / RequiredHits; // 击打进度 (0 到 1)

        // 计算目标缩放：从初始缩放线性插值到最大缩放
        float targetScale = Mathf.Lerp(originalLocalScale.x, maxScaleLimit, hitProgress);

        // 设置目标缩放（统一XYZ轴）
        targetMeshScale = Vector3.one * targetScale;

        //Debug.Log($"[{name}] 击打变大效果 - 击打进度: {hitProgress:F2}, 目标缩放: {targetScale:F2}, 当前击打次数: {CurrentHits}/{RequiredHits}");
    }

    #endregion

    #region 高度压缩功能

    #region 高度压缩功能方法

    /// <summary>
    /// 检测高度压缩状态
    /// </summary>
    private void CheckHeightCompression()
    {
        if (gridInfo == null)
        {
            return;
        }

        var controller = TileCube_Controller.Instance;
        if (controller == null)
        {
            return;
        }

        int currentGridZ = gridInfo.gridZ;

        // 检测网格Z坐标是否发生变化
        if (lastKnownGridZ != currentGridZ)
        {
            lastKnownGridZ = currentGridZ;

            // 判断是否进入或离开压缩区域
            bool shouldBeInCompressionZone = currentGridZ > controller.HeightProp_ClearColorGrid_goldPigCompressionZThreshold;

            //Debug.Log($"[GoldPig {name}] Z坐标变化 - 应该在压缩区域: {shouldBeInCompressionZone} (Z: {currentGridZ} > 阈值: {controller.HeightProp_ClearColorGrid_goldPigCompressionZThreshold})");

            if (shouldBeInCompressionZone && !isInCompressionZone)
            {
                // 进入压缩区域
                //Debug.Log($"[GoldPig {name}] 进入压缩区域，Z坐标: {currentGridZ}, 高度层: {gridInfo.height}");
                EnterCompressionZone(controller);
            }
            else if (!shouldBeInCompressionZone && isInCompressionZone)
            {
                // 离开压缩区域
                //Debug.Log($"[GoldPig {name}] 离开压缩区域，Z坐标: {currentGridZ}, 高度层: {gridInfo.height}");
                ExitCompressionZone(controller);
            }
        }
    }

    /// <summary>
    /// 进入压缩区域
    /// </summary>
    private void EnterCompressionZone(TileCube_Controller controller)
    {
        isInCompressionZone = true;

        if (compressionCoroutine != null)
        {
            StopCoroutine(compressionCoroutine);
        }

        compressionCoroutine = StartCoroutine(ProcessCompression(controller));
    }

    /// <summary>
    /// 离开压缩区域
    /// </summary>
    private void ExitCompressionZone(TileCube_Controller controller)
    {
        isInCompressionZone = false;

        if (compressionCoroutine != null)
        {
            StopCoroutine(compressionCoroutine);
        }

        // 只让同一位置的最低高度层的块来处理恢复，避免重复处理
        if (IsLowestGoldPigAtPosition())
        {
            compressionCoroutine = StartCoroutine(ProcessRestore(controller));
        }
    }

    /// <summary>
    /// 处理压缩过程
    /// </summary>
    private IEnumerator ProcessCompression(TileCube_Controller controller)
    {
        isCompressing = true;
        int currentHeight = gridInfo.height;

        if (currentHeight == controller.HeightProp_ClearColorGrid_goldPigCompressionHeightA)
        {
            // 高度层A：基于当前击打变大缩放进行压缩，而不是原始缩放
            Vector3 compressedScale = currentMeshScale; // 使用当前的击打变大缩放
            compressedScale.y = currentMeshScale.y * controller.HeightProp_ClearColorGrid_goldPigCompressedScale;
            transform.localScale = compressedScale;
            //Debug.Log($"[GoldPig {name}] 高度层{currentHeight}立即压缩，从{currentMeshScale.y}直接设置为{compressedScale.y}");
        }
        else if (currentHeight >= controller.HeightProp_ClearColorGrid_goldPigCompressionHeightB)
        {
            // 高度层B及以上：隐藏并设置Y缩放为0，但保持XZ轴的击打变大效果
            SetVisibility(false);
            Vector3 targetScale = currentMeshScale; // 保持击打变大的XZ缩放
            targetScale.y = 0f; // 只将Y轴设为0
            transform.localScale = targetScale;
            //Debug.Log($"[GoldPig {name}] 高度层{currentHeight}立即隐藏");
        }

        isCompressing = false;
        yield break; // 由于没有动画，立即结束协程
    }

    /// <summary>
    /// 处理恢复过程
    /// </summary>
    private IEnumerator ProcessRestore(TileCube_Controller controller)
    {
        isRestoring = true;

        // 获取当前网格位置的所有高度层GoldPig
        var allGoldPigsAtPosition = GetAllGoldPigsAtSamePosition();

        // 按高度层从低到高排序，只处理需要恢复的高度层
        var goldPigsToRestore = allGoldPigsAtPosition
            .Where(pig => pig.gridInfo.height >= controller.HeightProp_ClearColorGrid_goldPigCompressionHeightA)
            .OrderBy(pig => pig.gridInfo.height)
            .ToList();

        //Debug.Log($"[GoldPig {name}] 开始恢复过程，共{goldPigsToRestore.Count}个金猪需要恢复");

        // 逐层恢复
        for (int i = 0; i < goldPigsToRestore.Count; i++)
        {
            var pig = goldPigsToRestore[i];

            // 显示物体（如果之前被隐藏）
            pig.SetVisibility(true);

            // 判断是否为最后一层
            bool isLastLayer = (i == goldPigsToRestore.Count - 1);

            if (isLastLayer)
            {
                // 最后一层：先缩放到当前击打变大值+弹跳值，再缩放回当前击打变大值
                //Debug.Log($"[GoldPig {pig.name}] 最后一层恢复，高度层{pig.gridInfo.height}，带弹跳效果");
                float targetY = pig.currentMeshScale.y; // 使用当前击打变大的Y值
                yield return StartCoroutine(pig.AnimateScaleY(pig.transform.localScale.y, targetY + controller.HeightProp_ClearColorGrid_goldPigBounceScale, controller.HeightProp_ClearColorGrid_goldPigCompressionAnimationDuration * 0.6f));
                yield return StartCoroutine(pig.AnimateScaleY(targetY + controller.HeightProp_ClearColorGrid_goldPigBounceScale, targetY, controller.HeightProp_ClearColorGrid_goldPigCompressionAnimationDuration * 0.4f));
            }
            else
            {
                // 其他层：直接恢复到当前击打变大值
                //Debug.Log($"[GoldPig {pig.name}] 恢复高度层{pig.gridInfo.height}，从{pig.transform.localScale.y}到{pig.currentMeshScale.y}");
                yield return StartCoroutine(pig.AnimateScaleY(pig.transform.localScale.y, pig.currentMeshScale.y, controller.HeightProp_ClearColorGrid_goldPigCompressionAnimationDuration));
            }

            // 等待当前层恢复完成后再处理下一层
            yield return new WaitForSeconds(0.1f);
        }

        isRestoring = false;
    }

    /// <summary>
    /// 获取同一网格位置的所有GoldPig
    /// </summary>
    private List<TileCube_GoldPig> GetAllGoldPigsAtSamePosition()
    {
        var result = new List<TileCube_GoldPig>();
        var controller = TileCube_Controller.Instance;
        if (controller == null || gridInfo == null) return result;

        // 遍历所有GoldPig，找到同一位置的
        foreach (var obj in controller.Current_Objects)
        {
            if (obj is TileCube_GoldPig goldPig && goldPig.gridInfo != null)
            {
                if (goldPig.gridInfo.gridX == gridInfo.gridX && goldPig.gridInfo.gridZ == gridInfo.gridZ)
                {
                    result.Add(goldPig);
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 检查当前金猪是否为同一位置的最低高度层金猪
    /// </summary>
    private bool IsLowestGoldPigAtPosition()
    {
        var allGoldPigsAtPosition = GetAllGoldPigsAtSamePosition();
        if (allGoldPigsAtPosition.Count == 0) return true;

        int minHeight = allGoldPigsAtPosition.Min(pig => pig.gridInfo.height);
        return gridInfo.height == minHeight;
    }

    /// <summary>
    /// 动画缩放Y轴
    /// </summary>
    private IEnumerator AnimateScaleY(float fromY, float toY, float duration)
    {
        Vector3 startScale = transform.localScale;
        Vector3 targetScale = startScale;
        startScale.y = fromY;
        targetScale.y = toY;

        float elapsed = 0f;
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;

            // 使用平滑插值
            float currentY = Mathf.Lerp(fromY, toY, Mathf.SmoothStep(0f, 1f, progress));
            Vector3 currentScale = transform.localScale;
            currentScale.y = currentY;
            transform.localScale = currentScale;

            yield return null;
        }

        // 确保最终值准确
        Vector3 finalScale = transform.localScale;
        finalScale.y = toY;
        transform.localScale = finalScale;
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 设置物体可见性
    /// </summary>
    private void SetVisibility(bool visible)
    {
        if (meshRenderer != null)
        {
            meshRenderer.enabled = visible;
        }

        // 如果有碰撞体，也要控制
        var collider = GetComponent<Collider>();
        if (collider != null)
        {
            collider.enabled = visible;
        }
    }

    #endregion

    #endregion

    #region 破碎效果系统

    /// <summary>
    /// 初始化破碎块数据
    /// </summary>
    private void InitializeFragments()
    {
        fragments.Clear();

        if (broke_Child == null) return;

        // 获取broke_Child第一层所有子物体
        for (int i = 0; i < broke_Child.transform.childCount; i++)
        {
            Transform child = broke_Child.transform.GetChild(i);

            FragmentData fragmentData = new FragmentData
            {
                transform = child,
                originalLocalPosition = child.localPosition,
                originalLocalRotation = child.localEulerAngles,
                originalLocalScale = child.localScale,
                velocity = Vector3.zero,
                angularVelocity = Vector3.zero
            };

            // 计算爆炸力
            CalculateFragmentForces(fragmentData);

            fragments.Add(fragmentData);
        }
    }

    /// <summary>
    /// 计算破碎块的爆炸力
    /// </summary>
    private void CalculateFragmentForces(FragmentData fragment)
    {
        // 1. 径向爆炸力（以broke_Child中心为基准）
        Vector3 direction = (fragment.transform.position - broke_Child.transform.position).normalized;
        if (direction.magnitude < 0.1f) direction = Random.insideUnitSphere.normalized;

        // 2. 基础爆炸力
        Vector3 explosiveForce = direction * explosionForce;

        // 3. 向上力
        explosiveForce.y += upwardForce;

        // 4. 击打方向附加力
        if (lastHitDirection != Vector3.zero)
        {
            // 根据击打方向判断左中右
            float dotProduct = Vector3.Dot(lastHitDirection.normalized, Vector3.right);
            Vector3 hitForce = Vector3.zero;

            if (dotProduct > 0.3f) // 右侧击打
            {
                hitForce = Vector3.right * hitDirectionForce;
            }
            else if (dotProduct < -0.3f) // 左侧击打
            {
                hitForce = Vector3.left * hitDirectionForce;
            }
            else // 中间击打
            {
                hitForce = lastHitDirection * hitDirectionForce;
            }

            explosiveForce += hitForce;
        }

        // 5. 添加随机性
        explosiveForce += Random.insideUnitSphere * (explosionForce * 0.3f);

        // 6. 设置初始速度和角速度
        fragment.velocity = explosiveForce;
        fragment.angularVelocity = enableFragmentRotation ? Random.insideUnitSphere * 360f : Vector3.zero;
    }

    /// <summary>
    /// 开始破碎模拟
    /// </summary>
    private void StartFragmentSimulation()
    {
        TileCube_Controller.On_PlaySound?.Invoke("CNT_金猪破碎");
        isFragmentSimulating = true;
        fragmentStartTime = Time.time;
        hasGridCleared = false;
        hasObjectDestroyed = false;

        // 停止所有其他效果
        if (tool_JRJELLY_Simple != null)
        {
            tool_JRJELLY_Simple.StopAll();
        }
    }

    /// <summary>
    /// 更新破碎模拟
    /// </summary>
    private void UpdateFragmentSimulation()
    {
        float deltaTime = Time.deltaTime;

        foreach (var fragment in fragments)
        {
            if (fragment.transform == null) continue;

            // 应用重力
            fragment.velocity.y -= 9.81f * gravityMultiplier * deltaTime;

            // 更新位置
            fragment.transform.position += fragment.velocity * deltaTime;

            // 更新旋转（如果启用）
            if (enableFragmentRotation)
            {
                fragment.transform.Rotate(fragment.angularVelocity * deltaTime);
            }
        }
    }

    #endregion

    #region 前方阻挡检测倾倒功能

    /// <summary>
    /// 检测前方阻挡并更新倾倒状态
    /// </summary>
    private void CheckObstacleAndUpdateFallState()
    {
        // 按间隔时间检测
        if (Time.time - lastObstacleCheckTime < obstacleCheckInterval)
            return;

        lastObstacleCheckTime = Time.time;

        // 检测前方是否有阻挡
        bool obstacleDetected = HasObstacleInFront();

        // 如果检测结果与当前状态不同，更新倾倒状态
        if (obstacleDetected != hasObstacleInFront)
        {
            hasObstacleInFront = obstacleDetected;

            // 根据阻挡情况设置目标旋转角度
            if (hasObstacleInFront)
            {
                // 有阻挡：起立
                targetMeshRotationX = standUpRotationX;
                //Debug.Log($"[GoldPig {name}] 检测到前方有阻挡，切换到起立状态");
            }
            else
            {
                // 无阻挡：倾倒
                targetMeshRotationX = fallDownRotationX;
                //Debug.Log($"[GoldPig {name}] 检测到前方无阻挡，切换到倾倒状态");
            }
        }
    }

    /// <summary>
    /// 检测前方Z格是否有障碍物（只检查同一层高度）
    /// </summary>
    /// <returns>前方是否有障碍物</returns>
    private bool HasObstacleInFront()
    {
        if (gridInfo == null) return true; // 没有网格信息时默认为有阻挡

        var controller = TileCube_Controller.Instance;
        if (controller?.TileGrid == null) return true;

        // 前方位置是gridZ - 1（推进方向是向Z轴负方向）
        int frontGridX = gridInfo.gridX;
        int frontGridZ = gridInfo.gridZ - 1;
        int currentHeight = gridInfo.height; // 当前金猪的高度层

        // 只检查前方位置的同一高度层是否有物体
        var frontObject = controller.TileGrid.GetObjectAtPosition(frontGridX, frontGridZ, currentHeight);

        return frontObject != null; // 同一高度层有物体就返回true，否则返回false
    }

    /// <summary>
    /// 更新mesh倾倒旋转效果
    /// </summary>
    private void UpdateMeshFallRotation()
    {
        if (meshRenderer == null) return;

        // 平滑插值到目标X旋转角度
        currentMeshRotationX = Mathf.Lerp(currentMeshRotationX, targetMeshRotationX, Time.deltaTime * fallTransitionSpeed);

        // 应用旋转到meshRenderer，只修改X轴，保持YZ不变
        Vector3 meshRotation = meshRenderer.transform.localEulerAngles;
        meshRotation.x = currentMeshRotationX;
        meshRenderer.transform.localEulerAngles = meshRotation;

        // 检查是否接近目标角度
        if (Mathf.Abs(currentMeshRotationX - targetMeshRotationX) < 0.1f)
        {
            currentMeshRotationX = targetMeshRotationX;
            meshRotation.x = currentMeshRotationX;
            meshRenderer.transform.localEulerAngles = meshRotation;
        }
    }

    #endregion
}