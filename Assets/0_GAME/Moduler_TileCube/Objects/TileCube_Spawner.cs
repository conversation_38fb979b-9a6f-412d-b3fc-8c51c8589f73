using System.Collections;
using System.Collections.Generic;
using JRJelly;
using TMPro;
using UnityEngine;

/// <summary>
/// 出块库 - 矩形，不可移动，X占2个格，Z占3个格，只向前方（TileGrid推进方向）吐出单一块物体
/// 一个一个地生成，直到库存耗尽则销毁自身
/// 支持预生成：在体内预生成单块，等前方无阻挡时再过渡到前方
/// </summary>
public class TileCube_Spawner : TileCube_Object
{
    [Header("出块库特有属性")]
    public readonly int SpawnerSizeX = 2;   // 出块库X轴尺寸
    public readonly int SpawnerSizeZ = 3;   // 出块库Z轴尺寸
    public List<int> CurrentSpawnColorSingleBlocks;
    public bool IsImmovable = true;         // 不可移动

    [Header("吐出配置")]
    public float SpawnDelay = 1f;           // 吐出延迟时间
    public float SpawnCheckInterval = 0.5f; // 检测间隔

    [Header("预生成配置")]
    public float TransitionDuration = 0.8f; // 单块从预生成位置过渡到前方网格的时间
    public int TransitionCurveType = 1;     // 过渡曲线类型 (0=线性, 1=平滑, 2=快进慢出)
    public bool EnablePreSpawn = true;      // 是否启用预生成功能
    public Vector3 PreSpawnVisualOffset = new Vector3(0, 0.5f, 0); // 预生成单块的等待位置偏移（相对于基础网格位置）
    public Vector3 PreSpawnInitialOffset = new Vector3(0, -0.5f, 0); // 预生成单块的初始生成位置偏移（相对于PreSpawnVisualOffset）
    public float PreSpawnTransitionDuration = 0.6f; // 从初始位置过渡到预生成位置的时间
    public int PreSpawnTransitionCurveType = 1; // 预生成过渡曲线类型 (0=线性, 1=平滑, 2=快进慢出)
    public int MaxPreSpawnBlocks = 2;       // 最大预生成单块数量（左右各一个）

    [Header("销毁配置")]
    public float DestroyDelay = 0.5f;       // 库存耗尽后的销毁延迟
    public float ShrinkDuration = 0.8f;     // 缩小持续时间
    public int ShrinkCurveType = 1;         // 缩小曲线类型 (0=线性, 1=平滑, 2=快进慢出)

    [Header("压缩动画配置")]
    [Tooltip("压缩动画持续时间")]
    public float compressionAnimDuration = 0.6f;
    [Tooltip("X轴缩放值")]
    public float compressionXScale = 1.5f;
    [Tooltip("Y轴缩放值")]
    public float compressionYScale = 0.5f;
    [Tooltip("X轴超出值")]
    public float compressionXOvershoot = 0.1f;
    [Tooltip("Y轴超出值")]
    public float compressionYOvershoot = 0.1f;

    private TileCube_Data_Spawner spawnerData;
    private bool isSpawning = false;        // 是否正在吐出
    private bool isDestroying = false;      // 是否正在销毁
    private Coroutine spawnCheckCoroutine;  // 检测协程
    private int lastSpawnIndex = -1;        // 上次生成的位置索引，用于轮流生成

    // 预生成相关变量
    private List<TileCube_SingleBlock> preSpawnedBlocks = new List<TileCube_SingleBlock>(); // 预生成的单块列表
    private Dictionary<TileCube_SingleBlock, Vector2Int> preSpawnedPositions = new Dictionary<TileCube_SingleBlock, Vector2Int>(); // 预生成单块的位置记录
    private Dictionary<TileCube_SingleBlock, PreSpawnState> preSpawnedStates = new Dictionary<TileCube_SingleBlock, PreSpawnState>(); // 预生成单块的状态记录
    private bool isTransitioning = false;   // 是否正在过渡
    private bool isPreSpawnTransitioning = false; // 是否正在预生成过渡

    // 压缩动画相关
    private Coroutine compressionCoroutine; // 压缩动画协程

    [Header("显示个数")]
    public TextMeshProUGUI txtCount;

    //public Tool_JRJELLY_Simple tool_JRJELLY_Simple;

    /// <summary>
    /// 预生成状态枚举
    /// </summary>
    private enum PreSpawnState
    {
        Initial,        // 初始生成状态（在InitialOffset位置）
        Transitioning,  // 正在过渡到等待位置
        Waiting,        // 在等待位置（PreSpawnVisualOffset）
        ReadyToExit     // 准备出库
    }

    /// <summary>
    /// 初始化出块库
    /// </summary>
    public void Init(TileCube_Data_Spawner data)
    {
        spawnerData = data;
        ObjectType = TileCube_ObjectType.Spawner;

        // 从数据中复制颜色块列表
        if (data.SpawnColorSingleBlocks != null)
        {
            CurrentSpawnColorSingleBlocks = new List<int>(data.SpawnColorSingleBlocks);
        }
        else
        {
            CurrentSpawnColorSingleBlocks = new List<int>();
        }

        // 初始化显示个数
        UpdateCountDisplay();

        // 开始定期检测
        StartSpawnCheck();
    }

    /// <summary>
    /// 更新个数显示
    /// </summary>
    private void UpdateCountDisplay()
    {
        if (txtCount != null)
        {
            // 显示数量 = 库存数量 + 预生成单块数量（因为预生成的还没有真正出库）
            int displayCount = GetRemainingCount() + GetPreSpawnedCount();
            txtCount.text = displayCount.ToString();
        }
    }

    /// <summary>
    /// 开始生成检测
    /// </summary>
    private void StartSpawnCheck()
    {
        if (spawnCheckCoroutine == null)
        {
            spawnCheckCoroutine = StartCoroutine(SpawnCheckLoop());
        }
    }

    /// <summary>
    /// 停止生成检测
    /// </summary>
    private void StopSpawnCheck()
    {
        if (spawnCheckCoroutine != null)
        {
            StopCoroutine(spawnCheckCoroutine);
            spawnCheckCoroutine = null;
        }
    }

    /// <summary>
    /// 生成检测循环
    /// </summary>
    private IEnumerator SpawnCheckLoop()
    {
        while (true)
        {
            yield return new WaitForSeconds(SpawnCheckInterval);

            if (EnablePreSpawn)
            {
                // 预生成模式：检查是否需要预生成、预生成过渡或出库过渡
                if (CanPreSpawn())
                {
                    StartCoroutine(PreSpawnSingleBlock());
                }
                else if (CanPreSpawnTransition())
                {
                    StartCoroutine(PreSpawnTransitionToWaiting());
                }
                else if (CanTransitionToFront())
                {
                    StartCoroutine(TransitionBlockToFront());
                }
            }
            else
            {
                // 原始模式：直接生成到前方
                if (CanSpawn())
                {
                    StartCoroutine(SpawnSingleBlock());
                }
            }

            // 检查是否需要销毁自己
            if (ShouldDestroySelf())
            {
                DestroySelf();
                break;
            }
        }
    }

    /// <summary>
    /// 检查是否可以预生成（在初始位置生成）
    /// </summary>
    private bool CanPreSpawn()
    {
        // 如果正在生成、正在销毁、正在过渡或没有库存，不能预生成
        if (isSpawning || isDestroying || isTransitioning || isPreSpawnTransitioning || TileCube_Spawner_Func.IsInventoryEmpty(CurrentSpawnColorSingleBlocks))
            return false;

        // 检查是否已经达到最大预生成数量
        if (preSpawnedBlocks.Count >= MaxPreSpawnBlocks)
            return false;

        return true;
    }

    /// <summary>
    /// 检查是否可以进行预生成过渡（从初始位置到等待位置）
    /// </summary>
    private bool CanPreSpawnTransition()
    {
        // 如果正在预生成过渡，不能再次过渡
        if (isPreSpawnTransitioning)
            return false;

        // 检查是否有处于初始状态的预生成块
        foreach (var block in preSpawnedBlocks)
        {
            if (block != null && preSpawnedStates.ContainsKey(block) && preSpawnedStates[block] == PreSpawnState.Initial)
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 检查是否可以将预生成的单块过渡到前方
    /// </summary>
    private bool CanTransitionToFront()
    {
        // 如果正在过渡、没有预生成的单块，不能过渡
        if (isTransitioning || preSpawnedBlocks.Count == 0)
            return false;

        // 检查是否有处于等待状态的预生成块，且其对应的前方位置是空的
        foreach (var block in preSpawnedBlocks)
        {
            if (block != null && preSpawnedStates.ContainsKey(block) && preSpawnedStates[block] == PreSpawnState.Waiting)
            {
                // 检查这个预生成块对应的前方位置是否可用
                if (preSpawnedPositions.ContainsKey(block))
                {
                    Vector2Int preSpawnPos = preSpawnedPositions[block];
                    Vector2Int correspondingFrontPos = new Vector2Int(preSpawnPos.x, preSpawnPos.y - 1); // 前方位置

                    if (IsFrontPositionAvailable(correspondingFrontPos))
                    {
                        return true; // 找到至少一个可以过渡的预生成块
                    }
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 检查是否可以生成（原始模式）
    /// </summary>
    private bool CanSpawn()
    {
        // 如果正在生成、正在销毁或没有库存，不能生成
        if (isSpawning || isDestroying || TileCube_Spawner_Func.IsInventoryEmpty(CurrentSpawnColorSingleBlocks))
            return false;

        // 检查前方是否有可用的生成位置
        return HasAvailableSpawnPosition();
    }

    /// <summary>
    /// 检查前方是否有可用的生成位置
    /// </summary>
    private bool HasAvailableSpawnPosition()
    {
        if (gridInfo == null || TileCube_Controller.Instance?.TileGrid == null)
            return false;

        var tileGrid = TileCube_Controller.Instance.TileGrid;

        // 只检查前方位置
        List<Vector2Int> frontPositions = GetFrontPositions();

        // 检查是否有至少一个空位
        foreach (var pos in frontPositions)
        {
            GameObject existingObj = tileGrid.GetObjectAtPosition(pos.x, pos.y, 0);
            if (existingObj == null)
            {
                return true; // 找到至少一个空位
            }
        }

        return false;
    }

    /// <summary>
    /// 检查特定前方位置是否可用
    /// </summary>
    private bool IsFrontPositionAvailable(Vector2Int frontPosition)
    {
        if (TileCube_Controller.Instance?.TileGrid == null)
            return false;

        var tileGrid = TileCube_Controller.Instance.TileGrid;
        GameObject existingObj = tileGrid.GetObjectAtPosition(frontPosition.x, frontPosition.y, 0);
        return existingObj == null;
    }

    /// <summary>
    /// 获取前方位置列表
    /// </summary>
    private List<Vector2Int> GetFrontPositions()
    {
        List<Vector2Int> positions = new List<Vector2Int>();

        if (gridInfo == null)
            return positions;

        // 前方 - TileGrid推进方向 (gridZ - 1)
        int frontZ = gridInfo.gridZ - 1;
        for (int x = gridInfo.gridX; x < gridInfo.gridX + SpawnerSizeX; x++)
        {
            positions.Add(new Vector2Int(x, frontZ));
        }

        return positions;
    }

    /// <summary>
    /// 获取出块库内部位置列表（用于预生成）
    /// </summary>
    private List<Vector2Int> GetInternalPositions()
    {
        return TileCube_Spawner_Func.GetInternalPositions(gridInfo, SpawnerSizeX, SpawnerSizeZ);
    }

    /// <summary>
    /// 预生成单一块 - 在初始位置生成
    /// </summary>
    private IEnumerator PreSpawnSingleBlock()
    {
        if (TileCube_Spawner_Func.IsInventoryEmpty(CurrentSpawnColorSingleBlocks))
            yield break;

        isSpawning = true;

        // 延迟生成
        yield return new WaitForSeconds(SpawnDelay);

        var controller = TileCube_Controller.Instance;
        if (controller == null)
        {
            isSpawning = false;
            yield break;
        }

        // 找到一个可用的预生成位置
        Vector2Int? preSpawnPosition = FindAvailablePreSpawnPosition();

        if (preSpawnPosition.HasValue)
        {
            // 从库存中取出一个颜色ID
            List<int> colorsToSpawn = TileCube_Spawner_Func.TakeFromInventory(CurrentSpawnColorSingleBlocks, 1);

            if (colorsToSpawn.Count > 0)
            {
                // 创建预生成单块（在初始位置，不参与网格系统）
                TileCube_SingleBlock preSpawnedBlock = CreatePreSpawnedBlock(colorsToSpawn[0], preSpawnPosition.Value, true);

                if (preSpawnedBlock != null)
                {
                    preSpawnedBlocks.Add(preSpawnedBlock);
                    preSpawnedPositions[preSpawnedBlock] = preSpawnPosition.Value; // 记录位置
                    preSpawnedStates[preSpawnedBlock] = PreSpawnState.Initial; // 设置初始状态

                    // 预生成时不更新计数显示，等真正出库到网格时再更新

                    //Debug.Log($"出块库在初始位置预生成了 1 个SingleBlock，预生成位置：({preSpawnPosition.Value.x}, {preSpawnPosition.Value.y})，初始偏移：{PreSpawnInitialOffset}，剩余库存：{TileCube_Spawner_Func.GetInventoryCount(CurrentSpawnColorSingleBlocks)}");
                }
            }
        }

        isSpawning = false;
    }

    /// <summary>
    /// 预生成过渡 - 从初始位置过渡到等待位置
    /// </summary>
    private IEnumerator PreSpawnTransitionToWaiting()
    {
        // 找到第一个处于初始状态的预生成块
        TileCube_SingleBlock blockToTransition = null;
        foreach (var block in preSpawnedBlocks)
        {
            if (block != null && preSpawnedStates.ContainsKey(block) && preSpawnedStates[block] == PreSpawnState.Initial)
            {
                blockToTransition = block;
                break;
            }
        }

        if (blockToTransition == null)
            yield break;




        isPreSpawnTransitioning = true;
        preSpawnedStates[blockToTransition] = PreSpawnState.Transitioning;

        var controller = TileCube_Controller.Instance;
        var tileGrid = controller?.TileGrid;
        if (tileGrid == null)
        {
            isPreSpawnTransitioning = false;
            yield break;
        }

        // 获取预生成位置
        Vector2Int gridPosition = preSpawnedPositions[blockToTransition];

        // 计算起始位置和目标位置
        Vector3 baseWorldPos = tileGrid.GridToWorldPosition(gridPosition.x, gridPosition.y, 0, 1, 1);

        // 根据预生成位置确定左右偏移
        Vector3 adjustedVisualOffset = GetAdjustedVisualOffset(gridPosition);
        Vector3 adjustedInitialOffset = GetAdjustedInitialOffset(gridPosition);

        Vector3 startWorldPos = baseWorldPos + adjustedVisualOffset + adjustedInitialOffset; // 初始位置
        Vector3 targetWorldPos = baseWorldPos + adjustedVisualOffset; // 等待位置

        // 动画过渡
        float elapsed = 0f;
        while (elapsed < PreSpawnTransitionDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / PreSpawnTransitionDuration;

            // 根据曲线类型计算位置
            float curveProgress = TileCube_Spawner_Func.CalculateAnimationProgress(progress, PreSpawnTransitionCurveType);
            Vector3 currentPos = Vector3.Lerp(startWorldPos, targetWorldPos, curveProgress);

            blockToTransition.transform.position = currentPos;
            yield return null;
        }

        // 确保到达目标位置
        blockToTransition.transform.position = targetWorldPos;

        // 更新状态
        preSpawnedStates[blockToTransition] = PreSpawnState.Waiting;
        isPreSpawnTransitioning = false;

        //Debug.Log($"预生成单块完成过渡到等待位置：{targetWorldPos}");
    }

    /// <summary>
    /// 将预生成的单块过渡到前方
    /// </summary>
    private IEnumerator TransitionBlockToFront()
    {
        if (preSpawnedBlocks.Count == 0)
            yield break;

        // 触发压缩动画（有超出阶段）
        PlayCompressionAnimation(true);

        isTransitioning = true;

        // 找到第一个可以过渡的等待状态预生成块（其对应前方位置是空的）
        TileCube_SingleBlock blockToTransition = null;
        Vector2Int targetFrontPosition = Vector2Int.zero;

        foreach (var block in preSpawnedBlocks)
        {
            if (block != null && preSpawnedStates.ContainsKey(block) && preSpawnedStates[block] == PreSpawnState.Waiting)
            {
                if (preSpawnedPositions.ContainsKey(block))
                {
                    Vector2Int preSpawnPos = preSpawnedPositions[block];
                    Vector2Int correspondingFrontPos = new Vector2Int(preSpawnPos.x, preSpawnPos.y - 1); // 对应的前方位置

                    if (IsFrontPositionAvailable(correspondingFrontPos))
                    {
                        blockToTransition = block;
                        targetFrontPosition = correspondingFrontPos;
                        break; // 找到第一个可用的就停止
                    }
                }
            }
        }

        if (blockToTransition != null)
        {
            // 记录原始预生成位置用于调试
            Vector2Int originalPreSpawnPos = preSpawnedPositions.ContainsKey(blockToTransition) ? preSpawnedPositions[blockToTransition] : Vector2Int.zero;

            preSpawnedBlocks.Remove(blockToTransition);
            preSpawnedPositions.Remove(blockToTransition); // 移除位置记录
            preSpawnedStates.Remove(blockToTransition); // 移除状态记录

            // 开始过渡动画
            yield return StartCoroutine(AnimateBlockTransition(blockToTransition, targetFrontPosition));

            // 触发生成事件
            TileCube_Controller.OnSpawnerEject?.Invoke(this, null);

            // 真正出库到网格时更新个数显示
            UpdateCountDisplay();

            //Debug.Log($"出块库成功过渡了 1 个SingleBlock到其对应前方，预生成位置->前方位置：({originalPreSpawnPos})->({targetFrontPosition.x}, {targetFrontPosition.y})");
        }

        isTransitioning = false;
    }

    /// <summary>
    /// 找到匹配前方位置的预生成单块
    /// </summary>
    private TileCube_SingleBlock FindMatchingPreSpawnedBlock(Vector2Int frontPosition)
    {
        // 根据X坐标匹配：前方位置的X坐标应该与预生成位置的X坐标相同
        foreach (var block in preSpawnedBlocks)
        {
            if (block != null && preSpawnedPositions.ContainsKey(block))
            {
                var blockPos = preSpawnedPositions[block];
                if (blockPos.x == frontPosition.x)
                {
                    return block;
                }
            }
        }

        // 如果没有找到匹配的，返回第一个可用的
        return preSpawnedBlocks.Count > 0 ? preSpawnedBlocks[0] : null;
    }

    /// <summary>
    /// 找到匹配前方位置且处于等待状态的预生成单块
    /// </summary>
    private TileCube_SingleBlock FindMatchingWaitingPreSpawnedBlock(Vector2Int frontPosition)
    {
        // 根据X坐标匹配：前方位置的X坐标应该与预生成位置的X坐标相同，且必须是等待状态
        foreach (var block in preSpawnedBlocks)
        {
            if (block != null && preSpawnedPositions.ContainsKey(block) && preSpawnedStates.ContainsKey(block))
            {
                var blockPos = preSpawnedPositions[block];
                var blockState = preSpawnedStates[block];
                if (blockPos.x == frontPosition.x && blockState == PreSpawnState.Waiting)
                {
                    return block;
                }
            }
        }

        return null; // 严格匹配，不返回其他块
    }

    /// <summary>
    /// 获取预生成的特定位置（左右两个位置）
    /// </summary>
    private List<Vector2Int> GetPreSpawnPositions()
    {
        return TileCube_Spawner_Func.GetPreSpawnPositions(gridInfo, SpawnerSizeX, SpawnerSizeZ);
    }

    /// <summary>
    /// 根据预生成位置获取调整后的视觉偏移量（左边负偏移，右边正偏移）
    /// </summary>
    /// <param name="gridPosition">预生成位置</param>
    /// <returns>调整后的视觉偏移量</returns>
    private Vector3 GetAdjustedVisualOffset(Vector2Int gridPosition)
    {
        if (gridInfo == null)
            return PreSpawnVisualOffset;

        // 判断是左边还是右边的预生成位置
        // 左边位置：gridPosition.x == gridInfo.gridX
        // 右边位置：gridPosition.x == gridInfo.gridX + 1
        bool isLeftPosition = (gridPosition.x == gridInfo.gridX);

        Vector3 adjustedOffset = PreSpawnVisualOffset;
        if (isLeftPosition)
        {
            // 左边位置：X偏移取负值
            adjustedOffset.x = -Mathf.Abs(PreSpawnVisualOffset.x);
        }
        else
        {
            // 右边位置：X偏移取正值
            adjustedOffset.x = Mathf.Abs(PreSpawnVisualOffset.x);
        }

        return adjustedOffset;
    }

    /// <summary>
    /// 根据预生成位置获取调整后的初始偏移量（左边负偏移，右边正偏移）
    /// </summary>
    /// <param name="gridPosition">预生成位置</param>
    /// <returns>调整后的初始偏移量</returns>
    private Vector3 GetAdjustedInitialOffset(Vector2Int gridPosition)
    {
        if (gridInfo == null)
            return PreSpawnInitialOffset;

        // 判断是左边还是右边的预生成位置
        // 左边位置：gridPosition.x == gridInfo.gridX
        // 右边位置：gridPosition.x == gridInfo.gridX + 1
        bool isLeftPosition = (gridPosition.x == gridInfo.gridX);

        Vector3 adjustedOffset = PreSpawnInitialOffset;
        if (isLeftPosition)
        {
            // 左边位置：X偏移取负值
            adjustedOffset.x = -Mathf.Abs(PreSpawnInitialOffset.x);
        }
        else
        {
            // 右边位置：X偏移取正值
            adjustedOffset.x = Mathf.Abs(PreSpawnInitialOffset.x);
        }

        return adjustedOffset;
    }

    /// <summary>
    /// 找到可用的预生成位置
    /// </summary>
    private Vector2Int? FindAvailablePreSpawnPosition()
    {
        List<Vector2Int> preSpawnPositions = GetPreSpawnPositions();
        var controller = TileCube_Controller.Instance;
        var tileGrid = controller?.TileGrid;

        if (preSpawnPositions.Count == 0 || tileGrid == null)
            return null;

        // 检查哪些位置还没有预生成的单块
        foreach (var pos in preSpawnPositions)
        {
            // 检查该位置是否已经有预生成的单块
            bool positionOccupied = false;
            foreach (var existingBlock in preSpawnedBlocks)
            {
                if (existingBlock != null && preSpawnedPositions.ContainsKey(existingBlock))
                {
                    var blockPos = preSpawnedPositions[existingBlock];
                    if (blockPos.x == pos.x && blockPos.y == pos.y)
                    {
                        positionOccupied = true;
                        break;
                    }
                }
            }

            if (!positionOccupied)
            {
                // 预生成不占用网格位置，只要该位置没有预生成单块就可以使用
                return pos;
            }
        }

        return null;
    }

    /// <summary>
    /// 在指定位置创建单块
    /// </summary>
    private TileCube_SingleBlock CreateSingleBlockAtPosition(int colorId, Vector2Int gridPosition, int height)
    {
        var controller = TileCube_Controller.Instance;
        return TileCube_Spawner_Func.CreateSingleBlockWithReference(colorId, gridPosition, controller, height);
    }

    /// <summary>
    /// 创建预生成单块（不参与网格系统，仅设置视觉位置）
    /// </summary>
    /// <param name="colorId">颜色ID</param>
    /// <param name="gridPosition">网格位置</param>
    /// <param name="useInitialPosition">是否使用初始位置（true=初始位置，false=等待位置）</param>
    private TileCube_SingleBlock CreatePreSpawnedBlock(int colorId, Vector2Int gridPosition, bool useInitialPosition = false)
    {
        var controller = TileCube_Controller.Instance;
        if (controller == null)
            return null;

        // 获取SingleBlock预制体
        GameObject prefab = TileCube_Manager.GetSingleBlockPrefab(colorId);
        if (prefab == null)
        {
            //Debug.LogError($"无法找到颜色ID {colorId} 的SingleBlock预制体");
            return null;
        }

        // 实例化物体
        GameObject obj = Object.Instantiate(prefab, controller.TileCube_Parent.transform);
        TileCube_SingleBlock singleBlock = obj.GetComponent<TileCube_SingleBlock>();

        if (singleBlock == null)
        {
            //Debug.LogError("SingleBlock组件不存在");
            Object.Destroy(obj);
            return null;
        }

        // 创建数据（临时数据，不参与网格）
        var blockData = new TileCube_Data_SingleBlock
        {
            GridX = gridPosition.x,
            GridZ = gridPosition.y,
            Height = 0, // 网格高度层始终为0
            ColorId = colorId
        };

        // 初始化单一块
        singleBlock.Init(blockData);

        // 设置为预留块状态（不能被子弹击中）
        singleBlock.SetPreSpawnState(true);

        // 计算视觉世界位置（应用偏移量）
        var tileGrid = controller.TileGrid;
        if (tileGrid != null)
        {
            Vector3 baseWorldPos = tileGrid.GridToWorldPosition(gridPosition.x, gridPosition.y, 0, 1, 1);
            Vector3 visualWorldPos;

            // 根据预生成位置确定左右偏移
            Vector3 adjustedVisualOffset = GetAdjustedVisualOffset(gridPosition);
            Vector3 adjustedInitialOffset = GetAdjustedInitialOffset(gridPosition);

            if (useInitialPosition)
            {
                // 初始位置：调整后的PreSpawnVisualOffset + 调整后的PreSpawnInitialOffset
                visualWorldPos = baseWorldPos + adjustedVisualOffset + adjustedInitialOffset;
                // //Debug.Log($"预生成单块创建在初始位置：网格位置({gridPosition.x}, {gridPosition.y})，世界位置{baseWorldPos}，初始位置{visualWorldPos}，偏移量{adjustedVisualOffset + adjustedInitialOffset}");
            }
            else
            {
                // 等待位置：调整后的PreSpawnVisualOffset
                visualWorldPos = baseWorldPos + adjustedVisualOffset;
                ////Debug.Log($"预生成单块创建在等待位置：网格位置({gridPosition.x}, {gridPosition.y})，世界位置{baseWorldPos}，等待位置{visualWorldPos}，偏移量{adjustedVisualOffset}");
            }

            obj.transform.position = visualWorldPos;
        }

        // 添加到控制器列表（但不添加到网格系统）
        controller.Current_Objects.Add(singleBlock);



        return singleBlock;
    }

    /// <summary>
    /// 单块过渡动画
    /// </summary>
    private IEnumerator AnimateBlockTransition(TileCube_SingleBlock block, Vector2Int targetPosition)
    {
        if (block == null || block.gameObject == null)
            yield break;

        var controller = TileCube_Controller.Instance;
        var tileGrid = controller?.TileGrid;
        if (tileGrid == null)
            yield break;

        // 记录起始位置
        Vector3 startWorldPos = block.transform.position;
        Vector3 targetWorldPos = tileGrid.GridToWorldPosition(targetPosition.x, targetPosition.y, 0, 1, 1);

        // 先从网格中移除（准备移动到新位置）
        tileGrid.RemoveObject(block.gameObject);

        // 动画过渡
        float elapsed = 0f;
        while (elapsed < TransitionDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / TransitionDuration;

            // 根据曲线类型计算位置
            float curveProgress = CalculateTransitionProgress(progress, TransitionCurveType);
            Vector3 currentPos = Vector3.Lerp(startWorldPos, targetWorldPos, curveProgress);

            block.transform.position = currentPos;
            yield return null;
        }

        // 确保到达目标位置
        block.transform.position = targetWorldPos;

        // 将预生成的单块正式加入网格系统
        var gridSystem = controller.TileGrid;
        if (gridSystem != null)
        {
            var gridInfo = gridSystem.PlaceObject(block.gameObject, targetPosition.x, targetPosition.y, 0, 1, 1, false);
            if (gridInfo != null)
            {
                block.SetGridInfo(gridInfo);

                // 更新单块的数据
                var blockData = new TileCube_Data_SingleBlock
                {
                    GridX = targetPosition.x,
                    GridZ = targetPosition.y,
                    Height = 0,
                    ColorId = block.ColorId
                };
                block.Init(blockData);

                // 清除预留块状态（现在可以被子弹击中了）
                block.SetPreSpawnState(false);

                // 确保JRJelly组件记录正确的原始位置（在Init之后）
                if (block.tool_JRJELLY_Simple != null)
                {



                    block.tool_JRJELLY_Simple.RecordOriginalPosition();


                    block.tool_JRJELLY_Simple.TriggerLandingCushion(1.2f, 0.5f);
                    ////Debug.Log($"预留块落地触发果冻效果：位置({targetPosition.x}, {targetPosition.y})，强度2f，持续时间0.3f");
                    ///
                    TileCube_Controller.OnSpanwer_BlockLand?.Invoke(this, block);
                }
            }
        }
    }

    /// <summary>
    /// 根据曲线类型计算过渡进度
    /// </summary>
    /// <param name="progress">原始进度 (0-1)</param>
    /// <param name="curveType">曲线类型</param>
    /// <returns>调整后的进度 (0-1)</returns>
    private float CalculateTransitionProgress(float progress, int curveType)
    {
        return TileCube_Spawner_Func.CalculateAnimationProgress(progress, curveType);
    }

    /// <summary>
    /// 生成单一块 - 一个一个地生成（原始模式）
    /// </summary>
    private IEnumerator SpawnSingleBlock()
    {
        if (TileCube_Spawner_Func.IsInventoryEmpty(CurrentSpawnColorSingleBlocks))
            yield break;

        isSpawning = true;

        // 延迟生成
        yield return new WaitForSeconds(SpawnDelay);



        var controller = TileCube_Controller.Instance;
        if (controller == null)
        {
            isSpawning = false;
            yield break;
        }

        // 找到下一个可用位置（轮流生成）
        Vector2Int? spawnPosition = FindNextAvailableFrontPosition();

        if (spawnPosition.HasValue)
        {
            // 从库存中取出一个颜色ID
            List<int> colorsToSpawn = TileCube_Spawner_Func.TakeFromInventory(CurrentSpawnColorSingleBlocks, 1);

            if (colorsToSpawn.Count > 0)
            {
                // 创建单个SingleBlock
                List<Vector2Int> spawnPositions = new List<Vector2Int> { spawnPosition.Value };
                int successCount = TileCube_Spawner_Func.CreateSingleBlocks(colorsToSpawn, spawnPositions, controller, 0);

                // 触发生成事件
                if (successCount > 0)
                {
                    TileCube_Controller.OnSpawnerEject?.Invoke(this, null);

                    // 更新个数显示
                    UpdateCountDisplay();

                    //Debug.Log($"出块库成功生成了 1 个SingleBlock，位置：({spawnPosition.Value.x}, {spawnPosition.Value.y})，剩余库存：{TileCube_Spawner_Func.GetInventoryCount(CurrentSpawnColorSingleBlocks)}");
                }
            }
        }

        isSpawning = false;
    }

    /// <summary>
    /// 找到下一个可用位置（轮流生成）
    /// </summary>
    private Vector2Int? FindNextAvailableFrontPosition()
    {
        if (gridInfo == null || TileCube_Controller.Instance?.TileGrid == null)
            return null;

        var tileGrid = TileCube_Controller.Instance.TileGrid;
        List<Vector2Int> frontPositions = GetFrontPositions();

        if (frontPositions.Count == 0)
            return null;

        // 收集所有可用位置
        List<int> availableIndices = new List<int>();
        for (int i = 0; i < frontPositions.Count; i++)
        {
            GameObject existingObj = tileGrid.GetObjectAtPosition(frontPositions[i].x, frontPositions[i].y, 0);
            if (existingObj == null)
            {
                availableIndices.Add(i);
            }
        }

        if (availableIndices.Count == 0)
            return null;

        // 如果只有一个可用位置，直接返回
        if (availableIndices.Count == 1)
        {
            lastSpawnIndex = availableIndices[0];
            return frontPositions[lastSpawnIndex];
        }

        // 如果有多个可用位置，实现轮流生成
        int nextIndex;
        if (lastSpawnIndex == -1)
        {
            // 第一次生成，选择第一个可用位置
            nextIndex = availableIndices[0];
        }
        else
        {
            // 找到上次生成位置在可用位置列表中的索引
            int lastIndexInAvailable = availableIndices.IndexOf(lastSpawnIndex);

            if (lastIndexInAvailable == -1)
            {
                // 上次的位置现在不可用了，选择第一个可用位置
                nextIndex = availableIndices[0];
            }
            else
            {
                // 选择下一个可用位置（循环）
                int nextAvailableIndex = (lastIndexInAvailable + 1) % availableIndices.Count;
                nextIndex = availableIndices[nextAvailableIndex];
            }
        }

        lastSpawnIndex = nextIndex;
        return frontPositions[nextIndex];
    }

    /// <summary>
    /// 检查是否应该销毁自己
    /// </summary>
    private bool ShouldDestroySelf()
    {
        return TileCube_Spawner_Func.IsInventoryEmpty(CurrentSpawnColorSingleBlocks)
               && !isSpawning
               && !isDestroying
               && !isTransitioning
               && preSpawnedBlocks.Count == 0; // 确保没有预生成的单块
    }

    /// <summary>
    /// 销毁自己 - 使用缩小动画
    /// </summary>
    private void DestroySelf()
    {
        if (isDestroying) return; // 防止重复销毁

        isDestroying = true;
        StopSpawnCheck();

        // 清理预生成的单块
        CleanupPreSpawnedBlocks();

        // 开始缩小销毁协程
        StartCoroutine(ShrinkToZeroAndDestroy());

        //Debug.Log($"出块库库存耗尽，开始缩小销毁，位置：({gridInfo?.gridX}, {gridInfo?.gridZ})");
    }

    /// <summary>
    /// 清理预生成的单块
    /// </summary>
    private void CleanupPreSpawnedBlocks()
    {
        foreach (var block in preSpawnedBlocks)
        {
            if (block != null && block.gameObject != null)
            {
                // 获取控制器实例，如果为null则说明正在销毁过程中
                var controller = TileCube_Controller.Instance;
                if (controller != null)
                {
                    controller.Current_Objects.Remove(block);
                }

                // 预生成的单块不在网格系统中，所以不需要从网格中移除
                // 直接销毁游戏对象
                Destroy(block.gameObject);
            }
        }
        preSpawnedBlocks.Clear();
        preSpawnedPositions.Clear(); // 清理位置记录
        preSpawnedStates.Clear(); // 清理状态记录
    }

    /// <summary>
    /// 获取占用的所有网格位置
    /// </summary>
    public List<Vector2Int> GetOccupiedPositions()
    {
        List<Vector2Int> positions = new List<Vector2Int>();

        if (gridInfo != null)
        {
            for (int x = gridInfo.gridX; x < gridInfo.gridX + SpawnerSizeX; x++)
            {
                for (int z = gridInfo.gridZ; z < gridInfo.gridZ + SpawnerSizeZ; z++)
                {
                    positions.Add(new Vector2Int(x, z));
                }
            }
        }

        return positions;
    }

    /// <summary>
    /// 获取当前库存数量
    /// </summary>
    public int GetRemainingCount()
    {
        return TileCube_Spawner_Func.GetInventoryCount(CurrentSpawnColorSingleBlocks);
    }

    /// <summary>
    /// 获取预生成单块数量
    /// </summary>
    public int GetPreSpawnedCount()
    {
        return preSpawnedBlocks.Count;
    }

    /// <summary>
    /// 获取是否正在生成
    /// </summary>
    public bool IsSpawning()
    {
        return isSpawning;
    }

    /// <summary>
    /// 获取是否正在销毁
    /// </summary>
    public bool IsDestroying()
    {
        return isDestroying;
    }

    /// <summary>
    /// 获取是否正在过渡
    /// </summary>
    public bool IsTransitioning()
    {
        return isTransitioning;
    }

    /// <summary>
    /// 手动触发生成（用于测试或特殊情况）
    /// </summary>
    public void ManualSpawn()
    {
        if (EnablePreSpawn)
        {
            if (CanPreSpawn())
            {
                StartCoroutine(PreSpawnSingleBlock());
            }
            else if (CanPreSpawnTransition())
            {
                StartCoroutine(PreSpawnTransitionToWaiting());
            }
            else if (CanTransitionToFront())
            {
                StartCoroutine(TransitionBlockToFront());
            }
        }
        else
        {
            if (CanSpawn())
            {
                StartCoroutine(SpawnSingleBlock());
            }
        }
    }

    /// <summary>
    /// 添加库存
    /// </summary>
    /// <param name="colorIds">要添加的颜色ID列表</param>
    public void AddInventory(List<int> colorIds)
    {
        if (colorIds != null && colorIds.Count > 0)
        {
            CurrentSpawnColorSingleBlocks.AddRange(colorIds);

            // 更新个数显示
            UpdateCountDisplay();

            //Debug.Log($"出块库添加了 {colorIds.Count} 个物品，当前库存：{GetRemainingCount()}");
        }
    }

    /// <summary>
    /// 设置预生成模式
    /// </summary>
    /// <param name="enabled">是否启用预生成</param>
    public void SetPreSpawnMode(bool enabled)
    {
        EnablePreSpawn = enabled;
        //Debug.Log($"出块库预生成模式：{(enabled ? "启用" : "禁用")}");
    }

    #region 压缩动画相关方法

    /// <summary>
    /// 执行压缩动画
    /// </summary>
    /// <param name="hasOvershoot">是否有超出阶段</param>
    public void PlayCompressionAnimation(bool hasOvershoot = true)
    {
        if (compressionCoroutine != null)
        {
            StopCoroutine(compressionCoroutine);
        }
        compressionCoroutine = StartCoroutine(CompressionAnimationCoroutine(hasOvershoot));
    }

    /// <summary>
    /// 停止压缩动画
    /// </summary>
    public void StopCompressionAnimation()
    {
        if (compressionCoroutine != null)
        {
            StopCoroutine(compressionCoroutine);
            compressionCoroutine = null;
        }
    }

    /// <summary>
    /// 检查是否正在压缩动画
    /// </summary>
    public bool IsCompressing()
    {
        return compressionCoroutine != null;
    }

    /// <summary>
    /// 压缩动画协程
    /// </summary>
    private IEnumerator CompressionAnimationCoroutine(bool hasOvershoot)
    {
        Transform spawnerTransform = transform;
        Vector3 originalScale = spawnerTransform.localScale;

        if (hasOvershoot)
        {
            // 有超出的三阶段动画
            // 第一阶段：放大X轴，缩小Y轴 (33%时间)
            Vector3 firstPhaseScale = new Vector3(originalScale.x * compressionXScale, originalScale.y * compressionYScale, originalScale.z);
            yield return ScaleToTarget(spawnerTransform, firstPhaseScale, compressionAnimDuration * 0.33f);

            // 第二阶段：X轴回到原值减超出值，Y轴回到原值加超出值 (33%时间)
            Vector3 secondPhaseScale = new Vector3(originalScale.x - compressionXOvershoot, originalScale.y + compressionYOvershoot, originalScale.z);
            yield return ScaleToTarget(spawnerTransform, secondPhaseScale, compressionAnimDuration * 0.33f);

            // 第三阶段：恢复到原始缩放 (34%时间)
            yield return ScaleToTarget(spawnerTransform, originalScale, compressionAnimDuration * 0.34f);
        }
        else
        {
            // 无超出的两阶段动画
            // 第一阶段：放大X轴，缩小Y轴 (50%时间)
            Vector3 firstPhaseScale = new Vector3(originalScale.x * compressionXScale, originalScale.y * compressionYScale, originalScale.z);
            yield return ScaleToTarget(spawnerTransform, firstPhaseScale, compressionAnimDuration * 0.5f);

            // 第二阶段：直接恢复到原始缩放 (50%时间)
            yield return ScaleToTarget(spawnerTransform, originalScale, compressionAnimDuration * 0.5f);
        }

        compressionCoroutine = null;
    }

    /// <summary>
    /// 缩放到目标尺寸
    /// </summary>
    private IEnumerator ScaleToTarget(Transform targetTransform, Vector3 targetScale, float duration)
    {
        Vector3 startScale = targetTransform.localScale;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;

            Vector3 currentScale = Vector3.Lerp(startScale, targetScale, progress);
            targetTransform.localScale = currentScale;

            yield return null;
        }

        // 确保最终缩放准确
        targetTransform.localScale = targetScale;
    }

    #endregion

    /// <summary>
    /// 缩小到0并销毁的协程
    /// </summary>
    private IEnumerator ShrinkToZeroAndDestroy()
    {
        // 销毁前延迟
        if (DestroyDelay > 0)
        {
            yield return new WaitForSeconds(DestroyDelay);
        }

        // 记录原始缩放
        Vector3 originalScale = transform.localScale;
        float elapsed = 0f;

        //Debug.Log($"开始缩小销毁：出块库，位置：({gridInfo?.gridX}, {gridInfo?.gridZ})");

        // 缩小动画循环
        while (elapsed < ShrinkDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / ShrinkDuration;

            // 根据曲线类型计算缩放倍数
            float scaleMultiplier = CalculateScaleMultiplier(progress, ShrinkCurveType);

            // 应用缩放
            transform.localScale = originalScale * scaleMultiplier;

            yield return null;
        }

        // 确保完全缩小
        transform.localScale = Vector3.zero;

        //Debug.Log($"缩小完成，通过Controller销毁：{name}");

        // 通过Controller正确销毁物体
        DestroyByController();
    }

    /// <summary>
    /// 根据曲线类型计算缩放倍数
    /// </summary>
    /// <param name="progress">进度 (0-1)</param>
    /// <param name="curveType">曲线类型</param>
    /// <returns>缩放倍数 (1-0)</returns>
    private float CalculateScaleMultiplier(float progress, int curveType)
    {
        // 将进度从 0-1 转换为缩放倍数 1-0
        switch (curveType)
        {
            case 0: // 线性
                return Mathf.Lerp(1f, 0f, progress);

            case 1: // 平滑 (SmoothStep)
                return Mathf.Lerp(1f, 0f, Mathf.SmoothStep(0f, 1f, progress));

            case 2: // 快进慢出 (EaseOut)
                float easeOut = 1f - Mathf.Pow(1f - progress, 2f);
                return Mathf.Lerp(1f, 0f, easeOut);

            default:
                return Mathf.Lerp(1f, 0f, Mathf.SmoothStep(0f, 1f, progress));
        }
    }

    /// <summary>
    /// 通过Controller正确销毁物体
    /// </summary>
    private void DestroyByController()
    {
        // 尝试使用基类的DestroyObject方法
        try
        {
            DestroyObject();
        }
        catch (System.Exception e)
        {
            //Debug.LogWarning($"使用基类销毁方法失败，使用备用方法：{e.Message}");

            // 备用销毁方法：手动清理
            // 获取控制器实例，如果为null则说明正在销毁过程中，直接销毁即可
            var controller = TileCube_Controller.Instance;
            if (controller != null)
            {
                // 从网格中移除
                if (controller.TileGrid != null)
                {
                    controller.TileGrid.RemoveObject(gameObject);
                }

                // 从控制器列表中移除
                if (controller.Current_Objects.Contains(this))
                {
                    controller.Current_Objects.Remove(this);
                }
            }

            // 直接销毁游戏对象
            Destroy(gameObject);
        }
    }

    private void OnDestroy()
    {
        StopSpawnCheck();
        CleanupPreSpawnedBlocks();
        StopCompressionAnimation();
    }
}