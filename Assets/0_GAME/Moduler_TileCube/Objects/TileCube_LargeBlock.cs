using System.Collections;
using System.Collections.Generic;
using JRJelly;
using TMPro;
using UnityEngine;

/// <summary>
/// 大型块 - 四方形，横竖占4个格，只在第一层出现，有不同的颜色
/// 需要击打N次才会销毁，支持子弹穿过和击中效果
/// </summary>
public class TileCube_LargeBlock : TileCube_Object
{
    [Header("大型块特有属性")]
    public readonly int BlockSize = 2;      // 大型块尺寸 (2x2)
    public bool IsFirstLayerOnly = true;    // 只能在第一层

    [Header("击打销毁设置")]
    [Tooltip("需要击打的次数才销毁")]
    public int RequiredHits = 3;            // 需要击打次数
    [Tooltip("当前已被击打次数")]
    public int CurrentHits = 0;             // 当前击打次数

    [Header("渲染组件")]
    public MeshRenderer meshRenderer;       // 渲染器

    [Header("果冻效果设置")]
    [Tooltip("推进刹车效果强度")]
    [Range(0.1f, 2f)]
    public float pushBrakeIntensity = 0.8f;

    [Tooltip("子弹穿过甩动基础强度")]
    [Range(0.1f, 2f)]
    public float bulletPassSwingIntensity = 0.5f;

    [Tooltip("子弹击中甩动强度")]
    [Range(0.1f, 2f)]
    public float bulletHitSwingIntensity = 1.2f;

    [Tooltip("击中后缩小持续时间")]
    [Range(0.2f, 1f)]
    public float hitShrinkDuration = 0.3f;

    [Header("子弹检测参数")]
    [Tooltip("穿过甩动持续时间")]
    [Range(0.1f, 1f)]
    public float bulletPassSwingDuration = 0.6f;

    [Tooltip("击中甩动持续时间")]
    [Range(0.1f, 1f)]
    public float bulletHitSwingDuration = 0.8f;

    [Tooltip("击中回来缩小的时间比例")]
    [Range(0f, 1f)]
    public float bulletHitToScalePerent = 0.6f;

    [Header("倾斜效果参数")]
    [Tooltip("穿过时倾斜强度")]
    [Range(0f, 30f)]
    public float bulletPassTiltIntensity = 8f;

    [Tooltip("击中时倾斜强度")]
    [Range(0f, 30f)]
    public float bulletHitTiltIntensity = 15f;

    [Tooltip("倾斜恢复速度")]
    [Range(1f, 10f)]
    public float tiltRecoverySpeed = 3f;

    [Tooltip("多子弹连续击中时倾斜叠加强度")]
    [Range(0f, 1f)]
    public float multiHitTiltMultiplier = 0.7f;


    // 私有变量
    public TileCube_Data_LargeBlock blockData;
    private MaterialPropertyBlock propertyBlock;
    private bool isByBulletLocked = false;
    private bool isDestroying = false;      // 防止重复销毁

    // 倾斜效果相关变量
    private Vector3 targetTiltRotation = Vector3.zero;    // 目标倾斜角度
    private Vector3 currentTiltRotation = Vector3.zero;   // 当前倾斜角度
    private Vector3 originalRotation = Vector3.zero;     // 原始旋转角度
    private List<Vector3> activeTiltDirections = new List<Vector3>(); // 活跃的倾斜方向列表
    private List<float> activeTiltEndTimes = new List<float>();       // 对应的结束时间列表

    public Tool_JRJELLY_Simple tool_JRJELLY_Simple;

    [Header("显示个数")]
    public TextMeshProUGUI txtCount;

    /// <summary>
    /// 初始化大型块
    /// </summary>
    public void Init(TileCube_Data_LargeBlock data)
    {
        blockData = data;
        ObjectType = TileCube_ObjectType.LargeBlock;

        // 记录原始旋转角度（用于倾斜效果）
        originalRotation = transform.localEulerAngles;

        // 初始化批处理优化
        InitializeBatchingOptimization();

        RequiredHits = data.HitValues;

        // 初始化击打值显示
        InitializeHitCountDisplay();

        // 应用颜色
        if (data.ColorId > 0)
            SetColor(data.ColorId);
    }

    /// <summary>
    /// 初始化批处理优化
    /// </summary>
    private void InitializeBatchingOptimization()
    {
        if (meshRenderer == null) return;

        // 只创建一次PropertyBlock
        if (propertyBlock == null)
        {
            propertyBlock = new MaterialPropertyBlock();
            //Debug.Log($"[{gameObject.name}] PropertyBlock创建完成");
        }

        // 获取当前状态
        meshRenderer.GetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 初始化击打值显示
    /// </summary>
    private void InitializeHitCountDisplay()
    {
        if (txtCount != null)
        {
            // 显示剩余击打次数
            int remainingHits = RequiredHits - CurrentHits;
            txtCount.text = remainingHits.ToString();
        }
    }

    /// <summary>
    /// 更新击打值显示
    /// </summary>
    private void UpdateHitCountDisplay()
    {
        if (txtCount != null)
        {
            // 计算剩余击打次数
            int remainingHits = RequiredHits - CurrentHits;
            txtCount.text = remainingHits.ToString();
        }
    }

    /// <summary>
    /// 设置颜色
    /// </summary>
    public virtual void SetColor(int colorId)
    {
        Color color = MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data.GetColorById(colorId);
        ApplyColor(color);
    }

    /// <summary>
    /// 应用颜色到材质
    /// </summary>
    protected virtual void ApplyColor(Color color)
    {
        if (meshRenderer == null) return;

        // 直接设置颜色，无需重复创建PropertyBlock
        Tool_MaterialBatchOptimizer.SetColor(meshRenderer, propertyBlock, color);
    }

    /// <summary>
    /// 检查是否可以放置在指定位置
    /// </summary>
    public bool CanPlaceAt(int gridX, int gridZ, int height = 0)
    {
        // 大型块只能在第一层
        if (IsFirstLayerOnly && height != 0)
        {
            return false;
        }

        var tileGrid = TileCube_Controller.Instance?.TileGrid;
        if (tileGrid == null) return false;

        // 检查2x2区域是否都可用
        for (int x = gridX; x < gridX + BlockSize; x++)
        {
            for (int z = gridZ; z < gridZ + BlockSize; z++)
            {
                var existingObject = tileGrid.GetObjectAtPosition(x, z, height);
                if (existingObject != null)
                {
                    return false;
                }
            }
        }

        return true;
    }

    /// <summary>
    /// 获取占用的所有网格位置
    /// </summary>
    public List<Vector2Int> GetOccupiedPositions()
    {
        List<Vector2Int> positions = new List<Vector2Int>();

        if (gridInfo != null)
        {
            for (int x = gridInfo.gridX; x < gridInfo.gridX + BlockSize; x++)
            {
                for (int z = gridInfo.gridZ; z < gridInfo.gridZ + BlockSize; z++)
                {
                    positions.Add(new Vector2Int(x, z));
                }
            }
        }

        return positions;
    }

    /// <summary>
    /// 检查向指定方向推进是否可行
    /// </summary>
    public bool CanPushInDirection(Vector2Int direction)
    {
        if (gridInfo == null) return false;

        var tileGrid = TileCube_Controller.Instance?.TileGrid;
        if (tileGrid == null) return false;

        // 检查推进后的目标位置是否都可用
        int targetX = gridInfo.gridX + direction.x;
        int targetZ = gridInfo.gridZ + direction.y;

        return CanPlaceAt(targetX, targetZ, gridInfo.height);
    }

    /// <summary>
    /// 获取中心位置
    /// </summary>
    public Vector2Int GetCenterPosition()
    {
        if (gridInfo == null) return Vector2Int.zero;

        return new Vector2Int(
            gridInfo.gridX + BlockSize / 2,
            gridInfo.gridZ + BlockSize / 2
        );
    }

    /// <summary>
    /// 检查是否阻挡了指定位置
    /// </summary>
    public bool IsBlockingPosition(int gridX, int gridZ)
    {
        if (gridInfo == null) return false;

        return gridX >= gridInfo.gridX && gridX < gridInfo.gridX + BlockSize &&
               gridZ >= gridInfo.gridZ && gridZ < gridInfo.gridZ + BlockSize;
    }

    /// <summary>
    /// 获取边界位置（用于推进计算）
    /// </summary>
    public List<Vector2Int> GetBoundaryPositions(Vector2Int direction)
    {
        List<Vector2Int> boundaryPositions = new List<Vector2Int>();

        if (gridInfo == null) return boundaryPositions;

        // 根据推进方向获取边界位置
        if (direction.x > 0) // 向右推进
        {
            for (int z = gridInfo.gridZ; z < gridInfo.gridZ + BlockSize; z++)
            {
                boundaryPositions.Add(new Vector2Int(gridInfo.gridX + BlockSize - 1, z));
            }
        }
        else if (direction.x < 0) // 向左推进
        {
            for (int z = gridInfo.gridZ; z < gridInfo.gridZ + BlockSize; z++)
            {
                boundaryPositions.Add(new Vector2Int(gridInfo.gridX, z));
            }
        }
        else if (direction.y > 0) // 向上推进
        {
            for (int x = gridInfo.gridX; x < gridInfo.gridX + BlockSize; x++)
            {
                boundaryPositions.Add(new Vector2Int(x, gridInfo.gridZ + BlockSize - 1));
            }
        }
        else if (direction.y < 0) // 向下推进
        {
            for (int x = gridInfo.gridX; x < gridInfo.gridX + BlockSize; x++)
            {
                boundaryPositions.Add(new Vector2Int(x, gridInfo.gridZ));
            }
        }

        return boundaryPositions;
    }

    #region 击打销毁逻辑

    /// <summary>
    /// 物体被击打时的处理逻辑
    /// </summary>
    public virtual void OnHit(int damage = 1)
    {
        if (isDestroying) return;

        CurrentHits += damage;

        // 触发进度更新（大型块每次被击打减对应伤害值）
        TileCube_Controller.Instance?.UpdateProgress(damage);

        //Debug.Log($"大型块 {name} 被击打，当前击打次数: {CurrentHits}/{RequiredHits}");

        // 更新击打值显示
        UpdateHitCountDisplay();

        // 可以在这里添加击打效果，比如颜色变化、缩放等
        UpdateVisualBasedOnHits();

        // 检查是否达到销毁条件
        if (CurrentHits >= RequiredHits)
        {
            StartDestroySequence();
        }
    }

    /// <summary>
    /// 根据击打次数更新视觉效果
    /// </summary>
    private void UpdateVisualBasedOnHits()
    {

    }

    /// <summary>
    /// 开始销毁序列
    /// </summary>
    private void StartDestroySequence()
    {
        if (isDestroying) return;
        isDestroying = true;

        //Debug.Log($"大型块 {name} 开始销毁序列");
        StartCoroutine(ShrinkToZeroAndDestroy());
    }



    /// <summary>
    /// 缩小到0并销毁的动画（强制执行）
    /// </summary>
    private IEnumerator ShrinkToZeroAndDestroy()
    {
        // 等待甩动效果基本完成
        yield return new WaitForSeconds(bulletHitSwingDuration * bulletHitToScalePerent);

        // 强制执行缩小销毁，不再检查果冻活跃状态
        //Debug.Log($"[{name}] 强制开始缩小销毁流程");

        Vector3 originalScale = transform.localScale;
        float elapsed = 0f;

        while (elapsed < hitShrinkDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / hitShrinkDuration;

            // 使用平滑的缩小曲线
            float scaleMultiplier = Mathf.Lerp(1f, 0f, Mathf.SmoothStep(0f, 1f, progress));
            transform.localScale = originalScale * scaleMultiplier;

            yield return null;
        }

        // 确保完全缩小
        transform.localScale = Vector3.zero;

        //Debug.Log($"[{name}] 缩小完成，通过Controller销毁");

        // 通过Controller销毁物体
        DestroyByController();
    }

    /// <summary>
    /// 通过Controller销毁物体（推荐的销毁方式）
    /// </summary>
    private void DestroyByController()
    {
        // 使用基类的DestroyObject方法，它会正确处理网格移除和Controller列表清理
        DestroyObject();
    }


    public override void OnRebound()
    {
        // 应用Z行隐藏优化
        //TileCube_Controller.Instance.ApplyZRowOptimizationToSingleBlock(this);

        // 推进完成时触发果冻刹车效果
        if (tool_JRJELLY_Simple != null)
        {
            // 检查前方格子是否有物体
            if (HasObjectInFront() || gridInfo.gridZ == 0)
            {
                // 将2D方向转换为3D方向（Y轴设为0，保持在水平面）
                Vector3 brakeDirection = -transform.forward;
                bool success = tool_JRJELLY_Simple.TriggerRebound(brakeDirection, pushBrakeIntensity, 0.3f);

                if (success)
                {
                    //Debug.Log($"[{name}] 前方有物体，触发刹车效果");
                }
            }
            else
            {
                //Debug.Log($"[{name}] 前方无物体，跳过刹车效果");
            }
        }
    }

    /// <summary>
    /// 检查前方格子是否有物体
    /// </summary>
    /// <returns>前方是否有物体</returns>
    private bool HasObjectInFront()
    {
        if (gridInfo == null) return false;

        var controller = TileCube_Controller.Instance;
        if (controller?.TileGrid == null) return false;

        // 推进方向是向Z轴负方向，前方位置是gridZ - 1
        int frontGridX = gridInfo.gridX;
        int frontGridZ = gridInfo.gridZ - 1;

        // 检查前方位置的所有高度层是否有物体
        int maxHeight = controller.TileGrid.maxHeightLayers;
        for (int height = 0; height < maxHeight; height++)
        {
            var frontObject = controller.TileGrid.GetObjectAtPosition(frontGridX, frontGridZ, height);
            if (frontObject != null)
            {
                return true; // 找到任意高度层有物体就返回true
            }
        }

        return false; // 所有高度层都没有物体
    }

    #endregion

    #region 子弹检测重写方法

    /// <summary>
    /// 重写基类：被子弹瞄准锁定时触发
    /// </summary>
    public override void OnBulletTargeted(GameObject bullet, object bulletData = null)
    {
        base.OnBulletTargeted(bullet, bulletData);
        isByBulletLocked = true;
    }

    /// <summary>
    /// 重写基类：被子弹穿过时触发
    /// </summary>
    public override void OnBulletPenetrate(GameObject bullet, object bulletData = null)
    {
        base.OnBulletPenetrate(bullet, bulletData);

        if (tool_JRJELLY_Simple == null || isDestroying) return;

        // 如果正在销毁，不再处理穿过效果
        if (isDestroying)
        {
            //Debug.Log($"[{name}] 正在销毁中，忽略子弹穿过效果");
            return;
        }

        // 检查是否正在甩动，如果是则返回
        if (tool_JRJELLY_Simple.HasActiveEffects)
        {
            //Debug.Log($"[{name}] 正在甩动中，忽略子弹穿过");
            return;
        }

        Vector3 bulletPos = bullet.transform.position;

        // 直接使用固定的甩动强度，不计算距离影响
        float finalSwingIntensity = bulletPassSwingIntensity;

        // 触发甩动效果
        bool success = tool_JRJELLY_Simple.TriggerSwing(bulletPos, finalSwingIntensity, bulletPassSwingDuration);

        // 添加倾斜效果
        AddTiltEffect(bulletPos, bulletPassTiltIntensity, bulletPassSwingDuration, false);

        if (success)
        {
            //Debug.Log($"[{name}] 子弹穿过甩动 - 固定强度: {finalSwingIntensity:F2}");
        }
    }

    /// <summary>
    /// 重写基类：被子弹击中时触发
    /// </summary>
    public override bool OnBulletHit(GameObject bullet, object bulletData = null)
    {
        base.OnBulletHit(bullet, bulletData);

        if (isDestroying) return true; // 如果正在销毁，直接销毁子弹
        if (tool_JRJELLY_Simple == null) return false;

        // 击中时强制停止所有正在进行的果冻效果，确保击中效果优先执行
        if (tool_JRJELLY_Simple.HasActiveEffects)
        {
            //Debug.Log($"[{name}] 击中时强制停止正在进行的果冻效果");
            tool_JRJELLY_Simple.StopAll(); // 停止所有正在进行的效果
        }

        float swingIntensity = bulletHitSwingIntensity;

        // 强制触发击中甩动效果（不检查当前状态）
        bool swingSuccess = tool_JRJELLY_Simple.TriggerSwing(bullet.transform.position, swingIntensity, bulletHitSwingDuration);

        // 添加倾斜效果
        AddTiltEffect(bullet.transform.position, bulletHitTiltIntensity, bulletHitSwingDuration, true);

        if (swingSuccess)
        {
            //Debug.Log($"[{name}] 子弹击中甩动（强制执行） - 位置: {bullet.transform.position}, 强度: {swingIntensity}");
        }
        else
        {
            //Debug.LogWarning($"[{name}] 子弹击中甩动失败，强制执行击打逻辑");
        }

        // 强制触发击打逻辑
        OnHit(1);

        // 大型块被击中后销毁子弹
        return true;
    }



    #endregion

    #region Unity生命周期

    void Update()
    {
        // 更新倾斜效果
        UpdateTiltEffect();
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        StopAllCoroutines();
    }

    #endregion

    #region 倾斜效果系统

    /// <summary>
    /// 更新倾斜效果
    /// </summary>
    private void UpdateTiltEffect()
    {
        // 清理已过期的倾斜方向
        float currentTime = Time.time;
        for (int i = activeTiltDirections.Count - 1; i >= 0; i--)
        {
            if (currentTime >= activeTiltEndTimes[i])
            {
                activeTiltDirections.RemoveAt(i);
                activeTiltEndTimes.RemoveAt(i);
            }
        }

        // 计算合成的目标倾斜角度
        Vector3 combinedTiltDirection = Vector3.zero;
        for (int i = 0; i < activeTiltDirections.Count; i++)
        {
            // 后加入的倾斜效果权重更高，用于处理连续击中
            float weight = 1f + (i * multiHitTiltMultiplier);
            combinedTiltDirection += activeTiltDirections[i] * weight;
        }

        // 限制最大倾斜角度
        targetTiltRotation = Vector3.ClampMagnitude(combinedTiltDirection, Mathf.Max(bulletPassTiltIntensity, bulletHitTiltIntensity));

        // 如果没有活跃的倾斜效果，目标角度回归原始角度
        if (activeTiltDirections.Count == 0)
        {
            targetTiltRotation = Vector3.zero;
        }

        // 平滑插值到目标角度，使用更高的插值速度避免闪烁
        currentTiltRotation = Vector3.Lerp(currentTiltRotation, targetTiltRotation, Time.deltaTime * tiltRecoverySpeed);

        // 应用倾斜旋转到父物体transform，基于原始角度
        Vector3 finalRotation = originalRotation + currentTiltRotation;
        transform.localEulerAngles = finalRotation;
    }

    /// <summary>
    /// 根据子弹位置计算倾斜方向（与子弹位置相反，穿过和击中都影响XZ轴）
    /// </summary>
    /// <param name="bulletPosition">子弹世界坐标</param>
    /// <param name="isHit">是否为击中</param>
    /// <returns>倾斜方向向量</returns>
    private Vector3 CalculateTiltDirection(Vector3 bulletPosition, bool isHit)
    {
        // 计算从子弹到大型块的方向
        Vector3 bulletToBlock = transform.position - bulletPosition;
        bulletToBlock.y = 0; // 忽略Y轴差异，只考虑水平面

        // 如果距离太近，使用默认倾斜方向
        if (bulletToBlock.magnitude < 0.1f)
        {
            return new Vector3(-10f, 0, 10f); // 默认前倾+右倾
        }

        // 归一化方向向量
        bulletToBlock = bulletToBlock.normalized;

        // 穿过和击中都影响XZ轴（前后+左右倾斜）
        Vector3 tiltDirection = new Vector3(
            -bulletToBlock.z,  // 子弹在前方时，大型块向前倾斜（负X轴旋转）
            0,
            bulletToBlock.x    // 子弹在右方时，大型块向左倾斜（正Z轴旋转）
        );

        return tiltDirection;
    }

    /// <summary>
    /// 添加倾斜效果
    /// </summary>
    /// <param name="bulletPosition">子弹位置</param>
    /// <param name="intensity">倾斜强度</param>
    /// <param name="duration">持续时间</param>
    /// <param name="isHit">是否为击中</param>
    private void AddTiltEffect(Vector3 bulletPosition, float intensity, float duration, bool isHit)
    {
        if (isDestroying) return;

        // 计算倾斜方向
        Vector3 tiltDirection = CalculateTiltDirection(bulletPosition, isHit) * intensity;

        // 添加到活跃倾斜列表
        activeTiltDirections.Add(tiltDirection);
        activeTiltEndTimes.Add(Time.time + duration);

        //Debug.Log($"[{name}] 添加倾斜效果 - 子弹位置: {bulletPosition}, 倾斜方向: {tiltDirection}, 强度: {intensity}, 持续时间: {duration}, 击中: {isHit}");
    }

    #endregion
}