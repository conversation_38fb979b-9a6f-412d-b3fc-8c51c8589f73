using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 出库块管理工具 - 处理出库块的通用逻辑
/// 功能：位置检查、SingleBlock生成、库存管理、自动销毁、预生成管理
/// 适合工具化，方便其他项目复用
/// </summary>
public class TileCube_Spawner_Func
{
    /// <summary>
    /// 检查指定位置列表是否全部为空
    /// </summary>
    /// <param name="positions">要检查的位置列表</param>
    /// <param name="tileGrid">网格系统</param>
    /// <param name="checkHeight">检查的高度层</param>
    /// <returns>是否全部为空</returns>
    public static bool ArePositionsEmpty(List<Vector2Int> positions, Tool_TileGrid tileGrid, int checkHeight = 0)
    {
        if (positions == null || positions.Count == 0 || tileGrid == null)
            return false;

        foreach (var pos in positions)
        {
            GameObject existingObj = tileGrid.GetObjectAtPosition(pos.x, pos.y, checkHeight);
            if (existingObj != null)
            {
                return false; // 有物体占用
            }
        }

        return true; // 所有位置都为空
    }

    /// <summary>
    /// 获取出库块前方的生成位置
    /// </summary>
    /// <param name="spawnerGridInfo">出库块的网格信息</param>
    /// <param name="spawnerSize">出库块尺寸（兼容旧版本）</param>
    /// <param name="spawnDirection">生成方向 (0=前方, 1=右方, 2=后方, 3=左方)</param>
    /// <returns>生成位置列表</returns>
    public static List<Vector2Int> GetSpawnPositions(GridTileObject spawnerGridInfo, int spawnerSize, int spawnDirection = 0)
    {
        return GetSpawnPositions(spawnerGridInfo, spawnerSize, spawnerSize, spawnDirection);
    }

    /// <summary>
    /// 获取出库块前方的生成位置
    /// </summary>
    /// <param name="spawnerGridInfo">出库块的网格信息</param>
    /// <param name="spawnerSizeX">出库块X轴尺寸</param>
    /// <param name="spawnerSizeZ">出库块Z轴尺寸</param>
    /// <param name="spawnDirection">生成方向 (0=前方, 1=右方, 2=后方, 3=左方)</param>
    /// <returns>生成位置列表</returns>
    public static List<Vector2Int> GetSpawnPositions(GridTileObject spawnerGridInfo, int spawnerSizeX, int spawnerSizeZ, int spawnDirection = 0)
    {
        List<Vector2Int> positions = new List<Vector2Int>();

        if (spawnerGridInfo == null)
            return positions;

        // 方向向量映射
        Vector2Int[] directionVectors = {
            Vector2Int.down,    // 0 = 前方 (gridZ - 1)
            Vector2Int.right,   // 1 = 右方 (gridX + spawnerSizeX)
            Vector2Int.up,      // 2 = 后方 (gridZ + spawnerSizeZ)
            Vector2Int.left     // 3 = 左方 (gridX - 1)
        };

        Vector2Int direction = directionVectors[spawnDirection % 4];

        switch (spawnDirection % 4)
        {
            case 0: // 前方
                int frontZ = spawnerGridInfo.gridZ - 1;
                for (int x = spawnerGridInfo.gridX; x < spawnerGridInfo.gridX + spawnerSizeX; x++)
                {
                    positions.Add(new Vector2Int(x, frontZ));
                }
                break;

            case 1: // 右方
                int rightX = spawnerGridInfo.gridX + spawnerSizeX;
                for (int z = spawnerGridInfo.gridZ; z < spawnerGridInfo.gridZ + spawnerSizeZ; z++)
                {
                    positions.Add(new Vector2Int(rightX, z));
                }
                break;

            case 2: // 后方
                int backZ = spawnerGridInfo.gridZ + spawnerSizeZ;
                for (int x = spawnerGridInfo.gridX; x < spawnerGridInfo.gridX + spawnerSizeX; x++)
                {
                    positions.Add(new Vector2Int(x, backZ));
                }
                break;

            case 3: // 左方
                int leftX = spawnerGridInfo.gridX - 1;
                for (int z = spawnerGridInfo.gridZ; z < spawnerGridInfo.gridZ + spawnerSizeZ; z++)
                {
                    positions.Add(new Vector2Int(leftX, z));
                }
                break;
        }

        return positions;
    }

    /// <summary>
    /// 获取出库块内部位置（用于预生成）
    /// </summary>
    /// <param name="spawnerGridInfo">出库块的网格信息</param>
    /// <param name="spawnerSize">出库块尺寸（兼容旧版本）</param>
    /// <returns>内部位置列表</returns>
    public static List<Vector2Int> GetInternalPositions(GridTileObject spawnerGridInfo, int spawnerSize)
    {
        return GetInternalPositions(spawnerGridInfo, spawnerSize, spawnerSize);
    }

    /// <summary>
    /// 获取出库块内部位置（用于预生成）
    /// </summary>
    /// <param name="spawnerGridInfo">出库块的网格信息</param>
    /// <param name="spawnerSizeX">出库块X轴尺寸</param>
    /// <param name="spawnerSizeZ">出库块Z轴尺寸</param>
    /// <returns>内部位置列表</returns>
    public static List<Vector2Int> GetInternalPositions(GridTileObject spawnerGridInfo, int spawnerSizeX, int spawnerSizeZ)
    {
        List<Vector2Int> positions = new List<Vector2Int>();

        if (spawnerGridInfo == null)
            return positions;

        // 内部位置 - 出库块自身占用的格子
        for (int x = spawnerGridInfo.gridX; x < spawnerGridInfo.gridX + spawnerSizeX; x++)
        {
            for (int z = spawnerGridInfo.gridZ; z < spawnerGridInfo.gridZ + spawnerSizeZ; z++)
            {
                positions.Add(new Vector2Int(x, z));
            }
        }

        return positions;
    }

    /// <summary>
    /// 获取出库块预生成位置（左右两个特定位置）
    /// </summary>
    /// <param name="spawnerGridInfo">出库块的网格信息</param>
    /// <param name="spawnerSize">出库块尺寸（兼容旧版本）</param>
    /// <returns>预生成位置列表</returns>
    public static List<Vector2Int> GetPreSpawnPositions(GridTileObject spawnerGridInfo, int spawnerSize)
    {
        return GetPreSpawnPositions(spawnerGridInfo, spawnerSize, spawnerSize);
    }

    /// <summary>
    /// 获取出库块预生成位置（左右两个特定位置）
    /// </summary>
    /// <param name="spawnerGridInfo">出库块的网格信息</param>
    /// <param name="spawnerSizeX">出库块X轴尺寸</param>
    /// <param name="spawnerSizeZ">出库块Z轴尺寸</param>
    /// <returns>预生成位置列表</returns>
    public static List<Vector2Int> GetPreSpawnPositions(GridTileObject spawnerGridInfo, int spawnerSizeX, int spawnerSizeZ)
    {
        List<Vector2Int> positions = new List<Vector2Int>();

        if (spawnerGridInfo == null)
            return positions;

        // 对于2x3的出库块，我们在前方两个位置预生成（左前、右前）
        // 左前位置：出库块左前角
        Vector2Int leftPos = new Vector2Int(spawnerGridInfo.gridX, spawnerGridInfo.gridZ);
        positions.Add(leftPos);
        // 右前位置：出库块右前角  
        Vector2Int rightPos = new Vector2Int(spawnerGridInfo.gridX + 1, spawnerGridInfo.gridZ);
        positions.Add(rightPos);

        //Debug.Log($"出库块预生成位置计算：出库块位置({spawnerGridInfo.gridX}, {spawnerGridInfo.gridZ})，尺寸({spawnerSizeX}x{spawnerSizeZ})，左预生成{leftPos}，右预生成{rightPos}");

        return positions;
    }

    /// <summary>
    /// 批量创建SingleBlock
    /// </summary>
    /// <param name="colorIds">颜色ID列表</param>
    /// <param name="spawnPositions">生成位置列表</param>
    /// <param name="controller">TileCube控制器</param>
    /// <param name="spawnHeight">生成高度</param>
    /// <returns>成功创建的SingleBlock数量</returns>
    public static int CreateSingleBlocks(List<int> colorIds, List<Vector2Int> spawnPositions, TileCube_Controller controller, int spawnHeight = 0)
    {
        if (colorIds == null || spawnPositions == null || controller == null)
            return 0;

        int successCount = 0;
        int createCount = Mathf.Min(colorIds.Count, spawnPositions.Count);

        for (int i = 0; i < createCount; i++)
        {
            if (CreateSingleBlock(colorIds[i], spawnPositions[i], controller, spawnHeight))
            {
                successCount++;
            }
        }

        return successCount;
    }

    /// <summary>
    /// 创建单个SingleBlock
    /// </summary>
    /// <param name="colorId">颜色ID</param>
    /// <param name="gridPosition">网格位置</param>
    /// <param name="controller">TileCube控制器</param>
    /// <param name="spawnHeight">生成高度</param>
    /// <returns>是否创建成功</returns>
    public static bool CreateSingleBlock(int colorId, Vector2Int gridPosition, TileCube_Controller controller, int spawnHeight = 0)
    {
        if (controller == null)
        {
            //Debug.LogError("TileCube控制器为空，无法创建SingleBlock");
            return false;
        }

        // 获取SingleBlock预制体
        GameObject prefab = TileCube_Manager.GetSingleBlockPrefab(colorId);
        if (prefab == null)
        {
            //Debug.LogError($"无法找到颜色ID {colorId} 的SingleBlock预制体");
            return false;
        }

        // 实例化物体
        GameObject obj = Object.Instantiate(prefab, controller.TileCube_Parent.transform);
        TileCube_SingleBlock singleBlock = obj.GetComponent<TileCube_SingleBlock>();

        if (singleBlock == null)
        {
            //Debug.LogError("SingleBlock组件不存在");
            Object.Destroy(obj);
            return false;
        }

        // 创建数据
        var blockData = new TileCube_Data_SingleBlock
        {
            GridX = gridPosition.x,
            GridZ = gridPosition.y,
            Height = spawnHeight,
            ColorId = colorId
        };

        // 初始化单一块
        singleBlock.Init(blockData);

        // 放置到网格上
        var tileGrid = controller.TileGrid;
        if (tileGrid != null)
        {
            var gridInfo = tileGrid.PlaceObject(obj, gridPosition.x, gridPosition.y, spawnHeight, 1, 1, false);
            if (gridInfo != null)
            {
                singleBlock.SetGridInfo(gridInfo);
                controller.Current_Objects.Add(singleBlock);
                return true;
            }
            else
            {
                //Debug.LogError($"无法将SingleBlock放置到网格位置 ({gridPosition.x}, {gridPosition.y}, {spawnHeight})");
                Object.Destroy(obj);
                return false;
            }
        }

        return false;
    }

    /// <summary>
    /// 创建单个SingleBlock并返回组件引用（用于预生成）
    /// </summary>
    /// <param name="colorId">颜色ID</param>
    /// <param name="gridPosition">网格位置</param>
    /// <param name="controller">TileCube控制器</param>
    /// <param name="spawnHeight">生成高度</param>
    /// <returns>创建的SingleBlock组件，失败返回null</returns>
    public static TileCube_SingleBlock CreateSingleBlockWithReference(int colorId, Vector2Int gridPosition, TileCube_Controller controller, int spawnHeight = 0)
    {
        if (controller == null)
        {
            //Debug.LogError("TileCube控制器为空，无法创建SingleBlock");
            return null;
        }

        // 获取SingleBlock预制体
        GameObject prefab = TileCube_Manager.GetSingleBlockPrefab(colorId);
        if (prefab == null)
        {
            //Debug.LogError($"无法找到颜色ID {colorId} 的SingleBlock预制体");
            return null;
        }

        // 实例化物体
        GameObject obj = Object.Instantiate(prefab, controller.TileCube_Parent.transform);
        TileCube_SingleBlock singleBlock = obj.GetComponent<TileCube_SingleBlock>();

        if (singleBlock == null)
        {
            //Debug.LogError("SingleBlock组件不存在");
            Object.Destroy(obj);
            return null;
        }

        // 创建数据
        var blockData = new TileCube_Data_SingleBlock
        {
            GridX = gridPosition.x,
            GridZ = gridPosition.y,
            Height = spawnHeight,
            ColorId = colorId
        };

        // 初始化单一块
        singleBlock.Init(blockData);

        // 放置到网格上
        var tileGrid = controller.TileGrid;
        if (tileGrid != null)
        {
            var gridInfo = tileGrid.PlaceObject(obj, gridPosition.x, gridPosition.y, spawnHeight, 1, 1, false);
            if (gridInfo != null)
            {
                singleBlock.SetGridInfo(gridInfo);
                controller.Current_Objects.Add(singleBlock);
                return singleBlock;
            }
            else
            {
                //Debug.LogError($"无法将SingleBlock放置到网格位置 ({gridPosition.x}, {gridPosition.y}, {spawnHeight})");
                Object.Destroy(obj);
                return null;
            }
        }

        return null;
    }

    /// <summary>
    /// 移动SingleBlock到新位置（用于预生成过渡）
    /// </summary>
    /// <param name="block">要移动的SingleBlock</param>
    /// <param name="newGridPosition">新的网格位置</param>
    /// <param name="newHeight">新的高度层</param>
    /// <param name="controller">TileCube控制器</param>
    /// <returns>是否移动成功</returns>
    public static bool MoveSingleBlockToPosition(TileCube_SingleBlock block, Vector2Int newGridPosition, int newHeight, TileCube_Controller controller)
    {
        if (block == null || block.gameObject == null || controller == null)
            return false;

        var tileGrid = controller.TileGrid;
        if (tileGrid == null)
            return false;

        // 先从网格中移除
        tileGrid.RemoveObject(block.gameObject);

        // 重新放置到新位置
        var newGridInfo = tileGrid.PlaceObject(block.gameObject, newGridPosition.x, newGridPosition.y, newHeight, 1, 1, false);
        if (newGridInfo != null)
        {
            block.SetGridInfo(newGridInfo);

            // 更新单块的数据
            var blockData = new TileCube_Data_SingleBlock
            {
                GridX = newGridPosition.x,
                GridZ = newGridPosition.y,
                Height = newHeight,
                ColorId = block.ColorId
            };
            block.Init(blockData);

            // 更新世界位置
            Vector3 targetWorldPos = tileGrid.GridToWorldPosition(newGridPosition.x, newGridPosition.y, newHeight, 1, 1);
            block.transform.position = targetWorldPos;

            return true;
        }

        return false;
    }

    /// <summary>
    /// 清理SingleBlock列表
    /// </summary>
    /// <param name="blocks">要清理的SingleBlock列表</param>
    /// <param name="controller">TileCube控制器</param>
    public static void CleanupSingleBlocks(List<TileCube_SingleBlock> blocks, TileCube_Controller controller)
    {
        if (blocks == null || controller == null)
            return;

        foreach (var block in blocks)
        {
            if (block != null && block.gameObject != null)
            {
                // 从控制器列表中移除
                controller.Current_Objects.Remove(block);

                // 从网格中移除
                if (controller.TileGrid != null)
                {
                    controller.TileGrid.RemoveObject(block.gameObject);
                }

                // 销毁游戏对象
                Object.Destroy(block.gameObject);
            }
        }
        blocks.Clear();
    }

    /// <summary>
    /// 计算过渡动画进度
    /// </summary>
    /// <param name="progress">原始进度 (0-1)</param>
    /// <param name="curveType">曲线类型 (0=线性, 1=平滑, 2=快进慢出)</param>
    /// <returns>调整后的进度 (0-1)</returns>
    public static float CalculateAnimationProgress(float progress, int curveType)
    {
        switch (curveType)
        {
            case 0: // 线性
                return progress;

            case 1: // 平滑 (SmoothStep)
                return Mathf.SmoothStep(0f, 1f, progress);

            case 2: // 快进慢出 (EaseOut)
                return 1f - Mathf.Pow(1f - progress, 2f);

            case 3: // 慢进快出 (EaseIn)
                return Mathf.Pow(progress, 2f);

            case 4: // 弹性 (Elastic)
                return ElasticEaseOut(progress);

            default:
                return Mathf.SmoothStep(0f, 1f, progress);
        }
    }

    /// <summary>
    /// 弹性缓动函数
    /// </summary>
    private static float ElasticEaseOut(float t)
    {
        if (t == 0f || t == 1f) return t;

        float p = 0.3f;
        float s = p / 4f;
        return Mathf.Pow(2f, -10f * t) * Mathf.Sin((t - s) * (2f * Mathf.PI) / p) + 1f;
    }

    /// <summary>
    /// 检查库存是否为空
    /// </summary>
    /// <param name="inventory">库存列表</param>
    /// <returns>是否为空</returns>
    public static bool IsInventoryEmpty(List<int> inventory)
    {
        return inventory == null || inventory.Count == 0;
    }

    /// <summary>
    /// 从库存中取出指定数量的物品
    /// </summary>
    /// <param name="inventory">库存列表</param>
    /// <param name="count">要取出的数量</param>
    /// <returns>取出的物品列表</returns>
    public static List<int> TakeFromInventory(List<int> inventory, int count)
    {
        List<int> takenItems = new List<int>();

        if (inventory == null || inventory.Count == 0)
            return takenItems;

        int takeCount = Mathf.Min(count, inventory.Count);

        for (int i = 0; i < takeCount; i++)
        {
            takenItems.Add(inventory[0]);
            inventory.RemoveAt(0);
        }

        return takenItems;
    }

    /// <summary>
    /// 获取库存剩余数量
    /// </summary>
    /// <param name="inventory">库存列表</param>
    /// <returns>剩余数量</returns>
    public static int GetInventoryCount(List<int> inventory)
    {
        return inventory?.Count ?? 0;
    }

    /// <summary>
    /// 创建出库块数据
    /// </summary>
    /// <param name="gridX">网格X坐标</param>
    /// <param name="gridZ">网格Z坐标</param>
    /// <param name="spawnColors">生成颜色列表</param>
    /// <returns>出库块数据</returns>
    public static TileCube_Data_Spawner CreateSpawnerData(int gridX, int gridZ, List<int> spawnColors)
    {
        return new TileCube_Data_Spawner
        {
            GridX = gridX,
            GridZ = gridZ,
            SpawnColorSingleBlocks = spawnColors != null ? new List<int>(spawnColors) : new List<int>()
        };
    }
}