using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 钥匙 - 推进到最前方的位置时发出触发通知，外部决定触发消除占位
/// </summary>
public class TileCube_Key : TileCube_Object
{
    [Header("钥匙特有属性")]
    public int KeyId;                       // 钥匙ID（用于匹配锁）
    public bool HasReachedFront = false;    // 是否已到达最前方
    public bool IsSpecialType = true;       // 特殊类型标记

    [Header("检测配置")]
    public float DetectionInterval = 0.1f;  // 检测间隔时间

    [Header("飞行动画配置")]
    public float flightHeight = 2f;                    // 上方插入高度
    public float flightTime = 2f;                      // 整体飞行时间（包含飞行+旋转+缩放）
    public float insertAngle = 45f;                     // 插入角度
    public float insertScale = 0.8f;                   // 插入时的缩放大小

    public float insertDuration = 0.3f;                // 插入动画持续时间
    public float unlockAngleY = 90f;                   // 开锁角度Y
    public float unlockRotationTime = 1f;              // 旋转开锁时间
    public float expandTime = 0.25f;                    // 放大时间
    public float expandScale = 1.5f;                   // 放大大小
    public float shrinkTime = 0.3f;                    // 缩小时间

    [Header("待机旋转配置")]
    public float idleRotationSpeed = 30f;              // 待机时Y轴旋转速度

    private TileCube_Data_Key keyData;
    private Coroutine frontDetectionCoroutine; // 前方检测协程
    private TileCube_Controller controller;   // 控制器引用

    // 飞行相关状态
    private bool isFlying = false;                     // 飞出状态
    private Coroutine flightCoroutine;                 // 飞行协程
    private Coroutine idleRotationCoroutine;           // 待机旋转协程

    /// <summary>
    /// 初始化钥匙
    /// </summary>
    public void Init(TileCube_Data_Key data)
    {
        keyData = data;
        ObjectType = TileCube_ObjectType.Key;
        KeyId = data.KeyId;

        // 获取控制器引用
        controller = TileCube_Controller.Instance;

        // 开始前方检测
        StartFrontDetection();

        // 开始待机旋转
        StartIdleRotation();
    }
    void Update()
    {
        if (Input.GetKeyDown(KeyCode.A))
            FlyToTarget(new Vector3(3.87f, 0, -3.71f));
    }


    /// <summary>
    /// 开始前方检测
    /// </summary>
    private void StartFrontDetection()
    {
        if (frontDetectionCoroutine != null)
        {
            StopCoroutine(frontDetectionCoroutine);
        }
        frontDetectionCoroutine = StartCoroutine(FrontDetectionLoop());
    }

    /// <summary>
    /// 停止前方检测
    /// </summary>
    private void StopFrontDetection()
    {
        if (frontDetectionCoroutine != null)
        {
            StopCoroutine(frontDetectionCoroutine);
            frontDetectionCoroutine = null;
        }
    }

    /// <summary>
    /// 前方检测循环
    /// </summary>
    private IEnumerator FrontDetectionLoop()
    {
        while (true)
        {
            yield return new WaitForSeconds(DetectionInterval);

            if (!HasReachedFront && IsAtFrontOfGrid())
            {
                HasReachedFront = true;
                OnReachedFront();
            }
        }
    }

    /// <summary>
    /// 检查是否到达网格最前方
    /// </summary>
    private bool IsAtFrontOfGrid()
    {
        if (controller == null || controller.TileGrid == null || gridInfo == null)
            return false;

        // 网格的推进方向是向Z轴负方向（gridZ - 1），最前方是 gridZ = 0
        // 当钥匙到达 gridZ = 0 时，表示已到达网格最前方
        return gridInfo.gridZ <= 0;
    }

    /// <summary>
    /// 到达最前方时的处理
    /// </summary>
    private void OnReachedFront()
    {
        StartCoroutine("Delay_OnKeyReachedFront");
    }

    IEnumerator Delay_OnKeyReachedFront()
    {
        yield return new WaitForSeconds(0.5f);
        TileCube_Controller.OnKeyReachedFront?.Invoke(this);

        // 停止检测，避免重复触发
        StopFrontDetection();
    }

    #region 飞行开锁动画系统

    /// <summary>
    /// 钥匙飞向目标位置并执行开锁动画
    /// </summary>
    /// <param name="targetPosition">目标位置</param>
    public void FlyToTarget(Vector3 targetPosition)
    {
        if (flightCoroutine != null)
        {
            StopCoroutine(flightCoroutine);
        }

        flightCoroutine = StartCoroutine(FlightSequence(targetPosition));
    }

    /// <summary>
    /// 完整的飞行开锁序列
    /// </summary>
    private IEnumerator FlightSequence(Vector3 targetPosition)
    {
        // 1. 设置飞出状态，清除位置占用
        isFlying = true;
        StopIdleRotation();

        // 清除网格占用
        if (controller != null && controller.TileGrid != null)
        {
            controller.TileGrid.RemoveObject(gameObject);
        }

        Vector3 startPos = transform.position;
        Vector3 flightTarget = targetPosition + Vector3.up * flightHeight;

        TileCube_Controller.On_PlaySound?.Invoke("CNT_宝箱飞起");

        // 2. 抛物线飞行 + 旋转 + 缩放（使用设定的飞行时间）
        yield return StartCoroutine(ParabolicFlight(startPos, flightTarget, flightTime));

        // 3. 插入动画
        yield return StartCoroutine(InsertAnimation(flightTarget, targetPosition));

        // 4. 开锁动画
        yield return StartCoroutine(UnlockAnimation(targetPosition));


        TileCube_Controller.On_PlaySound?.Invoke("CNT_宝箱解锁");
        TileCube_Controller.On_PlayVib?.Invoke(MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data.Virb_Normal);

        // 5. 放大缩小销毁
        yield return StartCoroutine(FinalScaleAndDestroy());
    }

    /// <summary>
    /// 抛物线飞行动画（在设定时间内完成飞行+旋转+缩放）
    /// </summary>
    private IEnumerator ParabolicFlight(Vector3 start, Vector3 target, float duration)
    {
        Vector3 startScale = transform.localScale;
        Vector3 targetScale = startScale * insertScale;

        Vector3 startEuler = transform.eulerAngles;
        float startY = startEuler.y;
        float targetY = insertAngle;

        float elapsed = 0f;
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;

            // 先快后慢的缓动曲线
            float easedT = 1f - Mathf.Pow(1f - t, 2f);

            // 抛物线位置计算
            Vector3 currentPos = Vector3.Lerp(start, target, easedT);
            // 添加抛物线高度弧线
            float parabolicHeight = 4f * (t - t * t) * Vector3.Distance(start, target) * 0.3f;
            currentPos.y += parabolicHeight;

            transform.position = currentPos;

            // 在飞行时间内同时进行缩放
            transform.localScale = Vector3.Lerp(startScale, targetScale, t);

            // 在飞行时间内同时进行Y轴旋转到插入角度
            float currentY = Mathf.LerpAngle(startY, targetY, t);
            transform.eulerAngles = new Vector3(startEuler.x, currentY, startEuler.z);

            yield return null;
        }

        // 确保到达精确位置、缩放和角度
        transform.position = target;
        transform.localScale = targetScale;
        transform.eulerAngles = new Vector3(startEuler.x, targetY, startEuler.z);
    }

    /// <summary>
    /// 插入动画（直接平滑插入到目标位置）
    /// </summary>
    private IEnumerator InsertAnimation(Vector3 fromPos, Vector3 targetPos)
    {
        // 直接开始平滑插入到目标位置
        float elapsed = 0f;

        while (elapsed < insertDuration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / insertDuration;

            // 平滑插入到目标位置
            transform.position = Vector3.Lerp(fromPos, targetPos, Mathf.SmoothStep(0f, 1f, t));
            yield return null;
        }

        transform.position = targetPos;
    }

    /// <summary>
    /// 开锁旋转动画（只旋转Y轴）
    /// </summary>
    private IEnumerator UnlockAnimation(Vector3 targetPos)
    {
        TileCube_Controller.OnKeyStartUnlock?.Invoke(this, targetPos);

        Vector3 currentEuler = transform.eulerAngles;
        float startY = currentEuler.y;
        float targetY = unlockAngleY;

        float elapsed = 0f;
        while (elapsed < unlockRotationTime)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / unlockRotationTime;

            // 只旋转Y轴到开锁角度
            float currentY = Mathf.LerpAngle(startY, targetY, Mathf.SmoothStep(0f, 1f, t));
            transform.eulerAngles = new Vector3(currentEuler.x, currentY, currentEuler.z);
            yield return null;
        }

        // 确保到达精确角度
        transform.eulerAngles = new Vector3(currentEuler.x, targetY, currentEuler.z);
    }

    /// <summary>
    /// 最终放大缩小并销毁
    /// </summary>
    private IEnumerator FinalScaleAndDestroy()
    {
        Vector3 currentScale = transform.localScale;
        Vector3 maxScale = currentScale * expandScale;

        // 放大阶段
        float elapsed = 0f;
        while (elapsed < expandTime)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / expandTime;

            transform.localScale = Vector3.Lerp(currentScale, maxScale, Mathf.SmoothStep(0f, 1f, t));
            yield return null;
        }

        transform.localScale = maxScale;

        // 缩小阶段
        elapsed = 0f;
        while (elapsed < shrinkTime)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / shrinkTime;

            transform.localScale = Vector3.Lerp(maxScale, Vector3.zero, Mathf.SmoothStep(0f, 1f, t));
            yield return null;
        }

        // 确保完全销毁：通过Controller销毁以更新进度
        DestroyObject();
    }

    #endregion

    #region 待机旋转系统

    /// <summary>
    /// 开始待机旋转
    /// </summary>
    private void StartIdleRotation()
    {
        if (!isFlying && idleRotationCoroutine == null)
        {
            idleRotationCoroutine = StartCoroutine(IdleRotationLoop());
        }
    }

    /// <summary>
    /// 停止待机旋转
    /// </summary>
    private void StopIdleRotation()
    {
        if (idleRotationCoroutine != null)
        {
            StopCoroutine(idleRotationCoroutine);
            idleRotationCoroutine = null;
        }
    }

    /// <summary>
    /// 待机旋转循环（只旋转Y轴）
    /// </summary>
    private IEnumerator IdleRotationLoop()
    {
        while (!isFlying)
        {
            // 只绕Y轴旋转
            Vector3 currentEuler = transform.eulerAngles;
            currentEuler.y += idleRotationSpeed * Time.deltaTime;
            transform.eulerAngles = currentEuler;
            yield return null;
        }
    }

    #endregion

    /// <summary>
    /// 销毁钥匙 - 从控制器和网格中清除
    /// </summary>
    public void DestroyKey()
    {
        // 触发进度更新（钥匙销毁时减1）
        TileCube_Controller.Instance?.UpdateProgress(1);

        // 停止所有协程
        StopFrontDetection();
        StopIdleRotation();

        if (flightCoroutine != null)
        {
            StopCoroutine(flightCoroutine);
            flightCoroutine = null;
        }

        // 从控制器列表中移除
        if (controller != null && controller.Current_Objects.Contains(this))
        {
            controller.Current_Objects.Remove(this);
        }

        // 从网格中移除
        if (controller != null && controller.TileGrid != null)
        {
            controller.TileGrid.RemoveObject(gameObject);
        }

        // 销毁游戏对象
        Destroy(gameObject);
    }

    /// <summary>
    /// 重写基类的销毁方法
    /// </summary>
    public override void DestroyObject()
    {
        DestroyKey();
    }

    /// <summary>
    /// 手动重置前方到达状态（用于测试或特殊情况）
    /// </summary>
    public void ResetFrontStatus()
    {
        HasReachedFront = false;
        StartFrontDetection();
    }

    /// <summary>
    /// 获取当前是否正在飞行
    /// </summary>
    public bool IsFlying()
    {
        return isFlying;
    }

    protected override void OnDestroy()
    {
        // 确保所有协程被停止
        StopFrontDetection();
        StopIdleRotation();

        if (flightCoroutine != null)
        {
            StopCoroutine(flightCoroutine);
        }

        base.OnDestroy();
    }
}