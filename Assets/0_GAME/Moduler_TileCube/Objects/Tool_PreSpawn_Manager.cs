using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 预生成管理工具 - 管理出块库的预生成功能
/// 功能：预生成单块、过渡动画、状态管理
/// 适合工具化，方便其他项目复用
/// </summary>
public class Tool_PreSpawn_Manager
{
    #region 配置参数
    public float TransitionDuration { get; set; } = 0.8f;
    public int TransitionCurveType { get; set; } = 1;
    public bool EnablePreSpawn { get; set; } = true;
    #endregion

    #region 状态管理
    private List<TileCube_SingleBlock> preSpawnedBlocks = new List<TileCube_SingleBlock>();
    private bool isTransitioning = false;
    private MonoBehaviour ownerBehaviour; // 拥有者行为组件，用于启动协程
    #endregion

    #region 构造函数
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="owner">拥有者MonoBehaviour组件</param>
    public Tool_PreSpawn_Manager(MonoBehaviour owner)
    {
        ownerBehaviour = owner;
    }
    #endregion

    #region 公共接口
    /// <summary>
    /// 检查是否可以预生成
    /// </summary>
    /// <param name="isSpawning">是否正在生成</param>
    /// <param name="isDestroying">是否正在销毁</param>
    /// <param name="hasInventory">是否有库存</param>
    /// <returns>是否可以预生成</returns>
    public bool CanPreSpawn(bool isSpawning, bool isDestroying, bool hasInventory)
    {
        if (!EnablePreSpawn)
            return false;

        // 如果正在生成、正在销毁、正在过渡或没有库存，不能预生成
        if (isSpawning || isDestroying || isTransitioning || !hasInventory)
            return false;

        // 检查是否已经有预生成的单块
        if (preSpawnedBlocks.Count > 0)
            return false;

        return true;
    }

    /// <summary>
    /// 检查是否可以过渡到前方
    /// </summary>
    /// <param name="hasFrontSpace">前方是否有空间</param>
    /// <returns>是否可以过渡</returns>
    public bool CanTransitionToFront(bool hasFrontSpace)
    {
        if (!EnablePreSpawn)
            return false;

        // 如果正在过渡、没有预生成的单块，不能过渡
        if (isTransitioning || preSpawnedBlocks.Count == 0)
            return false;

        return hasFrontSpace;
    }

    /// <summary>
    /// 预生成单块
    /// </summary>
    /// <param name="colorId">颜色ID</param>
    /// <param name="internalPosition">内部位置</param>
    /// <param name="height">高度层</param>
    /// <param name="controller">控制器</param>
    /// <returns>是否预生成成功</returns>
    public bool PreSpawnBlock(int colorId, Vector2Int internalPosition, int height, TileCube_Controller controller)
    {
        if (!EnablePreSpawn)
            return false;

        TileCube_SingleBlock preSpawnedBlock = TileCube_Spawner_Func.CreateSingleBlockWithReference(
            colorId, internalPosition, controller, height);

        if (preSpawnedBlock != null)
        {
            preSpawnedBlocks.Add(preSpawnedBlock);
            //Debug.Log($"预生成管理器：成功预生成单块，内部位置：({internalPosition.x}, {internalPosition.y})");
            return true;
        }

        return false;
    }

    /// <summary>
    /// 开始过渡到前方
    /// </summary>
    /// <param name="frontPosition">前方位置</param>
    /// <param name="controller">控制器</param>
    /// <param name="onComplete">完成回调</param>
    public void StartTransitionToFront(Vector2Int frontPosition, TileCube_Controller controller, System.Action onComplete = null)
    {
        if (!EnablePreSpawn || preSpawnedBlocks.Count == 0 || isTransitioning)
            return;

        if (ownerBehaviour != null)
        {
            ownerBehaviour.StartCoroutine(TransitionBlockToFrontCoroutine(frontPosition, controller, onComplete));
        }
    }

    /// <summary>
    /// 获取预生成单块数量
    /// </summary>
    /// <returns>预生成单块数量</returns>
    public int GetPreSpawnedCount()
    {
        return preSpawnedBlocks.Count;
    }

    /// <summary>
    /// 获取是否正在过渡
    /// </summary>
    /// <returns>是否正在过渡</returns>
    public bool IsTransitioning()
    {
        return isTransitioning;
    }

    /// <summary>
    /// 清理所有预生成的单块
    /// </summary>
    /// <param name="controller">控制器</param>
    public void CleanupPreSpawnedBlocks(TileCube_Controller controller)
    {
        TileCube_Spawner_Func.CleanupSingleBlocks(preSpawnedBlocks, controller);
    }

    /// <summary>
    /// 重置状态
    /// </summary>
    public void Reset()
    {
        isTransitioning = false;
        preSpawnedBlocks.Clear();
    }
    #endregion

    #region 私有方法
    /// <summary>
    /// 过渡到前方的协程
    /// </summary>
    private IEnumerator TransitionBlockToFrontCoroutine(Vector2Int frontPosition, TileCube_Controller controller, System.Action onComplete)
    {
        if (preSpawnedBlocks.Count == 0)
            yield break;

        isTransitioning = true;

        // 取出一个预生成的单块
        TileCube_SingleBlock blockToTransition = preSpawnedBlocks[0];
        preSpawnedBlocks.RemoveAt(0);

        // 开始过渡动画
        yield return ownerBehaviour.StartCoroutine(AnimateBlockTransition(blockToTransition, frontPosition, controller));

        isTransitioning = false;

        // 调用完成回调
        onComplete?.Invoke();

        //Debug.Log($"预生成管理器：成功过渡单块到前方，位置：({frontPosition.x}, {frontPosition.y})");
    }

    /// <summary>
    /// 单块过渡动画
    /// </summary>
    private IEnumerator AnimateBlockTransition(TileCube_SingleBlock block, Vector2Int targetPosition, TileCube_Controller controller)
    {
        if (block == null || block.gameObject == null)
            yield break;

        var tileGrid = controller?.TileGrid;
        if (tileGrid == null)
            yield break;

        // 记录起始位置
        Vector3 startWorldPos = block.transform.position;
        Vector3 targetWorldPos = tileGrid.GridToWorldPosition(targetPosition.x, targetPosition.y, 0, 1, 1);

        // 先从网格中移除（准备移动到新位置）
        tileGrid.RemoveObject(block.gameObject);

        // 动画过渡
        float elapsed = 0f;
        while (elapsed < TransitionDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / TransitionDuration;

            // 根据曲线类型计算位置
            float curveProgress = TileCube_Spawner_Func.CalculateAnimationProgress(progress, TransitionCurveType);
            Vector3 currentPos = Vector3.Lerp(startWorldPos, targetWorldPos, curveProgress);

            block.transform.position = currentPos;
            yield return null;
        }

        // 确保到达目标位置
        block.transform.position = targetWorldPos;

        // 重新放置到网格的目标位置
        TileCube_Spawner_Func.MoveSingleBlockToPosition(block, targetPosition, 0, controller);
    }
    #endregion

    #region 静态工厂方法
    /// <summary>
    /// 创建预生成管理器
    /// </summary>
    /// <param name="owner">拥有者MonoBehaviour组件</param>
    /// <param name="transitionDuration">过渡持续时间</param>
    /// <param name="transitionCurveType">过渡曲线类型</param>
    /// <param name="enablePreSpawn">是否启用预生成</param>
    /// <returns>预生成管理器实例</returns>
    public static Tool_PreSpawn_Manager Create(MonoBehaviour owner, float transitionDuration = 0.8f, int transitionCurveType = 1, bool enablePreSpawn = true)
    {
        var manager = new Tool_PreSpawn_Manager(owner)
        {
            TransitionDuration = transitionDuration,
            TransitionCurveType = transitionCurveType,
            EnablePreSpawn = enablePreSpawn
        };

        return manager;
    }
    #endregion
}