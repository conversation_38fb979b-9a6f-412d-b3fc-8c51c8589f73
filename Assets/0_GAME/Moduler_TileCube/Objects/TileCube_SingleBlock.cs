using System.Collections;
using System.Collections.Generic;
using System.Linq;
using JRJelly;
using UnityEngine;

/// <summary>
/// 单一块 - 只占一格，可以叠多格高度层，有不同的颜色
/// 使用材质批处理优化，避免材质实例化
/// </summary>
public class TileCube_SingleBlock : TileCube_Object
{
    [Header("单一块属性")]
    public bool CanStack = true;            // 是否可以堆叠
    public int MaxStackHeight = 10;         // 最大堆叠高度
    public int ColorId;                     // 颜色ID

    [Header("渲染组件")]
    public MeshRenderer meshRenderer;       // 渲染器

    [Header("果冻效果设置")]
    [Tooltip("推进刹车效果强度")]
    [Range(0.1f, 2f)]
    public float pushBrakeIntensity = 0.8f;

    [Tooltip("子弹穿过甩动基础强度")]
    [Range(0.1f, 2f)]
    public float bulletPassSwingIntensity = 0.5f;

    [Tooltip("子弹击中甩动强度")]
    [Range(0.1f, 2f)]
    public float bulletHitSwingIntensity = 1.2f;

    [Tooltip("击中后缩小持续时间")]
    [Range(0.2f, 1f)]
    public float hitShrinkDuration = 0.3f;

    [Header("子弹检测参数")]

    [Tooltip("穿过甩动持续时间")]
    [Range(0.1f, 1f)]
    public float bulletPassSwingDuration = 0.6f;

    [Tooltip("击中甩动持续时间")]
    [Range(0.1f, 1f)]
    public float bulletHitSwingDuration = 0.8f;

    [Tooltip("击中回来缩小的时间比例")]
    [Range(0f, 1f)]
    public float bulletHitToScalePerent = 0.6f;

    // 预留块状态标记
    [Header("预留块状态")]
    [Tooltip("是否为预留块状态（预留块状态下不能被子弹击中）")]
    public bool IsPreSpawnState = false;

    public bool isByBulletLocked = false;
    public bool isByBulletHited = false;

    // 私有变量
    public TileCube_Data_SingleBlock blockData;
    private MaterialPropertyBlock propertyBlock;

    public Tool_JRJELLY_Simple tool_JRJELLY_Simple;

    #region 高度压缩功能相关变量

    // 高度压缩功能相关变量
    private Vector3 originalLocalScale; // 记录原始的local scale
    private bool isInCompressionZone = false; // 是否在压缩区域
    private bool isCompressing = false; // 是否正在压缩过程中
    private bool isRestoring = false; // 是否正在恢复过程中
    private Coroutine compressionCoroutine; // 压缩协程
    private int lastKnownGridZ = -1; // 上次已知的网格Z坐标

    #endregion



    #region 初始化方法

    /// <summary>
    /// 初始化单一块
    /// </summary>
    public void Init(TileCube_Data_SingleBlock data)
    {
        blockData = data;
        ObjectType = TileCube_ObjectType.SingleBlock;
        ColorId = data.ColorId;

        // 记录原始local scale（用于高度压缩功能） - 只在第一次初始化时记录
        if (originalLocalScale == Vector3.zero)
        {
            originalLocalScale = transform.localScale;
            //Debug.Log($"[{name}] 第一次初始化，记录originalLocalScale: {originalLocalScale}");
        }
        else
        {
            //Debug.Log($"[{name}] 重复初始化，保持originalLocalScale: {originalLocalScale}，当前scale: {transform.localScale}");
        }

        /* 
                tool_JRJELLY_Simple = GetComponent<Tool_JRJELLY_Simple>();

                if (meshRenderer == null)
                    meshRenderer = GetComponent<MeshRenderer>();
         */
        // 初始化批处理优化
        InitializeBatchingOptimization();

        // 应用颜色
        if (ColorId > 0)
            SetColor(ColorId);
    }



    /// <summary>
    /// 初始化批处理优化
    /// </summary>
    private void InitializeBatchingOptimization()
    {
        if (meshRenderer == null) return;

        // 只创建一次PropertyBlock
        if (propertyBlock == null)
        {
            propertyBlock = new MaterialPropertyBlock();
            //Debug.Log($"[{gameObject.name}] PropertyBlock创建完成");
        }

        // 获取当前状态
        meshRenderer.GetPropertyBlock(propertyBlock);
    }



    #endregion

    #region 颜色和材质属性设置

    /// <summary>
    /// 设置颜色
    /// </summary>
    public virtual void SetColor(int colorId)
    {
        ColorId = colorId;
        Color color = MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data.GetColorById(colorId);
        ApplyColor(color);
    }


    /// <summary>
    /// 应用颜色到材质
    /// </summary>
    protected virtual void ApplyColor(Color color)
    {
        if (meshRenderer == null) return;


        //meshRenderer.material.SetColor("_Color", color);
        // 直接设置颜色，无需重复创建PropertyBlock
        Tool_MaterialBatchOptimizer.SetColor(meshRenderer, propertyBlock, color);
    }

    /// <summary>
    /// 设置材质属性（支持多种属性类型）
    /// </summary>
    public void SetMaterialProperties(Tool_MaterialBatchOptimizer.MaterialPropertyData propertyData)
    {
        if (meshRenderer == null) return;

        // 直接应用属性，无需重复创建PropertyBlock
        Tool_MaterialBatchOptimizer.ApplyPropertiesToBlock(propertyBlock, propertyData);
    }




    #endregion

    #region 网格信息重写

    /// <summary>
    /// 重写设置网格信息，初始化压缩功能相关数据
    /// </summary>
    public override void SetGridInfo(GridTileObject info)
    {
        base.SetGridInfo(info);

        // 初始化lastKnownGridZ并立即检查是否应该在压缩区域
        if (info != null)
        {
            lastKnownGridZ = info.gridZ;

            // 立即检查初始状态是否应该在压缩区域
            var controller = TileCube_Controller.Instance;
            if (controller != null)
            {
                bool shouldBeInCompressionZone = info.gridZ > controller.HeightProp_ClearColorGrid_compressionZThreshold;
                //Debug.Log($"[{name}] 初始化压缩状态检查 - Z: {info.gridZ}, 阈值: {controller.HeightProp_ClearColorGrid_compressionZThreshold}, 应该在压缩区域: {shouldBeInCompressionZone}");

                if (shouldBeInCompressionZone && !isInCompressionZone)
                {
                    //Debug.Log($"[{name}] 初始化时进入压缩区域");
                    EnterCompressionZone(controller);
                }

            }
        }
    }

    #endregion

    #region 游戏逻辑方法

    /// <summary>
    /// 物体被击打时的处理逻辑
    /// </summary>
    public virtual void OnHit(int damage = 1)
    {
        //Debug.Log($"SingleBlock {name} 被击打，伤害: {damage}");
        // 可以在这里添加特效、音效等
    }

    /// <summary>
    /// 支持堆叠的特殊逻辑
    /// </summary>
    public bool CanStackWith(TileCube_SingleBlock other)
    {
        if (!CanStack || !other.CanStack) return false;

        var myPos = GetGridPosition();
        var otherPos = other.GetGridPosition();

        return myPos.x == otherPos.x && myPos.z == otherPos.z;
    }

    /// <summary>
    /// 获取堆叠高度
    /// </summary>
    public int GetStackHeight()
    {
        return GetGridPosition().y;
    }

    /// <summary>
    /// 检查是否在最高层
    /// </summary>
    public bool IsAtTopLayer()
    {
        if (gridInfo == null) return true;

        var tileGrid = TileCube_Controller.Instance?.TileGrid;
        if (tileGrid != null)
        {
            var aboveObject = tileGrid.GetObjectAtPosition(gridInfo.gridX, gridInfo.gridZ, gridInfo.height + 1);
            return aboveObject == null;
        }

        return true;
    }

    public override void OnRebound()
    {
        // 检测网格位置变化，处理高度压缩
        CheckHeightCompression();

        // 应用Z行隐藏优化
        TileCube_Controller.Instance.ApplyZRowOptimizationToSingleBlock(this);

        // 推进完成时触发果冻刹车效果
        if (tool_JRJELLY_Simple != null)
        {

            // 检查前方格子是否有物体
            if (HasObjectInFront() || gridInfo.gridZ == 0)
            {
                // 将2D方向转换为3D方向（Y轴设为0，保持在水平面）
                Vector3 brakeDirection = -transform.forward;
                bool success = tool_JRJELLY_Simple.TriggerRebound(brakeDirection, pushBrakeIntensity, 0.3f);


            }

        }
    }

    /// <summary>
    /// 检查前方格子是否有物体
    /// </summary>
    /// <returns>前方是否有物体</returns>
    private bool HasObjectInFront()
    {
        if (gridInfo == null) return false;

        var controller = TileCube_Controller.Instance;
        if (controller?.TileGrid == null) return false;

        // 推进方向是向Z轴负方向，前方位置是gridZ - 1
        int frontGridX = gridInfo.gridX;
        int frontGridZ = gridInfo.gridZ - 1;

        // 检查前方位置的所有高度层是否有物体
        int maxHeight = controller.TileGrid.maxHeightLayers;
        for (int height = 0; height < maxHeight; height++)
        {
            var frontObject = controller.TileGrid.GetObjectAtPosition(frontGridX, frontGridZ, height);
            if (frontObject != null)
            {
                return true; // 找到任意高度层有物体就返回true
            }
        }

        return false; // 所有高度层都没有物体
    }

    #endregion

    #region 果冻甩动效果功能


    /// <summary>
    /// 处理子弹击中后的序列：甩动 -> 回弹 -> 缩小 -> 销毁（强制执行）
    /// </summary>
    private IEnumerator ProcessBulletHitSequence()
    {
        // 等待甩动效果基本完成（使用面板参数）
        yield return new WaitForSeconds(bulletHitSwingDuration * bulletHitToScalePerent);

        // 击中后强制执行缩小销毁，不再检查果冻活跃状态
        // 注释掉原来的等待逻辑，确保击中效果强制执行
        /*    
        // 检查果冻是否还有活跃效果
        while (tool_JRJELLY_Simple != null && tool_JRJELLY_Simple.HasActiveEffects)
        {
            yield return new WaitForSeconds(0.1f);
        } 
        */

        //Debug.Log($"[{name}] 击中后强制开始缩小销毁流程");

        // 强制开始缩小动画
        yield return StartCoroutine(ShrinkToZeroAndDestroy());
    }

    /// <summary>
    /// 缩小到0并销毁的动画
    /// </summary>
    private IEnumerator ShrinkToZeroAndDestroy()
    {
        Vector3 originalScale = transform.localScale;
        float elapsed = 0f;

        while (elapsed < hitShrinkDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / hitShrinkDuration;

            // 使用平滑的缩小曲线
            float scaleMultiplier = Mathf.Lerp(1f, 0f, Mathf.SmoothStep(0f, 1f, progress));
            transform.localScale = originalScale * scaleMultiplier;

            yield return null;
        }

        // 确保完全缩小
        transform.localScale = Vector3.zero;

        //Debug.Log($"[{name}] 缩小完成，通过Controller销毁");

        // 通过Controller销毁（这会正确处理网格移除等）
        DestroyByController();
    }

    /// <summary>
    /// 通过Controller销毁物体（推荐的销毁方式）
    /// </summary>
    private void DestroyByController()
    {
        // 触发进度更新（SingleBlock销毁时减1）
        TileCube_Controller.Instance?.UpdateProgress(1);

        // 使用基类的DestroyObject方法，它会正确处理网格移除和Controller列表清理
        DestroyObject();
    }

    #endregion

    #region Unity生命周期

    protected override void OnDestroy()
    {
        base.OnDestroy();

        // 停止压缩协程
        if (compressionCoroutine != null)
        {
            StopCoroutine(compressionCoroutine);
            compressionCoroutine = null;
        }


        StopAllCoroutines();
    }

    #endregion

    #region 预留块状态管理

    /// <summary>
    /// 设置预留块状态
    /// </summary>
    /// <param name="isPreSpawn">是否为预留块状态</param>
    public void SetPreSpawnState(bool isPreSpawn)
    {
        IsPreSpawnState = isPreSpawn;
        //Debug.Log($"[{name}] 预留块状态设置为：{(isPreSpawn ? "启用" : "禁用")}");
    }

    /// <summary>
    /// 获取预留块状态
    /// </summary>
    /// <returns>是否为预留块状态</returns>
    public bool GetPreSpawnState()
    {
        return IsPreSpawnState;
    }

    #endregion

    #region 子弹检测重写方法

    /// <summary>
    /// 重写基类：被子弹瞄准锁定时触发
    /// </summary>
    public override void OnBulletTargeted(GameObject bullet, object bulletData = null)
    {
        base.OnBulletTargeted(bullet, bulletData);

        // 如果是预留块状态，不响应子弹瞄准
        if (IsPreSpawnState)
        {
            //Debug.Log($"[{name}] 预留块状态，忽略子弹瞄准");
            return;
        }

        isByBulletLocked = true;

    }

    /// <summary>
    /// 重写基类：被子弹穿过时触发
    /// </summary>
    public override void OnBulletPenetrate(GameObject bullet, object bulletData = null)
    {
        base.OnBulletPenetrate(bullet);

        // 如果是预留块状态，不响应子弹穿过
        if (IsPreSpawnState)
        {
            //Debug.Log($"[{name}] 预留块状态，忽略子弹穿过效果");
            return;
        }

        if (tool_JRJELLY_Simple == null) return;

        // 如果已经被击中，不再处理穿过效果
        if (isByBulletHited)
        {
            //Debug.Log($"[{name}] 已被击中，忽略子弹穿过效果");
            return;
        }

        // 检查是否正在甩动，如果是则返回
        if (tool_JRJELLY_Simple.HasActiveEffects)
        {
            //Debug.Log($"[{name}] 正在甩动中，忽略子弹穿过");
            return;
        }

        Vector3 bulletPos = bullet.transform.position;

        // 直接使用固定的甩动强度，不计算距离影响
        float finalSwingIntensity = bulletPassSwingIntensity;

        // 触发甩动效果
        bool success = tool_JRJELLY_Simple.TriggerSwing(bulletPos, finalSwingIntensity, bulletPassSwingDuration);

        if (success)
        {
            //Debug.Log($"[{name}] 子弹穿过甩动 - 固定强度: {finalSwingIntensity:F2}");
        }
    }

    /// <summary>
    /// 重写基类：被子弹击中时触发
    /// </summary>
    public override bool OnBulletHit(GameObject bullet, object bulletData = null)
    {
        base.OnBulletHit(bullet, bulletData);

        // 如果是预留块状态，不响应子弹击中
        if (IsPreSpawnState)
        {
            //Debug.Log($"[{name}] 预留块状态，忽略子弹击中");
            return false; // 不销毁子弹，让子弹穿过
        }

        if (tool_JRJELLY_Simple == null) return false;
        if (isByBulletHited) return false; // 防止重复触发

        isByBulletHited = true;

        // 击中时强制停止所有正在进行的果冻效果，确保击中效果优先执行
        if (tool_JRJELLY_Simple.HasActiveEffects)
        {
            //Debug.Log($"[{name}] 击中时强制停止正在进行的果冻效果");
            tool_JRJELLY_Simple.StopAll(); // 停止所有正在进行的效果
        }

        float swingIntensity = bulletHitSwingIntensity;

        // 如果没有指定甩动强度，使用默认值
        if (swingIntensity < 0) swingIntensity = bulletHitSwingIntensity;

        // 强制触发击中甩动效果（不检查当前状态）
        bool swingSuccess = tool_JRJELLY_Simple.TriggerSwing(bullet.transform.position, swingIntensity, bulletHitSwingDuration);

        if (swingSuccess)
        {
            //Debug.Log($"[{name}] 子弹击中甩动（强制执行） - 位置: {bullet.transform.position}, 强度: {swingIntensity}");

            // 强制启动击中后的处理协程
            StartCoroutine(ProcessBulletHitSequence());
        }
        else
        {
            //Debug.LogWarning($"[{name}] 子弹击中甩动失败，强制直接销毁");
            // 如果甩动仍然失败，强制直接销毁
            StopAllCoroutines(); // 停止所有协程
            DestroyByController();
        }

        // 单一块被击中后销毁子弹
        return true;
    }

    #endregion

    #region 高度压缩功能

    /// <summary>
    /// 检测高度压缩状态
    /// </summary>
    private void CheckHeightCompression()
    {
        if (gridInfo == null)
        {
            //Debug.Log($"[{name}] gridInfo为null，跳过压缩检测");
            return;
        }

        var controller = TileCube_Controller.Instance;
        if (controller == null)
        {
            //Debug.Log($"[{name}] TileCube_Controller实例为null，跳过压缩检测");
            return;
        }

        int currentGridZ = gridInfo.gridZ;

        // 添加详细的调试信息
        // //Debug.Log($"[{name}] 压缩检测 - 当前Z: {currentGridZ}, 上次Z: {lastKnownGridZ}, 阈值: {controller.HeightProp_ClearColorGrid_compressionZThreshold}, 高度层: {gridInfo.height}, 压缩区域状态: {isInCompressionZone}");

        // 检测网格Z坐标是否发生变化
        if (lastKnownGridZ != currentGridZ)
        {
            lastKnownGridZ = currentGridZ;

            // 判断是否进入或离开压缩区域
            bool shouldBeInCompressionZone = currentGridZ > controller.HeightProp_ClearColorGrid_compressionZThreshold;

            //   //Debug.Log($"[{name}] Z坐标变化 - 应该在压缩区域: {shouldBeInCompressionZone} (Z: {currentGridZ} > 阈值: {controller.HeightProp_ClearColorGrid_compressionZThreshold})");

            if (shouldBeInCompressionZone && !isInCompressionZone)
            {
                // 进入压缩区域
                // //Debug.Log($"[{name}] 进入压缩区域，Z坐标: {currentGridZ}, 高度层: {gridInfo.height}");
                EnterCompressionZone(controller);
            }
            else if (!shouldBeInCompressionZone && isInCompressionZone)
            {
                // 离开压缩区域
                // //Debug.Log($"[{name}] 离开压缩区域，Z坐标: {currentGridZ}, 高度层: {gridInfo.height}");
                ExitCompressionZone(controller);
            }


        }

    }

    /// <summary>
    /// 进入压缩区域
    /// </summary>
    private void EnterCompressionZone(TileCube_Controller controller)
    {
        isInCompressionZone = true;

        if (compressionCoroutine != null)
        {
            StopCoroutine(compressionCoroutine);
        }

        compressionCoroutine = StartCoroutine(ProcessCompression(controller));
    }

    /// <summary>
    /// 离开压缩区域
    /// </summary>
    private void ExitCompressionZone(TileCube_Controller controller)
    {
        isInCompressionZone = false;

        if (compressionCoroutine != null)
        {
            StopCoroutine(compressionCoroutine);
        }

        // 只让同一位置的最低高度层的块来处理恢复，避免重复处理
        if (IsLowestBlockAtPosition())
        {
            compressionCoroutine = StartCoroutine(ProcessRestore(controller));
        }
    }

    /// <summary>
    /// 处理压缩过程
    /// </summary>
    private IEnumerator ProcessCompression(TileCube_Controller controller)
    {
        isCompressing = true;
        int currentHeight = gridInfo.height;

        if (currentHeight == controller.HeightProp_ClearColorGrid_compressionHeightA)
        {
            // 高度层A：直接设置压缩后的Y缩放，无动画过渡
            Vector3 compressedScale = originalLocalScale;
            compressedScale.y = originalLocalScale.y * controller.HeightProp_ClearColorGrid_compressedScale;
            transform.localScale = compressedScale;
            //Debug.Log($"[{name}] 高度层{currentHeight}立即压缩，从{originalLocalScale.y}直接设置为{compressedScale.y}");
        }
        else if (currentHeight >= controller.HeightProp_ClearColorGrid_compressionHeightB)
        {
            // 高度层B及以上：隐藏并设置Y缩放为0
            SetVisibility(false);
            Vector3 targetScale = originalLocalScale;
            targetScale.y = 0f;
            transform.localScale = targetScale;
            //Debug.Log($"[{name}] 高度层{currentHeight}立即隐藏");
        }

        isCompressing = false;
        yield break; // 由于没有动画，立即结束协程
    }

    /// <summary>
    /// 处理恢复过程
    /// </summary>
    private IEnumerator ProcessRestore(TileCube_Controller controller)
    {
        isRestoring = true;

        // 获取当前网格位置的所有高度层SingleBlock
        var allBlocksAtPosition = GetAllSingleBlocksAtSamePosition();

        // 按高度层从低到高排序，只处理需要恢复的高度层
        var blocksToRestore = allBlocksAtPosition
            .Where(block => block.gridInfo.height >= controller.HeightProp_ClearColorGrid_compressionHeightA)
            .OrderBy(block => block.gridInfo.height)
            .ToList();

        //Debug.Log($"[{name}] 开始恢复过程，共{blocksToRestore.Count}个块需要恢复");

        // 逐层恢复
        for (int i = 0; i < blocksToRestore.Count; i++)
        {
            var block = blocksToRestore[i];

            // 显示物体（如果之前被隐藏）
            block.SetVisibility(true);

            // 判断是否为最后一层
            bool isLastLayer = (i == blocksToRestore.Count - 1);

            if (isLastLayer)
            {
                // 最后一层：先缩放到原始值+弹跳值，再缩放回原始值
                //Debug.Log($"[{block.name}] 最后一层恢复，高度层{block.gridInfo.height}，带弹跳效果");
                yield return StartCoroutine(block.AnimateScaleY(block.transform.localScale.y, block.originalLocalScale.y + controller.HeightProp_ClearColorGrid_bounceScale, controller.HeightProp_ClearColorGrid_compressionAnimationDuration * 0.6f));
                yield return StartCoroutine(block.AnimateScaleY(block.originalLocalScale.y + controller.HeightProp_ClearColorGrid_bounceScale, block.originalLocalScale.y, controller.HeightProp_ClearColorGrid_compressionAnimationDuration * 0.4f));
            }
            else
            {
                // 其他层：直接恢复到原始值
                //Debug.Log($"[{block.name}] 恢复高度层{block.gridInfo.height}，从{block.transform.localScale.y}到{block.originalLocalScale.y}");
                yield return StartCoroutine(block.AnimateScaleY(block.transform.localScale.y, block.originalLocalScale.y, controller.HeightProp_ClearColorGrid_compressionAnimationDuration));
            }

            // 等待当前层恢复完成后再处理下一层
            yield return new WaitForSeconds(0.1f);
        }

        isRestoring = false;
    }

    /// <summary>
    /// 获取同一网格位置的所有SingleBlock
    /// </summary>
    private List<TileCube_SingleBlock> GetAllSingleBlocksAtSamePosition()
    {
        var result = new List<TileCube_SingleBlock>();
        var controller = TileCube_Controller.Instance;
        if (controller == null || gridInfo == null) return result;

        // 遍历所有SingleBlock，找到同一位置的
        foreach (var obj in controller.Current_Objects)
        {
            if (obj is TileCube_SingleBlock singleBlock && singleBlock.gridInfo != null)
            {
                if (singleBlock.gridInfo.gridX == gridInfo.gridX && singleBlock.gridInfo.gridZ == gridInfo.gridZ)
                {
                    result.Add(singleBlock);
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 检查当前块是否为同一位置的最低高度层块
    /// </summary>
    private bool IsLowestBlockAtPosition()
    {
        var allBlocksAtPosition = GetAllSingleBlocksAtSamePosition();
        if (allBlocksAtPosition.Count == 0) return true;

        int minHeight = allBlocksAtPosition.Min(block => block.gridInfo.height);
        return gridInfo.height == minHeight;
    }

    /// <summary>
    /// 动画缩放Y轴
    /// </summary>
    private IEnumerator AnimateScaleY(float fromY, float toY, float duration)
    {
        Vector3 startScale = transform.localScale;
        Vector3 targetScale = startScale;
        startScale.y = fromY;
        targetScale.y = toY;

        float elapsed = 0f;
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;

            // 使用平滑插值
            float currentY = Mathf.Lerp(fromY, toY, Mathf.SmoothStep(0f, 1f, progress));
            Vector3 currentScale = transform.localScale;
            currentScale.y = currentY;
            transform.localScale = currentScale;

            yield return null;
        }

        // 确保最终值准确
        Vector3 finalScale = transform.localScale;
        finalScale.y = toY;
        transform.localScale = finalScale;
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 设置物体可见性
    /// </summary>
    private void SetVisibility(bool visible)
    {
        if (meshRenderer != null)
        {
            meshRenderer.enabled = visible;
        }

        // 如果有碰撞体，也要控制
        var collider = GetComponent<Collider>();
        if (collider != null)
        {
            collider.enabled = visible;
        }
    }

    #endregion

    #region 清除颜色道具相关方法

    /// <summary>
    /// 设置Tool_JRJELLY_Shader_Fixed_Outline的_OverlayAlpha参数
    /// </summary>
    public void SetOverlayAlpha(float alpha)
    {
        if (meshRenderer != null)
        {
            if (propertyBlock == null)
            {
                propertyBlock = new MaterialPropertyBlock();
            }

            meshRenderer.GetPropertyBlock(propertyBlock);
            propertyBlock.SetFloat("_OverlayAlpha", alpha);
            meshRenderer.SetPropertyBlock(propertyBlock);
        }
    }

    /// <summary>
    /// 触发缩小销毁（供道具系统调用）
    /// </summary>
    public void TriggerShrinkDestroy()
    {
        if (!isByBulletHited)
        {
            isByBulletHited = true;
            //Debug.Log($"[{name}] 道具触发缩小销毁");
            StartCoroutine(ShrinkToZeroAndDestroy());
        }
    }

    /// <summary>
    /// 处理点击事件（在道具状态下）
    /// </summary>
    private void OnMouseDown()
    {

        if (!TileCube_InputManager.Can_Prop_ClearColor_Click())
            return;

        if (!Tool_InputManager.CanProcessInput() && Tool_IsPointerOverUI.Check())
            return;

        var controller = TileCube_Controller.Instance;
        if (controller != null && controller.GetCurrentState() == TileCube_Controller.ControllerState.Prop_ClearColorGrid)
        {
            controller.Prop_ClearColorGrid_HandleSingleBlockClick(this);
        }
    }

    #endregion
}