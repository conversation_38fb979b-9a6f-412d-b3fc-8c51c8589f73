using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 物理阻挡条 - 根部占0.5格，可向四个方向伸展，被击打后从根节点开始视觉缩短，从尾端开始释放网格
/// 新版本：由多段prefabSeg拼接而成，支持颜色交替，平滑缩短
/// 
/// 核心逻辑：
/// - 输入参数：占用格数、每格击打次数、格子长度、单节长度
/// - 根节点特殊：占用半格长度，从中心点开始伸展，不参与击打
/// - 长度计算：根半格 + (占用格数-1) × 格子长度
/// - 击打逻辑：总击打次数分配给非根节点格子，缩短到根节点时直接销毁
/// - 视觉效果：从根节点开始缩短（段的位置和缩放更新）
/// - 网格释放：从尾端开始释放（保持连续的网格占用）
/// 
/// 示例：
/// - 占用格数：6格
/// - 每格击打次数：5次
/// - 横向格子长度：0.5，竖向格子长度：0.5
/// - 单节长度：0.45
/// - 总击打次数：6 × 5 = 30次（包括根节点）
/// - 实际击打格数：5格（不包括根节点）
/// - 击打次数分配：30 ÷ 5 = 6次/格（正好整除）
/// - 总长度：0.25(根半格) + 5×0.5(其他格) = 2.75
/// - 总段数：ceil(2.75 / 0.45) = 7段
/// 
/// 3格示例（展示余数分配）：
/// - 占用格数：3格，每格击打次数：5次
/// - 总击打次数：3 × 5 = 15次
/// - 实际击打格数：2格（不包括根节点）
/// - 击打次数分配：15 ÷ 2 = 7余1，所以第1格8次，第2格7次
/// 
/// 击打过程（视觉从根节点开始缩短，网格从尾端释放）：
/// - 击打1-6次：根节点旁边的第1格逐渐缩短并消失，但网格仍保持连续占用
/// - 击打7-12次：第2格逐渐缩短并消失，尾端第6格的网格被释放
/// - 击打13-18次：第3格逐渐缩短并消失，第5格的网格被释放
/// - 击打19-24次：第4格逐渐缩短并消失，第4格的网格被释放
/// - 击打25-30次：第5格逐渐缩短并消失，第3格的网格被释放，只剩根节点和第2格，触发销毁
/// </summary>
public class TileCube_Barrier : TileCube_Object
{
    [Header("物理阻挡条特有属性")]
    public int Direction;               // 伸展方向 (0=上, 1=右, 2=下, 3=左)
    public int HitValues;               // 总击打次数
    public int CurrentHitValues;        // 当前剩余击打次数
    public bool IsImmovable = true;     // 不可移动

    [Header("阻挡条核心配置")]
    [Tooltip("总共占用多少格")]
    public int gridCount = 6;

    [Tooltip("每格需要击打的次数")]
    public int hitsPerGrid = 5;

    [Tooltip("横向格子长度（X轴方向）")]
    public float gridLengthX = 0.5f;

    [Tooltip("竖向格子长度（Z轴方向）")]
    public float gridLengthZ = 0.5f;

    [Tooltip("单节长度")]
    public float segmentLength = 0.45f;

    /// <summary>
    /// 当前方向的格子长度
    /// </summary>
    private float CurrentGridLength
    {
        get
        {
            // 0=上(Z+), 1=右(X+), 2=下(Z-), 3=左(X-)
            return (Direction == 1 || Direction == 3) ? gridLengthX : gridLengthZ;
        }
    }

    /// <summary>
    /// 根节点长度（自动计算为当前方向格子长度的一半）
    /// </summary>
    private float RootLength => CurrentGridLength * 0.5f;

    /// <summary>
    /// 实际击打格数（不包括根节点）
    /// </summary>
    private int ActualHitGrids => gridCount - 1;

    /// <summary>
    /// 获取指定格子的击打次数（将根节点的击打次数分配给其他格子）
    /// </summary>
    /// <param name="gridIndex">格子索引（从0开始，0是最接近根节点的格子）</param>
    /// <returns>该格子需要的击打次数</returns>
    private int GetHitsForGrid(int gridIndex)
    {
        if (ActualHitGrids <= 0) return 0;

        // 将总击打次数分配给非根节点格子
        int baseHitsPerGrid = HitValues / ActualHitGrids;
        int remainingHits = HitValues % ActualHitGrids;

        // 前面的格子分配多一次击打（如果有余数）
        return baseHitsPerGrid + (gridIndex < remainingHits ? 1 : 0);
    }

    [Tooltip("额外颜色（用于颜色交替）")]
    public Color extraColor = Color.white;

    [Tooltip("击打缩短的过渡时间")]
    public float shrinkTransitionTime = 0.3f;

    [Header("击打销毁设置")]
    [Tooltip("击中后缩小持续时间")]
    [Range(0.2f, 1f)]
    public float hitShrinkDuration = 0.3f;

    [Header("阻挡条组件")]
    public MeshRenderer rootmesh;
    public MeshRenderer rootmesh2;
    public MeshRenderer tailmesh;
    public GameObject tailobj;
    public Transform segZeroPoint;
    public GameObject prefabSeg;

    // 私有变量
    public TileCube_Data_Barrier barrierData;
    private bool isDestroying = false;      // 防止重复销毁
    private List<Vector2Int> occupiedPositions = new List<Vector2Int>(); // 当前占用的网格位置

    // 新增：存储虚拟网格物体，用于正确的网格占用管理
    private Dictionary<Vector2Int, GameObject> virtualGridObjects = new Dictionary<Vector2Int, GameObject>();

    // 新的段管理变量
    private List<GameObject> segmentObjects = new List<GameObject>();  // 所有段的物体
    private List<MeshRenderer> segmentRenderers = new List<MeshRenderer>();  // 所有段的渲染器
    private Color primaryColor;      // 主要颜色
    private float totalLength;       // 总长度
    private int totalSegments;       // 总段数
    private int currentSegmentIndex; // 当前正在缩短的段索引
    private bool isTransitioning;    // 是否正在过渡中

    // 过渡状态管理
    private float currentDisplayLength;  // 当前显示的长度（用于插值）
    private float targetDisplayLength;   // 目标显示长度
    private bool isLengthTransitioning = false;  // 是否正在进行长度过渡
    private Coroutine lengthTransitionCoroutine;  // 长度过渡协程

    // 方向向量映射
    private static readonly Vector2Int[] DirectionVectors = {
        Vector2Int.up,      // 0: 上 (Z+)
        Vector2Int.right,   // 1: 右 (X+)
        Vector2Int.down,    // 2: 下 (Z-)
        Vector2Int.left     // 3: 左 (X-)
    };

    /// <summary>
    /// 初始化物理阻挡条
    /// </summary>
    public void Init(TileCube_Data_Barrier data)
    {
        barrierData = data;
        ObjectType = TileCube_ObjectType.Barrier;
        Direction = data.Direction;

        // 从数据中读取占用格数
        gridCount = data.GridCount;

        hitsPerGrid = data.HitsPerGrid;
        // 根据格子数量计算总击打次数
        HitValues = gridCount * hitsPerGrid;
        CurrentHitValues = HitValues;

        // 计算段数和总长度
        totalSegments = CalculateTotalSegments();
        totalLength = CalculateTotalLength();

        // 设置颜色
        SetupColors(data.ColorId);

        // 创建所有段
        CreateAllSegments();

        // 设置尾部位置
        UpdateTailPosition();

        // 设置视觉组件方向
        SetupVisualComponents();

        // 初始化过渡状态
        currentDisplayLength = CalculateTotalLength();
        targetDisplayLength = currentDisplayLength;
        isLengthTransitioning = false;
    }

    /// <summary>
    /// 计算总长度（根半格 + 其他格数×格子长度）
    /// </summary>
    private float CalculateTotalLength()
    {
        // 根节点占用半格，其他格子占用全格
        return RootLength + (gridCount - 1) * CurrentGridLength;
    }

    /// <summary>
    /// 计算总段数（根据总长度和段长度计算）
    /// </summary>
    private int CalculateTotalSegments()
    {
        float totalLength = CalculateTotalLength();
        return Mathf.CeilToInt(totalLength / segmentLength);
    }

    /// <summary>
    /// 设置颜色
    /// </summary>
    public virtual void SetColor(int colorId)
    {
        primaryColor = MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data.GetColorById(colorId);
        SetupColors(colorId);
    }

    /// <summary>
    /// 设置颜色系统
    /// </summary>
    private void SetupColors(int colorId)
    {
        // 获取主要颜色
        primaryColor = MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data.GetColorById(colorId);

        // 设置根部颜色：第一个材质用主要颜色，第二个材质用额外颜色
        if (rootmesh != null && rootmesh.materials.Length > 0)
        {
            Material[] rootMaterials = rootmesh.materials;
            rootMaterials[0].color = primaryColor;
            rootmesh.materials = rootMaterials;
        }

        if (rootmesh2 != null && rootmesh2.materials.Length > 0)
        {
            Material[] rootMaterials = rootmesh2.materials;
            rootMaterials[0].color = extraColor;
            rootmesh2.materials = rootMaterials;
        }

        // 设置尾部颜色：第一个材质用额外颜色，第二个材质用主要颜色
        if (tailmesh != null && tailmesh.materials.Length > 1)
        {
            Material[] tailMaterials = tailmesh.materials;
            tailMaterials[0].color = extraColor;
            tailMaterials[1].color = primaryColor;
            tailmesh.materials = tailMaterials;
        }
    }

    /// <summary>
    /// 重写基类：设置网格信息后立即更新视觉组件
    /// </summary>
    public override void SetGridInfo(GridTileObject info)
    {
        base.SetGridInfo(info);

        // 网格信息设置后，更新占用位置
        if (info != null)
        {
            UpdateOccupiedPositions();
        }
    }

    /// <summary>
    /// 设置网格占用（在Controller中调用）
    /// 新方法：为每个非根节点位置创建虚拟GameObject，使用Tool_TileGrid的正规API
    /// </summary>
    public void SetupGridOccupancy(Tool_TileGrid tileGrid)
    {
        if (gridInfo == null || tileGrid == null) return;

        //Debug.Log($"[SetupGridOccupancy] 开始设置网格占用，总格数: {gridCount}");

        // 计算并占用所有网格位置
        UpdateOccupiedPositions();

        // 占用除根部外的其他网格位置（根部已由Controller占用）
        for (int i = 1; i < occupiedPositions.Count; i++)
        {
            Vector2Int pos = occupiedPositions[i];

            // 为每个位置创建一个虚拟GameObject
            GameObject virtualObj = new GameObject($"Barrier_VirtualGrid_{pos.x}_{pos.y}");
            virtualObj.transform.SetParent(this.transform); // 设置为阻挡条的子物体
            virtualObj.SetActive(false); // 隐藏，只用于网格占用

            // 使用Tool_TileGrid的正规API添加到网格中
            bool bindSuccess = tileGrid.BindObjectToGrid(virtualObj, pos.x, pos.y, 0, 1, 1, true);

            if (bindSuccess)
            {
                // 存储虚拟对象的引用，以便后续释放
                virtualGridObjects[pos] = virtualObj;
                //Debug.Log($"[SetupGridOccupancy] 成功占用格子: {pos}");
            }
            else
            {
                //Debug.LogWarning($"[SetupGridOccupancy] 无法占用格子: {pos}，可能已被其他物体占用");
                // 清理创建的GameObject
                DestroyImmediate(virtualObj);
            }
        }

        //Debug.Log($"[SetupGridOccupancy] 网格占用设置完成，占用{virtualGridObjects.Count}个额外格子");
    }

    /// <summary>
    /// 创建所有段
    /// </summary>
    private void CreateAllSegments()
    {
        // 清理现有段
        ClearExistingSegments();

        float totalLength = CalculateTotalLength();
        float currentPosition = 0f;

        // 按照段长度创建段，直到覆盖总长度
        for (int i = 0; i < totalSegments; i++)
        {
            // 创建段物体
            GameObject segment = Instantiate(prefabSeg, segZeroPoint);

            // 计算这一段的长度
            float remainingLength = totalLength - currentPosition;
            float thisSegmentLength = Mathf.Min(segmentLength, remainingLength);

            // 设置位置
            segment.transform.localPosition = new Vector3(0, 0, currentPosition);

            // 设置缩放
            Vector3 scale = segment.transform.localScale;
            scale.z = thisSegmentLength;
            segment.transform.localScale = scale;

            // 设置颜色（奇偶交替）
            MeshRenderer renderer = segment.GetComponent<MeshRenderer>();
            if (renderer != null)
            {
                Color targetColor = (i % 2 == 0) ? primaryColor : extraColor;
                Material[] materials = renderer.materials;
                if (materials.Length > 0)
                {
                    materials[0].color = targetColor;
                    renderer.materials = materials;
                }
            }

            // 存储引用
            segmentObjects.Add(segment);
            segmentRenderers.Add(renderer);

            currentPosition += thisSegmentLength;

            // 如果已经覆盖了总长度，跳出循环
            if (currentPosition >= totalLength)
                break;
        }
    }

    /// <summary>
    /// 清理现有段
    /// </summary>
    private void ClearExistingSegments()
    {
        foreach (GameObject segment in segmentObjects)
        {
            if (segment != null)
                DestroyImmediate(segment);
        }
        segmentObjects.Clear();
        segmentRenderers.Clear();
    }

    /// <summary>
    /// 设置视觉组件方向
    /// </summary>
    private void SetupVisualComponents()
    {
        // 根据方向设置旋转 - 直接旋转根物体
        float rotationY = Direction * 90f; // 0=0°, 1=90°, 2=180°, 3=270°
        transform.rotation = Quaternion.Euler(0, rotationY, 0);
    }

    /// <summary>
    /// 每帧更新：现在主要用于监控状态，过渡由协程处理
    /// </summary>
    private void Update()
    {
        // 这里可以添加其他需要每帧更新的逻辑
        // 长度过渡现在由协程LengthTransitionCoroutine处理
    }

    /// <summary>
    /// 更新尾部位置
    /// 兼容性方法：调用基于当前显示长度的新方法
    /// </summary>
    private void UpdateTailPosition()
    {
        // 如果正在过渡中，使用当前显示长度；否则计算目标长度
        float length = isLengthTransitioning ? currentDisplayLength : CalculateCurrentTargetLength();
        UpdateTailPositionToLength(length);
    }

    /// <summary>
    /// 计算占用的格数
    /// </summary>
    private int CalculateOccupiedGrids()
    {
        return gridCount;
    }

    /// <summary>
    /// 计算当前实际占用的格数（考虑击打缩短）
    /// 修复逻辑：从根节点开始保留连续的格子，击打完的格子从尾端开始释放
    /// 这样可以保持网格占用的连续性，避免中间格子被释放而尾端格子仍然占用的问题
    /// </summary>
    private int CalculateCurrentOccupiedGrids()
    {
        int hitsDealt = HitValues - CurrentHitValues;

        // 如果没有击打，返回初始格数
        if (hitsDealt <= 0)
        {
            return gridCount;
        }

        // 计算从根节点旁边开始完全被击打完的非根节点格子数量
        int completeGridsDestroyed = 0;
        int remainingHits = hitsDealt;

        // 从最接近根节点的格子开始计算（索引0）
        for (int gridIndex = 0; gridIndex < ActualHitGrids && remainingHits > 0; gridIndex++)
        {
            int hitsForThisGrid = GetHitsForGrid(gridIndex);
            if (remainingHits >= hitsForThisGrid)
            {
                remainingHits -= hitsForThisGrid;
                completeGridsDestroyed++;
            }
            else
            {
                break; // 这个格子还没被完全击打完
            }
        }

        //Debug.Log($"[CalculateCurrentOccupiedGrids] 击打次数:{hitsDealt}, 从根节点开始完全销毁格数:{completeGridsDestroyed}");

        // 计算当前仍然占用的格数
        // 从尾端开始释放：总格数 - 完全被击打完的格数 = 仍占用的格数
        int currentOccupiedGrids = gridCount - completeGridsDestroyed;

        // 确保至少占用根节点（1格）
        currentOccupiedGrids = Mathf.Max(1, currentOccupiedGrids);

        //Debug.Log($"[CalculateCurrentOccupiedGrids] 当前占用格数: {currentOccupiedGrids}/{gridCount}（从根节点开始保留连续的{currentOccupiedGrids}格）");

        return currentOccupiedGrids;
    }

    /// <summary>
    /// 更新占用的网格位置列表
    /// 修复：从尾端开始释放格子，而不是从根节点旁边开始释放
    /// </summary>
    private void UpdateOccupiedPositions()
    {
        occupiedPositions.Clear();

        if (gridInfo == null) return;

        Vector2Int dirVector = DirectionVectors[Direction];
        int currentOccupiedGrids = CalculateCurrentOccupiedGrids(); // 当前实际占用格数

        //Debug.Log($"[UpdateOccupiedPositions] 当前占用格数: {currentOccupiedGrids}, 方向向量: {dirVector}");

        // 计算从根节点旁边开始被销毁的格子数
        int hitsDealt = HitValues - CurrentHitValues;
        int destroyedGridsFromRoot = 0;
        int remainingHits = hitsDealt;

        for (int gridIndex = 0; gridIndex < ActualHitGrids && remainingHits > 0; gridIndex++)
        {
            int hitsForThisGrid = GetHitsForGrid(gridIndex);
            if (remainingHits >= hitsForThisGrid)
            {
                remainingHits -= hitsForThisGrid;
                destroyedGridsFromRoot++;
            }
            else
            {
                break;
            }
        }

        // 新逻辑：从根节点开始，保留前面的currentOccupiedGrids个格子
        // 这样被销毁的格子实际上是从尾端开始的
        for (int i = 0; i < currentOccupiedGrids; i++)
        {
            Vector2Int pos = new Vector2Int(
                gridInfo.gridX + dirVector.x * i,
                gridInfo.gridZ + dirVector.y * i
            );
            occupiedPositions.Add(pos);
            //Debug.Log($"[UpdateOccupiedPositions] 添加占用位置[{i}]: {pos}");
        }

        //Debug.Log($"[UpdateOccupiedPositions] 从根节点开始完全被击打完格数:{destroyedGridsFromRoot}, 总共占用{occupiedPositions.Count}个位置（从根节点开始保留{currentOccupiedGrids}格）");
    }

    /// <summary>
    /// 获取占用的所有网格位置
    /// </summary>
    public List<Vector2Int> GetOccupiedPositions()
    {
        UpdateOccupiedPositions();
        return new List<Vector2Int>(occupiedPositions);
    }

    /// <summary>
    /// 击打后更新网格占用
    /// 显示详细的网格释放信息
    /// </summary>
    private void UpdateGridOccupancyAfterHit(List<Vector2Int> previousOccupiedPositions)
    {
        //Debug.Log($"[UpdateGridOccupancyAfterHit] 击打前占用位置: {string.Join(", ", previousOccupiedPositions)}");

        // 获取当前占用位置
        List<Vector2Int> currentOccupiedPositions = GetOccupiedPositions();

        //Debug.Log($"[UpdateGridOccupancyAfterHit] 击打后占用位置: {string.Join(", ", currentOccupiedPositions)}");

        // 找出不再占用的位置
        List<Vector2Int> positionsToRemove = new List<Vector2Int>();
        foreach (Vector2Int prevPos in previousOccupiedPositions)
        {
            if (!currentOccupiedPositions.Contains(prevPos))
            {
                positionsToRemove.Add(prevPos);
            }
        }

        // 从网格中移除不再占用的位置
        if (positionsToRemove.Count > 0)
        {
            //Debug.Log($"[UpdateGridOccupancyAfterHit] 准备释放{positionsToRemove.Count}个格子: {string.Join(", ", positionsToRemove)}");
            RemoveGridOccupancy(positionsToRemove);
        }
        else
        {
            //Debug.Log($"[UpdateGridOccupancyAfterHit] 本次击打没有释放任何格子");
        }
    }

    /// <summary>
    /// 移除网格占用
    /// 新方法：使用Tool_TileGrid的正规RemoveObject方法来释放虚拟GameObject
    /// </summary>
    private void RemoveGridOccupancy(List<Vector2Int> positionsToRemove)
    {
        var tileGrid = TileCube_Controller.Instance?.TileGrid;
        if (tileGrid == null)
        {
            //Debug.LogWarning("[RemoveGridOccupancy] TileGrid为空，无法释放网格");
            return;
        }

        foreach (Vector2Int pos in positionsToRemove)
        {
            // 查找这个位置对应的虚拟GameObject
            if (virtualGridObjects.TryGetValue(pos, out GameObject virtualObj))
            {
                // 使用Tool_TileGrid的正规API移除
                bool removed = tileGrid.UnbindObject(virtualObj);

                if (removed)
                {
                    //Debug.Log($"[RemoveGridOccupancy] 成功释放格子: {pos}");

                    // 销毁虚拟GameObject
                    DestroyImmediate(virtualObj);

                    // 从字典中移除引用
                    virtualGridObjects.Remove(pos);
                }
                else
                {
                    //Debug.LogWarning($"[RemoveGridOccupancy] Tool_TileGrid.RemoveObject失败: {pos}");
                }
            }
            else
            {
                //Debug.LogWarning($"[RemoveGridOccupancy] 未找到格子{pos}对应的虚拟GameObject，可能已经被释放或不存在");
            }
        }

        //Debug.Log($"[RemoveGridOccupancy] 网格释放完成，还剩{virtualGridObjects.Count}个虚拟格子");
    }

    #region 击打销毁逻辑

    /// <summary>
    /// 物体被击打时的处理逻辑
    /// </summary>
    public virtual void OnHit(int damage = 1)
    {
        if (isDestroying || isTransitioning) return;

        // 触发进度更新（物理阻挡条每次被击打减对应伤害值）
        TileCube_Controller.Instance?.UpdateProgress(damage);

        // 记录击打前的占用位置
        List<Vector2Int> previousOccupiedPositions = new List<Vector2Int>(GetOccupiedPositions());

        CurrentHitValues -= damage;

        // 重新计算当前应该显示的长度
        UpdateBarrierLength();

        // 更新占用位置并清空不再占用的网格
        UpdateGridOccupancyAfterHit(previousOccupiedPositions);

        // 通知控制器
        TileCube_Controller.Instance?.HandleBarrierHit(this, damage);

        // 检查是否应该销毁
        // 1. 击打次数归零时销毁
        // 2. 或者当所有非根节点格子都被击打完时销毁（根节点不参与击打，直接销毁整体）
        // 3. 或者当长度缩短到接近根节点长度时提前销毁
        int hitsDealt = HitValues - CurrentHitValues;
        float currentTargetLength = CalculateCurrentTargetLength();

        // 计算完全被击打完的格子数
        int completeGridsDestroyed = 0;
        int remainingHits = hitsDealt;
        for (int gridIndex = 0; gridIndex < ActualHitGrids && remainingHits > 0; gridIndex++)
        {
            int hitsForThisGrid = GetHitsForGrid(gridIndex);
            if (remainingHits >= hitsForThisGrid)
            {
                remainingHits -= hitsForThisGrid;
                completeGridsDestroyed++;
            }
            else
            {
                break;
            }
        }

        // 销毁条件：击打次数归零 OR 所有非根节点格子被击打完 OR 长度缩短到接近根节点
        bool shouldDestroy = CurrentHitValues <= 0 ||
                           completeGridsDestroyed >= ActualHitGrids ||
                           currentTargetLength <= RootLength * 1.1f; // 留一点容差

        if (shouldDestroy)
        {
            //Debug.Log($"[OnHit] 触发销毁 - 剩余击打次数:{CurrentHitValues}, 从根节点开始完全击打完格数:{completeGridsDestroyed}/{ActualHitGrids}, 当前长度:{currentTargetLength:F2}");
            StartDestroySequence();
        }
    }

    /// <summary>
    /// 更新阻挡条长度（基于格子逻辑）
    /// 使用协程驱动的平滑过渡系统
    /// </summary>
    private void UpdateBarrierLength()
    {
        // 计算当前应该显示的长度
        float newTargetLength = CalculateCurrentTargetLength();

        //Debug.Log($"[UpdateBarrierLength] 目标长度: {newTargetLength:F2}, 当前击打次数: {HitValues - CurrentHitValues}/{HitValues}");

        // 检查长度是否发生变化，如果变化则启动平滑过渡
        if (Mathf.Abs(newTargetLength - targetDisplayLength) > 0.001f)
        {
            //Debug.Log($"[UpdateBarrierLength] 长度发生变化，启动平滑过渡：{targetDisplayLength:F2} -> {newTargetLength:F2}");

            // 更新目标长度
            targetDisplayLength = newTargetLength;

            // 启动平滑过渡协程
            StartLengthTransition();
        }
    }

    /// <summary>
    /// 启动长度过渡协程
    /// </summary>
    private void StartLengthTransition()
    {
        // 如果已经有过渡在进行，先停止它
        if (lengthTransitionCoroutine != null)
        {
            StopCoroutine(lengthTransitionCoroutine);
        }

        // 启动新的过渡协程
        lengthTransitionCoroutine = StartCoroutine(LengthTransitionCoroutine());
    }

    /// <summary>
    /// 长度过渡协程 - 平滑地从当前长度过渡到目标长度
    /// </summary>
    private IEnumerator LengthTransitionCoroutine()
    {
        isLengthTransitioning = true;
        float startLength = currentDisplayLength;
        float elapsed = 0f;

        //Debug.Log($"[LengthTransition] 开始过渡：{startLength:F2} -> {targetDisplayLength:F2}");

        while (elapsed < shrinkTransitionTime)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / shrinkTransitionTime;

            // 使用平滑插值
            currentDisplayLength = Mathf.Lerp(startLength, targetDisplayLength, Mathf.SmoothStep(0f, 1f, progress));

            // 更新所有段和尾部位置
            UpdateSegmentsToDisplayLength(currentDisplayLength);
            UpdateTailPositionToLength(currentDisplayLength);

            yield return null;
        }

        // 确保最终状态正确
        currentDisplayLength = targetDisplayLength;
        UpdateSegmentsToDisplayLength(currentDisplayLength);
        UpdateTailPositionToLength(currentDisplayLength);

        isLengthTransitioning = false;
        lengthTransitionCoroutine = null;

        //Debug.Log($"[LengthTransition] 过渡完成：{currentDisplayLength:F2}");
    }

    /// <summary>
    /// 计算当前应该显示的长度
    /// 新逻辑：从根节点开始缩短，根节点不参与击打但会消失，后续格子前移
    /// </summary>
    private float CalculateCurrentTargetLength()
    {
        int hitsDealt = HitValues - CurrentHitValues;

        //Debug.Log($"[CalculateCurrentTargetLength] 击打{hitsDealt}/{HitValues}次, 总格数:{gridCount}, 实际击打格数:{ActualHitGrids}");

        // 如果没有击打，返回初始总长度
        if (hitsDealt <= 0)
        {
            float initialLength = RootLength + (gridCount - 1) * CurrentGridLength;
            //Debug.Log($"[CalculateCurrentTargetLength] 未击打，返回初始长度: {initialLength:F2}");
            return initialLength;
        }

        // 计算从根节点旁边开始被击打的情况
        int completeGridsDestroyed = 0;
        int remainingHits = hitsDealt;
        int currentGridHits = 0;

        // 从最接近根节点的格子开始计算（索引0）
        for (int gridIndex = 0; gridIndex < ActualHitGrids && remainingHits > 0; gridIndex++)
        {
            int hitsForThisGrid = GetHitsForGrid(gridIndex);
            if (remainingHits >= hitsForThisGrid)
            {
                remainingHits -= hitsForThisGrid;
                completeGridsDestroyed++;
            }
            else
            {
                currentGridHits = remainingHits;
                break;
            }
        }

        //Debug.Log($"[CalculateCurrentTargetLength] 从根节点开始完全被击打完的格子数: {completeGridsDestroyed}, 当前格子击打次数: {currentGridHits}");

        // 如果所有非根节点格子都被击打完，返回根节点长度（准备销毁）
        if (completeGridsDestroyed >= ActualHitGrids)
        {
            //Debug.Log($"[CalculateCurrentTargetLength] 所有非根节点格子都被击打完，返回根节点长度准备销毁");
            return RootLength;
        }

        // 计算总长度：
        // 根节点长度 + 完整保留的格子长度 + 正在缩短的格子长度
        float totalLength = RootLength; // 根节点始终保留

        // 1. 完整保留的非根节点格子数（跳过被销毁的格子）
        int fullyRemainingNonRootGrids = ActualHitGrids - completeGridsDestroyed;
        if (currentGridHits > 0)
        {
            fullyRemainingNonRootGrids--; // 如果有格子正在被击打，减去这个格子
        }

        // 2. 加上完整保留的非根节点格子长度
        totalLength += fullyRemainingNonRootGrids * CurrentGridLength;

        // 3. 如果有格子正在被击打，加上这个格子的剩余部分
        if (currentGridHits > 0 && completeGridsDestroyed < ActualHitGrids)
        {
            int hitsForCurrentGrid = GetHitsForGrid(completeGridsDestroyed);
            float currentGridRemainingRatio = 1f - ((float)currentGridHits / hitsForCurrentGrid);
            totalLength += CurrentGridLength * currentGridRemainingRatio;
        }

        //Debug.Log($"[CalculateCurrentTargetLength] 计算结果: 完整保留非根格数={fullyRemainingNonRootGrids}, 总长度={totalLength:F2}");
        return Mathf.Max(0, totalLength);
    }

    /// <summary>
    /// 更新段的缩放来匹配目标长度
    /// 已废弃：使用UpdateSegmentsToDisplayLength和协程过渡系统代替
    /// </summary>
    [System.Obsolete("使用UpdateSegmentsToDisplayLength和协程过渡系统代替")]
    private void UpdateSegmentsToTargetLength(float targetLength)
    {
        //Debug.LogWarning("UpdateSegmentsToTargetLength方法已废弃，应该使用新的协程过渡系统");
        // 为了向后兼容，调用新的方法
        UpdateSegmentsToDisplayLength(targetLength);
    }

    /// <summary>
    /// 开始缩短指定段（已废弃，使用UpdateSegmentsToTargetLength）
    /// </summary>
    private void StartShrinkSegment(int segmentIndex)
    {
        // 这个方法已废弃，新的逻辑直接在UpdateBarrierLength中处理
        //Debug.LogWarning("StartShrinkSegment 方法已废弃");
    }

    /// <summary>
    /// 缩短段的协程
    /// </summary>
    private IEnumerator ShrinkSegmentCoroutine(int segmentIndex)
    {
        GameObject segment = segmentObjects[segmentIndex];
        if (segment == null) yield break;

        Vector3 originalScale = segment.transform.localScale;
        float startScale = originalScale.z;
        float targetScale = CalculateSegmentTargetScale(segmentIndex);

        float elapsed = 0f;
        while (elapsed < shrinkTransitionTime)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / shrinkTransitionTime;

            // 平滑缩放
            float currentScale = Mathf.Lerp(startScale, targetScale, progress);
            segment.transform.localScale = new Vector3(originalScale.x, originalScale.y, currentScale);

            // 更新后续段的位置
            UpdateSubsequentSegmentPositions(segmentIndex);

            // 更新尾部位置
            UpdateTailPosition();

            yield return null;
        }

        // 确保最终状态
        segment.transform.localScale = new Vector3(originalScale.x, originalScale.y, targetScale);
        UpdateSubsequentSegmentPositions(segmentIndex);
        UpdateTailPosition();

        // 如果缩放为0，销毁这个段
        if (targetScale <= 0)
        {
            DestroySegment(segmentIndex);
        }

        isTransitioning = false;
    }

    /// <summary>
    /// 更新当前段的缩放（已废弃，使用UpdateSegmentsToTargetLength）
    /// </summary>
    private void UpdateCurrentSegmentScale()
    {
        // 这个方法已废弃，新的逻辑直接在UpdateBarrierLength中处理
        //Debug.LogWarning("UpdateCurrentSegmentScale 方法已废弃");
    }

    /// <summary>
    /// 计算段的目标缩放（已废弃，现在使用UpdateSegmentsToTargetLength）
    /// </summary>
    private float CalculateSegmentTargetScale(int segmentIndex)
    {
        // 这个方法已经不再使用，新的逻辑在UpdateSegmentsToTargetLength中
        // 保留这个方法是为了避免编译错误，但不应该被调用
        //Debug.LogWarning("CalculateSegmentTargetScale 方法已废弃，应该使用 UpdateSegmentsToTargetLength");
        return segmentLength;
    }

    /// <summary>
    /// 更新后续段的位置
    /// </summary>
    private void UpdateSubsequentSegmentPositions(int fromIndex)
    {
        float cumulativeZ = 0f;

        // 计算从起始点到指定索引的累积长度
        for (int i = 0; i <= fromIndex; i++)
        {
            if (i < segmentObjects.Count && segmentObjects[i] != null)
            {
                cumulativeZ += segmentObjects[i].transform.localScale.z;
            }
        }

        // 更新后续段的位置
        for (int i = fromIndex + 1; i < segmentObjects.Count; i++)
        {
            if (segmentObjects[i] != null)
            {
                segmentObjects[i].transform.localPosition = new Vector3(0, 0, cumulativeZ);
                cumulativeZ += segmentObjects[i].transform.localScale.z;
            }
        }
    }

    /// <summary>
    /// 销毁指定段
    /// </summary>
    private void DestroySegment(int segmentIndex)
    {
        if (segmentIndex < segmentObjects.Count && segmentObjects[segmentIndex] != null)
        {
            DestroyImmediate(segmentObjects[segmentIndex]);
            segmentObjects[segmentIndex] = null;
            segmentRenderers[segmentIndex] = null;
        }
    }

    /// <summary>
    /// 开始销毁序列
    /// </summary>
    private void StartDestroySequence()
    {
        if (isDestroying) return;
        isDestroying = true;

        StartCoroutine(ShrinkToZeroAndDestroy());
    }

    /// <summary>
    /// 缩小到0并销毁的动画
    /// </summary>
    private IEnumerator ShrinkToZeroAndDestroy()
    {
        Vector3 originalScale = transform.localScale;
        float elapsed = 0f;

        while (elapsed < hitShrinkDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / hitShrinkDuration;

            // 使用平滑的缩小曲线
            float scaleMultiplier = Mathf.Lerp(1f, 0f, Mathf.SmoothStep(0f, 1f, progress));
            transform.localScale = originalScale * scaleMultiplier;

            yield return null;
        }

        // 确保完全缩小
        transform.localScale = Vector3.zero;

        // 通过Controller销毁物体
        DestroyByController();
    }

    /// <summary>
    /// 通过Controller销毁物体
    /// </summary>
    private void DestroyByController()
    {
        // 使用基类的DestroyObject方法
        DestroyObject();
    }

    #endregion

    #region 子弹检测重写方法

    /// <summary>
    /// 重写基类：被子弹击中时触发
    /// </summary>
    public override bool OnBulletHit(GameObject bullet, object bulletData = null)
    {
        base.OnBulletHit(bullet, bulletData);

        if (isDestroying) return false;

        // 触发击打逻辑
        OnHit(1);
        return true; // 返回true表示击中成功
    }

    /// <summary>
    /// 重写基类：被子弹穿过时触发（阻挡条不做任何反应）
    /// </summary>
    public override void OnBulletPenetrate(GameObject bullet, object bulletData = null)
    {
        // 阻挡条被子弹穿过时不做任何反应
        // 不调用基类方法，不输出日志，完全静默处理
    }

    #endregion

    /// <summary>
    /// 检查是否阻挡了指定位置
    /// </summary>
    public bool IsBlockingPosition(int gridX, int gridZ)
    {
        var positions = GetOccupiedPositions();
        return positions.Contains(new Vector2Int(gridX, gridZ));
    }

    protected override void OnDestroy()
    {
        // 停止长度过渡协程
        if (lengthTransitionCoroutine != null)
        {
            StopCoroutine(lengthTransitionCoroutine);
            lengthTransitionCoroutine = null;
        }

        // 清理所有段
        ClearExistingSegments();

        // 清理所有虚拟网格对象
        CleanupVirtualGridObjects();

        base.OnDestroy();
    }

    /// <summary>
    /// 清理所有虚拟网格对象
    /// </summary>
    private void CleanupVirtualGridObjects()
    {
        var tileGrid = TileCube_Controller.Instance?.TileGrid;
        if (tileGrid != null)
        {
            // 释放所有剩余的虚拟网格对象
            List<Vector2Int> remainingPositions = new List<Vector2Int>(virtualGridObjects.Keys);
            foreach (Vector2Int pos in remainingPositions)
            {
                if (virtualGridObjects.TryGetValue(pos, out GameObject virtualObj))
                {
                    tileGrid.UnbindObject(virtualObj);
                    DestroyImmediate(virtualObj);
                }
            }
        }
        else
        {
            // 如果TileGrid已经不存在，直接销毁GameObject
            foreach (var kvp in virtualGridObjects)
            {
                if (kvp.Value != null)
                {
                    DestroyImmediate(kvp.Value);
                }
            }
        }

        virtualGridObjects.Clear();
        //Debug.Log("[CleanupVirtualGridObjects] 所有虚拟网格对象已清理");
    }

    /// <summary>
    /// 调试方法：显示当前状态
    /// </summary>
    [ContextMenu("调试当前状态")]
    public void DebugCurrentState()
    {
        //Debug.Log($"=== 阻挡条状态 ===");
        //Debug.Log($"总格数: {gridCount}");
        //Debug.Log($"横向格子长度: {gridLengthX}, 竖向格子长度: {gridLengthZ}, 当前方向格子长度: {CurrentGridLength}");
        //Debug.Log($"根节点长度: {RootLength}");
        //Debug.Log($"原始每格击打次数: {hitsPerGrid}");
        //Debug.Log($"实际击打格数: {ActualHitGrids}（不包括根节点）");
        //Debug.Log($"击打次数分配: 基础{HitValues / ActualHitGrids}次, 余数{HitValues % ActualHitGrids}个格子+1次");
        //Debug.Log($"总击打次数: {HitValues} = {gridCount} × {hitsPerGrid}");
        //Debug.Log($"当前剩余击打次数: {CurrentHitValues}");
        //Debug.Log($"已击打次数: {HitValues - CurrentHitValues}");
        //Debug.Log($"单节长度: {segmentLength}");
        //Debug.Log($"总段数: {totalSegments}");
        //Debug.Log($"初始总长度: {totalLength}");
        //Debug.Log($"当前目标长度: {CalculateCurrentTargetLength()}");
        //Debug.Log($"初始占用格数: {CalculateOccupiedGrids()}");
        //Debug.Log($"当前占用格数: {CalculateCurrentOccupiedGrids()}");
        //Debug.Log($"是否正在过渡: {isTransitioning}");
        //Debug.Log($"段物体数量: {segmentObjects.Count}");
        //Debug.Log($"占用网格数量: {GetOccupiedPositions().Count}");
        //Debug.Log($"占用位置: {string.Join(", ", GetOccupiedPositions())}");
        //Debug.Log($"虚拟网格对象数量: {virtualGridObjects.Count}");
        //Debug.Log($"虚拟网格位置: {string.Join(", ", virtualGridObjects.Keys)}");

        // 详细显示每格的击打情况
        int hitsDealt = HitValues - CurrentHitValues;
        int completeGridsDestroyed = 0;
        int remainingHits = hitsDealt;

        //Debug.Log($"=== 每格击打详情 ===");

        // 显示格子击打情况（从根节点旁边开始编号）
        //Debug.Log($"根节点: 长度{RootLength:F2}, 状态:不参与击打，缩短时消失");

        for (int gridIndex = 0; gridIndex < ActualHitGrids; gridIndex++)
        {
            // 从根节点旁边开始计算，第1格是根节点旁边的格子
            int realGridIndex = gridIndex + 1; // 实际的格子索引（从1开始）
            int hitsForThisGrid = GetHitsForGrid(gridIndex);

            // 计算这个格子被击打的次数
            int hitsOnThisGrid = 0;
            if (remainingHits > 0)
            {
                hitsOnThisGrid = Mathf.Min(hitsForThisGrid, remainingHits);
                remainingHits -= hitsOnThisGrid;

                if (hitsOnThisGrid >= hitsForThisGrid)
                {
                    completeGridsDestroyed++;
                }
            }

            float remainingRatio = 1f - ((float)hitsOnThisGrid / hitsForThisGrid);
            float remainingLength = CurrentGridLength * remainingRatio;

            string status = hitsOnThisGrid >= hitsForThisGrid ? "已销毁" : hitsOnThisGrid > 0 ? "正在缩短" : "完整";

            // 新逻辑：网格从尾端开始释放，所以需要判断这个格子在当前占用范围内
            bool isGridStillOccupied = realGridIndex < CalculateCurrentOccupiedGrids(); // 检查这个格子是否仍在占用范围内
            string gridStatus = isGridStillOccupied ? "仍占用网格" : "已释放网格";

            //Debug.Log($"第{realGridIndex}格(从根节点起第{gridIndex + 1}格): 击打{hitsOnThisGrid}/{hitsForThisGrid}次, 剩余长度{remainingLength:F2}, 状态:{status}, 网格:{gridStatus}");
        }

        // 显示网格释放详情
        //Debug.Log($"=== 网格释放详情（从尾端开始释放）===");
        //Debug.Log($"完全被击打完的格子数: {completeGridsDestroyed}");
        //Debug.Log($"从尾端释放的网格数: {completeGridsDestroyed}");
        //Debug.Log($"仍占用的网格数: {gridCount - completeGridsDestroyed}（从根节点开始连续保留）");
        //Debug.Log($"当前占用范围: 根节点(第1格)到第{CalculateCurrentOccupiedGrids()}格");

        // 显示具体的占用位置
        var currentOccupiedPositions = GetOccupiedPositions();
        //Debug.Log($"具体占用位置: {string.Join(", ", currentOccupiedPositions)}");

        // 显示整体状态
        int remainingNonRootGrids = ActualHitGrids - (hitsDealt / ActualHitGrids);
        float currentTargetLength = CalculateCurrentTargetLength();

        if (completeGridsDestroyed >= ActualHitGrids)
        {
            //Debug.Log($"=== 整体状态: 所有非根节点格子已被击打完，只剩根节点长度{RootLength:F2}，准备销毁 ===");
        }
        else if (currentTargetLength <= RootLength * 1.1f)
        {
            //Debug.Log($"=== 整体状态: 长度已缩短到接近根节点({currentTargetLength:F2}≤{RootLength * 1.1f:F2})，准备销毁 ===");
        }
        else
        {
            //Debug.Log($"=== 整体状态: 还有{remainingNonRootGrids}格未被击打完，当前长度{currentTargetLength:F2} ===");
        }
    }

    /// <summary>
    /// 获取当前实际长度
    /// </summary>
    private float GetCurrentActualLength()
    {
        return CalculateCurrentTargetLength();
    }

    /// <summary>
    /// 调试方法：测试击打效果
    /// </summary>
    [ContextMenu("测试击打效果")]
    public void DebugTestHit()
    {
        //Debug.Log("=== 测试击打效果 ===");
        //Debug.Log($"击打前: 剩余击打次数={CurrentHitValues}, 当前段索引={currentSegmentIndex}");

        // 模拟击打
        OnHit(1);

        //Debug.Log($"击打后: 剩余击打次数={CurrentHitValues}, 当前段索引={currentSegmentIndex}");
    }

    /// <summary>
    /// 根据指定的显示长度更新段的缩放和位置
    /// 新方法：直接基于显示长度，不使用插值（插值在协程中控制）
    /// </summary>
    private void UpdateSegmentsToDisplayLength(float displayLength)
    {
        //Debug.Log($"[UpdateSegmentsToDisplayLength] 更新段到显示长度: {displayLength:F2}, 段数量: {segmentObjects.Count}");

        // 计算被缩短的长度（原始总长度 - 当前显示长度）
        float originalLength = RootLength + (gridCount - 1) * CurrentGridLength;
        float shrunkLength = originalLength - displayLength;

        //Debug.Log($"[UpdateSegmentsToDisplayLength] 原始长度: {originalLength:F2}, 被缩短长度: {shrunkLength:F2}");

        // 从根节点开始的偏移位置
        float currentPos = 0f;

        for (int i = 0; i < segmentObjects.Count; i++)
        {
            if (segmentObjects[i] == null) continue;

            // 计算这个段的原始范围
            float segmentStart = currentPos;
            float segmentEnd = currentPos + segmentLength;

            // 计算被缩短后，这个段的有效显示范围
            // 从shrunkLength位置开始显示，到originalLength结束
            float displayStart = shrunkLength;
            float displayEnd = originalLength;

            // 计算这个段与显示范围的交集
            float visibleStart = Mathf.Max(segmentStart, displayStart);
            float visibleEnd = Mathf.Min(segmentEnd, displayEnd);

            if (visibleStart >= visibleEnd)
            {
                // 这个段完全被隐藏
                segmentObjects[i].SetActive(false);
            }
            else
            {
                // 确保段是显示的
                segmentObjects[i].SetActive(true);

                // 计算显示长度和位置
                float visibleLength = visibleEnd - visibleStart;
                float displayPosition = visibleStart - shrunkLength; // 相对于新的起始点的位置

                // 直接设置位置和缩放（不使用插值，因为插值在协程中控制）
                segmentObjects[i].transform.localPosition = new Vector3(0, 0, displayPosition);
                segmentObjects[i].transform.localScale = new Vector3(
                    segmentObjects[i].transform.localScale.x,
                    segmentObjects[i].transform.localScale.y,
                    visibleLength
                );
            }

            currentPos += segmentLength;
        }
    }

    /// <summary>
    /// 根据指定长度更新尾部位置
    /// 新方法：直接基于长度，不使用插值（插值在协程中控制）
    /// </summary>
    private void UpdateTailPositionToLength(float length)
    {
        if (tailobj == null || segZeroPoint == null) return;

        // 直接设置目标位置：x和z与segZeroPoint一致，z根据长度计算
        Vector3 targetPosition = new Vector3(
            segZeroPoint.localPosition.x,  // x坐标与segZeroPoint一致
            tailobj.transform.localPosition.y,  // y坐标保持不变
            segZeroPoint.localPosition.z + length  // z坐标 = segZeroPoint的z + 长度
        );

        // 直接设置位置（不使用插值，因为插值在协程中控制）
        tailobj.transform.localPosition = targetPosition;
    }
}