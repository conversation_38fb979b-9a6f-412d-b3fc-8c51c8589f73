using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 瓦片方块基础物体类 - 所有具体物体类型的基类
/// 提供基础的网格信息管理和通用功能
/// 子类可根据需要自行实现渲染、材质、颜色等功能
/// </summary>
public abstract class TileCube_Object : MonoBehaviour
{
    [Header("基础信息")]
    public int ObjectId;                    // 物体ID
    public TileCube_ObjectType ObjectType;  // 物体类型

    [Header("网格信息")]
    public GridTileObject gridInfo;  // 网格信息引用

    /// <summary>
    /// 物体类型枚举
    /// </summary>
    public enum TileCube_ObjectType
    {
        SingleBlock,    // 单一块
        LargeBlock,     // 大型块
        GoldPig,        // 金猪
        Barrier,        // 物理阻挡条
        Key,            // 钥匙
        Spawner         // 出块库
    }

    /// <summary>
    /// 设置网格信息
    /// </summary>
    public virtual void SetGridInfo(GridTileObject info)
    {
        gridInfo = info;
    }

    /// <summary>
    /// 获取网格信息
    /// </summary>
    public virtual GridTileObject GetGridInfo()
    {
        return gridInfo;
    }

    /// <summary>
    /// 获取网格位置
    /// </summary>
    public virtual Vector3Int GetGridPosition()
    {
        if (gridInfo != null)
        {
            return new Vector3Int(gridInfo.gridX, gridInfo.gridZ, gridInfo.height);
        }
        return Vector3Int.zero;
    }


    /// <summary>
    /// 检查是否到达指定位置
    /// </summary>
    public virtual bool IsAtPosition(int gridX, int gridZ, int height = -1)
    {
        if (gridInfo == null) return false;

        if (height == -1)
        {
            return gridInfo.gridX == gridX && gridInfo.gridZ == gridZ;
        }
        else
        {
            return gridInfo.gridX == gridX && gridInfo.gridZ == gridZ && gridInfo.height == height;
        }
    }

    /// <summary>
    /// 获取前方位置（基于指定方向）
    /// </summary>
    public virtual Vector2Int GetFrontPosition(Vector2Int direction)
    {
        if (gridInfo == null) return Vector2Int.zero;

        return new Vector2Int(gridInfo.gridX + direction.x, gridInfo.gridZ + direction.y);
    }

    public virtual void RemoveFromList()
    {
        // 获取控制器实例，如果为null则说明正在销毁过程中，直接销毁即可
        var controller = TileCube_Controller.Instance;
        if (controller != null)
        {
            // 从网格中移除
            if (controller.TileGrid != null)
            {
                controller.TileGrid.RemoveObject(gameObject);
            }

            // 从控制器列表中移除
            controller.Current_Objects.Remove(this);
        }

    }

    /// <summary>
    /// 销毁物体
    /// </summary>
    public virtual void DestroyObject()
    {
        RemoveFromList();

        DestroyGameObj();
    }

    public virtual void DestroyGameObj()
    {

        // 销毁游戏对象
        Destroy(gameObject);
    }

    public virtual void OnRebound()
    {

    }


    protected virtual void OnDestroy()
    {
        // 清理网格信息
        gridInfo = null;
    }

    #region 子弹检测相关函数
    /// <summary>
    /// 被子弹瞄准锁定时触发
    /// </summary>
    /// <param name="bullet">子弹对象</param>
    /// <param name="bulletData">子弹数据</param>
    public virtual void OnBulletTargeted(GameObject bullet, object bulletData = null)
    {

    }

    /// <summary>
    /// 被子弹穿过时触发
    /// </summary>
    /// <param name="bullet">子弹对象</param>
    /// <param name="penetratePosition">穿过位置</param>
    /// <param name="bulletData">子弹数据</param>
    public virtual void OnBulletPenetrate(GameObject bullet, object bulletData = null)
    {

    }

    /// <summary>
    /// 被子弹击中时触发
    /// </summary>
    /// <param name="bullet">子弹对象</param>
    /// <param name="hitPosition">击中位置</param>
    /// <param name="bulletData">子弹数据</param>
    /// <returns>是否销毁子弹</returns>
    public virtual bool OnBulletHit(GameObject bullet, object bulletData = null)
    {


        // 默认击中后销毁子弹
        return true;
    }



    /// <summary>
    /// 获取子弹检测的碰撞器
    /// 如果返回null，将使用物体本身的碰撞器
    /// </summary>
    /// <returns>用于子弹检测的碰撞器</returns>
    public virtual Collider GetBulletDetectionCollider()
    {
        return GetComponent<Collider>();
    }
    #endregion
}