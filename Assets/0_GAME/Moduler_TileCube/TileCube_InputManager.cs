
using UnityEngine;
using System.Collections.Generic;

public static class TileCube_InputManager
{
    private static List<TileCube_IInputChecker> checkers = new List<TileCube_IInputChecker>();

    public static void RegisterChecker(TileCube_IInputChecker checker)
    {
        if (!checkers.Contains(checker))
        {
            checkers.Add(checker);
        }
    }

    public static void UnregisterChecker(TileCube_IInputChecker checker)
    {
        checkers.Remove(checker);
    }


    public static bool Can_Prop_ClearColor_Click()
    {
        // 如果没有检查器，允许输入
        if (checkers.Count == 0) return true;

        // 所有检查器都必须允许输入才返回true
        foreach (var checker in checkers)
        {
            if (!checker.Can_Prop_ClearColor_Click())
                return false;
        }
        return true;
    }

    // 可选：清理所有检查器
    public static void ClearCheckers()
    {
        checkers.Clear();
    }
}

public interface TileCube_IInputChecker
{
    bool Can_Prop_ClearColor_Click();

}

