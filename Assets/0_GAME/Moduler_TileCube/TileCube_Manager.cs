using System.Collections;
using System.Collections.Generic;
using LitJson;
using UnityEngine;

/// <summary>
/// 瓦片方块管理器 - 负责资源加载和数据管理
/// 参考CupQueue_Manager架构模式
/// </summary>
public class TileCube_Manager
{
    #region 关卡数据加载
    // 缓存字典 <关卡号, 关卡数据>
    private static Dictionary<int, TileCube_Data_Level> loadedLevels =
        new Dictionary<int, TileCube_Data_Level>();

    /// <summary>
    /// 加载指定关卡数据（带缓存功能）
    /// </summary>
    public static TileCube_Data_Level LoadLevelData(int levelNumber)
    {
        // 优先从缓存读取
        if (loadedLevels.TryGetValue(levelNumber, out TileCube_Data_Level cachedData))
        {
            return cachedData;
        }

        // 加载加密文本资源
        TextAsset encryptedText = Resources.Load<TextAsset>(MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data.LevelJSON_Path + $"/Level_{levelNumber}");
        if (encryptedText == null)
        {
            //Debug.LogError($"TileCube关卡文件不存在: Level {levelNumber}");
            return null;
        }

        // 解密文本
        string jsonData;
        try
        {
            jsonData = Tool_Code4927.DecryptTextFile(encryptedText);
        }
        catch (System.Exception ex)
        {
            //Debug.LogError($"TileCube解密失败 - Level {levelNumber}: {ex.Message}");
            return null;
        }

        // 解析JSON数据
        TileCube_Data_Level dataLevel;
        try
        {
            // 先解析成JsonData以处理字符串key
            JsonData jsonObj = JsonMapper.ToObject(jsonData);

            dataLevel = new TileCube_Data_Level();

            // 解析各种物体数据
            if (jsonObj.ContainsKey("SingleBlocks") && jsonObj["SingleBlocks"] != null)
            {
                dataLevel.SingleBlocks = JsonMapper.ToObject<List<TileCube_Data_SingleBlock>>(jsonObj["SingleBlocks"].ToJson());
            }

            if (jsonObj.ContainsKey("LargeBlocks") && jsonObj["LargeBlocks"] != null)
            {
                dataLevel.LargeBlocks = JsonMapper.ToObject<List<TileCube_Data_LargeBlock>>(jsonObj["LargeBlocks"].ToJson());
            }

            if (jsonObj.ContainsKey("GoldPigs") && jsonObj["GoldPigs"] != null)
            {
                dataLevel.GoldPigs = JsonMapper.ToObject<List<TileCube_Data_GoldPig>>(jsonObj["GoldPigs"].ToJson());
            }

            if (jsonObj.ContainsKey("Barriers") && jsonObj["Barriers"] != null)
            {
                dataLevel.Barriers = JsonMapper.ToObject<List<TileCube_Data_Barrier>>(jsonObj["Barriers"].ToJson());
            }

            if (jsonObj.ContainsKey("Keys") && jsonObj["Keys"] != null)
            {
                dataLevel.Keys = JsonMapper.ToObject<List<TileCube_Data_Key>>(jsonObj["Keys"].ToJson());
            }

            if (jsonObj.ContainsKey("Spawners") && jsonObj["Spawners"] != null)
            {
                dataLevel.Spawners = JsonMapper.ToObject<List<TileCube_Data_Spawner>>(jsonObj["Spawners"].ToJson());
            }
        }
        catch (JsonException ex)
        {
            //Debug.LogError($"TileCube JSON解析失败 - Level {levelNumber}: {ex.Message}\n原始JSON: {jsonData}");
            return null;
        }
        catch (System.Exception ex)
        {
            //Debug.LogError($"TileCube数据处理失败 - Level {levelNumber}: {ex.Message}\n{ex.StackTrace}\n原始JSON: {jsonData}");
            return null;
        }

        // 数据验证
        if (dataLevel == null)
        {
            //Debug.LogError($"无效的TileCube关卡数据: Level {levelNumber}");
            return null;
        }

        // 存入缓存
        loadedLevels[levelNumber] = dataLevel;

        return dataLevel;
    }
    #endregion

    #region 资源管理

    // 获取配置中的预制体路径
    private static string GetPrefabPath(string prefabType)
    {
        var config = MXR_BRIGE.Cos_GameSetting.TileCube_Const_Data;
        return config.PrefabPaths.ContainsKey(prefabType) ? config.PrefabPaths[prefabType] : null;
    }

    // 单一块预制体缓存
    private static GameObject singleBlockPrefab;

    // 大型块预制体缓存
    private static GameObject largeBlockPrefab;

    // 金猪预制体缓存
    private static GameObject goldPigPrefab;

    // 物理阻挡条预制体缓存
    private static GameObject barrierPrefab;

    // 钥匙预制体缓存
    private static GameObject keyPrefab;

    // 出块库预制体缓存
    private static GameObject spawnerPrefab;

    #endregion

    #region 公共接口方法

    /// <summary>
    /// 获取单一块预制体
    /// </summary>
    public static GameObject GetSingleBlockPrefab(int colorId)
    {
        if (singleBlockPrefab == null)
        {
            string path = GetPrefabPath("SingleBlock");
            if (!string.IsNullOrEmpty(path))
            {
                singleBlockPrefab = Resources.Load<GameObject>(path);
                if (singleBlockPrefab == null)
                {
                    //Debug.LogWarning($"无法加载单一块预制体: {path}，创建临时物体");
                    singleBlockPrefab = CreateTempPrefab("TempSingleBlock", Color.white);
                }
            }
            else
            {
                //Debug.LogWarning("单一块预制体路径未配置，创建临时物体");
                singleBlockPrefab = CreateTempPrefab("TempSingleBlock", Color.white);
            }
        }
        return singleBlockPrefab;
    }

    /// <summary>
    /// 获取大型块预制体
    /// </summary>
    public static GameObject GetLargeBlockPrefab(int colorId)
    {
        if (largeBlockPrefab == null)
        {
            string path = GetPrefabPath("LargeBlock");
            if (!string.IsNullOrEmpty(path))
            {
                largeBlockPrefab = Resources.Load<GameObject>(path);
                if (largeBlockPrefab == null)
                {
                    //Debug.LogWarning($"无法加载大型块预制体: {path}，创建临时物体");
                    largeBlockPrefab = CreateTempPrefab("TempLargeBlock", Color.cyan, new Vector3(2f, 1f, 2f));
                }
            }
            else
            {
                //Debug.LogWarning("大型块预制体路径未配置，创建临时物体");
                largeBlockPrefab = CreateTempPrefab("TempLargeBlock", Color.cyan, new Vector3(2f, 1f, 2f));
            }
        }
        return largeBlockPrefab;
    }

    /// <summary>
    /// 获取金猪预制体
    /// </summary>
    public static GameObject GetGoldPigPrefab()
    {
        if (goldPigPrefab == null)
        {
            string path = GetPrefabPath("GoldPig");
            if (!string.IsNullOrEmpty(path))
            {
                goldPigPrefab = Resources.Load<GameObject>(path);
                if (goldPigPrefab == null)
                {
                    //Debug.LogWarning($"无法加载金猪预制体: {path}，创建临时物体");
                    goldPigPrefab = CreateTempPrefab("TempGoldPig", Color.yellow);
                }
            }
            else
            {
                //Debug.LogWarning("金猪预制体路径未配置，创建临时物体");
                goldPigPrefab = CreateTempPrefab("TempGoldPig", Color.yellow);
            }
        }
        return goldPigPrefab;
    }

    /// <summary>
    /// 获取物理阻挡条预制体
    /// </summary>
    public static GameObject GetBarrierPrefab(int colorId)
    {
        if (barrierPrefab == null)
        {
            string path = GetPrefabPath("PhysicBar");
            if (!string.IsNullOrEmpty(path))
            {
                barrierPrefab = Resources.Load<GameObject>(path);
                if (barrierPrefab == null)
                {
                    //Debug.LogWarning($"无法加载阻挡条预制体: {path}，创建临时物体");
                    barrierPrefab = CreateTempPrefab("TempBarrier", Color.red, new Vector3(1f, 0.5f, 3f));
                }
            }
            else
            {
                //Debug.LogWarning("阻挡条预制体路径未配置，创建临时物体");
                barrierPrefab = CreateTempPrefab("TempBarrier", Color.red, new Vector3(1f, 0.5f, 3f));
            }
        }
        return barrierPrefab;
    }

    /// <summary>
    /// 获取钥匙预制体
    /// </summary>
    public static GameObject GetKeyPrefab()
    {
        if (keyPrefab == null)
        {
            string path = GetPrefabPath("Key");
            if (!string.IsNullOrEmpty(path))
            {
                keyPrefab = Resources.Load<GameObject>(path);
                if (keyPrefab == null)
                {
                    //Debug.LogWarning($"无法加载钥匙预制体: {path}，创建临时物体");
                    keyPrefab = CreateTempPrefab("TempKey", Color.magenta, new Vector3(0.8f, 1.2f, 0.8f));
                }
            }
            else
            {
                //Debug.LogWarning("钥匙预制体路径未配置，创建临时物体");
                keyPrefab = CreateTempPrefab("TempKey", Color.magenta, new Vector3(0.8f, 1.2f, 0.8f));
            }
        }
        return keyPrefab;
    }

    /// <summary>
    /// 获取出块库预制体
    /// </summary>
    public static GameObject GetSpawnerPrefab()
    {
        if (spawnerPrefab == null)
        {
            string path = GetPrefabPath("SpawnBox");
            if (!string.IsNullOrEmpty(path))
            {
                spawnerPrefab = Resources.Load<GameObject>(path);
                if (spawnerPrefab == null)
                {
                    //Debug.LogWarning($"无法加载出块库预制体: {path}，创建临时物体");
                    spawnerPrefab = CreateTempPrefab("TempSpawner", Color.green, new Vector3(2f, 1.5f, 2f));
                }
            }
            else
            {
                //Debug.LogWarning("出块库预制体路径未配置，创建临时物体");
                spawnerPrefab = CreateTempPrefab("TempSpawner", Color.green, new Vector3(2f, 1.5f, 2f));
            }
        }
        return spawnerPrefab;
    }



    /// <summary>
    /// 创建临时预制体（当Resources中没有对应预制体时使用）
    /// </summary>
    private static GameObject CreateTempPrefab(string name, Color color, Vector3? scale = null)
    {
        GameObject tempObj = GameObject.CreatePrimitive(PrimitiveType.Cube);
        tempObj.name = name;

        // 设置颜色
        Renderer renderer = tempObj.GetComponent<Renderer>();
        if (renderer != null)
        {
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = color;
            renderer.material = mat;
        }

        // 设置缩放
        if (scale.HasValue)
        {
            tempObj.transform.localScale = scale.Value;
        }

        // 添加对应的组件
        if (name.Contains("SingleBlock"))
        {
            tempObj.AddComponent<TileCube_SingleBlock>();
        }
        else if (name.Contains("LargeBlock"))
        {
            tempObj.AddComponent<TileCube_LargeBlock>();
        }
        else if (name.Contains("GoldPig"))
        {
            tempObj.AddComponent<TileCube_GoldPig>();
        }
        else if (name.Contains("Barrier"))
        {
            tempObj.AddComponent<TileCube_Barrier>();
        }
        else if (name.Contains("Key"))
        {
            tempObj.AddComponent<TileCube_Key>();
        }
        else if (name.Contains("Spawner"))
        {
            tempObj.AddComponent<TileCube_Spawner>();
        }

        //Debug.Log($"创建了临时预制体: {name}，颜色: {color}");
        return tempObj;
    }

    #endregion
}