using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

/// <summary>
/// 瓦片方块地图组件 - 管理地图相关的UI和信息显示
/// 参考CupQueue_Map的架构模式
/// </summary>
public class TileCube_Map : MonoBehaviour
{
    [Header("UI组件")]
    public TextMeshProUGUI TxtObjectCount;      // 物体数量显示
    public TextMeshProUGUI TxtKeyCount;         // 钥匙数量显示
    public TextMeshProUGUI TxtSpawnerCount;     // 出块库数量显示
    public TextMeshProUGUI TxtLevel;            // 关卡显示

    [Header("状态文本")]
    public TextMeshProUGUI txtObjectsLabel;     // "物体"标签
    public TextMeshProUGUI txtKeysLabel;        // "钥匙"标签
    public TextMeshProUGUI txtSpawnersLabel;    // "出块库"标签
    public TextMeshProUGUI txtLevelLabel;       // "关卡"标签

    [Header("地图信息")]
    public int CurrentLevel;                    // 当前关卡
    public int TotalObjects;                    // 总物体数量
    public int RemainingKeys;                   // 剩余钥匙数量
    public int ActiveSpawners;                  // 活跃的出块库数量

    private void Awake()
    {
        // 初始化本地化文本
        InitializeLocalizedText();
    }

    private void Start()
    {
        // 更新显示
        UpdateDisplay();
    }

    /// <summary>
    /// 初始化本地化文本
    /// </summary>
    private void InitializeLocalizedText()
    {
        if (txtObjectsLabel != null)
        {
            txtObjectsLabel.text = Tool_LanguageManager.GetText("物体");
        }

        if (txtKeysLabel != null)
        {
            txtKeysLabel.text = Tool_LanguageManager.GetText("钥匙");
        }

        if (txtSpawnersLabel != null)
        {
            txtSpawnersLabel.text = Tool_LanguageManager.GetText("出块库");
        }

        if (txtLevelLabel != null)
        {
            txtLevelLabel.text = Tool_LanguageManager.GetText("关卡");
        }
    }

    /// <summary>
    /// 更新显示信息
    /// </summary>
    public void UpdateDisplay()
    {
        // 更新物体数量
        if (TxtObjectCount != null)
        {
            TxtObjectCount.text = TotalObjects.ToString();
        }

        // 更新钥匙数量
        if (TxtKeyCount != null)
        {
            TxtKeyCount.text = RemainingKeys.ToString();
        }

        // 更新出块库数量
        if (TxtSpawnerCount != null)
        {
            TxtSpawnerCount.text = ActiveSpawners.ToString();
        }

        // 更新关卡
        if (TxtLevel != null)
        {
            TxtLevel.text = CurrentLevel.ToString();
        }
    }

    /// <summary>
    /// 设置关卡信息
    /// </summary>
    public void SetLevelInfo(int level)
    {
        CurrentLevel = level;
        UpdateDisplay();
    }



    /// <summary>
    /// 钥匙到达前方时更新显示
    /// </summary>
    public void OnKeyReachedFront()
    {
        RemainingKeys = Mathf.Max(0, RemainingKeys - 1);
        UpdateDisplay();
    }

    /// <summary>
    /// 出块库用完时更新显示
    /// </summary>
    public void OnSpawnerExhausted()
    {
        ActiveSpawners = Mathf.Max(0, ActiveSpawners - 1);
        UpdateDisplay();
    }

    /// <summary>
    /// 物体被销毁时更新显示
    /// </summary>
    public void OnObjectDestroyed()
    {
        TotalObjects = Mathf.Max(0, TotalObjects - 1);
        UpdateDisplay();
    }

    /// <summary>
    /// 新物体被创建时更新显示
    /// </summary>
    public void OnObjectCreated()
    {
        TotalObjects++;
        UpdateDisplay();
    }

    /// <summary>
    /// 获取地图状态信息
    /// </summary>
    public string GetMapStatusInfo()
    {
        return $"关卡: {CurrentLevel}, 物体: {TotalObjects}, 钥匙: {RemainingKeys}, 出块库: {ActiveSpawners}";
    }

    /// <summary>
    /// 检查是否完成关卡条件
    /// </summary>
    public bool IsLevelComplete()
    {
        // 这里可以根据游戏规则定义完成条件
        // 例如：所有钥匙都到达前方，或者所有出块库都用完等
        return RemainingKeys == 0 && ActiveSpawners == 0;
    }

    /// <summary>
    /// 重置地图状态
    /// </summary>
    public void ResetMapStatus()
    {
        TotalObjects = 0;
        RemainingKeys = 0;
        ActiveSpawners = 0;
        CurrentLevel = 0;
        UpdateDisplay();
    }
}