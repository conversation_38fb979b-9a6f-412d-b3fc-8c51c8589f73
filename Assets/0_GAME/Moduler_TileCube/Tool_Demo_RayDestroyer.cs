using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// TileCube射线销毁工具 - 处理射线发射和物体销毁功能
/// 功能：射线检测、物体销毁、网格占位解除、特殊物体处理
/// </summary>
public class Tool_Demo_RayDestroyer
{
    [Header("销毁配置")]
    public bool CanDestroyImmovableObjects = false; // 是否可以销毁不可移动物体
    public bool ShowDestroyEffects = true;          // 是否显示销毁特效
    public bool PlayDestroySound = true;            // 是否播放销毁音效

    /// <summary>
    /// 销毁TileCube物体
    /// </summary>
    /// <param name="tileCubeObject">要销毁的TileCube物体</param>
    /// <param name="controller">TileCube控制器</param>
    /// <param name="tileGrid">网格系统</param>
    /// <returns>是否成功销毁</returns>
    public bool DestroyTileCubeObject(TileCube_Object tileCubeObject, TileCube_Controller controller, Tool_TileGrid tileGrid)
    {
        if (tileCubeObject == null)
        {
            //Debug.LogWarning("要销毁的物体为空");
            return false;
        }

        if (controller == null)
        {
            //Debug.LogWarning("TileCube控制器为空");
            return false;
        }

        if (tileGrid == null)
        {
            //Debug.LogWarning("网格系统为空");
            return false;
        }

        // 获取物体的网格信息
        var gridInfo = tileCubeObject.GetGridInfo();
        if (gridInfo == null)
        {
            //Debug.LogWarning($"物体 {tileCubeObject.name} 没有网格信息");
            return false;
        }

        // 检查是否可以销毁不可移动物体
        if (gridInfo.isImmovable && !CanDestroyImmovableObjects)
        {
            //Debug.LogWarning($"物体 {tileCubeObject.name} 是不可移动物体，无法销毁");
            return false;
        }

        // 根据物体类型执行特殊销毁逻辑
        bool canDestroy = ProcessSpecialDestroyLogic(tileCubeObject);
        if (!canDestroy)
        {
            return false;
        }

        // 播放销毁效果
        if (ShowDestroyEffects)
        {
            PlayDestroyEffect(tileCubeObject);
        }

        // 播放销毁音效
        if (PlayDestroySound)
        {
            PlayDestroySoundEffect(tileCubeObject);
        }

        // 从网格系统中移除物体
        bool removedFromGrid = tileGrid.RemoveObject(tileCubeObject.gameObject);
        if (!removedFromGrid)
        {
            //Debug.LogWarning($"无法从网格中移除物体 {tileCubeObject.name}");
        }

        // 从控制器列表中移除物体
        if (controller.Current_Objects.Contains(tileCubeObject))
        {
            controller.Current_Objects.Remove(tileCubeObject);
        }

        // 记录销毁信息
        LogDestroyInfo(tileCubeObject, gridInfo);

        // 销毁游戏对象
        Object.Destroy(tileCubeObject.gameObject);

        return true;
    }

    /// <summary>
    /// 处理特殊物体类型的销毁逻辑
    /// </summary>
    /// <param name="tileCubeObject">要销毁的物体</param>
    /// <returns>是否可以销毁</returns>
    bool ProcessSpecialDestroyLogic(TileCube_Object tileCubeObject)
    {
        switch (tileCubeObject.ObjectType)
        {
            case TileCube_Object.TileCube_ObjectType.Spawner:
                // 出块库销毁前的特殊处理
                return ProcessSpawnerDestroy(tileCubeObject as TileCube_Spawner);

            case TileCube_Object.TileCube_ObjectType.Barrier:
                // 物理阻挡条销毁前的特殊处理
                return ProcessBarrierDestroy(tileCubeObject as TileCube_Barrier);

            case TileCube_Object.TileCube_ObjectType.Key:
                // 钥匙销毁前的特殊处理
                return ProcessKeyDestroy(tileCubeObject as TileCube_Key);

            case TileCube_Object.TileCube_ObjectType.GoldPig:
                // 金猪销毁前的特殊处理
                return ProcessGoldPigDestroy(tileCubeObject as TileCube_GoldPig);

            case TileCube_Object.TileCube_ObjectType.SingleBlock:
                // 单一块销毁前的特殊处理
                return ProcessSingleBlockDestroy(tileCubeObject as TileCube_SingleBlock);

            case TileCube_Object.TileCube_ObjectType.LargeBlock:
                // 大型块销毁前的特殊处理
                return ProcessLargeBlockDestroy(tileCubeObject as TileCube_LargeBlock);

            default:
                return true; // 默认允许销毁
        }
    }

    /// <summary>
    /// 处理出块库销毁
    /// </summary>
    bool ProcessSpawnerDestroy(TileCube_Spawner spawner)
    {
        if (spawner == null) return true;

        // 出块库可能需要特殊权限才能销毁
        //Debug.Log($"销毁出块库：{spawner.name}");

        // 可以在这里添加特殊逻辑，比如需要特定条件才能销毁
        return true;
    }

    /// <summary>
    /// 处理物理阻挡条销毁
    /// </summary>
    bool ProcessBarrierDestroy(TileCube_Barrier barrier)
    {
        if (barrier == null) return true;

        //Debug.Log($"销毁物理阻挡条：{barrier.name}");

        // 物理阻挡条销毁时可能需要清理额外的占位
        // 这里可以添加清理阻挡条延伸部分的逻辑
        return true;
    }

    /// <summary>
    /// 处理钥匙销毁
    /// </summary>
    bool ProcessKeyDestroy(TileCube_Key key)
    {
        if (key == null) return true;

        //Debug.Log($"销毁钥匙：{key.name}");

        // 钥匙销毁可能影响游戏进度
        return true;
    }

    /// <summary>
    /// 处理金猪销毁
    /// </summary>
    bool ProcessGoldPigDestroy(TileCube_GoldPig goldPig)
    {
        if (goldPig == null) return true;

        //Debug.Log($"销毁金猪：{goldPig.name}");

        // 金猪销毁可能给予奖励
        return true;
    }

    /// <summary>
    /// 处理单一块销毁
    /// </summary>
    bool ProcessSingleBlockDestroy(TileCube_SingleBlock singleBlock)
    {
        if (singleBlock == null) return true;

        //Debug.Log($"销毁单一块：{singleBlock.name}，颜色ID：{singleBlock.ColorId}");
        return true;
    }

    /// <summary>
    /// 处理大型块销毁
    /// </summary>
    bool ProcessLargeBlockDestroy(TileCube_LargeBlock largeBlock)
    {
        if (largeBlock == null) return true;


        return true;
    }

    /// <summary>
    /// 播放销毁特效
    /// </summary>
    void PlayDestroyEffect(TileCube_Object tileCubeObject)
    {
        if (tileCubeObject == null) return;

        // 这里可以根据物体类型播放不同的销毁特效
        Vector3 effectPosition = tileCubeObject.transform.position;

        // 简单的粒子效果示例（需要粒子系统）
        // 可以根据物体类型选择不同的特效
        //Debug.Log($"在位置 {effectPosition} 播放销毁特效");

        // 实际项目中可以这样实现：
        // GameObject effect = GetDestroyEffect(tileCubeObject.ObjectType);
        // if (effect != null)
        // {
        //     GameObject.Instantiate(effect, effectPosition, Quaternion.identity);
        // }
    }

    /// <summary>
    /// 播放销毁音效
    /// </summary>
    void PlayDestroySoundEffect(TileCube_Object tileCubeObject)
    {
        if (tileCubeObject == null) return;

        // 根据物体类型播放不同的销毁音效
        string soundName = GetDestroySoundName(tileCubeObject.ObjectType);

        //Debug.Log($"播放销毁音效：{soundName}");

        // 通过TileCube控制器的音效系统播放
        // TileCube_Controller.On_PlaySound?.Invoke(soundName);
    }

    /// <summary>
    /// 获取销毁音效名称
    /// </summary>
    string GetDestroySoundName(TileCube_Object.TileCube_ObjectType objectType)
    {
        switch (objectType)
        {
            case TileCube_Object.TileCube_ObjectType.SingleBlock:
                return "destroy_single_block";
            case TileCube_Object.TileCube_ObjectType.LargeBlock:
                return "destroy_large_block";
            case TileCube_Object.TileCube_ObjectType.GoldPig:
                return "destroy_gold_pig";
            case TileCube_Object.TileCube_ObjectType.Barrier:
                return "destroy_barrier";
            case TileCube_Object.TileCube_ObjectType.Key:
                return "destroy_key";
            case TileCube_Object.TileCube_ObjectType.Spawner:
                return "destroy_spawner";
            default:
                return "destroy_default";
        }
    }

    /// <summary>
    /// 记录销毁信息
    /// </summary>
    void LogDestroyInfo(TileCube_Object tileCubeObject, GridTileObject gridInfo)
    {
        //Debug.Log($"=== 物体销毁信息 ===");
        //Debug.Log($"物体名称：{tileCubeObject.name}");
        //Debug.Log($"物体类型：{tileCubeObject.ObjectType}");
        //Debug.Log($"网格位置：({gridInfo.gridX}, {gridInfo.gridZ}, {gridInfo.height})");
        //Debug.Log($"占用大小：{gridInfo.sizeX}x{gridInfo.sizeZ}");
        //Debug.Log($"是否不可移动：{gridInfo.isImmovable}");

    }

    /// <summary>
    /// 批量销毁多个物体
    /// </summary>
    /// <param name="tileCubeObjects">要销毁的物体列表</param>
    /// <param name="controller">TileCube控制器</param>
    /// <param name="tileGrid">网格系统</param>
    /// <returns>成功销毁的物体数量</returns>
    public int DestroyMultipleTileCubeObjects(List<TileCube_Object> tileCubeObjects, TileCube_Controller controller, Tool_TileGrid tileGrid)
    {
        if (tileCubeObjects == null || tileCubeObjects.Count == 0)
        {
            return 0;
        }

        int destroyedCount = 0;

        // 创建副本以避免在遍历时修改列表
        var objectsToDestroy = new List<TileCube_Object>(tileCubeObjects);

        foreach (var obj in objectsToDestroy)
        {
            if (DestroyTileCubeObject(obj, controller, tileGrid))
            {
                destroyedCount++;
            }
        }

        //Debug.Log($"批量销毁完成：成功销毁 {destroyedCount}/{objectsToDestroy.Count} 个物体");
        return destroyedCount;
    }

    /// <summary>
    /// 根据位置销毁物体
    /// </summary>
    /// <param name="gridX">网格X坐标</param>
    /// <param name="gridZ">网格Z坐标</param>
    /// <param name="height">高度层（-1表示所有高度层）</param>
    /// <param name="controller">TileCube控制器</param>
    /// <param name="tileGrid">网格系统</param>
    /// <returns>是否成功销毁</returns>
    public bool DestroyObjectAtPosition(int gridX, int gridZ, int height, TileCube_Controller controller, Tool_TileGrid tileGrid)
    {
        if (tileGrid == null) return false;

        GameObject targetObject = tileGrid.GetObjectAtPosition(gridX, gridZ, height);
        if (targetObject == null)
        {
            //Debug.LogWarning($"位置({gridX}, {gridZ}, {height})没有物体");
            return false;
        }

        TileCube_Object tileCubeObject = targetObject.GetComponent<TileCube_Object>();
        if (tileCubeObject == null)
        {
            //Debug.LogWarning($"位置({gridX}, {gridZ}, {height})的物体不是TileCube物体");
            return false;
        }

        return DestroyTileCubeObject(tileCubeObject, controller, tileGrid);
    }
}