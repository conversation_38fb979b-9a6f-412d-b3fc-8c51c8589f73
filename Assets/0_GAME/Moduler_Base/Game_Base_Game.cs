using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Game_Base_Game : Mono<PERSON><PERSON><PERSON><PERSON>, Tool_IInputChecker
{


    protected virtual void Awake()
    {
        Tool_InputManager.RegisterChecker(this);
    }


    protected virtual void OnDestroy()
    {
        Tool_InputManager.UnregisterChecker(this);
    }




    // 处理声音播放事件
    protected void OnPlaySound(string soundName)
    {
        // 检查用户数据中的声音开关
        if (MatchUI_UserData_Manager.Instance.Get.Setting_Sound)
        {
            // 如果声音效果开启，则播放声音
            MXR_BRIGE.Sound_PlayEffect(soundName);
        }
    }

    // 处理音乐播放事件
    protected void OnPlayMusic(string musicName)
    {
        // 检查用户数据中的音乐开关
        if (MatchUI_UserData_Manager.Instance.Get.Setting_Music)
        {



        }
    }

    // 处理音乐停止事件
    protected void OnStopMusic()
    {

    }

    protected void On_PlayVib(bool islong)
    {
        if (!MatchUI_UserData_Manager.Instance.Get.Setting_Vib)
            return;
        if (islong)
            MXR_BRIGE.Virb_Long();
        else
            MXR_BRIGE.Virb_Short();
    }

    protected void On_PlayVib(int value)
    {
        if (!MatchUI_UserData_Manager.Instance.Get.Setting_Vib)
            return;
//        MXR_BRIGE.Virb(value);
    }

    public virtual bool CanProcessInput()
    {
        return false;
    }



}
