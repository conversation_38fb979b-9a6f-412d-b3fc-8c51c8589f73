using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
public class MatchUI_ShopPack : MonoBehaviour
{
    public string Key;
    public int Index;
    public MatchUI_ShopPack_Data Data;

    public TextMeshProUGUI txt_Name;
    public TextMeshP<PERSON>UGUI txt_Coin;
    public TextMeshProUGUI txt_Cash;
    public Image img_NoADS;
    public Button btn_Buy;
    public Image[] img_Props;
    public TextMeshProUGUI[] txt_PropsCount;

    public TextMeshProUGUI txt_Selled;

    public Image[] img_PropIcons;

    public Image img_Pack;
    public Sprite spr_Sellout;

    private void Awake()
    {
        btn_Buy.onClick.AddListener(OnBtnBuyClick);


    }

    public void UpdateData(string key, int index, MatchUI_ShopPack_Data data)
    {
        if (img_Pack == null)
            img_Pack = GetComponent<Image>();

        Key = key;
        Index = index;
        Data = data;

        var userdata = MatchUI_UserData_Manager.Instance.Get;

        if (txt_Name != null)
            txt_Name.text = Tool_LanguageManager.GetText(Data.Name);

        if (txt_Coin != null)
            txt_Coin.text = "X" + Data.Coin.ToString();

        if (txt_Cash != null)
            txt_Cash.text = Data.NeedCash;

        if (img_NoADS != null)
        {
            //检测是否已经移除广告
            if (!userdata.AD_Remove_FullAD)
                img_NoADS.gameObject.SetActive(Data.RemoveADS);
            else
                img_NoADS.gameObject.SetActive(false);
        }



        if (img_Props.Length > 0)
            if (txt_PropsCount.Length > 0)
            {
                for (var i = 0; i < img_Props.Length; i++)
                {

                    if (Data.PropsCount[i] <= 0)
                    {
                        img_Props[i].gameObject.SetActive(false);
                    }
                    else
                    {
                        img_Props[i].gameObject.SetActive(true);
                        txt_PropsCount[i].text = "X" + Data.PropsCount[i].ToString();
                    }
                }
            }

        if (userdata.Pack_Buyed[Key][Index] >= Data.ReserveCount)
        {
            //购买上限   
            if (Key == "Coin")
            {
                btn_Buy.enabled = false;
                txt_Cash.gameObject.SetActive(false);
            }
            else
            {
                btn_Buy.gameObject.SetActive(false);
            }

            txt_Selled.gameObject.SetActive(true);

            img_Pack.sprite = spr_Sellout;

        }
        else
        {
            txt_Selled.gameObject.SetActive(false);
        }


        if (Key == "NoADS")
        {
            if (userdata.AD_Remove_FullAD)
            {
                txt_Selled.gameObject.SetActive(true);
                btn_Buy.gameObject.SetActive(false);

                img_Pack.sprite = spr_Sellout;
            }
        }


        for (int i = 0; i < img_PropIcons.Length; i++)
        {
            img_PropIcons[i].sprite =
                MatchUI_PropIconRes.LoadPropIcon(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.PropIcon_Paths[i]);
        }

    }

    public void OnBtnBuyClick()
    {

    }

    public void On_PaySuccess()
    {
        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_获得奖励");

        if (Key == "NoADS")
        {
            MXR_BRIGE.MessageTip_Show(Tool_LanguageManager.GetText("移除成功!"));
        }
        else
        {
            MXR_BRIGE.MessageTip_Show(Tool_LanguageManager.GetText("购买成功!"));
        }

        MatchUI_UserData_Manager.Instance.Get.Coin_Current += Data.Coin;

        for (var i = 0; i < Data.PropsCount.Count; i++)
        {
            MatchUI_UserData_Manager.Instance.Get.Prop_Count[i] += Data.PropsCount[i];
        }


        if (!MatchUI_UserData_Manager.Instance.Get.AD_Remove_FullAD)
            if (Data.RemoveADS)
            {
                MatchUI_UserData_Manager.Instance.Get.AD_Remove_FullAD = true;
                MatchUI_Controller.On_RemoveADSSuccess?.Invoke();
            }



        MatchUI_UserData_Manager.Instance.Set.Pack_Buyed[Key][Index] += 1;

        MatchUI_ShopPanel.Instance.UpdatePack();

        if (MatchUI_GamePanel.Instance != null)
            MatchUI_GamePanel.Instance.UpdateBtnPropState();

        MatchUI_Controller.On_BuyPackSuccess?.Invoke(this);
    }

}
