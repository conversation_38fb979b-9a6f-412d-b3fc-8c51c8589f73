using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_GuidePanel : MatchUI_BasePanel<MatchUI_GuidePanel>
{


    protected static string resourcesName = "3MatchUI_GuidePanel";

    public static void ShowPanel()
    {
        GameObject prefab = Resources.Load<GameObject>("MatchUI/Panel/" + resourcesName);
        if (prefab != null)
        {
            GameObject panelObj = GameObject.Instantiate(prefab);
            Canvas canvas = GameObject.Find("Canvas").GetComponent<Canvas>();
            if (canvas != null)
            {
                panelObj.transform.SetParent(canvas.transform, false);
            }
            else
            {
                Debug.LogError("No Canvas found in scene");
            }
        }
        else
        {
            Debug.LogError("Failed to load panel: " + resourcesName);
        }

        MatchUI_GuidePanel.Instance.Init();

        // 播放显示动画
        MatchUI_GuidePanel.Instance.PlayShowAnimation();
    }

    public static void ClosePanel()
    {
        MatchUI_GuidePanel panel = FindObjectOfType<MatchUI_GuidePanel>();
        if (panel != null)
        {
            // 播放隐藏动画，完成后销毁
            panel.PlayHideAnimation(() =>
            {
                Destroy(panel.gameObject);
            });
        }
    }


    public GameObject Type1;

    public GameObject Type2;

    public TextMeshProUGUI txtType1Title;
    public Button btnType1GetProp;
    public Image[] imgType1PropIcons;
    public Image[] imgType1PropTips;
    public TextMeshProUGUI txtType1Guide;

    public TextMeshProUGUI txtType2Guide;


    public GameObject Type3;
    public TextMeshProUGUI txtType3Title;
    public Button btnType3OK;
    public Image imgType3Icon;
    public TextMeshProUGUI txtType3Guide;


    public void Init()
    {

        int currentLevel = MatchUI_UserData_Manager.Instance.Get.Level_Current;

        // Find the guide data for the current level
        MatchUI_GuideLevel_Data guideData = null;
        foreach (var guide in MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Guide_Levels)
        {
            if (guide.Level == currentLevel)
            {
                guideData = guide;
                break;
            }
        }

        if (guideData == null)
        {
            Debug.LogError("No guide data found for level: " + currentLevel);
            ClosePanel();
            return;
        }

        // Show/hide panels based on guide type
        Type1.SetActive(guideData.Type == 1);
        Type2.SetActive(guideData.Type == 2);
        Type3.SetActive(guideData.Type == 3);

        // Fill in data based on type
        if (guideData.Type == 1)
        {
            // Set title and guide text
            txtType1Title.text = Tool_LanguageManager.GetText(guideData.Title);
            txtType1Guide.text = Tool_LanguageManager.GetText(guideData.Guide);



            for (int i = 0; i < imgType1PropIcons.Length; i++)
            {
                imgType1PropIcons[i].gameObject.SetActive(i == guideData.PropIconIndex);
                imgType1PropTips[i].gameObject.SetActive(i == guideData.PropIconIndex);
            }
        }
        else if (guideData.Type == 2)
        {
            // Set guide text for type 2
            txtType2Guide.text = Tool_LanguageManager.GetText(guideData.Guide);
        }
        else if (guideData.Type == 3)
        {
            // 设置Type3的标题和引导文本
            txtType3Title.text = Tool_LanguageManager.GetText(guideData.Title);
            txtType3Guide.text = Tool_LanguageManager.GetText(guideData.Guide);

            // 根据Type3IconPath设置图标
            if (!string.IsNullOrEmpty(guideData.Type3IconPath))
            {
                imgType3Icon.sprite = MatchUI_PropIconRes.LoadPropIcon(guideData.Type3IconPath);
                //  imgType3Icon.SetNativeSize();
            }


        }

        for (int i = 0; i < imgType1PropIcons.Length; i++)
        {
            imgType1PropTips[i].sprite =
                MatchUI_PropIconRes.LoadPropIcon(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.PropIcon_Paths[i]);
            imgType1PropIcons[i].sprite =
                MatchUI_PropIconRes.LoadPropIcon(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.PropIcon_Paths[i]);
        }
    }

    public void OnBtnbtnType1GetPropClick()
    {
        int currentLevel = MatchUI_UserData_Manager.Instance.Get.Level_Current;

        // Find the guide data for the current level
        MatchUI_GuideLevel_Data guideData = null;
        foreach (var guide in MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Guide_Levels)
        {
            if (guide.Level == currentLevel)
            {
                guideData = guide;
                break;
            }
        }

        if (guideData != null && guideData.GiftPropCount != null)
        {


            if (MatchUI_UserData_Manager.Instance.Get.Prop_Count[guideData.PropIconIndex] == 0)
                MatchUI_UserData_Manager.Instance.Set.Prop_Count[guideData.PropIconIndex] += guideData.GiftPropCount;

            MatchUI_GamePanel.Instance.UpdateBtnPropState();

        }

        // 关闭引导面板
        ClosePanel();
    }

    /// <summary>
    /// Type3的OK按钮点击事件
    /// </summary>
    public void OnBtnType3OKClick()
    {
        // MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
        // 关闭引导面板
        ClosePanel();
    }

}
