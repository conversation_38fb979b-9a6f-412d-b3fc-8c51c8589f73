using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class MatchUI_ShopPanel : MatchUI_BasePanel<MatchUI_ShopPanel>
{
    protected static string resourcesName = "2MatchUI_ShopPanel";


    public static void ShowPanel()
    {
        // 如果正在播放动画，忽略此次调用
        if (isAnimating) return;

        MatchUI_ShopPanel panel = Instance;
        if (panel != null && MatchUI_MenuBtnPanel.Instance != null)
        {
            panel.initialScrollBodyAnchorPosition = panel.Scroll_Body.anchoredPosition;
            MatchUI_ShopPanel.Instance.UpdatePack();
            panel.gameObject.SetActive(true);

            // 设置动画状态并播放显示动画
            isAnimating = true;
            MatchUI_ShopPanel.Instance.PlayShowAnimation(() =>
            {
                isAnimating = false; // 动画完成，重置状态
            });
            return;
        }

        GameObject prefab = Resources.Load<GameObject>("MatchUI/Panel/" + resourcesName);
        if (prefab != null)
        {
            GameObject panelObj = GameObject.Instantiate(prefab);
            Canvas canvas = GameObject.Find("Canvas").GetComponent<Canvas>();
            if (canvas != null)
            {
                panelObj.transform.SetParent(canvas.transform, false);
            }
            else
            {
                Debug.LogError("No Canvas found in scene");
            }

            MatchUI_ShopPanel.Instance.UpdatePack();

            // 设置动画状态并播放显示动画
            isAnimating = true;
            MatchUI_ShopPanel.Instance.PlayShowAnimation(() =>
            {
                isAnimating = false; // 动画完成，重置状态
            });
        }
        else
        {
            Debug.LogError("Failed to load panel: " + resourcesName);
        }
    }

    private static bool isClosing = false; // 防止重复关闭的标志
    private static bool isAnimating = false; // 防止动画期间操作的标志

    /// <summary>
    /// 检查面板是否正在关闭中
    /// </summary>
    public static bool IsClosing()
    {
        return isClosing;
    }

    /// <summary>
    /// 检查面板是否正在播放动画（显示或关闭）
    /// </summary>
    public static bool IsAnimating()
    {
        return isAnimating;
    }

    /// <summary>
    /// 重置所有状态标志（用于强制清理状态）
    /// </summary>
    public static void ResetStates()
    {
        isClosing = false;
        isAnimating = false;
    }

    public static void ClosePanel()
    {
        MatchUI_ShopPanel panel = FindObjectOfType<MatchUI_ShopPanel>();
        if (panel != null && !isClosing && !isAnimating)
        {
            isClosing = true;
            isAnimating = true;

            if (MatchUI_MenuBtnPanel.Instance != null)
            {
                // 播放隐藏动画，完成后隐藏面板
                panel.PlayHideAnimation(() =>
                {
                    // 直接调用MenuBtnPanel的内部逻辑，避免重复调用ClosePanel
                    if (MatchUI_MenuBtnPanel.Instance != null && MatchUI_MenuBtnPanel.Instance.currentIndex != 0)
                    {
                        MatchUI_MenuBtnPanel.Instance.HandleMenuButtonClick(0);
                        MatchUI_MenuBtnPanel.Instance.currentIndex = 0;
                    }

                    panel.Scroll_Body.anchoredPosition = panel.initialScrollBodyAnchorPosition;
                    panel.gameObject.SetActive(false);
                    isClosing = false; // 重置标志
                    isAnimating = false; // 重置动画标志
                });
            }
            else
            {
                // 播放隐藏动画，完成后销毁
                panel.PlayHideAnimation(() =>
                {
                    Destroy(panel.gameObject);
                    isClosing = false; // 重置标志
                    isAnimating = false; // 重置动画标志
                });
            }
        }
    }

    public RectTransform Scroll_Body;
    private Vector2 initialScrollBodyAnchorPosition;

    public MatchUI_ShopPack pack_Special;
    public List<MatchUI_ShopPack> pack_Gifts;
    public List<MatchUI_ShopPack> pack_Coins;
    public MatchUI_ShopPack pack_NoADS;

    public void UpdatePack()
    {
        var datasp = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Pack_Specials;
        var datagifts = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Pack_Gifts;
        var datacoins = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Pack_Coins;
        var datanoads = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Pack_NoADSs;

        pack_Special.UpdateData("Special", 0, datasp);
        for (var i = 0; i < pack_Gifts.Count; i++)
            pack_Gifts[i].UpdateData("Gift", i, datagifts[i]);
        for (var i = 0; i < pack_Coins.Count; i++)
            pack_Coins[i].UpdateData("Coin", i, datacoins[i]);
        pack_NoADS.UpdateData("NoADS", 0, datanoads);

    }


    public void On_BtnClose_Click()
    {
        // 只有在没有动画播放时才允许关闭
        if (!isAnimating)
        {
            ClosePanel();
        }
    }

    private void OnDestroy()
    {
        // 面板被销毁时重置状态标志
        ResetStates();
    }

}
