using System.Collections;
using UnityEngine;
using UnityEngine.UI;

public enum PanelAnimationType
{
    透明度,     // Alpha动画
    透明缩放    // Alpha + Scale动画
}

public class MatchUI_PanelAnimator : MonoBehaviour
{
    [Header("动画设置")]
    public PanelAnimationType animationType = PanelAnimationType.透明度;
    public float animationDuration = 0.3f;

    [Header("缩放动画专用 - 需要拖拽设置")]
    public Image[] fadeImages = new Image[0];     // 透明度控制的图片数组
    public Transform[] scaleTransforms = new Transform[0]; // 缩放控制的物体数组

    [Header("缩放超出效果设置")]
    public bool enableOvershoot = true; // 是否启用超出效果
    [Range(0f, 0.5f)]
    public float overshootPercent = 0.1f; // 超出百分比，0.1 = 10%超出

    // 私有变量存储初始状态
    private CanvasGroup canvasGroup;
    private float[] originalAlphas;
    private Vector3[] originalScales;
    private bool isInitialized = false;
    private bool isAnimating = false;

    private void Awake()
    {
        InitializeAnimation();
    }

    /// <summary>
    /// 初始化动画组件
    /// </summary>
    private void InitializeAnimation()
    {
        if (isInitialized) return;

        if (animationType == PanelAnimationType.透明度)
        {
            // 透明度动画：确保有CanvasGroup组件
            canvasGroup = GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = gameObject.AddComponent<CanvasGroup>();
            }
        }
        else if (animationType == PanelAnimationType.透明缩放)
        {
            // 透明缩放动画：记录初始状态
            if (fadeImages.Length > 0)
            {
                originalAlphas = new float[fadeImages.Length];
                for (int i = 0; i < fadeImages.Length; i++)
                {
                    if (fadeImages[i] != null)
                    {
                        originalAlphas[i] = fadeImages[i].color.a;
                    }
                }
            }

            if (scaleTransforms.Length > 0)
            {
                originalScales = new Vector3[scaleTransforms.Length];
                for (int i = 0; i < scaleTransforms.Length; i++)
                {
                    if (scaleTransforms[i] != null)
                    {
                        originalScales[i] = scaleTransforms[i].localScale;
                    }
                }
            }
        }

        isInitialized = true;
    }

    /// <summary>
    /// 播放显示动画
    /// </summary>
    public void PlayShowAnimation(System.Action onComplete = null)
    {
        if (isAnimating) return;

        InitializeAnimation();
        StopAllCoroutines();
        StartCoroutine(DoShowAnimation(onComplete));
    }

    /// <summary>
    /// 播放隐藏动画
    /// </summary>
    public void PlayHideAnimation(System.Action onComplete = null)
    {
        if (isAnimating) return;

        InitializeAnimation();
        StopAllCoroutines();
        StartCoroutine(DoHideAnimation(onComplete));
    }

    /// <summary>
    /// 执行显示动画协程
    /// </summary>
    private IEnumerator DoShowAnimation(System.Action onComplete)
    {
        isAnimating = true;

        if (animationType == PanelAnimationType.透明度)
        {
            // 透明度动画：0->1
            canvasGroup.alpha = 0f;
            canvasGroup.interactable = false;
            canvasGroup.blocksRaycasts = true;

            float elapsed = 0f;
            while (elapsed < animationDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / animationDuration;
                canvasGroup.alpha = Mathf.Lerp(0f, 1f, progress);
                yield return null;
            }

            canvasGroup.alpha = 1f;
            canvasGroup.interactable = true;
        }
        else if (animationType == PanelAnimationType.透明缩放)
        {
            // 透明缩放动画：设置初始状态
            SetFadeImagesAlpha(0f);
            SetScaleTransformsScale(Vector3.zero);

            float elapsed = 0f;
            while (elapsed < animationDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / animationDuration;

                // 透明度从0到目标值（线性）
                SetFadeImagesAlphaProgress(progress);

                // 缩放从0到目标值，可选超出效果
                if (enableOvershoot && overshootPercent > 0f)
                {
                    float scaleProgress = CalculateScaleProgressWithOvershoot(progress);
                    SetScaleTransformsScaleProgressWithOvershoot(scaleProgress);
                }
                else
                {
                    // 不使用超出效果，直接线性缩放
                    SetScaleTransformsScaleProgress(progress);
                }

                yield return null;
            }

            // 确保最终状态正确
            SetFadeImagesAlphaProgress(1f);
            SetScaleTransformsScaleProgress(1f);
        }

        isAnimating = false;
        onComplete?.Invoke();
    }

    /// <summary>
    /// 执行隐藏动画协程
    /// </summary>
    private IEnumerator DoHideAnimation(System.Action onComplete)
    {
        isAnimating = true;

        if (animationType == PanelAnimationType.透明度)
        {
            // 透明度动画：1->0
            canvasGroup.interactable = false;

            float elapsed = 0f;
            while (elapsed < animationDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / animationDuration;
                canvasGroup.alpha = Mathf.Lerp(1f, 0f, progress);
                yield return null;
            }

            canvasGroup.alpha = 0f;
        }
        else if (animationType == PanelAnimationType.透明缩放)
        {
            float elapsed = 0f;
            while (elapsed < animationDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / animationDuration;

                // 透明度从目标值到0
                SetFadeImagesAlphaProgress(1f - progress);
                // 缩放从目标值到0（关闭动画不需要超出效果，直接线性缩放）
                SetScaleTransformsScaleProgress(1f - progress);

                yield return null;
            }

            // 确保最终状态
            SetFadeImagesAlpha(0f);
            SetScaleTransformsScale(Vector3.zero);
        }

        isAnimating = false;
        onComplete?.Invoke();
    }

    /// <summary>
    /// 设置透明度图片的透明度
    /// </summary>
    private void SetFadeImagesAlpha(float alpha)
    {
        if (fadeImages == null) return;

        for (int i = 0; i < fadeImages.Length; i++)
        {
            if (fadeImages[i] != null)
            {
                Color color = fadeImages[i].color;
                color.a = alpha;
                fadeImages[i].color = color;
            }
        }
    }

    /// <summary>
    /// 根据进度设置透明度图片的透明度
    /// </summary>
    private void SetFadeImagesAlphaProgress(float progress)
    {
        if (fadeImages == null || originalAlphas == null) return;

        for (int i = 0; i < fadeImages.Length && i < originalAlphas.Length; i++)
        {
            if (fadeImages[i] != null)
            {
                Color color = fadeImages[i].color;
                color.a = Mathf.Lerp(0f, originalAlphas[i], progress);
                fadeImages[i].color = color;
            }
        }
    }

    /// <summary>
    /// 设置缩放物体的缩放
    /// </summary>
    private void SetScaleTransformsScale(Vector3 scale)
    {
        if (scaleTransforms == null) return;

        for (int i = 0; i < scaleTransforms.Length; i++)
        {
            if (scaleTransforms[i] != null)
            {
                scaleTransforms[i].localScale = scale;
            }
        }
    }

    /// <summary>
    /// 根据进度设置缩放物体的缩放
    /// </summary>
    private void SetScaleTransformsScaleProgress(float progress)
    {
        if (scaleTransforms == null || originalScales == null) return;

        for (int i = 0; i < scaleTransforms.Length && i < originalScales.Length; i++)
        {
            if (scaleTransforms[i] != null)
            {
                scaleTransforms[i].localScale = Vector3.Lerp(Vector3.zero, originalScales[i], progress);
            }
        }
    }

    /// <summary>
    /// 计算带超出效果的缩放进度
    /// </summary>
    private float CalculateScaleProgressWithOvershoot(float normalizedProgress)
    {
        // 使用两阶段动画：快速到110% -> 平滑回到100%
        // 前75%时间：从0缩放到110%
        // 后25%时间：从110%缩放到100%

        float overshootAmount = 1f + overshootPercent; // 目标缩放 + 设置的超出百分比
        const float phase1Duration = 0.75f; // 前75%时间达到超出点

        if (normalizedProgress <= phase1Duration)
        {
            // 前75%时间：0 -> 1.1 (110%)，使用缓入缓出曲线
            float phase1Progress = normalizedProgress / phase1Duration;
            // 使用平滑曲线让缩放更自然
            float smoothProgress = Mathf.SmoothStep(0f, 1f, phase1Progress);
            return Mathf.Lerp(0f, overshootAmount, smoothProgress);
        }
        else
        {
            // 后25%时间：1.1 -> 1.0 (100%)，使用缓出曲线
            float phase2Progress = (normalizedProgress - phase1Duration) / (1f - phase1Duration);
            // 使用更平滑的回弹效果
            float smoothProgress = 1f - Mathf.Pow(1f - phase2Progress, 2f);
            return Mathf.Lerp(overshootAmount, 1.0f, smoothProgress);
        }
    }

    /// <summary>
    /// 根据超出进度设置缩放物体的缩放
    /// </summary>
    private void SetScaleTransformsScaleProgressWithOvershoot(float overshootProgress)
    {
        if (scaleTransforms == null || originalScales == null) return;

        for (int i = 0; i < scaleTransforms.Length && i < originalScales.Length; i++)
        {
            if (scaleTransforms[i] != null)
            {
                scaleTransforms[i].localScale = originalScales[i] * overshootProgress;
            }
        }
    }
}