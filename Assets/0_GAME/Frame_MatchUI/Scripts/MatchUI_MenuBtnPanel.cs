using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;


public class MatchUI_MenuBtnPanel : MatchUI_BasePanel<MatchUI_MenuBtnPanel>
{
    public Button[] btnMenus;

    public Image[] imgMenus;
    public Image[] imgMenuIcons;
    public TextMeshProUGUI[] txtMenus;
    // public Button btnMenu0;
    // public Button btnMenu1;
    public Button btnLockRight;
    public Button btnLockLeft;

    public int currentIndex = 0;
    private float originalIconY;
    private float originalScale;

    private void Start()
    {
        // Store the original Y position at initialization
        if (imgMenuIcons.Length > 0)
        {
            originalIconY = imgMenuIcons[0].rectTransform.anchoredPosition.y;
            originalScale = imgMenuIcons[1].rectTransform.localScale.x;
        }


        btnMenus[1].gameObject.SetActive(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business != "AD");
        btnLockLeft.gameObject.SetActive(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business == "AD");

    }
    public void On_BtnMenu0_Click()
    {
        // 如果ShopPanel正在播放动画，忽略此次点击
        if (MatchUI_ShopPanel.IsAnimating()) return;

        if (currentIndex != 0)
        {
            HandleMenuButtonClick(0);
            // 只有在ShopPanel不是正在关闭时才调用ClosePanel
            if (!MatchUI_ShopPanel.IsClosing())
            {
                MatchUI_ShopPanel.ClosePanel();
            }
        }

        currentIndex = 0;
    }

    public void On_BtnMenu1_Click()
    {
        // 如果ShopPanel正在播放动画，忽略此次点击
        if (MatchUI_ShopPanel.IsAnimating()) return;

        if (currentIndex != 1)
        {
            HandleMenuButtonClick(1);
            // 只有在ShopPanel不是正在关闭时才调用ShowPanel
            if (!MatchUI_ShopPanel.IsClosing())
            {
                MatchUI_ShopPanel.ShowPanel();
            }
        }

        currentIndex = 1;
    }

    public void On_BtnLockRight_Click()
    {

    }

    public void On_BtnLockLeft_Click()
    {

    }

    // Generalized function to handle menu button clicks
    public void HandleMenuButtonClick(int index)
    {
        if (currentIndex != index)
        {

            // Loop through all menu items
            for (int i = 0; i < imgMenus.Length; i++)
            {
                if (i != index) // Not current index
                {
                    // Animate non-selected menu images scale X to 0
                    // StartCoroutine(AnimateScaleY(imgMenus[i].transform, 0, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.MenuBtnPanel_MenuToAnimSpeed));

                    imgMenus[i].gameObject.SetActive(false);
                    // Animate non-selected icons Y position to 0
                    StartCoroutine(AnimatePositionY(imgMenuIcons[i], -20, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.MenuBtnPanel_IconAnimSpeed));

                    // Animate non-selected text Y scale to 0
                    StartCoroutine(AnimateScaleY(txtMenus[i].transform, 0, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.MenuBtnPanel_TextAnimSpeed));

                    // Scale non-selected icons to 0.7
                    StartCoroutine(AnimateScale(imgMenuIcons[i].transform, 0.8f, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.MenuBtnPanel_IconScaleAnimSpeed));
                }
                else // Current index
                {
                    // Animate selected menu image scale X to 1
                    //StartCoroutine(AnimateScaleY(imgMenus[i].transform, 1, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.MenuBtnPanel_MenuBackAnimSpeed));

                    imgMenus[i].gameObject.SetActive(true);
                    
                    // Animate selected icon Y position to original Y
                    StartCoroutine(AnimatePositionY(imgMenuIcons[i], originalIconY, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.MenuBtnPanel_IconAnimSpeed));

                    // Animate selected text Y scale to 1
                    StartCoroutine(AnimateScaleY(txtMenus[i].transform, 1, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.MenuBtnPanel_TextAnimSpeed));

                    // Scale selected icon to 1
                    StartCoroutine(AnimateScale(imgMenuIcons[i].transform, 1, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.MenuBtnPanel_IconScaleAnimSpeed));
                }
            }
            currentIndex = index;
        }
    }

    // Animate scale X of a transform
    private IEnumerator AnimateScaleX(Image image, float targetX, float speed)
    {
        Transform transform = image.transform;
        Vector3 startScale = transform.localScale;
        Vector3 targetScale = new Vector3(targetX, startScale.y, startScale.z);
        float time = 0;

        while (time < 1)
        {
            time += Time.deltaTime * speed;
            transform.localScale = Vector3.Lerp(startScale, targetScale, time);
            yield return null;
        }

        transform.localScale = targetScale;
    }

    // Animate position Y of a transform
    private IEnumerator AnimatePositionY(Image image, float targetY, float speed)
    {
        RectTransform transform = image.rectTransform;
        Vector3 startPos = transform.anchoredPosition;
        // 使用编辑器坐标系的绝对值 0
        Vector3 targetPos = new Vector3(startPos.x, targetY, startPos.z);
        float time = 0;

        // 确保在编辑器中使用的是绝对坐标
        while (time < 1)
        {
            time += Time.deltaTime * speed;
            transform.anchoredPosition = Vector3.Lerp(startPos, targetPos, time);
            yield return null;
        }

        // 最终设置为精确的目标位置
        transform.anchoredPosition = targetPos;
    }

    // Animate scale Y of a transform
    private IEnumerator AnimateScaleY(Transform transform, float targetY, float speed)
    {
        Vector3 startScale = transform.localScale;
        Vector3 targetScale = new Vector3(startScale.x, targetY, startScale.z);
        float time = 0;

        while (time < 1)
        {
            time += Time.deltaTime * speed;
            transform.localScale = Vector3.Lerp(startScale, targetScale, time);
            yield return null;
        }

        transform.localScale = targetScale;
    }

    // Animate uniform scale of a transform
    private IEnumerator AnimateScale(Transform transform, float targetScale, float speed)
    {
        Vector3 startScale = transform.localScale;
        Vector3 targetScaleVec = new Vector3(targetScale, targetScale, targetScale);
        float time = 0;

        while (time < 1)
        {
            time += Time.deltaTime * speed;
            transform.localScale = Vector3.Lerp(startScale, targetScaleVec, time);
            yield return null;
        }

        transform.localScale = targetScaleVec;
    }


}
