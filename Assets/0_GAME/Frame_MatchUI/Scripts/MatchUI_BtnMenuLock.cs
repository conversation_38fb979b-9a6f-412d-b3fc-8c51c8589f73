using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_BtnMenuLock : MatchUI_Btn_Common
{

    public Image imgTips;
    public Vector3 sacaleTarget = new Vector3(1, 1, 1);
    
    protected override void OnButtonClick_Self()
    {
        StartCoroutine(AnimateTips());
    }
    
    private IEnumerator AnimateTips()
    {
    	// 缩放到目标大小
        StartCoroutine(AnimateScale(imgTips.transform, sacaleTarget, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.BtnMenuLock_TipsScaleAnimSpeed));
        
        // Wait for display duration
        yield return new WaitForSeconds(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.BtnMenuLock_TipsDisplayDuration);
        
        // Scale down to 0
        StartCoroutine(AnimateScale(imgTips.transform, new Vector3(0,0,0), MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.BtnMenuLock_TipsScaleAnimSpeed));
    }
    
    private IEnumerator AnimateScale(Transform transform, Vector3 targetScale, float speed)
    {
        Vector3 startScale = transform.localScale;
        float time = 0;
        
        while (time < 1)
        {
            time += Time.deltaTime * speed;
            transform.localScale = Vector3.Lerp(startScale, targetScale, time);
            yield return null;
        }
        
        transform.localScale = targetScale;
    }
}
