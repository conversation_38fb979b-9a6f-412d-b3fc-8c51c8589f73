using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_StartPanel : MatchUI_BasePanel<MatchUI_StartPanel>
{
    public Button btnNoADS;
    //public MatchUI_imgCoinBar imgCoinBar;
    public TextMeshProUGUI txtLevel;



    // 添加标志位，用于跟踪是否已经播放过音乐
    private static bool hasMusicPlayed = false;


    protected void Start()
    {
        //base.Awake();

        if (!hasMusicPlayed)
        {
            MatchUI_Controller.On_PlayMusic?.Invoke("MatchUI_BGM");
            hasMusicPlayed = true;
        }

        btnNoADS.gameObject.SetActive(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business == "Cash");
        //imgCoinBar.gameObject.SetActive(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business == "Cash");

        Update_BtnRemoveADS();

        txtLevel.text = Tool_LanguageManager.GetText("关卡") + " " + (MatchUI_UserData_Manager.Instance.Get.Level_Display + 1);

        MatchUI_Controller.On_RemoveADSSuccess += On_RemoveADSSuccess;

        MatchUI_Controller.On_MainEvent?.Invoke("3StartPanel", MatchUI_UserData_Manager.Instance.Get.Level_Current, MatchUI_UserData_Manager.Instance.Get.Level_Display);


        // Check if current level is less than or equal to Guide_DirectGame_Level
        if (MatchUI_UserData_Manager.Instance.Get.Level_Current <= MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Guide_DirectGame_Level)
        {
            MatchUI_LoadingPanel.StartLoading("2Game", null, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[0], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[1], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[2]);
        }
        else
        {
            MXR_BRIGE.Action_OnEnterStartScene();
        }
    }

    private void OnDestroy()
    {
        MatchUI_Controller.On_RemoveADSSuccess -= On_RemoveADSSuccess;
    }

    private void FixedUpdate()
    {

    }

    public void OnBtnPlayClick()
    {
        MXR_BRIGE.Action_OnStartGameButtonClick(() => {     MatchUI_LoadingPanel.StartLoading("2Game", null, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[0], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[1], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[2]);
        });
      }

    void Update_BtnRemoveADS()
    {
        if (MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business == "Cash")
        {
            if (MatchUI_UserData_Manager.Instance.Get.AD_Remove_FullAD)
                btnNoADS.gameObject.SetActive(false);
            else
                btnNoADS.gameObject.SetActive(true);
        }
    }


    void On_RemoveADSSuccess()
    {
        Update_BtnRemoveADS();
    }

}
