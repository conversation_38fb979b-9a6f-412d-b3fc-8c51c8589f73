using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_NoAdsPanel : MatchUI_BasePanel<MatchUI_NoAdsPanel>
{


    protected static string resourcesName = "7MatchUI_NoAdsPanel";

    public static void ShowPanel()
    {
        GameObject prefab = Resources.Load<GameObject>("MatchUI/Panel/" + resourcesName);
        if (prefab != null)
        {
            GameObject panelObj = GameObject.Instantiate(prefab);
            Canvas canvas = GameObject.Find("Canvas").GetComponent<Canvas>();
            if (canvas != null)
            {
                panelObj.transform.SetParent(canvas.transform, false);
            }
            else
            {
                Debug.LogError("No Canvas found in scene");
            }
        }
        else
        {
            Debug.LogError("Failed to load panel: " + resourcesName);
        }

        MatchUI_NoAdsPanel.Instance.UpdatePanel();

        // 播放显示动画
        MatchUI_NoAdsPanel.Instance.PlayShowAnimation();
    }

    public static void ClosePanel()
    {
        MatchUI_NoAdsPanel panel = FindObjectOfType<MatchUI_NoAdsPanel>();
        if (panel != null)
        {
            // 播放隐藏动画，完成后销毁
            panel.PlayHideAnimation(() =>
            {
                Destroy(panel.gameObject);
            });
        }
    }

    public void On_BtnClose_Click()
    {
        ClosePanel();
    }


    public Button btnBuy;
    public TextMeshProUGUI txtCash;

    void UpdatePanel()
    {
        txtCash.text = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Pack_NoADSs.NeedCash;
    }

    public void On_BtnBuy_Click()
    {
     }

    void On_PaySuccess()
    {
        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_获得奖励");

        if (!MatchUI_UserData_Manager.Instance.Get.AD_Remove_FullAD)
            MatchUI_UserData_Manager.Instance.Set.AD_Remove_FullAD = true;

        MXR_BRIGE.MessageTip_Show(Tool_LanguageManager.GetText("移除成功!"));

        MatchUI_Controller.On_RemoveADSSuccess?.Invoke();

        ClosePanel();
    }

}
