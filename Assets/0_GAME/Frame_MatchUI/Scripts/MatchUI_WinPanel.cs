using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class MatchUI_WinPanel : MatchUI_BasePanel<MatchUI_WinPanel>
{


    protected static string resourcesName = "6MatchUI_WinPanel";

    int ExAddCoin = 0;

    public static void ShowPanel(int exaddcoin = 0)
    {
        MatchUI_GuidePanel.ClosePanel();

        MatchUI_Controller.On_PlayVib?.Invoke(false);

        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_关卡成功");



        string levelKey = MatchUI_UserData_Manager.Instance.Get.Level_Current.ToString();
        if (!MatchUI_UserData_Manager.Instance.Set.Count_LevelWin.ContainsKey(levelKey))
            MatchUI_UserData_Manager.Instance.Set.Count_LevelWin[levelKey] = 0;
        MatchUI_UserData_Manager.Instance.Set.Count_LevelWin[levelKey]++;

        GameObject prefab = Resources.Load<GameObject>("MatchUI/Panel/" + resourcesName);
        if (prefab != null)
        {
            GameObject panelObj = GameObject.Instantiate(prefab);
            Canvas canvas = GameObject.Find("Canvas").GetComponent<Canvas>();
            if (canvas != null)
            {
                panelObj.transform.SetParent(canvas.transform, false);
            }
            else
            {
                Debug.LogError("No Canvas found in scene");
            }
        }
        else
        {
            Debug.LogError("Failed to load panel: " + resourcesName);
        }

        MatchUI_Controller.On_MainEvent?.Invoke("6GameWin", MatchUI_UserData_Manager.Instance.Get.Level_Current, MatchUI_UserData_Manager.Instance.Get.Level_Display);

        MatchUI_GamePanel.Instance.gameObject.SetActive(false);

        MatchUI_WinPanel.Instance.ExAddCoin = exaddcoin;

        MatchUI_WinPanel.Instance.UpdatePanel();
    }

    public static void ClosePanel()
    {
        MatchUI_WinPanel panel = FindObjectOfType<MatchUI_WinPanel>();
        if (panel != null)
        {
            Destroy(panel.gameObject);
        }
    }





    public Button btnNextLevel;
    public Button btnReturn;
    public Button btnRewardADDoubleCoin;

    public Image imgCoinWin;
    public TextMeshProUGUI txtCoinWin;

    public TextMeshProUGUI txtLevelTip;

    private int targetCoin = 0;
    private int currentDisplayCoin = 0;
    private float coinAnimationSpeed = 10f; // Default value
    private float coinAnimationDelay = 0.5f; // Default value



    void UpdatePanel()
    {
        int currentLevel = MatchUI_UserData_Manager.Instance.Get.Level_Current;
        int directGameLevel = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Guide_DirectGame_Level;

        txtLevelTip.text = Tool_LanguageManager.GetText("关卡") +
                           " " + (MatchUI_UserData_Manager.Instance.Get.Level_Display + 1);

        if (currentLevel < directGameLevel)
        {
            btnReturn.gameObject.SetActive(false);
            RectTransform rectTransform = btnNextLevel.GetComponent<RectTransform>();
            Vector2 anchoredPosition = rectTransform.anchoredPosition;
            anchoredPosition.x = 0;
            rectTransform.anchoredPosition = anchoredPosition;
        }

        if (MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business == "AD")
        {
            imgCoinWin.gameObject.SetActive(false);
            btnRewardADDoubleCoin.gameObject.SetActive(false);
        }
        else
        {
            imgCoinWin.gameObject.SetActive(true);
            btnRewardADDoubleCoin.gameObject.SetActive(true);

            // Get coin animation parameters from Const_Data
            coinAnimationSpeed = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Win_CoinAnimSpeed;
            coinAnimationDelay = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Win_CoinAnimDelay;

            // Calculate target coin based on level
            targetCoin = CalculateLevelCoin(currentLevel);

            targetCoin += ExAddCoin;

            // Initialize coin display
            currentDisplayCoin = 0;
            txtCoinWin.text = "0";

            // Start coin animation
            StartCoroutine(AnimateCoinIncrease());
        }


        MatchUI_UserData_Manager.Instance.Set.Level_Current++;

    }

    public void On_BtnNextLevel_Click()
    {
        // Add coins to player's account
        if (MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business != "AD")
        {
            // Add the earned coins to player's account
            MatchUI_UserData_Manager.Instance.Set.Coin_Current += targetCoin;
        }

        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_胜利获得金币");

        MatchUI_LoadingPanel.StartLoading("2Game", null, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[0], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[1], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[2]);
    }

    public void On_BtnReturn_Click()
    {
        // Add coins to player's account
        if (MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business != "AD")
        {
            // Add the earned coins to player's account
            MatchUI_UserData_Manager.Instance.Set.Coin_Current += targetCoin;
        }
        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_胜利获得金币");

        MatchUI_LoadingPanel.StartLoading("1Start", null, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[0], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[1], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[2]);
    }

    public void On_BtnRewardADDoubleCoin_Click()
    {
        MXR_BRIGE.AD_ShowReward(On_RewardADDoubleCoin_Success);

    }

    void On_RewardADDoubleCoin_Success()
    {
        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_获得奖励");


        btnRewardADDoubleCoin.gameObject.SetActive(false);

        // Add double coins to player's account
        targetCoin = targetCoin * 2;

        // Show animation for double coins
        StartCoroutine(ShowDoubleCoinsAnimation());


    }

    private int CalculateLevelCoin(int level)
    {
        Dictionary<string, int> levelCoinMap = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Win_LevelCoin;

        // Convert string keys to integers and sort them
        Dictionary<int, int> numericKeyMap = new Dictionary<int, int>();
        foreach (var pair in levelCoinMap)
        {
            if (int.TryParse(pair.Key, out int keyLevel))
            {
                numericKeyMap[keyLevel] = pair.Value;
            }
        }

        // Sort keys to ensure we process them in ascending order
        List<int> sortedKeys = new List<int>(numericKeyMap.Keys);
        sortedKeys.Sort();

        // Default to the first value if available
        int coinAmount = sortedKeys.Count > 0 ? numericKeyMap[sortedKeys[0]] : 0;

        // Find the appropriate coin amount based on level
        foreach (int key in sortedKeys)
        {
            if (level <= key)
            {
                coinAmount = numericKeyMap[key];
                break;
            }
            // Update coin amount as we go through the keys
            coinAmount = numericKeyMap[key];
        }

        return coinAmount;
    }

    private IEnumerator AnimateCoinIncrease()
    {
        // Wait for the specified delay before starting animation
        yield return new WaitForSeconds(coinAnimationDelay);

        while (currentDisplayCoin < targetCoin)
        {
            // Increment by 1 or jump to target if we're close
            currentDisplayCoin = Mathf.Min(currentDisplayCoin + 1, targetCoin);

            // Update UI
            txtCoinWin.text = "+" + currentDisplayCoin.ToString();

            // Wait for next frame based on animation speed
            yield return new WaitForSeconds(1f / coinAnimationSpeed);
        }
    }

    private IEnumerator ShowDoubleCoinsAnimation()
    {
        // Reset current display to original target (before doubling)
        currentDisplayCoin = targetCoin / 2;
        txtCoinWin.text = "+" + currentDisplayCoin.ToString();

        // Use the already doubled targetCoin value
        int newTarget = targetCoin;

        // Wait a short moment before starting the animation
        yield return new WaitForSeconds(0.5f);

        // Animate to the new target
        while (currentDisplayCoin < newTarget)
        {
            // Increment by 1 or jump to target if we're close
            currentDisplayCoin = Mathf.Min(currentDisplayCoin + 1, newTarget);

            // Update UI
            txtCoinWin.text = "+" + currentDisplayCoin.ToString();

            // Wait for next frame based on animation speed
            yield return new WaitForSeconds(1f / coinAnimationSpeed);
        }
    }


}
