using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MatchUI_Controller : MonoBehaviour
{

    // private static MatchUI_Controller _instance;
    // public static MatchUI_Controller Instance
    // {
    //     get
    //     {
    //         if (_instance == null)
    //         {
    //             _instance = FindObjectOfType<MatchUI_Controller>();
    //             if (_instance == null)
    //             {
    //                 GameObject go = new GameObject(typeof(MatchUI_Controller).Name);
    //                 _instance = go.AddComponent<MatchUI_Controller>();
    //                 DontDestroyOnLoad(go); // 如需持久化
    //             }
    //         }
    //         return _instance;
    //     }
    // }



    public static Action<MatchUI_BtnClick_Event_Data> On_BtnClick;


    public static Action<MatchUI_ShopPack> On_BuyPackSuccess;
    public static Action On_RemoveADSSuccess;

    public static Action<GameObject> On_PanelShow;
    public static Action<GameObject> On_PanelHide;

    public static Action On_ReGame;
    public static Action On_ReGameSuccess;

    //level
    public static Action<string, int, int> On_MainEvent;

    //index,success call back
    public static Action<int, Action> On_Ask_UseProp;

    public static Action<string> On_PlaySound;
    public static Action<bool> On_PlayVib;
    public static Action<string> On_PlayMusic;
    public static Action On_StopMusic;

    public static Action<string> On_BtnZoomClick;


    public static void GameLose(bool needregame = true)
    {
        //MatchUI_GamePanel.Instance.gameObject.SetActive(false);
        MatchUI_GetPropPanel.ClosePanel();
        MatchUI_UnlockVacantPanel.ClosePanel();
        MatchUI_SettingPanel.ClosePanel();
        MatchUI_ShopPanel.ClosePanel();
        if (needregame)
            MatchUI_RegamePanel.ShowPanel();
        else
            MatchUI_LosePanel.ShowPanel();

    }

    public static void GameWin(int exaddcoin = 0)
    {
        //MatchUI_GamePanel.Instance.gameObject.SetActive(false);
        MatchUI_GetPropPanel.ClosePanel();
        MatchUI_UnlockVacantPanel.ClosePanel();
        MatchUI_SettingPanel.ClosePanel();
        MatchUI_ShopPanel.ClosePanel();
        MatchUI_WinPanel.ShowPanel(exaddcoin);
    }



}
