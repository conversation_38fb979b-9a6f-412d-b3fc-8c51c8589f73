using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_BtnCoinAdd : MatchUI_Btn_Common
{

    protected override void OnButtonClick_Self()
    {
        if (MatchUI_GamePanel.Instance != null)
            if (!Tool_InputManager.CanProcessInput())
                return;

        if (MatchUI_MenuBtnPanel.Instance != null)
        {
            MatchUI_MenuBtnPanel.Instance.On_BtnMenu1_Click();
        }
        else
        {
            
            MatchUI_ShopPanel.ShowPanel();

        }
    }
}
