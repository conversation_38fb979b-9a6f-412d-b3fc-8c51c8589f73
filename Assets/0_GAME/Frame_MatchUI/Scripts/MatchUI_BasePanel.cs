using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MatchUI_BasePanel<T> : MonoBehaviour where T : MatchUI_BasePanel<T>
{
    public static T _instance;

    public static T Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<T>();
            }
            return _instance;
        }
    }

    protected virtual void Awake()
    {
        LocLanguage();
    }

    protected virtual void OnEnable()
    {
        MatchUI_Controller.On_PanelShow?.Invoke(gameObject);
    }

    protected virtual void OnDisable()
    {
        MatchUI_Controller.On_PanelHide?.Invoke(gameObject);
    }

    void LocLanguage()
    {
        // Find all TextMeshProUGUI components in this panel
        TMPro.TextMeshProUGUI[] texts = GetComponentsInChildren<TMPro.TextMeshProUGUI>(true);

        // Replace each text with localized version
        foreach (TMPro.TextMeshProUGUI text in texts)
        {
            if (!string.IsNullOrEmpty(text.text))
            {
                text.text = Tool_LanguageManager.GetText(text.text);
            }
        }
    }

    /// <summary>
    /// 播放显示动画
    /// </summary>
    protected virtual void PlayShowAnimation(System.Action onComplete = null)
    {
        var animator = GetComponent<MatchUI_PanelAnimator>();
        if (animator != null)
        {
            animator.PlayShowAnimation(onComplete);
        }
        else
        {
            onComplete?.Invoke();
        }
    }

    /// <summary>
    /// 播放隐藏动画
    /// </summary>
    protected virtual void PlayHideAnimation(System.Action onComplete = null)
    {
        var animator = GetComponent<MatchUI_PanelAnimator>();
        if (animator != null)
        {
            animator.PlayHideAnimation(onComplete);
        }
        else
        {
            onComplete?.Invoke();
        }
    }
}