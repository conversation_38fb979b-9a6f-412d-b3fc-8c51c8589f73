using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_Btn_Common : MonoBehaviour
{
    private Button button;
    private string panelName = "No Panel Found";
    private bool panelSearched = false;

    protected virtual void Awake()
    {
        // Get Button component
        button = GetComponent<Button>();
        if (button != null)
        {
            // Add click event listener
            button.onClick.AddListener(OnButtonClick);
            button.onClick.AddListener(OnButtonClick_Self);


        }
        else
        {
            Debug.LogError("No Button component found on " + gameObject.name);
        }


        // Find panel once during initialization
        FindParentPanel();
    }

    private void FindParentPanel()
    {
        if (panelSearched) return;

        Transform current = transform;

        while (current != null)
        {
            MonoBehaviour[] components = current.GetComponents<MonoBehaviour>();
            foreach (MonoBehaviour component in components)
            {
                if (component != null && component.GetType().BaseType != null &&
                    component.GetType().BaseType.IsGenericType &&
                    component.GetType().BaseType.GetGenericTypeDefinition() == typeof(MatchUI_BasePanel<>))
                {
                    // 获取Panel名称
                    string panelComponentName = component.GetType().Name;

                    // 获取从Panel到按钮的父级路径（不包含按钮本身）
                    string pathFromPanel = GetPathFromPanelToButtonParent(current, transform);

                    // 组合Panel名称和路径
                    panelName = panelComponentName + "/" + pathFromPanel;
                    panelSearched = true;
                    return;
                }
            }

            current = current.parent;
        }

        panelSearched = true;
    }

    // 获取从Panel到按钮父级的路径（不包含按钮本身）
    private string GetPathFromPanelToButtonParent(Transform panelTransform, Transform buttonTransform)
    {
        if (buttonTransform == panelTransform || buttonTransform.parent == panelTransform)
            return "";

        Transform buttonParent = buttonTransform.parent;
        string path = buttonParent.name;
        Transform current = buttonParent.parent;

        while (current != null && current != panelTransform)
        {
            path = current.name + "/" + path;
            current = current.parent;
        }

        return path;
    }

    void OnButtonClick()
    {

        // Get self name
        string selfName = gameObject.name;

        MatchUI_BtnClick_Event_Data data = new MatchUI_BtnClick_Event_Data()
        {
            PreName = panelName,
            ButtonName = selfName
        };

        MatchUI_Controller.On_BtnClick?.Invoke(data);

        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");

        //  Debug.LogError(panelName + " " + selfName );

    }

    protected virtual void OnButtonClick_Self()
    {

    }

    void OnDestroy()
    {
        // Remove event listener to prevent memory leaks
        if (button != null)
        {
            button.onClick.RemoveListener(OnButtonClick);
            button.onClick.RemoveListener(OnButtonClick_Self);
        }
    }
}

public class MatchUI_BtnClick_Event_Data
{
    public string PreName;
    public string ButtonName;
}