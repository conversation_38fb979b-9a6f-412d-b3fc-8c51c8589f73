using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class MatchUI_UnlockVacantPanel : MatchUI_BasePanel<MatchUI_UnlockVacantPanel>
{
    protected static string resourcesName = "4MatchUI_UnlockVacantPanel";

    private Action Act_Success;

    public static void ShowPanel(int index, Action act_Success)
    {
        GameObject prefab = Resources.Load<GameObject>("MatchUI/Panel/" + resourcesName);
        if (prefab != null)
        {
            GameObject panelObj = GameObject.Instantiate(prefab);
            Canvas canvas = GameObject.Find("Canvas").GetComponent<Canvas>();
            if (canvas != null)
            {
                panelObj.transform.SetParent(canvas.transform, false);
            }
            else
            {
                Debug.LogError("No Canvas found in scene");
            }
        }
        else
        {
            Debug.LogError("Failed to load panel: " + resourcesName);
        }

        MatchUI_UnlockVacantPanel.Instance.UpdatePanel(index, act_Success);

        // 播放显示动画
        MatchUI_UnlockVacantPanel.Instance.PlayShowAnimation();
    }

    public static void ClosePanel()
    {
        MatchUI_UnlockVacantPanel panel = FindObjectOfType<MatchUI_UnlockVacantPanel>();
        if (panel != null)
        {
            // 播放隐藏动画，完成后销毁
            panel.PlayHideAnimation(() =>
            {
                Destroy(panel.gameObject);
            });
        }
    }

    public void On_BtnClose_Click()
    {
        Act_Success = null;
        ClosePanel();

    }


    public Button btnUnlcok;

    public TextMeshProUGUI txtGuide;

    public Image imgCoin;
    public Image imgRewardAD;

    public TextMeshProUGUI txtUnlockCoin;

    public Image[] imgVacantTips;

    private int currentVacantIndex;

    void UpdatePanel(int index, Action act_Success)
    {
        currentVacantIndex = index;
        Act_Success = act_Success;

        // Hide all prop tips first
        for (int i = 0; i < imgVacantTips.Length; i++)
        {
            if (imgVacantTips[i] != null)
            {
                imgVacantTips[i].gameObject.SetActive(false);
            }
        }

        for (int i = 0; i < imgVacantTips.Length; i++)
        {
            imgVacantTips[i].sprite =
                MatchUI_PropIconRes.LoadPropIcon(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.VacanIcon_Paths[i]);
        }

        // Show only the current prop tip
        if (currentVacantIndex >= 0 && currentVacantIndex < imgVacantTips.Length && imgVacantTips[currentVacantIndex] != null)
        {
            imgVacantTips[currentVacantIndex].gameObject.SetActive(true);
        }

        // Check business mode and show/hide UI elements accordingly
        string businessMode = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business;
        if (businessMode == "Cash")
        {
            // Show coin purchase option, hide AD reward option
            if (imgCoin != null) imgCoin.gameObject.SetActive(true);
            if (imgRewardAD != null) imgRewardAD.gameObject.SetActive(false);
        }
        else if (businessMode == "AD")
        {
            // Show AD reward option, hide coin purchase option
            if (imgCoin != null) imgCoin.gameObject.SetActive(false);
            if (imgRewardAD != null) imgRewardAD.gameObject.SetActive(true);
        }

        txtGuide.text = Tool_LanguageManager.GetText(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Vacant_Guide[index]);

        txtUnlockCoin.text = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Vacant_Unlock_Coin[index].ToString();
    }


    public void On_BtnUnlock_Click()
    {

        if (MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business == "AD")
        {
            MXR_BRIGE.AD_ShowReward(On_UnlcokSuccess);
        }
        else
        {
            int coinCost = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Vacant_Unlock_Coin[currentVacantIndex];
            if (MatchUI_UserData_Manager.Instance.Get.Coin_Current >= coinCost)
            {
                // 金币足够，直接购买
                MatchUI_UserData_Manager.Instance.Set.Coin_Current -= coinCost;
                On_UnlcokSuccess();
            }
            else
            {
                // 金币不足，打开商店
                MatchUI_ShopPanel.ShowPanel();
            }
        }


    }



    void On_UnlcokSuccess()
    {
        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_获得奖励");



        Act_Success?.Invoke();
        Act_Success = null;

        ClosePanel();
        MXR_BRIGE.MessageTip_Show(Tool_LanguageManager.GetText("解锁成功!"));



    }

}