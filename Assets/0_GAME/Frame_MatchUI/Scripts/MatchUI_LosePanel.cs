using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_LosePanel : MatchUI_BasePanel<MatchUI_LosePanel>
{


    protected static string resourcesName = "6MatchUI_LosePanel";

    public static void ShowPanel()
    {
        MatchUI_GuidePanel.ClosePanel();

        MatchUI_Controller.On_PlayVib?.Invoke(false);
        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_关卡失败");
        string levelKey = MatchUI_UserData_Manager.Instance.Get.Level_Current.ToString();
        if (!MatchUI_UserData_Manager.Instance.Set.Count_LevelLost.ContainsKey(levelKey))
            MatchUI_UserData_Manager.Instance.Set.Count_LevelLost[levelKey] = 0;
        MatchUI_UserData_Manager.Instance.Set.Count_LevelLost[levelKey]++;

        GameObject prefab = Resources.Load<GameObject>("MatchUI/Panel/" + resourcesName);
        if (prefab != null)
        {
            GameObject panelObj = GameObject.Instantiate(prefab);
            Canvas canvas = GameObject.Find("Canvas").GetComponent<Canvas>();
            if (canvas != null)
            {
                panelObj.transform.SetParent(canvas.transform, false);
            }
            else
            {
                Debug.LogError("No Canvas found in scene");
            }
        }
        else
        {
            Debug.LogError("Failed to load panel: " + resourcesName);
        }

        MatchUI_Controller.On_MainEvent?.Invoke("6GameLose", MatchUI_UserData_Manager.Instance.Get.Level_Current, MatchUI_UserData_Manager.Instance.Get.Level_Display);

        MatchUI_GamePanel.Instance.gameObject.SetActive(false);
        MatchUI_LosePanel.Instance.UpdatePanel();

        // 播放显示动画
        MatchUI_LosePanel.Instance.PlayShowAnimation();
    }

    public static void ClosePanel()
    {
        MatchUI_LosePanel panel = FindObjectOfType<MatchUI_LosePanel>();
        if (panel != null)
        {
            // 播放隐藏动画，完成后销毁
            panel.PlayHideAnimation(() =>
            {
                Destroy(panel.gameObject);
            });
        }
    }





    public Button btnTryAgain;
    public Button btnReturn;

    public TextMeshProUGUI txtLevelTip;


    void UpdatePanel()
    {
        int currentLevel = MatchUI_UserData_Manager.Instance.Get.Level_Current;
        int directGameLevel = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Guide_DirectGame_Level;

        txtLevelTip.text = Tool_LanguageManager.GetText("关卡") +
                    " " + (MatchUI_UserData_Manager.Instance.Get.Level_Display + 1);

        if (currentLevel < directGameLevel)
        {
            btnReturn.gameObject.SetActive(false);
            RectTransform rectTransform = btnTryAgain.GetComponent<RectTransform>();
            Vector2 anchoredPosition = rectTransform.anchoredPosition;
            anchoredPosition.x = 0;
            rectTransform.anchoredPosition = anchoredPosition;
        }
    }

    public void On_BtnTryAgain_Click()
    {
        MatchUI_LoadingPanel.StartLoading("2Game", null, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[0], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[1], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[2]);
    }

    public void On_BtnReturn_Click()
    {
        MatchUI_LoadingPanel.StartLoading("1Start", null, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[0], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[1], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[2]);
    }

}
