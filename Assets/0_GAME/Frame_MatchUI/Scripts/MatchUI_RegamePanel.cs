using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_RegamePanel : MatchUI_BasePanel<MatchUI_RegamePanel>
{
    protected static string resourcesName = "5MatchUI_RegamePanel";

    public static void ShowPanel()
    {
        MatchUI_Controller.On_PlayVib?.Invoke(false);

        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_是否复活");
        GameObject prefab = Resources.Load<GameObject>("MatchUI/Panel/" + resourcesName);
        if (prefab != null)
        {
            GameObject panelObj = GameObject.Instantiate(prefab);
            Canvas canvas = GameObject.Find("Canvas").GetComponent<Canvas>();
            if (canvas != null)
            {
                panelObj.transform.SetParent(canvas.transform, false);
            }
            else
            {
                Debug.LogError("No Canvas found in scene");
            }
        }
        else
        {
            Debug.LogError("Failed to load panel: " + resourcesName);
        }

        MatchUI_Controller.On_MainEvent?.Invoke("5ReGameShow", MatchUI_UserData_Manager.Instance.Get.Level_Current, MatchUI_UserData_Manager.Instance.Get.Level_Display);

        MatchUI_RegamePanel.Instance.UpdatePanel();

        // 播放显示动画
        MatchUI_RegamePanel.Instance.PlayShowAnimation();
    }

    public static void ClosePanel()
    {
        MatchUI_RegamePanel panel = FindObjectOfType<MatchUI_RegamePanel>();
        if (panel != null)
        {
            // 播放隐藏动画，完成后销毁
            panel.PlayHideAnimation(() =>
            {
                Destroy(panel.gameObject);
            });
        }
    }

    public void On_BtnClose_Click()
    {
        ClosePanel();

        MatchUI_LosePanel.ShowPanel();
    }


    public Button btnRegame;

    public TextMeshProUGUI txtGuide;
    public TextMeshProUGUI txtTitle;

    public Image imgCoin;
    public Image imgRewardAD;

    public TextMeshProUGUI txtRegameCoin;

    public Image imgRegameIcon;

    public TextMeshProUGUI txtLevel;


    void UpdatePanel()
    {

        imgRegameIcon.sprite =
            MatchUI_PropIconRes.LoadPropIcon(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.ReGame_IconPath);

        // Check business mode and show/hide UI elements accordingly
        string businessMode = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business;
        if (businessMode == "Cash")
        {
            // Show coin purchase option, hide AD reward option
            if (imgCoin != null) imgCoin.gameObject.SetActive(true);
            if (imgRewardAD != null) imgRewardAD.gameObject.SetActive(false);
        }
        else if (businessMode == "AD")
        {
            // Show AD reward option, hide coin purchase option
            if (imgCoin != null) imgCoin.gameObject.SetActive(false);
            if (imgRewardAD != null) imgRewardAD.gameObject.SetActive(true);
        }

        txtTitle.text = Tool_LanguageManager.GetText(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.ReGame_Title);

        txtGuide.text = Tool_LanguageManager.GetText(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.ReGame_Guide);

        txtLevel.text = Tool_LanguageManager.GetText("关卡") + " " + (MatchUI_UserData_Manager.Instance.Get.Level_Display + 1);

        txtRegameCoin.text = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.ReGame_Coin.ToString();
    }


    public void On_BtnReGame_Click()
    {

        if (MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business == "AD")
        {
            MXR_BRIGE.AD_ShowReward(On_RegameSuccess);
        }
        else
        {
            int coinCost = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.ReGame_Coin;
            if (MatchUI_UserData_Manager.Instance.Get.Coin_Current >= coinCost)
            {
                // 金币足够，直接购买
                MatchUI_UserData_Manager.Instance.Set.Coin_Current -= coinCost;
                On_RegameSuccess();

            }
            else
            {
                // 金币不足，打开商店
                MatchUI_ShopPanel.ShowPanel();
            }
        }

        MatchUI_Controller.On_ReGame?.Invoke();

    }



    void On_RegameSuccess()
    {
        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_获得奖励");

        MatchUI_Controller.On_ReGameSuccess?.Invoke();

        ClosePanel();

        //MXR_BRIGE.MessageTip_Show(MatchUI_LanguageManager.GetText("清理成功!"));

    }

}