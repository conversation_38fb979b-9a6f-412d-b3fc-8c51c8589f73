using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_GamePanel : MatchUI_BasePanel<MatchUI_GamePanel>
{

    protected void Start()
    {

MXR_BRIGE.Action_OnEnterGameScene();

        MatchUI_UserData_Manager.Instance.Set.Count_Round++;

        CheckAndShowGuide();

        txtLevelnormal.text = Tool_LanguageManager.GetText("第") + " " + (MatchUI_UserData_Manager.Instance.Get.Level_Display + 1) + " " + Tool_LanguageManager.GetText("关");
        txtLevelhard.text = Tool_LanguageManager.GetText("第") + " " + (MatchUI_UserData_Manager.Instance.Get.Level_Display + 1) + " " + Tool_LanguageManager.GetText("关");



        CheckHardLevel();

        // 居中显示active的btnProps
        CenterActiveBtnProps();

        // 初始化进度文本显示状态
        if (imgProgressText != null)
        {
            imgProgressText.gameObject.SetActive(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Progress_Text_Open);
        }

        // 初始化进度条状态
        InitializeProgressBar();

        MXR_BRIGE.Action_OnGamingStartGame(() => { });

        MatchUI_Controller.On_MainEvent?.Invoke("4GamePlay", MatchUI_UserData_Manager.Instance.Get.Level_Current, MatchUI_UserData_Manager.Instance.Get.Level_Display);
    }

    // 居中显示active的btnProps
    private void CenterActiveBtnProps()
    {
        // 获取所有active的btnProps
        List<MatchUI_BtnProp> activeBtnProps = new List<MatchUI_BtnProp>();
        foreach (var btnProp in btnProps)
        {
            if (btnProp.gameObject.activeSelf)
            {
                activeBtnProps.Add(btnProp);
            }
        }

        // 如果没有active的btnProps，直接返回
        if (activeBtnProps.Count == 0) return;

        // 计算原始间距（假设btnProps是水平排列的）
        float originalSpacing = 0;
        if (btnProps.Length >= 2)
        {
            originalSpacing = Mathf.Abs(btnProps[1].transform.localPosition.x - btnProps[0].transform.localPosition.x);
        }

        // 计算居中后的起始位置
        float totalWidth = originalSpacing * (activeBtnProps.Count - 1);
        float startX = -totalWidth / 2f;

        // 重新排列active的btnProps
        for (int i = 0; i < activeBtnProps.Count; i++)
        {
            Vector3 newPos = activeBtnProps[i].transform.localPosition;
            newPos.x = startX + (i * originalSpacing);
            activeBtnProps[i].transform.localPosition = newPos;
        }
    }

    public Button btnSetting;

    public MatchUI_imgCoinBar imgCoinBar;
    public MatchUI_BtnProp[] btnProps;

    // 进度文本相关
    public Image imgProgressText;
    public TextMeshProUGUI txtProgress;
    private int currentProgressCount = 0;
    private Coroutine progressUpdateCoroutine;

    // 进度条平滑过渡相关
    private float targetProgressValue = 0f; // 目标进度值
    private float currentProgressValue = 0f; // 当前进度值
    private float progressTransitionSpeed = 8f; // 过渡速度，可在Inspector中调整

    public Button btnZoom_Reset;
    public Button btnZoom_Add;
    public Button btnZoom_Minus;

    public Image imgPrnormal;
    public Image imgPrhard;

    public TextMeshProUGUI txtLevelnormal;
    public TextMeshProUGUI txtLevelhard;

    public Image imgLevelProgressBar;


    private void CheckAndShowGuide()
    {
        int currentLevel = MatchUI_UserData_Manager.Instance.Get.Level_Current;

        // Check if current level has guide data
        bool hasGuideForLevel = false;
        foreach (var guide in MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Guide_Levels)
        {
            if (guide.Level == currentLevel)
            {
                hasGuideForLevel = true;
                break;
            }
        }

        // If guide exists for current level, check if UI elements should be hidden
        if (hasGuideForLevel)
        {
            // Find the guide data for current level
            var guideData = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Guide_Levels.Find(g => g.Level == currentLevel);

            // Hide/show coin bar based on guide settings
            if (imgCoinBar != null)
                imgCoinBar.gameObject.SetActive(guideData.ShowCoinBar);

            // Hide/show settings button based on guide settings
            if (btnSetting != null)
                btnSetting.gameObject.SetActive(guideData.ShowSettingButton);
        }

        // Show guide panel if guide exists for current level
        if (hasGuideForLevel)
        {
            MatchUI_GuidePanel.ShowPanel();
        }

        btnZoom_Reset.gameObject.SetActive(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Btn_Zoom_Open[0]);
        btnZoom_Add.gameObject.SetActive(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Btn_Zoom_Open[1]);
        btnZoom_Minus.gameObject.SetActive(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Btn_Zoom_Open[2]);

    }

    public void UpdateBtnPropState()
    {
        foreach (var btnProp in btnProps)
        {
            btnProp.UpdateState();
        }
    }

    // public void UpdateBtnPropGreyShow(bool isgrey)
    // {
    //     foreach (var btnProp in btnProps)
    //     {
    //         btnProp.UpdateGrey(isgrey);
    //     }
    // }

    void CheckHardLevel()
    {
        int currentLevel = MatchUI_UserData_Manager.Instance.Get.Level_Current;

        // Check if current level is in the hard level list
        if (MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.HardLevel_TargetLevel != null)
        {
            foreach (var hardLevel in MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.HardLevel_TargetLevel)
            {
                if (hardLevel == currentLevel + 1)
                {
                    // Show hard level panel if current level is a hard level
                    MatchUI_HardLevelPanel.ShowPanel();

                    imgPrhard.gameObject.SetActive(true);
                    imgPrnormal.gameObject.SetActive(false);
                    return;
                }
            }
        }

        imgPrhard.gameObject.SetActive(false);
        imgPrnormal.gameObject.SetActive(true);
    }

    /// <summary>
    /// 更新进度文本
    /// </summary>
    /// <param name="count">剩余数量</param>
    public void UpdateProgressText(int count)
    {
        // 如果进度文本未启用，直接返回
        if (!MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Progress_Text_Open || imgProgressText == null || txtProgress == null)
            return;

        // 确保imgProgressText显示
        imgProgressText.gameObject.SetActive(true);

        txtProgress.text = Tool_LanguageManager.GetText("剩余") + "：" + count;

    }

    public void UpdateLevelProgressBar(float per)
    {
        targetProgressValue = per; // 设置目标进度值
    }

    public void OnBtn_ZoomClick(string type)
    {
        MatchUI_Controller.On_BtnZoomClick?.Invoke(type);
    }

    private void Update()
    {
        // 进度条平滑过渡效果
        UpdateProgressBarTransition();
    }

    /// <summary>
    /// 初始化进度条状态
    /// </summary>
    private void InitializeProgressBar()
    {
        if (imgLevelProgressBar != null)
        {
            // 初始化进度条为0，设置当前值和目标值都为0
            targetProgressValue = 0f;
            currentProgressValue = 0f;
            imgLevelProgressBar.fillAmount = 0f;
        }
    }

    /// <summary>
    /// 更新进度条的平滑过渡效果
    /// </summary>
    private void UpdateProgressBarTransition()
    {
        if (imgLevelProgressBar == null) return;

        // 如果当前值与目标值不一致，进行平滑过渡
        if (Mathf.Abs(currentProgressValue - targetProgressValue) > 0.001f)
        {
            // 使用Lerp实现平滑过渡
            currentProgressValue = Mathf.Lerp(currentProgressValue, targetProgressValue, progressTransitionSpeed * Time.deltaTime);

            // 更新进度条显示
            imgLevelProgressBar.fillAmount = currentProgressValue;
        }
        else
        {
            // 当非常接近目标值时，直接设置为目标值
            currentProgressValue = targetProgressValue;
            imgLevelProgressBar.fillAmount = currentProgressValue;
        }
    }


}
