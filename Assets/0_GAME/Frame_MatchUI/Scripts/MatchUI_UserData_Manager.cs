using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MatchUI_UserData_Manager : Tool_SaveData_Base<MatchUI_User_Data>
{
    // 单例实现
    private static MatchUI_UserData_Manager _instance;
    public static MatchUI_UserData_Manager Instance
    {
        get
        {
            if (_instance == null)
                _instance = new MatchUI_UserData_Manager();
            return _instance;
        }
    }

    protected override MatchUI_User_Data CreateData()
    {
        MatchUI_User_Data userData = new MatchUI_User_Data();

        // 初始化默认值
        userData.AD_Remove_FullAD = false;
        userData.Pack_Buyed = new Dictionary<string, List<int>>
        {
            { "Special", new List<int> { 0 } },
            { "Gift", new List<int> { 0, 0, 0 } },
            { "Coin", new List<int> { 0, 0, 0, 0, 0,0 } },
            { "NoADS", new List<int> { 0 } },
        };
        userData.Prop_Count = new List<int>() { 0, 0, 0, 0, 0, 0 };
        userData.Count_Round = 0;
        userData.Count_LevelLost = new Dictionary<string, int>
        {
            { "1", 0 }
        };
        userData.Count_LevelWin = new Dictionary<string, int>
        {
            { "1", 0 }
        };
        userData.Coin_Current = 0;
        userData.Level_Current = 0;
        userData.Loop_Count = 0;
        userData.Setting_Music = true;
        userData.Setting_Sound = true;
        userData.Setting_Vib = true;

        return userData;
    }
}
