using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.PlayerLoop;

public class MatchUI_imgCoinBar : MonoBehaviour
{


    
     TextMeshProUGUI txtCoin;
    // Start is called before the first frame update
    void Awake()
    {
        txtCoin = transform.Find("txtCoin").gameObject.GetComponent<TextMeshProUGUI>();
        
        gameObject.SetActive(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business == "Cash");

    }

    void FixedUpdate()
    {
        txtCoin.text = MatchUI_UserData_Manager.Instance.Get.Coin_Current.ToString();
    }

   
}
