using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_HardLevelPanel : MatchUI_BasePanel<MatchUI_HardLevelPanel>
{
    
    
    protected static string resourcesName = "3MatchUI_HardLevelPanel";
    
    public static void ShowPanel()
    {
        
        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_困难关卡");
        
        GameObject prefab = Resources.Load<GameObject>("MatchUI/Panel/" + resourcesName);
        if (prefab != null)
        {
            GameObject panelObj = GameObject.Instantiate(prefab);
            Canvas canvas =  GameObject.Find("Canvas").GetComponent<Canvas>();
            if (canvas != null)
            {
                panelObj.transform.SetParent(canvas.transform, false);
            }
            else
            {
                Debug.LogError("No Canvas found in scene");
            }
        }
        else
        {
            Debug.LogError("Failed to load panel: " + resourcesName);
        }
        
        MatchUI_HardLevelPanel.Instance.StartHeartbeatAnimation();
        
        // Start auto-close timer if enabled
        if (MatchUI_HardLevelPanel.Instance.enableAutoClose)
        {
            MatchUI_HardLevelPanel.Instance.StartAutoCloseTimer();
        }
    }

    public static void ClosePanel()
    {
        MatchUI_HardLevelPanel panel = FindObjectOfType<MatchUI_HardLevelPanel>();
        if (panel != null)
        {
            Destroy(panel.gameObject);
        }
    }

    public Image imgHardLevel;
    public Image imgScreen;
    public TextMeshProUGUI txtHardLevel;
    
    // Animation parameters
    public float maxAlpha;
    public float minAlpha;
    public float animationDuration;
    public float delayBetweenPulses;
    
    // Auto-close parameter
    public float autoCloseDelay;
    public bool enableAutoClose;
    
    // Fade-out parameters
    public float fadeOutDuration;
    
    private CanvasRenderer imgScreenRenderer;
    private CanvasRenderer imgCanvasRenderer;
    private CanvasRenderer txtCanvasRenderer;
    private Coroutine heartbeatCoroutine;
    private Coroutine autoCloseCoroutine;
    
    protected override void Awake()
    {
        base.Awake();
        imgCanvasRenderer = imgHardLevel.GetComponent<CanvasRenderer>();
        txtCanvasRenderer = txtHardLevel.GetComponent<CanvasRenderer>();
        imgScreenRenderer = imgScreen.GetComponent<CanvasRenderer>();
        
        // Initialize parameters from settings
        maxAlpha = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.HardLevelPanel_MaxAlpha;
        minAlpha = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.HardLevelPanel_MinAlpha;
        animationDuration = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.HardLevelPanel_AnimationDuration;
        delayBetweenPulses = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.HardLevelPanel_DelayBetweenPulses;
        autoCloseDelay = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.HardLevelPanel_AutoCloseDelay;
        enableAutoClose = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.HardLevelPanel_EnableAutoClose;
        fadeOutDuration = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.HardLevelPanel_FadeOutDuration;
    }
    
    private void OnDestroy()
    {
        if (heartbeatCoroutine != null)
        {
            StopCoroutine(heartbeatCoroutine);
            heartbeatCoroutine = null;
        }
        
        if (autoCloseCoroutine != null)
        {
            StopCoroutine(autoCloseCoroutine);
            autoCloseCoroutine = null;
        }
    }
    
    public void StartHeartbeatAnimation()
    {
        if (heartbeatCoroutine != null)
        {
            StopCoroutine(heartbeatCoroutine);
        }
        
        heartbeatCoroutine = StartCoroutine(HeartbeatAnimationCoroutine());
    }
    
    private IEnumerator HeartbeatAnimationCoroutine()
    {
        while (true)
        {
            // Fade out (from maxAlpha to minAlpha)
            float startTime = Time.time;
            float elapsedTime = 0f;
            
            while (elapsedTime < animationDuration / 2)
            {
                elapsedTime = Time.time - startTime;
                float t = elapsedTime / (animationDuration / 2);
                float currentAlpha = Mathf.Lerp(maxAlpha, minAlpha, t);
                
                imgCanvasRenderer.SetAlpha(currentAlpha);
                imgScreenRenderer.SetAlpha(currentAlpha);
                txtCanvasRenderer.SetAlpha(currentAlpha);
                
                yield return null;
            }
            
            // Fade in (from minAlpha to maxAlpha)
            startTime = Time.time;
            elapsedTime = 0f;
            
            while (elapsedTime < animationDuration / 2)
            {
                elapsedTime = Time.time - startTime;
                float t = elapsedTime / (animationDuration / 2);
                float currentAlpha = Mathf.Lerp(minAlpha, maxAlpha, t);
                
                imgCanvasRenderer.SetAlpha(currentAlpha);
                imgScreenRenderer.SetAlpha(currentAlpha);
                txtCanvasRenderer.SetAlpha(currentAlpha);
                
                yield return null;
            }
            
            // Delay between pulses
            yield return new WaitForSeconds(delayBetweenPulses);
        }
    }

    public void StartAutoCloseTimer()
    {
        if (autoCloseCoroutine != null)
        {
            StopCoroutine(autoCloseCoroutine);
        }
        
        autoCloseCoroutine = StartCoroutine(AutoCloseCoroutine());
    }

    private IEnumerator AutoCloseCoroutine()
    {
        yield return new WaitForSeconds(autoCloseDelay);
        
        // 停止心跳动画
        if (heartbeatCoroutine != null)
        {
            StopCoroutine(heartbeatCoroutine);
            heartbeatCoroutine = null;
        }
        
        // 获取当前透明度作为起始值
        float startAlpha = imgCanvasRenderer.GetAlpha();
        float startTime = Time.time;
        float elapsedTime = 0f;
        
        while (elapsedTime < fadeOutDuration)
        {
            elapsedTime = Time.time - startTime;
            float t = elapsedTime / fadeOutDuration;
            float currentAlpha = Mathf.Lerp(startAlpha, 0f, t);
            
            imgCanvasRenderer.SetAlpha(currentAlpha);
            imgScreenRenderer.SetAlpha(currentAlpha);
            txtCanvasRenderer.SetAlpha(currentAlpha);
            
            yield return null;
        }
        
        // Ensure alpha is exactly 0 at the end
        imgCanvasRenderer.SetAlpha(0f);
        imgScreenRenderer.SetAlpha(0f);
        txtCanvasRenderer.SetAlpha(0f);
        
        ClosePanel();
    }
}
