using System.Collections.Generic;

public class MatchUI_Const_Data
{



    // 场景切换过渡时间
    public float SCENE_TRANSITION_TIME = 3.0f;

    // 加载文字
    public string LOADING_TEXT = "加载中......";

    public string[] Loading_BGAndIcon_Path;
    public string[] Loading_First_BGAndIcon_Path;



    public bool Open_PrivacyPolicyBtn;
    public bool Open_UserAgreementBtn;
    public string PrivacyPolicy_Text;
    public string UserAgreement_Text;


    // Add these to your MatchUI_Const_Data class
    public float MenuBtnPanel_MenuToAnimSpeed = 3f;
    public float MenuBtnPanel_MenuBackAnimSpeed = 5f;
    public float MenuBtnPanel_IconAnimSpeed = 4f;
    public float MenuBtnPanel_TextAnimSpeed = 4.5f;
    public float MenuBtnPanel_IconScaleAnimSpeed = 3.5f;

    public float BtnMenuLock_TipsScaleAnimSpeed = 5f;
    public float BtnMenuLock_TipsDisplayDuration = 1f;

    // 语言设置
    public string CurrentLanguage = "Chinese"; // 当前语言

    public string Mode_Business = "AD";//Cash AD



    public int Guide_DirectGame_Level = 1;

    public List<int> Prop_Coin;
    public List<int> Prop_UnlockLevel;
    public List<string> Prop_BtnName;
    public List<string> Prop_Name;
    public List<string> Prop_Guide;

    // HardLevelPanel parameters
    public List<int> HardLevel_TargetLevel;
    public float HardLevelPanel_MaxAlpha = 0.8f;
    public float HardLevelPanel_MinAlpha = 0.3f;
    public float HardLevelPanel_AnimationDuration = 1.0f;
    public float HardLevelPanel_DelayBetweenPulses = 0.2f;
    public float HardLevelPanel_AutoCloseDelay = 1.5f;
    public bool HardLevelPanel_EnableAutoClose = true;
    public float HardLevelPanel_FadeOutDuration = 0.25f;

    public int ReGame_Coin;
    public string ReGame_IconPath;
    public string ReGame_Title;
    public string ReGame_Guide;


    public Dictionary<string, int> Win_LevelCoin;
    public float Win_CoinAnimSpeed;
    public float Win_CoinAnimDelay;

    public bool Progress_Text_Open = true;


    public bool[] Btn_Zoom_Open;



    public List<int> Vacant_Unlock_Coin;
    public List<string> Vacant_Guide;


    public List<MatchUI_GuideLevel_Data> Guide_Levels;

    public MatchUI_ShopPack_Data Pack_Specials;

    public List<MatchUI_ShopPack_Data> Pack_Gifts;

    public List<MatchUI_ShopPack_Data> Pack_Coins;

    public MatchUI_ShopPack_Data Pack_NoADSs;



    public string[] PropIcon_Paths;
    public string[] VacanIcon_Paths;

    public int Max_Level;

    // 循环关卡设置
    public int Loop_Start_Level = 30; // 从第N关开始循环
}
public class MatchUI_User_Data
{
    public bool AD_Remove_FullAD = false;
    public Dictionary<string, int> AD_Count;


    public Dictionary<string, List<int>> Pack_Buyed = new Dictionary<string, List<int>>();

    public List<int> Prop_Count;

    public int Count_Round;

    public Dictionary<string, int> Count_LevelLost;
    public Dictionary<string, int> Count_LevelWin;

    public int Coin_Current = 0;

    // 关卡系统，支持循环
    private int level_Current = 0;
    public int Loop_Count = 0; // 循环计数器，需要保存

    public int Level_Current
    {
        get { return level_Current; }
        set
        {
            var constData = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data;

            if (value <= 0)
            {
                level_Current = 0;
                Loop_Count = 0;
            }
            else if (value > constData.Max_Level - 1)
            {
                // 超过最大关卡，回到循环起始点
                level_Current = constData.Loop_Start_Level;
                Loop_Count++; // 循环次数+1
            }
            else
            {
                level_Current = value;
            }
        }
    }

    // 显示给用户的关卡数（连续递增）
    public int Level_Display
    {
        get
        {
            var constData = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data;

            if (Loop_Count == 0)
            {
                // 还未进入循环，显示实际关卡
                return level_Current;
            }
            else
            {
                // 已进入循环阶段，计算显示关卡
                int cycleLength = constData.Max_Level - constData.Loop_Start_Level;
                int currentInCycle = level_Current - constData.Loop_Start_Level;
                return constData.Max_Level + ((Loop_Count - 1) * cycleLength) + currentInCycle;
            }
        }
    }



    public bool Setting_Music = false;
    public bool Setting_Sound = true;
    public bool Setting_Vib = true;

}
public class MatchUI_ShopPack_Data
{
    public string Name;
    public bool RemoveADS;
    public int Coin;
    public List<int> PropsCount;

    public string NeedCash;

    public int ReserveCount;
}

public class MatchUI_GuideLevel_Data
{
    public int Level;
    public int Type;
    public string Title;
    public string Guide;
    public bool ShowCoinBar;
    public bool ShowSettingButton;
    public int GiftPropCount;
    public int PropIconIndex;

    public string Type3IconPath;



}
