using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MatchUI_PropIconRes
{


    // 缓存字典 <关卡号, 关卡数据>
    private static Dictionary<string, Sprite> propIconRes =
        new Dictionary<string, Sprite>();



    /// <summary>
    /// 加载指定关卡数据（带缓存功能）
    /// </summary>
    public static Sprite LoadPropIcon(string path)
    {
        // 优先从缓存读取
        if (propIconRes.TryGetValue(path, out Sprite cachedData))
        {
            // Debug.Log($"已读取缓存关卡数据: Level {levelNumber}");
            return cachedData;
        }


        // 加载加密文本资源
        Sprite icon = Resources.Load<Sprite>(path);
        if (icon == null)
        {
            Debug.LogError($"PropIcon 不存在:  {path}");
            return null;
        }



        // 存入缓存
        propIconRes[path] = icon;

        return icon;
    }




}
