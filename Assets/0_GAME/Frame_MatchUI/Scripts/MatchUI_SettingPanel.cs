using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_SettingPanel : MatchUI_BasePanel<MatchUI_SettingPanel>
{

    protected static string resourcesName = "7MatchUI_SettingPanel";

    public static void ShowPanel()
    {
        GameObject prefab = Resources.Load<GameObject>("MatchUI/Panel/" + resourcesName);
        if (prefab != null)
        {
            GameObject panelObj = GameObject.Instantiate(prefab);
            Canvas canvas = GameObject.Find("Canvas").GetComponent<Canvas>();
            if (canvas != null)
            {
                panelObj.transform.SetParent(canvas.transform, false);
            }
            else
            {
                Debug.LogError("No Canvas found in scene");
            }
        }
        else
        {
            Debug.LogError("Failed to load panel: " + resourcesName);
        }

        MatchUI_SettingPanel.Instance.UpdatePanel();

        // 播放显示动画
        MatchUI_SettingPanel.Instance.PlayShowAnimation();
    }

    public static void ClosePanel()
    {
        MatchUI_SettingPanel panel = FindObjectOfType<MatchUI_SettingPanel>();
        if (panel != null)
        {
            // 播放隐藏动画，完成后销毁
            panel.PlayHideAnimation(() =>
            {
                Destroy(panel.gameObject);
            });
        }
    }

    public Button btnRemoveADS;
    public Button btnMusic;
    public Button btnSound;
    public Button btnVib;
    public Button btnExitLevel;



    // public Image imgBoard;

    public TextMeshProUGUI txtRead;

    public Image imgMusicON;
    public Image imgMusicOFF;

    public Image imgSoundON;
    public Image imgSoundOFF;

    public Image imgVibON;
    public Image imgVibOFF;

    public Image imgMainBoard;


    public Button btnPrivate;
    public Button btnUserAgree;

    public void On_BtnClose_Click()
    {
        ClosePanel();
    }


    void UpdatePanel()
    {
        var userData = MatchUI_UserData_Manager.Instance.Get;

        // 根据移除广告状态显示或隐藏按钮
        btnRemoveADS.gameObject.SetActive(!userData.AD_Remove_FullAD &&
                                          MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business == "Cash");


        // 更新音乐按钮状态 - 激活时显示imgMusicON，隐藏imgMusicOFF
        imgMusicON.gameObject.SetActive(userData.Setting_Music);
        imgMusicOFF.gameObject.SetActive(!userData.Setting_Music);

        // 更新音效按钮状态 - 激活时显示imgSoundON，隐藏imgSoundOFF
        imgSoundON.gameObject.SetActive(userData.Setting_Sound);
        imgSoundOFF.gameObject.SetActive(!userData.Setting_Sound);

        // 更新震动按钮状态 - 激活时显示imgVibON，隐藏imgVibOFF
        imgVibON.gameObject.SetActive(userData.Setting_Vib);
        imgVibOFF.gameObject.SetActive(!userData.Setting_Vib);

        btnPrivate.gameObject.SetActive(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Open_PrivacyPolicyBtn);
        btnUserAgree.gameObject.SetActive(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Open_UserAgreementBtn);

        // 更新退出关卡按钮状态 - 根据Guide_DirectGame_Level和GamePanel实例
        btnExitLevel.gameObject.SetActive(userData.Level_Current >
          MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Guide_DirectGame_Level &&
          MatchUI_GamePanel.Instance != null);

        if (btnPrivate.gameObject.activeInHierarchy && !btnUserAgree.gameObject.activeInHierarchy)
            btnPrivate.GetComponent<RectTransform>().localPosition = new Vector3(0, btnPrivate.GetComponent<RectTransform>().localPosition.y, btnPrivate.GetComponent<RectTransform>().localPosition.z);
        if (!btnPrivate.gameObject.activeInHierarchy && btnUserAgree.gameObject.activeInHierarchy)
            btnUserAgree.GetComponent<RectTransform>().localPosition = new Vector3(0, btnUserAgree.GetComponent<RectTransform>().localPosition.y, btnUserAgree.GetComponent<RectTransform>().localPosition.z);


        if (!btnExitLevel.gameObject.activeInHierarchy)
        {
            txtRead.gameObject.SetActive(true);
        }
        else
        {
            txtRead.gameObject.SetActive(false);
        }

    }

    public void On_BtnRemoveADS_Click()
    {
        ClosePanel();

    }

    public void On_BtnMusic_Click()
    {
        // 取反音乐设置
        MatchUI_UserData_Manager.Instance.Set.Setting_Music = !MatchUI_UserData_Manager.Instance.Get.Setting_Music;
        // 更新面板
        UpdatePanel();

        if (MatchUI_UserData_Manager.Instance.Get.Setting_Music)
        {
            MatchUI_Controller.On_PlayMusic?.Invoke("MatchUI_BGM");
        }
        else
        {
            MatchUI_Controller.On_StopMusic?.Invoke();
        }
    }

    public void On_BtnSound_Click()
    {
        // 取反音效设置
        MatchUI_UserData_Manager.Instance.Set.Setting_Sound = !MatchUI_UserData_Manager.Instance.Get.Setting_Sound;


        // 更新面板
        UpdatePanel();
    }

    public void On_BtnVib_Click()
    {
        // 取反震动设置
        MatchUI_UserData_Manager.Instance.Set.Setting_Vib = !MatchUI_UserData_Manager.Instance.Get.Setting_Vib;
        // 更新面板
        UpdatePanel();
    }

    public void On_BtnExitLevel_Click()
    {
        MatchUI_LoadingPanel.StartLoading("1Start", null, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[0], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[1], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_BGAndIcon_Path[2]);
    }




}
