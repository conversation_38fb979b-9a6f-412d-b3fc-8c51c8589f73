using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_BtnProp : MonoBehaviour
{
    public Image imgLock;
    public Image imgPropIcon;
    public Image imgCoin;
    public Image imgCount;

    public TextMeshProUGUI txtLock;

    public TextMeshProUGUI txtPropCoin;

    public TextMeshProUGUI txtCount;

    public TextMeshProUGUI txtName;

    public int PropIndex;

    private void Awake()
    {


        txtName.text = Tool_LanguageManager.GetText(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Prop_BtnName[PropIndex]);

        imgPropIcon.sprite =
        MatchUI_PropIconRes.LoadPropIcon(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.PropIcon_Paths[PropIndex]);

        UpdateState();

        GetComponent<Button>().onClick.AddListener(OnClick);
    }

    public enum MatchUI_BtnProp_State
    {
        Locked, // 锁定状态
        UnlockedADNeedWatch, // 已解锁但需要观看广告(广告模式)
        UnlockedADHasCount, // 已解锁且有数量(广告模式)
        UnlockedCashNeedBuy, // 已解锁但需要购买(现金模式)
        UnlockedCashHasCount // 已解锁且有数量(现金模式)
    }

    private MatchUI_BtnProp_State currentState;

    public void UpdateState()
    {
        // 检查道具是否锁定
        int currentLevel = MatchUI_UserData_Manager.Instance.Get.Level_Current;
        int unlockLevel = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Prop_UnlockLevel[PropIndex];

        if (currentLevel < unlockLevel)
        {
            // 锁定状态
            currentState = MatchUI_BtnProp_State.Locked;

            // 只显示锁定图标
            imgLock.gameObject.SetActive(true);
            imgPropIcon.gameObject.SetActive(false);
            imgCoin.gameObject.SetActive(false);
            imgCount.gameObject.SetActive(false);

            // 设置锁定文本显示所需等级
            txtLock.text = (unlockLevel + 1).ToString();
        }
        else
        {
            // 已解锁状态 - 检查商业模式
            string businessMode = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Mode_Business;
            int propCount = MatchUI_UserData_Manager.Instance.Get.Prop_Count[PropIndex];

            if (businessMode == "AD")
            {
                // 广告模式 - 检查是否有道具
                if (propCount <= 0)
                {
                    // 没有道具 - 显示广告图标
                    currentState = MatchUI_BtnProp_State.UnlockedADNeedWatch;

                    imgLock.gameObject.SetActive(false);
                    imgPropIcon.gameObject.SetActive(true);
                    imgCoin.gameObject.SetActive(false);
                    imgCount.gameObject.SetActive(true);

                    txtCount.text = propCount.ToString();
                    // txtPropCoin.text = "AD"; // 显示广告标识
                }
                else
                {
                    // 有道具 - 显示数量
                    currentState = MatchUI_BtnProp_State.UnlockedADHasCount;

                    imgLock.gameObject.SetActive(false);
                    imgPropIcon.gameObject.SetActive(true);
                    imgCoin.gameObject.SetActive(false);
                    imgCount.gameObject.SetActive(true);

                    txtCount.text = propCount.ToString();
                }
            }
            else if (businessMode == "Cash")
            {
                // 现金模式 - 检查是否有道具
                if (propCount <= 0)
                {
                    // 没有道具 - 显示金币价格
                    currentState = MatchUI_BtnProp_State.UnlockedCashNeedBuy;

                    imgLock.gameObject.SetActive(false);
                    imgPropIcon.gameObject.SetActive(true);
                    imgCoin.gameObject.SetActive(true);
                    imgCount.gameObject.SetActive(false);

                    txtPropCoin.text = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Prop_Coin[PropIndex].ToString();
                }
                else
                {
                    // 有道具 - 显示数量
                    currentState = MatchUI_BtnProp_State.UnlockedCashHasCount;


                    imgLock.gameObject.SetActive(false);
                    imgPropIcon.gameObject.SetActive(true);
                    imgCoin.gameObject.SetActive(false);
                    imgCount.gameObject.SetActive(true);

                    txtCount.text = propCount.ToString();
                }
            }
        }
    }

    // public void UpdateGrey(bool isgrey)
    // {
    //     //just show
    //     if (currentState != MatchUI_BtnProp_State.Locked)
    //     {
    //         imgLock.gameObject.SetActive(isgrey);
    //     }
    // }

    void OnClick()
    {
        if (!Tool_InputManager.CanProcessInput())
            return;

        switch (currentState)
        {
            case MatchUI_BtnProp_State.Locked:
                MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
                break;
            case MatchUI_BtnProp_State.UnlockedADNeedWatch:
                MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
                MatchUI_GetPropPanel.ShowPanel(PropIndex);
                break;
            case MatchUI_BtnProp_State.UnlockedADHasCount:
                MatchUI_Controller.On_Ask_UseProp?.Invoke(PropIndex, On_UsePropSuccess);
                //On_UsePropSuccess();
                break;
            case MatchUI_BtnProp_State.UnlockedCashNeedBuy:

                int coinCost = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Prop_Coin[PropIndex];
                if (MatchUI_UserData_Manager.Instance.Get.Coin_Current >= coinCost)
                {
                    // 金币足够，直接购买
                    MatchUI_UserData_Manager.Instance.Set.Coin_Current -= coinCost;
                    MatchUI_UserData_Manager.Instance.Set.Prop_Count[PropIndex]++;
                    MatchUI_GamePanel.Instance.UpdateBtnPropState();

                    MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_获得奖励");
                    MXR_BRIGE.MessageTip_Show(Tool_LanguageManager.GetText("购买成功!"));
                    MatchUI_Controller.On_PlayVib?.Invoke(false);
                }
                else
                {
                    MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
                    // 金币不足，打开商店
                    MatchUI_ShopPanel.ShowPanel();
                }
                break;
            case MatchUI_BtnProp_State.UnlockedCashHasCount:
                MatchUI_Controller.On_Ask_UseProp?.Invoke(PropIndex, On_UsePropSuccess);
                //On_UsePropSuccess();
                break;
        }
    }

    void On_UsePropSuccess()
    {
        MatchUI_UserData_Manager.Instance.Set.Prop_Count[PropIndex]--;

        MatchUI_GamePanel.Instance.UpdateBtnPropState();
    }
}