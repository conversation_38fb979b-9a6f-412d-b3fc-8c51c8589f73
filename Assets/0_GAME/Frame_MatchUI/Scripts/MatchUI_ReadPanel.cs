using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_ReadPanel : MatchUI_BasePanel<MatchUI_ReadPanel>
{

    protected static string resourcesName = "8MatchUI_ReadPanel";

    public static void ShowPanel(string title, string text)
    {
        GameObject prefab = Resources.Load<GameObject>("MatchUI/Panel/" + resourcesName);
        if (prefab != null)
        {
            GameObject panelObj = GameObject.Instantiate(prefab);
            Canvas canvas = GameObject.Find("Canvas").GetComponent<Canvas>();
            if (canvas != null)
            {
                panelObj.transform.SetParent(canvas.transform, false);
            }
            else
            {
                Debug.LogError("No Canvas found in scene");
            }
        }
        else
        {
            Debug.LogError("Failed to load panel: " + resourcesName);
        }

        MatchUI_ReadPanel.Instance.UpdatePanel(title, text);

        // 播放显示动画
        MatchUI_ReadPanel.Instance.PlayShowAnimation();
    }

    public static void ClosePanel()
    {
        MatchUI_ReadPanel panel = FindObjectOfType<MatchUI_ReadPanel>();
        if (panel != null)
        {
            // 播放隐藏动画，完成后销毁
            panel.PlayHideAnimation(() =>
            {
                Destroy(panel.gameObject);
            });
        }
    }




    // public Image imgBoard;

    public TextMeshProUGUI txtRead;

    public TextMeshProUGUI txtTitle;


    public void On_BtnClose_Click()
    {
        ClosePanel();
    }


    void UpdatePanel(string title, string text)
    {
        txtTitle.text = title;
        txtRead.text = text;

    }



}
