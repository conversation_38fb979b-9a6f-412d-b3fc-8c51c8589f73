using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MatchUI_GetPropPanel : MatchUI_BasePanel<MatchUI_GetPropPanel>
{
    protected static string resourcesName = "4MatchUI_GetPropPanel";

    public static void ShowPanel(int propindex)
    {
        GameObject prefab = Resources.Load<GameObject>("MatchUI/Panel/" + resourcesName);
        if (prefab != null)
        {
            GameObject panelObj = GameObject.Instantiate(prefab);
            Canvas canvas = GameObject.Find("Canvas").GetComponent<Canvas>();
            if (canvas != null)
            {
                panelObj.transform.SetParent(canvas.transform, false);
            }
            else
            {
                Debug.LogError("No Canvas found in scene");
            }
        }
        else
        {
            Debug.LogError("Failed to load panel: " + resourcesName);
        }

        MatchUI_GetPropPanel.Instance.UpdatePanel(propindex);

        // 播放显示动画
        MatchUI_GetPropPanel.Instance.PlayShowAnimation();
    }

    public static void ClosePanel()
    {
        MatchUI_GetPropPanel panel = FindObjectOfType<MatchUI_GetPropPanel>();
        if (panel != null)
        {
            // 播放隐藏动画，完成后销毁
            panel.PlayHideAnimation(() =>
            {
                Destroy(panel.gameObject);
            });
        }
    }

    public void On_BtnClose_Click()
    {
        ClosePanel();
    }


    public Button btnADGet;
    public TextMeshProUGUI txtPropGuide;
    public TextMeshProUGUI txtTitle;

    public Image[] imgPropIcon;

    private int currentPropIndex;

    void UpdatePanel(int propindex)
    {
        currentPropIndex = propindex;

        // Get prop name and guide from constants
        string propName = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Prop_Name[propindex];
        string propGuide = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Prop_Guide[propindex];

        // Update UI elements
        txtTitle.text = Tool_LanguageManager.GetText(propName);
        txtPropGuide.text = Tool_LanguageManager.GetText(propGuide);

        // Hide all prop icons first
        for (int i = 0; i < imgPropIcon.Length; i++)
        {
            imgPropIcon[i].gameObject.SetActive(false);
        }

        for (int i = 0; i < imgPropIcon.Length; i++)
        {
            imgPropIcon[i].sprite =
            MatchUI_PropIconRes.LoadPropIcon(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.PropIcon_Paths[i]);
        }


        // Show only the current prop icon
        if (propindex >= 0 && propindex < imgPropIcon.Length)
        {
            imgPropIcon[propindex].gameObject.SetActive(true);
        }
    }

    public void On_BtnADGet_Click()
    {
        MXR_BRIGE.AD_ShowReward( On_ADSuccess);
        //On_ADSuccess();
    }

    void On_ADSuccess()
    {
        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_获得奖励");
        MXR_BRIGE.MessageTip_Show(Tool_LanguageManager.GetText("领取成功!"));

        // Save the updated data
        MatchUI_UserData_Manager.Instance.Set.Prop_Count[currentPropIndex] += 1;

        MatchUI_GamePanel.Instance.UpdateBtnPropState();

        ClosePanel();
    }
}