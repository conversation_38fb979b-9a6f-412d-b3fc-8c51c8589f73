using System;
using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;

/// <summary>
/// 加载界面管理器
/// </summary>
public class MatchUI_LoadingPanel : MonoBehaviour
{
    // 预制体路径
    private const string PANEL_PATH = "MatchUI/Panel/0MatchUI_LoadingPanel";
    private static MatchUI_LoadingPanel currentPanel;

    [SerializeField, Tooltip("加载文本组件")]
    private TextMeshProUGUI txt_LoadingText;

    [SerializeField, Tooltip("加载图标1组件")]
    private Image img_LoadingIcon1;
    [SerializeField, Tooltip("加载图标2组件")]
    private Image img_LoadingIcon2;
    [SerializeField, Tooltip("背景扩大图片组件")]
    private Image img_BigBGImage;

    private AsyncOperation asyncOperation;
    private Action onLoadComplete;

    private static bool isFirstLoading = false;

    /// <summary>
    /// 开始加载场景
    /// </summary>
    /// <param name="sceneName">场景名称</param>
    /// <param name="callback">加载完成回调</param>
    /// <param name="backgroundImage">背景图片名称（可选）</param>
    /// <param name="iconImagePath1">图标1路径（可选）</param>
    /// <param name="iconImagePath2">图标2路径（可选）</param>
    public static void StartLoading(string sceneName, Action callback = null, string backgroundImage = null, string iconImagePath1 = null, string iconImagePath2 = null)
    {
        if (isFirstLoading)
        {



            MatchUI_Controller.On_MainEvent?.Invoke("1Loading", MatchUI_UserData_Manager.Instance.Get.Level_Current, MatchUI_UserData_Manager.Instance.Get.Level_Display);

            MatchUI_LoadingPanel.StartLoading("1Loading", null, MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_First_BGAndIcon_Path[0], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_First_BGAndIcon_Path[1], MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.Loading_First_BGAndIcon_Path[2]);
        }


        // 如果已存在加载面板，先销毁
        if (currentPanel != null)
        {
            Destroy(currentPanel.gameObject);
        }

        // 查找或创建Canvas
        Canvas canvas = FindOrCreateCanvas();
        if (canvas == null)
        {
            Debug.LogError("无法创建或找到Canvas");
            callback?.Invoke();
            return;
        }

        // 加载预制体
        GameObject prefab = Resources.Load<GameObject>(PANEL_PATH);



        if (prefab != null)
        {
            // 在Canvas下实例化预制体
            GameObject panelObj = Instantiate(prefab, canvas.transform);



            // 设置RectTransform属性以填充整个画布
            RectTransform rectTransform = panelObj.GetComponent<RectTransform>();
            if (rectTransform != null)
            {
                rectTransform.anchorMin = Vector2.zero;
                rectTransform.anchorMax = Vector2.one;
                rectTransform.offsetMin = Vector2.zero;
                rectTransform.offsetMax = Vector2.zero;
            }

            currentPanel = panelObj.GetComponent<MatchUI_LoadingPanel>();
            if (currentPanel != null)
            {
                currentPanel.Initialize(sceneName, callback, backgroundImage, iconImagePath1, iconImagePath2);
            }
            else
            {
                Debug.LogError($"预制体上未找到 MatchUI_LoadingPanelManager 组件: {PANEL_PATH}");
                callback?.Invoke();
            }
        }
        else
        {
            Debug.LogError($"未能在路径下找到加载面板预制体: {PANEL_PATH}");
            callback?.Invoke();
        }
    }

    /// <summary>
    /// 查找或创建Canvas
    /// </summary>
    private static Canvas FindOrCreateCanvas()
    {
        // 首先尝试查找现有的Canvas
        Canvas canvas = GameObject.Find("Canvas").GetComponent<Canvas>();



        // 如果没有找到Canvas，创建一个新的
        if (canvas == null)
        {
            GameObject canvasObj = new GameObject("Canvas");
            canvas = canvasObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;

            // 添加CanvasScaler组件
            CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            scaler.matchWidthOrHeight = 0.5f;

            // 添加GraphicRaycaster组件
            canvasObj.AddComponent<GraphicRaycaster>();

            // 设置Canvas为最高层级
            canvas.sortingOrder = 32767;
        }

        return canvas;
    }

    /// <summary>
    /// 初始化加载面板
    /// </summary>
    private void Initialize(string sceneName, Action callback, string backgroundImage, string iconImagePath1 = null, string iconImagePath2 = null)
    {
        // 检查并获取必要组件
        if (txt_LoadingText == null)
        {
            txt_LoadingText = GetComponentInChildren<TextMeshProUGUI>();
            if (txt_LoadingText == null)
            {
                Debug.LogError("加载面板中未找到 TextMeshProUGUI 组件");
                callback?.Invoke();
                Destroy(gameObject);
                return;
            }
        }

        onLoadComplete = callback;

        // 设置背景图片
        if (!string.IsNullOrEmpty(backgroundImage) && img_BigBGImage != null)
        {
            string spritePath = backgroundImage;
            Sprite loadedSprite = Resources.Load<Sprite>(spritePath);
            if (loadedSprite != null)
            {
                img_BigBGImage.sprite = loadedSprite;
            }
            else
            {
                Debug.LogWarning($"未找到背景图片: {spritePath}");
            }
        }

        // 设置图标1
        if (img_LoadingIcon1 != null)
        {
            if (!string.IsNullOrEmpty(iconImagePath1))
            {
                Sprite iconSprite1 = Resources.Load<Sprite>(iconImagePath1);
                if (iconSprite1 != null)
                {
                    img_LoadingIcon1.sprite = iconSprite1;
                    img_LoadingIcon1.SetNativeSize();
                    img_LoadingIcon1.gameObject.SetActive(true);
                }
                else
                {
                    Debug.LogWarning($"未找到图标1: {iconImagePath1}");
                    img_LoadingIcon1.gameObject.SetActive(false);
                }
            }
            else
            {
                img_LoadingIcon1.gameObject.SetActive(false);
            }
        }

        // 设置图标2
        if (img_LoadingIcon2 != null)
        {
            if (!string.IsNullOrEmpty(iconImagePath2))
            {
                Sprite iconSprite2 = Resources.Load<Sprite>(iconImagePath2);
                if (iconSprite2 != null)
                {
                    img_LoadingIcon2.sprite = iconSprite2;
                    img_LoadingIcon2.SetNativeSize();
                    img_LoadingIcon2.gameObject.SetActive(true);
                }
                else
                {
                    Debug.LogWarning($"未找到图标2: {iconImagePath2}");
                    img_LoadingIcon2.gameObject.SetActive(false);
                }
            }
            else
            {
                img_LoadingIcon2.gameObject.SetActive(false);
            }
        }

        InitializeLoadingText();
        StartCoroutine(LoadSceneAsync(sceneName));
    }

    /// <summary>
    /// 初始化加载文本
    /// </summary>
    private void InitializeLoadingText()
    {
        if (txt_LoadingText == null) return;

        txt_LoadingText.text = Tool_LanguageManager.GetText(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.LOADING_TEXT);


    }

    /// <summary>
    /// 异步加载场景
    /// </summary>
    private IEnumerator LoadSceneAsync(string sceneName)
    {
        asyncOperation = SceneManager.LoadSceneAsync(sceneName);
        asyncOperation.allowSceneActivation = false;

        while (asyncOperation.progress < 0.9f)
        {
            yield return null;
        }

        yield return new WaitForSeconds(MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.SCENE_TRANSITION_TIME);


        if (isFirstLoading)
        {
            isFirstLoading = true;
            MatchUI_Controller.On_MainEvent?.Invoke("2LoadingOver", MatchUI_UserData_Manager.Instance.Get.Level_Current, MatchUI_UserData_Manager.Instance.Get.Level_Display);
        }


        // 先触发回调
        onLoadComplete?.Invoke();
        // 再销毁对象
        //        Destroy(gameObject);
        // 最后激活新场景
        asyncOperation.allowSceneActivation = true;
    }
}