// Toony Colors Pro+Mobile 2
// (c) 2014-2023 <PERSON>

// Shader Generator Module: Ramp Shading

#FEATURES
mult	lbl="Ramp Style"				kw=Slider Ramp|,Texture Ramp|TEXTURE_RAMP,Crisp Ramp (Anti-aliased)|CRISP_RAMP,Crisp Ramp|CRISP_RAMP_NO_AA,Bands|RAMP_BANDS,Bands Crisp (Anti-aliased)|RAMP_BANDS_CRISP,Bands Crisp|RAMP_BANDS_CRISP_NO_AA,RGB Slider Ramp|RGB_RAMP,No Ramp|NO_RAMP,Unlit|NO_RAMP_UNLIT			tt="Defines the transitioning between dark and lit areas of the model"	help="featuresreference/lighting/rampstyle"
sngl	lbl="Offset/Size"				kw=TEXTURE_RAMP_SLIDERS		needs=TEXTURE_RAMP																			indent			tt="Enable threshold/smoothing controls when using a texture ramp"
sngl	lbl="2D Texture"				kw=TEXTURE_RAMP_2D			needs=TEXTURE_RAMP																			indent			tt="Use a 2D texture for the ramp shading, to interpolate between different types of ramp based on a property.  See the Properties to define how to control the texture vertical axis."
mult	lbl="Ramp Control"			kw=Global|,Main + Other Lights|RAMP_MAIN_OTHER,Main + Light Type|RAMP_MAIN_LIGHTTYPE	excl=NO_RAMP,NO_RAMP_UNLIT	toggles=RAMP_SEPARATED	tt="Defines how many ramp controls the material will have:\n\nGlobal: one control for all lights\n\nMain + Other Lights: one control for the main directional light and one for all other lights\n\nMain + Light Type: one control for the main directional light and one for each light type (directional, point, spot)"	help="featuresreference/lighting/rampcontrol"
mult	lbl="Wrapped Lighting"		kw=Off|,Half|WRAPPED_LIGHTING_HALF,Custom|WRAPPED_LIGHTING_CUSTOM						help="featuresreference/lighting/wrappedlighting"	tt="Enable wrapped lighting"
mult	lbl="Lights"				kw=All Lights|,Main Light|WRAPPED_LIGHTING_MAIN_LIGHT,Other Lights|WRAPPED_LIGHTING_OTHER_LIGHTS	indent	needsOr=WRAPPED_LIGHTING_HALF,WRAPPED_LIGHTING_CUSTOM		tt="Which lights does wrapped lighting applies to"
#END

//================================================================

#PROPERTIES_NEW
		header		Ramp Shading
/// IF RGB_RAMP
		color	Ramp Threshold RGB					lighting, imp(vector, label = "Threshold (RGB)", default = (0.5,0.5,0.5,1.0), drawer = "[TCP2Vector3FloatsDrawer(R,G,B,0,1,0,1,0,1)]")
		color	Ramp Smoothing RGB					lighting, imp(vector, label = "Smoothing (RGB)", default = (0.1,0.1,0.1,1.0), drawer = "[TCP2Vector3FloatsDrawer(R,G,B,0,1,0,1,0,1)]")
	/// IF RAMP_MAIN_OTHER
		color	Ramp Threshold RGB Other Lights		lighting, imp(vector, label = "Threshold (Other)", default = (0.5,0.5,0.5,1.0), drawer = "[TCP2Vector3FloatsDrawer(R,G,B,0,1,0,1,0,1)]")
		color	Ramp Smoothing RGB Other Lights		lighting, imp(vector, label = "Smoothing (Other)", default = (0.5,0.5,0.5,1.0), drawer = "[TCP2Vector3FloatsDrawer(R,G,B,0,1,0,1,0,1)]")
	/// ELIF RAMP_MAIN_LIGHTTYPE
		color	Ramp Threshold RGB Point			lighting, imp(vector, label = "Threshold (Point)", default = (0.5,0.5,0.5,1.0), drawer = "[TCP2Vector3FloatsDrawer(R,G,B,0,1,0,1,0,1)]")
		color	Ramp Smoothing RGB Point			lighting, imp(vector, label = "Smoothing (Point)", default = (0.5,0.5,0.5,1.0), drawer = "[TCP2Vector3FloatsDrawer(R,G,B,0,1,0,1,0,1)]")

		color	Ramp Threshold RGB Spot				lighting, imp(vector, label = "Threshold (Spot)", default = (0.5,0.5,0.5,1.0), drawer = "[TCP2Vector3FloatsDrawer(R,G,B,0,1,0,1,0,1)]")
		color	Ramp Smoothing RGB Spot				lighting, imp(vector, label = "Smoothing (Spot)", default = (0.5,0.5,0.5,1.0), drawer = "[TCP2Vector3FloatsDrawer(R,G,B,0,1,0,1,0,1)]")

		color	Ramp Threshold RGB Directional		lighting, imp(vector, label = "Threshold (Directional)", default = (0.5,0.5,0.5,1.0), drawer = "[TCP2Vector3FloatsDrawer(R,G,B,0,1,0,1,0,1)]")
		color	Ramp Smoothing RGB Directional		lighting, imp(vector, label = "Smoothing (Directional)", default = (0.5,0.5,0.5,1.0), drawer = "[TCP2Vector3FloatsDrawer(R,G,B,0,1,0,1,0,1)]")
	///
/// ELIF CRISP_RAMP || CRISP_RAMP_NO_AA
	/// IF CRISP_RAMP
		float	Ramp Crisp Smoothing				lighting, imp(constant, label = "Crisp Smoothing", default = 1.0)
	///
		float	Ramp Threshold						lighting, imp(range, label = "Threshold", default = 0.5, min = 0.01, max = 1.0)
	/// IF RAMP_MAIN_OTHER
		float	Ramp Threshold Other Lights			lighting, imp(range, label = "Threshold", default = 0.5, min = 0.01, max = 1.0)
	/// ELIF RAMP_MAIN_LIGHTTYPE
		float	Ramp Threshold Point				lighting, imp(range, label = "Threshold (Point)", default = 0.5, min = 0.01, max = 1.0)
		float	Ramp Threshold Spot					lighting, imp(range, label = "Threshold (Spot)", default = 0.5, min = 0.01, max = 1.0)
		float	Ramp Threshold Directional			lighting, imp(range, label = "Threshold (Directional)", default = 0.5, min = 0.01, max = 1.0)
	///
/// ELIF RAMP_BANDS || RAMP_BANDS_CRISP || RAMP_BANDS_CRISP_NO_AA
	/// IF RAMP_BANDS_CRISP
		float	Bands Crisp Smoothing				lighting, imp(constant, label = "Bands Crisp Smoothing", default = 2.0)
	///
		float	Ramp Threshold						lighting, imp(range, label = "Threshold", default = 0.5, min = 0.01, max = 1.0)
		float	Ramp Smoothing						lighting, imp(range, label = "Smoothing", default = 0.5, min = 0.001, max = 1.0)
		float	Bands Count							lighting, imp(range, label = "Bands Count", default = 4, min = 1, max = 20, drawer = "[IntRange]")
	/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
		float	Bands Smoothing						lighting, imp(range, label = "Bands Smoothing", default = 0.1, min = 0.001, max = 1.0)
	///
	/// IF RAMP_MAIN_OTHER
		float	Ramp Threshold Other Lights			lighting, imp(range, label = "Threshold", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Smoothing Other Lights			lighting, imp(range, label = "Smoothing", default = 0.5, min = 0.001, max = 1.0)
		float	Bands Count Other Lights			lighting, imp(range, label = "Bands Count", default = 4, min = 1, max = 20, drawer = "[IntRange]")
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
		float	Bands Smoothing Other Lights		lighting, imp(range, label = "Bands Smoothing", default = 0.1, min = 0.001, max = 1.0)
		///
	/// ELIF RAMP_MAIN_LIGHTTYPE
		float	Ramp Threshold Point				lighting, imp(range, label = "Threshold (Point)", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Smoothing Point				lighting, imp(range, label = "Smoothing (Point)", default = 0.5, min = 0.001, max = 1.0)
		float	Bands Count Point					lighting, imp(range, label = "Bands Count", default = 4, min = 1, max = 20, drawer = "[IntRange]")
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
		float	Bands Smoothing Point				lighting, imp(range, label = "Bands Smoothing", default = 0.1, min = 0.001, max = 1.0)
		///

		float	Ramp Threshold Spot					lighting, imp(range, label = "Threshold (Spot)", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Smoothing Spot					lighting, imp(range, label = "Smoothing (Spot)", default = 0.5, min = 0.001, max = 1.0)
		float	Bands Count Spot					lighting, imp(range, label = "Bands Count", default = 4, min = 1, max = 20, drawer = "[IntRange]")
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
		float	Bands Smoothing Spot				lighting, imp(range, label = "Bands Smoothing", default = 0.1, min = 0.001, max = 1.0)
		///

		float	Ramp Threshold Directional			lighting, imp(range, label = "Threshold (Directional)", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Smoothing Directional			lighting, imp(range, label = "Smoothing (Directional)", default = 0.5, min = 0.001, max = 1.0)
		float	Bands Count Directional				lighting, imp(range, label = "Bands Count", default = 4, min = 1, max = 20, drawer = "[IntRange]")
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
		float	Bands Smoothing Directional			lighting, imp(range, label = "Bands Smoothing", default = 0.1, min = 0.001, max = 1.0)
		///
	///
/// ELIF TEXTURE_RAMP && TEXTURE_RAMP_SLIDERS
		float	Ramp Offset							lighting, imp(range, label = "Ramp Offset", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Size							lighting, imp(range, label = "Ramp Size", default = 1.0, min = 0.001, max = 1.0)
	/// IF RAMP_MAIN_OTHER
		float	Ramp Offset Other Lights			lighting, imp(range, label = "Ramp Offset", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Size Other Lights				lighting, imp(range, label = "Ramp Size", default = 1.0, min = 0.001, max = 1.0)
	/// ELIF RAMP_MAIN_LIGHTTYPE
		float	Ramp Offset Point					lighting, imp(range, label = "Ramp Offset (Point)", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Size Point						lighting, imp(range, label = "Ramp Size (Point)", default = 1.0, min = 0.001, max = 1.0)

		float	Ramp Offset Spot					lighting, imp(range, label = "Ramp Offset (Spot)", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Size Spot						lighting, imp(range, label = "Ramp Size (Spot)", default = 1.0, min = 0.001, max = 1.0)

		float	Ramp Offset Directional				lighting, imp(range, label = "Ramp Offset (Directional)", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Size Directional				lighting, imp(range, label = "Ramp Size (Directional)", default = 1.0, min = 0.001, max = 1.0)
	///
/// ELIF !TEXTURE_RAMP && !NO_RAMP && !NO_RAMP_UNLIT
		float	Ramp Threshold						lighting, imp(range, label = "Threshold", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Smoothing						lighting, imp(range, label = "Smoothing", default = 0.1, min = 0.001, max = 1.0)
	/// IF RAMP_MAIN_OTHER
		float	Ramp Threshold Other Lights			lighting, imp(range, label = "Threshold", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Smoothing Other Lights			lighting, imp(range, label = "Smoothing", default = 0.5, min = 0.001, max = 1.0)
	/// ELIF RAMP_MAIN_LIGHTTYPE
		float	Ramp Threshold Point				lighting, imp(range, label = "Threshold (Point)", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Smoothing Point				lighting, imp(range, label = "Smoothing (Point)", default = 0.5, min = 0.001, max = 1.0)

		float	Ramp Threshold Spot					lighting, imp(range, label = "Threshold (Spot)", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Smoothing Spot					lighting, imp(range, label = "Smoothing (Spot)", default = 0.5, min = 0.001, max = 1.0)

		float	Ramp Threshold Directional			lighting, imp(range, label = "Threshold (Directional)", default = 0.5, min = 0.0, max = 1.0)
		float	Ramp Smoothing Directional			lighting, imp(range, label = "Smoothing (Directional)", default = 0.5, min = 0.001, max = 1.0)
	///
///
/// IF TEXTURE_RAMP && TEXTURE_RAMP_2D
		float	2D Ramp Lerp						lighting, imp(range, label = "2D Ramp Lerp", default = 0.0, min = 0.0, max = 1.0
///
/// IF WRAPPED_LIGHTING_CUSTOM
		float	Light Wrap Factor					lighting, imp(range, default = 0.5, min = 0.0, max = 2.0)
///
#END

//================================================================

#KEYWORDS
/// IF TEXTURE_RAMP_2D
	set_keyword		RampTextureDrawer	[NoScaleOffset]
	set_keyword		RampTextureLabel	2D Ramp Texture
/// ELSE
	set_keyword		RampTextureDrawer	[TCP2Gradient]
	set_keyword		RampTextureLabel	Ramp Texture
///
#END

//================================================================

#PROPERTIES_BLOCK
/// IF !NO_RAMP && !NO_RAMP_UNLIT
		[TCP2Header(Ramp Shading)]
///
/// IF TEXTURE_RAMP
	/// IF RAMP_SEPARATED
		[TCP2HeaderHelp(Main Directional Light)]
	///
		@%RampTextureDrawer%@ _Ramp ("@%RampTextureLabel%@ (RGB)", 2D) = "gray" {}
	/// IF TEXTURE_RAMP_SLIDERS
		[[PROP:Ramp Offset]]
		[[PROP:Ramp Size]]
	///
	/// IF RAMP_MAIN_OTHER
		[TCP2HeaderHelp(Other Lights)]
		@%RampTextureDrawer%@ _RampOtherLights ("@%RampTextureLabel%@", 2D) = "gray" {}
	  /// IF TEXTURE_RAMP_SLIDERS
		[[PROP:Ramp Offset Other Lights]]
		[[PROP:Ramp Size Other Lights]]
	  ///
		[Space]
	/// ELIF RAMP_MAIN_LIGHTTYPE
		[HideInInspector] __BeginGroup_OtherLights ("Other Lights", Float) = 0
	  /// IF TEXTURE_RAMP_SLIDERS
		@%RampTextureDrawer%@ _RampPoint ("@%RampTextureLabel%@ (Point)", 2D) = "gray" {}
		[[PROP:Ramp Offset Point]]
		[[PROP:Ramp Size Point]]
		[Space]
		@%RampTextureDrawer%@ _RampSpot ("@%RampTextureLabel%@ (Spot)", 2D) = "gray" {}
		[[PROP:Ramp Offset Spot]]
		[[PROP:Ramp Size Spot]]
		[Space]
		@%RampTextureDrawer%@ _RampDir ("@%RampTextureLabel%@ (Directional)", 2D) = "gray" {}
		[[PROP:Ramp Offset Directional]]
		[[PROP:Ramp Size Directional]]
	  /// ELSE
		@%RampTextureDrawer%@ _RampPoint ("@%RampTextureLabel%@ (Point)", 2D) = "gray" {}
		@%RampTextureDrawer%@ _RampSpot ("@%RampTextureLabel%@ (Spot)", 2D) = "gray" {}
		@%RampTextureDrawer%@ _RampDir ("@%RampTextureLabel%@ (Directional)", 2D) = "gray" {}
	  ///
		[HideInInspector] __EndGroup ("Other Lights", Float) = 0
	///
/// ELIF RGB_RAMP
	/// IF RAMP_SEPARATED
		[TCP2HeaderHelp(Main Directional Light)]
	///
		[[PROP:Ramp Threshold RGB]]
		[[PROP:Ramp Smoothing RGB]]
	/// IF RAMP_MAIN_OTHER
		[TCP2HeaderHelp(Other Lights)]
		[[PROP:Ramp Threshold RGB Other Lights]]
		[[PROP:Ramp Smoothing RGB Other Lights]]
		[Space]
	/// ELIF RAMP_MAIN_LIGHTTYPE
		[HideInInspector] __BeginGroup_OtherLights ("Other Lights", Float) = 0
		[[PROP:Ramp Threshold RGB Point]]
		[[PROP:Ramp Smoothing RGB Point]]
		[Space]
		[[PROP:Ramp Threshold RGB Spot]]
		[[PROP:Ramp Smoothing RGB Spot]]
		[Space]
		[[PROP:Ramp Threshold RGB Directional]]
		[[PROP:Ramp Smoothing RGB Directional]]
		[HideInInspector] __EndGroup ("Other Lights", Float) = 0
	///
/// ELIF CRISP_RAMP || CRISP_RAMP_NO_AA
	/// IF RAMP_SEPARATED
		[TCP2HeaderHelp(Main Directional Light)]
	///
	/// IF CRISP_RAMP
		[[PROP:Ramp Crisp Smoothing]]
	///
		[[PROP:Ramp Threshold]]
	/// IF RAMP_MAIN_OTHER
		[TCP2HeaderHelp(Other Lights)]
		[[PROP:Ramp Threshold Other Lights]]
		[Space]
	/// ELIF RAMP_MAIN_LIGHTTYPE
		[HideInInspector] __BeginGroup_OtherLights ("Other Lights", Float) = 0
		[[PROP:Ramp Threshold Point]]
		[[PROP:Ramp Threshold Spot]]
		[[PROP:Ramp Threshold Directional]]
		[HideInInspector] __EndGroup ("Other Lights", Float) = 0
		[Space]
	///
/// ELIF RAMP_BANDS || RAMP_BANDS_CRISP || RAMP_BANDS_CRISP_NO_AA
	/// IF RAMP_SEPARATED
		[TCP2HeaderHelp(Main Directional Light)]
	///
	/// IF RAMP_BANDS_CRISP
		[[PROP:Bands Crisp Smoothing]]
	///
		[[PROP:Ramp Threshold]]
		[[PROP:Ramp Smoothing]]
		[[PROP:Bands Count]]
	/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
		[[PROP:Bands Smoothing]]
	///
	/// IF RAMP_MAIN_OTHER
		[TCP2HeaderHelp(Other Lights)]
		[[PROP:Ramp Threshold Other Lights]]
		[[PROP:Ramp Smoothing Other Lights]]
		[[PROP:Bands Count Other Lights]]
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
		[[PROP:Bands Smoothing Other Lights]]
		///
		[Space]
	/// ELIF RAMP_MAIN_LIGHTTYPE
		[HideInInspector] __BeginGroup_OtherLights ("Other Lights", Float) = 0
		[[PROP:Ramp Threshold Point]]
		[[PROP:Ramp Smoothing Point]]
		[[PROP:Bands Count Point]]
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
		[[PROP:Bands Smoothing Point]]
		///
		[Space]
		[[PROP:Ramp Threshold Spot]]
		[[PROP:Ramp Smoothing Spot]]
		[[PROP:Bands Count Spot]]
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
		[[PROP:Bands Smoothing Spot]]
		///
		[Space]
		[[PROP:Ramp Threshold Directional]]
		[[PROP:Ramp Smoothing Directional]]
		[[PROP:Bands Count Directional]]
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
		[[PROP:Bands Smoothing Directional]]
		///
		[HideInInspector] __EndGroup ("Other Lights", Float) = 0
		[Space]
	///
/// ELIF !TEXTURE_RAMP && !NO_RAMP && !NO_RAMP_UNLIT
	/// IF RAMP_SEPARATED
		[TCP2HeaderHelp(Main Directional Light)]
	///
		[[PROP:Ramp Threshold]]
		[[PROP:Ramp Smoothing]]
	/// IF RAMP_MAIN_OTHER
		[TCP2HeaderHelp(Other Lights)]
		[[PROP:Ramp Threshold Other Lights]]
		[[PROP:Ramp Smoothing Other Lights]]
		[Space]
	/// ELIF RAMP_MAIN_LIGHTTYPE
		[HideInInspector] __BeginGroup_OtherLights ("Other Lights", Float) = 0
		[[PROP:Ramp Threshold Point]]
		[[PROP:Ramp Smoothing Point]]
		[Space]
		[[PROP:Ramp Threshold Spot]]
		[[PROP:Ramp Smoothing Spot]]
		[Space]
		[[PROP:Ramp Threshold Directional]]
		[[PROP:Ramp Smoothing Directional]]
		[HideInInspector] __EndGroup ("Other Lights", Float) = 0
		[Space]
	///
///
/// IF TEXTURE_RAMP && TEXTURE_RAMP_2D
		[[PROP:2D Ramp Lerp]]
///
/// IF WRAPPED_LIGHTING_CUSTOM
		[[PROP:Light Wrap Factor]]
///
/// IF !NO_RAMP && !NO_RAMP_UNLIT
		[TCP2Separator]
///
#END

//================================================================

#VARIABLES_OUTSIDE_CBUFFER
/// IF TEXTURE_RAMP
		sampler2D _Ramp;
	/// IF RAMP_MAIN_OTHER
		sampler2D _RampOtherLights;
	/// ELIF RAMP_MAIN_LIGHTTYPE
		sampler2D _RampPoint;
		sampler2D _RampSpot;
		sampler2D _RampDir;
	///
///
#END

//================================================================

#INPUT
#END

//================================================================

#VERTEX
#END

//================================================================

#LIGHTING(float3 ramp, float ndl)
/// IF (WRAPPED_LIGHTING_HALF || WRAPPED_LIGHTING_CUSTOM) && WRAPPED_LIGHTING_MAIN_LIGHT
			#if defined(UNITY_PASS_FORWARDBASE)
/// ELIF (WRAPPED_LIGHTING_HALF || WRAPPED_LIGHTING_CUSTOM) && WRAPPED_LIGHTING_OTHER_LIGHTS
			#if !defined(UNITY_PASS_FORWARDBASE)
///
/// IF WRAPPED_LIGHTING_HALF

			// Wrapped Lighting
			ndl = ndl * 0.5 + 0.5;
/// ELIF WRAPPED_LIGHTING_CUSTOM

			// Wrapped Lighting
			half lightWrap = [[VALUE:Light Wrap Factor]];
			ndl = (ndl + lightWrap) / (1 + lightWrap);
///
/// IF (WRAPPED_LIGHTING_HALF || WRAPPED_LIGHTING_CUSTOM) && (WRAPPED_LIGHTING_MAIN_LIGHT || WRAPPED_LIGHTING_OTHER_LIGHTS)
			#endif
///

/// IF TEXTURE_RAMP

			//Define ramp threshold and smoothstep depending on context
# -------------------------------------------------------------------------------
# TEXTURE RAMP
# -------------------------------------------------------------------------------
	/// IF RAMP_MAIN_OTHER
			#if defined(UNITY_PASS_FORWARDBASE)
				#define		RAMP_TEXTURE	_Ramp
			#else
				#define		RAMP_TEXTURE	_RampOtherLights
			#endif
	/// ELIF RAMP_MAIN_LIGHTTYPE
			#if defined(UNITY_PASS_FORWARDBASE)
					#define		RAMP_TEXTURE	_Ramp
			#else
				#if POINT
					#define		RAMP_TEXTURE	_RampPoint
				#elif SPOT
					#define		RAMP_TEXTURE	_RampSpot
				#else
					#define		RAMP_TEXTURE	_RampDir
				#endif
			#endif
	/// ELSE
			#define		RAMP_TEXTURE	_Ramp
	///
	/// IF TEXTURE_RAMP_SLIDERS
		/// IF RAMP_MAIN_OTHER
			#if defined(UNITY_PASS_FORWARDBASE)
				#define		RAMP_THRESHOLD	[[VALUE:Ramp Offset]]
				#define		RAMP_SMOOTH		[[VALUE:Ramp Size]]
			#else
				#define		RAMP_THRESHOLD	[[VALUE:Ramp Offset Other Lights]]
				#define		RAMP_SMOOTH		[[VALUE:Ramp Size Other Lights]]
			#endif
		/// ELIF RAMP_MAIN_LIGHTTYPE
			#if defined(UNITY_PASS_FORWARDBASE)
					#define		RAMP_THRESHOLD	[[VALUE:Ramp Offset]]
					#define		RAMP_SMOOTH		[[VALUE:Ramp Size]]
			#else
				#if POINT
					#define		RAMP_THRESHOLD	[[VALUE:Ramp Offset Point]]
					#define		RAMP_SMOOTH		[[VALUE:Ramp Size Point]]
				#elif SPOT
					#define		RAMP_THRESHOLD	[[VALUE:Ramp Offset Spot]]
					#define		RAMP_SMOOTH		[[VALUE:Ramp Size Spot]]
				#else
					#define		RAMP_THRESHOLD	[[VALUE:Ramp Offset Directional]]
					#define		RAMP_SMOOTH		[[VALUE:Ramp Size Directional]]
				#endif
			#endif
		/// ELIF !NO_RAMP && !NO_RAMP_UNLIT
			#define		RAMP_THRESHOLD	[[VALUE:Ramp Offset]]
			#define		RAMP_SMOOTH		[[VALUE:Ramp Size]]
		///
	///
/// ELIF RGB_RAMP
# -------------------------------------------------------------------------------
# RGB RAMP
# -------------------------------------------------------------------------------
	/// IF RAMP_MAIN_OTHER
			#if defined(UNITY_PASS_FORWARDBASE)
				#define		RAMP_THRESHOLD	(1 - [[VALUE:Ramp Threshold RGB]])
				#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing RGB]]
			#else
				#define		RAMP_THRESHOLD	(1 - [[VALUE:Ramp Threshold RGB Other Lights]])
				#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing RGB Other Lights]]
			#endif
	/// ELIF RAMP_MAIN_LIGHTTYPE
			#if defined(UNITY_PASS_FORWARDBASE)
					#define		RAMP_THRESHOLD	(1 - [[VALUE:Ramp Threshold RGB]])
					#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing RGB]]
			#else
				#if POINT
					#define		RAMP_THRESHOLD	(1 - [[VALUE:Ramp Threshold RGB Point]])
					#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing RGB Point]]
				#elif SPOT
					#define		RAMP_THRESHOLD	(1 - [[VALUE:Ramp Threshold RGB Spot]])
					#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing RGB Spot]]
				#else
					#define		RAMP_THRESHOLD	(1 - [[VALUE:Ramp Threshold RGB Directional]])
					#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing RGB Directional]]
				#endif
			#endif
	/// ELSE
			#define		RAMP_THRESHOLD	(1 - [[VALUE:Ramp Threshold RGB]])
			#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing RGB]]
	///
/// ELIF CRISP_RAMP || CRISP_RAMP_NO_AA
# -------------------------------------------------------------------------------
# CRISP RAMP
# -------------------------------------------------------------------------------
	/// IF RAMP_MAIN_OTHER
			#if defined(UNITY_PASS_FORWARDBASE)
				#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold]]
			#else
				#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold Other Lights]]
			#endif
	/// ELIF RAMP_MAIN_LIGHTTYPE
			#if defined(UNITY_PASS_FORWARDBASE)
					#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold]]
			#else
				#if POINT
					#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold Point]]
				#elif SPOT
					#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold Spot]]
				#else
					#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold Directional]]
				#endif
			#endif
	/// ELSE
			#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold]]
	///
/// ELIF RAMP_BANDS || RAMP_BANDS_CRISP || RAMP_BANDS_CRISP_NO_AA
# -------------------------------------------------------------------------------
# BANDS RAMP
# -------------------------------------------------------------------------------
	/// IF RAMP_MAIN_OTHER
			#if defined(UNITY_PASS_FORWARDBASE)
				#define		RAMP_THRESHOLD		[[VALUE:Ramp Threshold]]
				#define		RAMP_SMOOTH			[[VALUE:Ramp Smoothing]]
				#define		RAMP_BANDS			[[VALUE:Bands Count]]
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
				#define		RAMP_BANDS_SMOOTH	[[VALUE:Bands Smoothing]]
		///
			#else
				#define		RAMP_THRESHOLD		[[VALUE:Ramp Threshold Other Lights]]
				#define		RAMP_SMOOTH			[[VALUE:Ramp Smoothing Other Lights]]
				#define		RAMP_BANDS			[[VALUE:Bands Count Other Lights]]
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
				#define		RAMP_BANDS_SMOOTH	[[VALUE:Bands Smoothing Other Lights]]
		///
			#endif
	/// ELIF RAMP_MAIN_LIGHTTYPE
			#if defined(UNITY_PASS_FORWARDBASE)
					#define		RAMP_THRESHOLD		[[VALUE:Ramp Threshold]]
					#define		RAMP_SMOOTH			[[VALUE:Ramp Smoothing]]
					#define		RAMP_BANDS			[[VALUE:Bands Count]]
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
					#define		RAMP_BANDS_SMOOTH	[[VALUE:Bands Smoothing]]
		///
			#else
				#if POINT
					#define		RAMP_THRESHOLD		[[VALUE:Ramp Threshold Point]]
					#define		RAMP_SMOOTH			[[VALUE:Ramp Smoothing Point]]
					#define		RAMP_BANDS			[[VALUE:Bands Count Point]]
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
					#define		RAMP_BANDS_SMOOTH	[[VALUE:Bands Smoothing Point]]
		///
				#elif SPOT
					#define		RAMP_THRESHOLD		[[VALUE:Ramp Threshold Spot]]
					#define		RAMP_SMOOTH			[[VALUE:Ramp Smoothing Spot]]
					#define		RAMP_BANDS			[[VALUE:Bands Count Spot]]
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
					#define		RAMP_BANDS_SMOOTH	[[VALUE:Bands Smoothing Spot]]
		///
				#else
					#define		RAMP_THRESHOLD		[[VALUE:Ramp Threshold Directional]]
					#define		RAMP_SMOOTH			[[VALUE:Ramp Smoothing Directional]]
					#define		RAMP_BANDS			[[VALUE:Bands Count Directional]]
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
					#define		RAMP_BANDS_SMOOTH	[[VALUE:Bands Smoothing Directional]]
		///
				#endif
			#endif
	/// ELSE
			#define		RAMP_THRESHOLD		[[VALUE:Ramp Threshold]]
			#define		RAMP_SMOOTH			[[VALUE:Ramp Smoothing]]
			#define		RAMP_BANDS			[[VALUE:Bands Count]]
		/// IF !RAMP_BANDS_CRISP && !RAMP_BANDS_CRISP_NO_AA
			#define		RAMP_BANDS_SMOOTH	[[VALUE:Bands Smoothing]]
		///
	///
/// ELIF !TEXTURE_RAMP && !NO_RAMP && !NO_RAMP_UNLIT
# -------------------------------------------------------------------------------
# SLIDER RAMP
# -------------------------------------------------------------------------------
	/// IF RAMP_MAIN_OTHER
			#if defined(UNITY_PASS_FORWARDBASE)
				#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold]]
				#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing]]
			#else
				#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold Other Lights]]
				#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing Other Lights]]
			#endif
	/// ELIF RAMP_MAIN_LIGHTTYPE
			#if defined(UNITY_PASS_FORWARDBASE)
					#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold]]
					#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing]]
			#else
				#if POINT
					#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold Point]]
					#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing Point]]
				#elif SPOT
					#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold Spot]]
					#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing Spot]]
				#else
					#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold Directional]]
					#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing Directional]]
				#endif
			#endif
	/// ELIF !NO_RAMP && !NO_RAMP_UNLIT
			#define		RAMP_THRESHOLD	[[VALUE:Ramp Threshold]]
			#define		RAMP_SMOOTH		[[VALUE:Ramp Smoothing]]
	///
///
/// IF !TEXTURE_RAMP
			ndl = saturate(ndl);
///
/// IF TEXTURE_RAMP
			half2 rampUv = ndl.xx * 0.5 + 0.5;
	/// IF TEXTURE_RAMP_SLIDERS
			half remap_min = RAMP_THRESHOLD - RAMP_SMOOTH*0.5;
			half diff = (RAMP_THRESHOLD + RAMP_SMOOTH*0.5) - remap_min;
			rampUv = saturate(rampUv * (1.0 / diff) - (remap_min / diff));
	///
	/// IF TEXTURE_RAMP_2D
			rampUv.y = [[VALUE:2D Ramp Lerp]];
			ramp = tex2D(RAMP_TEXTURE, rampUv).rgb;
	/// ELSE
			ramp = tex2D(RAMP_TEXTURE, rampUv).rgb;
	///
/// ELIF CRISP_RAMP
			half gradientLength = fwidth(ndl);
			half thresholdWidth = [[VALUE:Ramp Crisp Smoothing]] * gradientLength;
			ramp = smoothstep(RAMP_THRESHOLD - thresholdWidth, RAMP_THRESHOLD + thresholdWidth, ndl);
/// ELIF CRISP_RAMP_NO_AA
			ramp = step(RAMP_THRESHOLD, ndl);
/// ELIF RAMP_BANDS || RAMP_BANDS_CRISP
			half bandsNdl = smoothstep(RAMP_THRESHOLD - RAMP_SMOOTH*0.5, RAMP_THRESHOLD + RAMP_SMOOTH*0.5, ndl);
		/// IF RAMP_BANDS_CRISP
			half gradientLength = fwidth(ndl);
			half bandsSmooth = gradientLength * ([[VALUE:Bands Crisp Smoothing]] + RAMP_BANDS);
		/// ELSE
			half bandsSmooth = RAMP_BANDS_SMOOTH * 0.5;
		///
			ramp = saturate((smoothstep(0.5 - bandsSmooth, 0.5 + bandsSmooth, frac(bandsNdl * RAMP_BANDS)) + floor(bandsNdl * RAMP_BANDS)) / RAMP_BANDS).xxx;
/// ELIF RAMP_BANDS_CRISP_NO_AA
			ramp = smoothstep(RAMP_THRESHOLD - RAMP_SMOOTH*0.5, RAMP_THRESHOLD + RAMP_SMOOTH*0.5, ndl);
			ramp = (round(ramp * RAMP_BANDS) / RAMP_BANDS) * step(ndl, 1);
/// ELIF NO_RAMP
			ramp = ndl.xxx;
/// ELIF NO_RAMP_UNLIT
			ramp = float3(1, 1, 1);
/// ELSE
			ramp = smoothstep(RAMP_THRESHOLD - RAMP_SMOOTH*0.5, RAMP_THRESHOLD + RAMP_SMOOTH*0.5, ndl);
///
#END
