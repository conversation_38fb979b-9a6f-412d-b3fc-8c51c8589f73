// Toony Colors Pro+Mobile 2
// (c) 2014-2023 <PERSON>

// Shader Generator Module: Specular

#FEATURES
mult	lbl="Specular"				kw=Off|,<PERSON>linn-<PERSON>ong|SPEC_LEGACY,PBR Blinn-Phong|SPEC_PBR_BLINNPHONG,GGX|SPEC_PBR_GGX,Anisotropic|SPECULAR_ANISOTROPIC,Hair Anisotropic|SPECULAR_ANISOTROPIC_HAIR		toggles=SPECULAR	help="featuresreference/lighting/specular"
mult	lbl="Stylized Specular"		kw=Off|,Cartoon|SPECULAR_TOON,Bands|SPECULAR_TOON_BAND,Texture Ramp|SPECULAR_RAMP	needsOr=SPEC_LEGACY,SPECULAR_ANISOTROPIC	indent		tt="Specular stylization"
mult	lbl="Anisotropic Specular Basis"	kw=Tangent|,Bitangent|SPECULAR_ANISOTROPIC_HAIR_BITANGENT					needs=SPECULAR_ANISOTROPIC_HAIR				indent		tt="Which vector to use as a basis for the anisotropic specular calculation. Depending on the base mesh, one or the other will give better results."
sngl	lbl="Visible over Shadows"	kw=SPECULAR_NO_ATTEN																needs=SPECULAR								indent		tt="Shadows don't affect specular visibility"	
sngl	lbl="Make Optional"			kw=SPECULAR_SHADER_FEATURE															needs=SPECULAR								indent		tt="Will make specular optional in the material inspector, using a shader keyword"
#END

//================================================================

#PROPERTIES_NEW
/// IF SPECULAR
	header			Specular
	/// IF !SPECULAR_ANISOTROPIC_HAIR
		color		Specular Color				lighting, imp(color, label = "Specular Color", default = (0.5, 0.5, 0.5, 1))
	///
	/// IF SPECULAR_NO_ATTEN
		float		Specular Shadow Attenuation	lighting, imp(float, default = 0.25), help = "Attenuates the specular value when it's in the shadowed areas"
	///
	/// IF SPECULAR_ANISOTROPIC || (SPEC_LEGACY && !SPECULAR_RAMP && !SPECULAR_TOON)
		float		Specular Smoothness			lighting, imp(float, label = "Smoothness", default = 0.2)
	/// ELIF SPEC_PBR_BLINNPHONG || SPEC_PBR_GGX
		float		Specular Roughness PBR		lighting, imp(range, label = "Roughness", default = 0.5, min = 0, max = 1)
	///
	/// IF SPECULAR_ANISOTROPIC
		float		Anisotropic Spread			lighting, imp(range, label = "Anisotropic Spread", default = 1.0, min = 0, max = 2)
	///
	/// IF SPECULAR_ANISOTROPIC_HAIR
		float		Anisotropic Tangent Shift	lighting, imp(texture, label = "Tangent Shift Texture", default = "gray", tiling_offset = true)
		float		Anisotropic Shift 1			lighting, imp(float, label = "Shift 1", default = 0.2)
		float		Tangent Shift Strength 1	lighting, imp(constant, label = "Tangent Shift Strength 1", default = 1.0)
		float		Anisotropic Exponent 1		lighting, imp(float, label = "Exponent 1", default = 4.0)
		color		Specular Color 1			lighting, imp(color, label = "Specular Color 1", default = (0.3, 0.3, 0.3, 1))
		float		Anisotropic Shift 2			lighting, imp(float, label = "Shift 2", default = 0.5)
		float		Tangent Shift Strength 2	lighting, imp(constant, label = "Tangent Shift Strength 2", default = 1.0)
		float		Anisotropic Exponent 2		lighting, imp(float, label = "Exponent 2", default = 400.0)
		color		Specular Color 2			lighting, imp(color, label = "Specular Color 2", default = (0.8, 0.8, 0.8, 1))
	///
	/// IF SPECULAR_TOON && (SPEC_LEGACY || SPECULAR_ANISOTROPIC)
		float		Specular Toon Size			lighting, imp(range, label = "Toon Size", default = 0.25, min = 0, max = 1)
		float		Specular Toon Smoothness	lighting, imp(range, label = "Toon Smoothness", default = 0.05, min = 0.001, max = 0.5)

	/// ELIF SPECULAR_TOON_BAND && (SPEC_LEGACY || SPECULAR_ANISOTROPIC)
		float		Specular Toon Bands			lighting, imp(float, label = "Specular Bands", default = 3)
	///
///
#END

//================================================================

#KEYWORDS
/// IF SPECULAR
	feature_on		USE_VIEW_DIRECTION_FRAGMENT
///
/// IF SPECULAR_ANISOTROPIC || SPECULAR_ANISOTROPIC_HAIR
	feature_on		USE_TANGENT_FRAGMENT
	feature_on		USE_BITANGENT_FRAGMENT
	feature_on		USE_NDV_FRAGMENT
///
#END

//================================================================

#SHADER_FEATURES_BLOCK
/// IF SPECULAR && SPECULAR_SHADER_FEATURE
	#pragma shader_feature_local_fragment TCP2_SPECULAR
///
#END

//================================================================

#PROPERTIES_BLOCK
/// IF SPECULAR

		[TCP2HeaderHelp(Specular)]
	/// IF SPECULAR_SHADER_FEATURE
		[Toggle(TCP2_SPECULAR)] _UseSpecular ("Enable Specular", Float) = 0
	///
	/// IF SPECULAR_RAMP && (SPEC_LEGACY || SPECULAR_ANISOTROPIC)
		[TCP2Gradient] _SpecularRamp ("Specular Ramp (RGB)", 2D) = "gray" {}
	///
	/// IF SPECULAR_ANISOTROPIC_HAIR
	#if_not_empty
	#start_not_empty_block
		[[PROP:Anisotropic Tangent Shift]]
		[[PROP:Anisotropic Shift 1]]
		[[PROP:Tangent Shift Strength 1]]
		[[PROP:Anisotropic Exponent 1]]
		[[PROP:Specular Color 1]]
	#end_not_empty_block
		[Space]
	#end_not_empty
	#if_not_empty
	#start_not_empty_block
		[[PROP:Anisotropic Shift 2]]
		[[PROP:Tangent Shift Strength 2]]
		[[PROP:Anisotropic Exponent 2]]
		[[PROP:Specular Color 2]]
	#end_not_empty_block
		[Space]
	#end_not_empty
	/// ELSE
		[[PROP:Specular Color]]
	///
	/// IF SPECULAR_NO_ATTEN
		[[PROP:Specular Shadow Attenuation]]
	///
	/// IF SPECULAR_ANISOTROPIC || (SPEC_LEGACY && !SPECULAR_RAMP && !SPECULAR_TOON)
		[[PROP:Specular Smoothness]]
	/// ELIF SPEC_PBR_BLINNPHONG || SPEC_PBR_GGX
		[[PROP:Specular Roughness PBR]]
	///
	/// IF SPECULAR_ANISOTROPIC
		[[PROP:Anisotropic Spread]]
	///
	/// IF SPECULAR_TOON && (SPEC_LEGACY || SPECULAR_ANISOTROPIC)
		[[PROP:Specular Toon Size]]
		[[PROP:Specular Toon Smoothness]]
	/// ELIF SPECULAR_TOON_BAND && (SPEC_LEGACY || SPECULAR_ANISOTROPIC)
		[[PROP:Specular Toon Bands]]
	///
		[TCP2Separator]
///
#END

//================================================================

#VARIABLES_OUTSIDE_CBUFFER
/// IF SPECULAR_RAMP && (SPEC_LEGACY || SPECULAR_ANISOTROPIC)
	sampler2D _SpecularRamp;
///
#END

//================================================================

#FUNCTIONS
/// IF SPECULAR

	//Specular help functions (from UnityStandardBRDF.cginc)
	inline float3 SpecSafeNormalize(float3 inVec)
	{
		half dp3 = max(0.001f, dot(inVec, inVec));
		return inVec * rsqrt(dp3);
	}
///
/// IF SPEC_PBR_BLINNPHONG

		//PBR Blinn-Phong
		#define TCP2_PI 3.14159265359
		inline half PercRoughnessToSpecPower(half roughness)
		{
			half sq = max(1e-4f, roughness*roughness);
			half n = (2.0 / sq) - 2.0;
			n = max(n, 1e-4f);
			return n;
		}
		inline half NDFBlinnPhong(half NdotH, half n)
		{
			// norm = (n+2)/(2*pi)
			half normTerm = (n + 2.0) * (0.5/TCP2_PI);

			half specTerm = pow (NdotH, n);
			return specTerm * normTerm;
		}
///
/// IF SPEC_PBR_GGX

		//GGX
		#define TCP2_PI			3.14159265359
		#define TCP2_INV_PI		0.31830988618f
		#if defined(SHADER_API_MOBILE)
			#define TCP2_EPSILON 1e-4f
		#else
			#define TCP2_EPSILON 1e-7f
		#endif
		inline half GGX(half NdotH, half roughness)
		{
			half a2 = roughness * roughness;
			half d = (NdotH * a2 - NdotH) * NdotH + 1.0f;
			return TCP2_INV_PI * a2 / (d * d + TCP2_EPSILON);
		}
///
/// IF SPECULAR_ANISOTROPIC_HAIR

		// Hair Anisotropic Specular
		inline half HairStrandSpecular(float3 tangent, half3 halfDir, float exponent)
		{
			half tdh = dot(tangent, halfDir);
			half sth = sqrt(1.0 - tdh * tdh);
			half dirAtten = smoothstep(-1.0, 0.0, tdh);
			return dirAtten * pow(1e-4h + sth, exponent);
		}
		inline float3 HairShiftTangent(float3 tangent, float3 normal, float shift)
		{
			return normalize(tangent + shift * normal);
		}
///
#END

//================================================================

#INPUT
#END

//================================================================

#VERTEX
#END

//================================================================

#LIGHTING(float4 color, float3 normal, float3 tangent, float3 lightDir, float3 viewDir, float ndl, float ndv, float atten, struct light)
/// IF SPECULAR
		half3 halfDir = SpecSafeNormalize(float3(lightDir) + float3(viewDir));

	/// IF SPECULAR_SHADER_FEATURE
		#if defined(TCP2_SPECULAR)
	///
	/// IF SPECULAR_ANISOTROPIC
		//Anisotropic Specular
		float ndh = max(0, dot (normal, halfDir));
		/// IF LWRP
#HACK: bitangent is already calculated in LWRP
		half3 binorm = bitangentWS.xyz;
		/// ELSE
		half3 binorm = cross(normal, tangent);
		///
		float aX = dot(halfDir, tangent) / [[VALUE:Anisotropic Spread]];
		float aY = dot(halfDir, binorm) / [[VALUE:Specular Smoothness]];
		float specAniso = sqrt(max(0.0, ndl / ndv)) * exp(-2.0 * (aX * aX + aY * aY) / (1.0 + ndh));
		/// IF SPECULAR_RAMP
		float3 spec = tex2D(_SpecularRamp, (specAniso*specAniso).xx).rgb;
		/// ELIF SPECULAR_TOON
		float spec = smoothstep([[VALUE:Specular Toon Size]] + [[VALUE:Specular Toon Smoothness]], [[VALUE:Specular Toon Size]] - [[VALUE:Specular Toon Smoothness]],1 - (specAniso / (1+[[VALUE:Specular Toon Smoothness]])));
		/// ELSE
		float spec = specAniso;
		///
		spec = saturate(spec);
		/// IF SPECULAR_TOON_BAND
		spec = floor(spec * [[VALUE:Specular Toon Bands]]) / [[VALUE:Specular Toon Bands]];
		///
	/// ELIF SPECULAR_ANISOTROPIC_HAIR
		// Hair Anisotropic Specular
		half tangentShift = [[VALUE:Anisotropic Tangent Shift]];
		half shift1 = [[VALUE:Anisotropic Shift 1]];
		half shift2 = [[VALUE:Anisotropic Shift 2]];
		/// IF SPECULAR_ANISOTROPIC_HAIR_BITANGENT
		float3 bitangent = cross(normal, tangent);
		float3 anisoTangent1 = HairShiftTangent(bitangent, normal, shift1 + tangentShift * [[VALUE:Tangent Shift Strength 1]]);
		float3 anisoTangent2 = HairShiftTangent(bitangent, normal, shift2 + tangentShift * [[VALUE:Tangent Shift Strength 2]]);
		/// ELSE
		float3 anisoTangent1 = HairShiftTangent(tangent, normal, shift1 + tangentShift * [[VALUE:Tangent Shift Strength 1]]);
		float3 anisoTangent2 = HairShiftTangent(tangent, normal, shift2 + tangentShift * [[VALUE:Tangent Shift Strength 2]]);
		///
		half exp1 = [[VALUE:Anisotropic Exponent 1]];
		half exp2 = [[VALUE:Anisotropic Exponent 2]];
		half spec1 = HairStrandSpecular(anisoTangent1, halfDir, exp1);
		half spec2 = HairStrandSpecular(anisoTangent2, halfDir, exp2);
		half3 specColor1 = spec1 * [[VALUE:Specular Color 1]];
		half3 specColor2 = spec2 * [[VALUE:Specular Color 2]];
		specColor1 *= ndl;
		specColor2 *= ndl;
	/// ELIF SPEC_PBR_BLINNPHONG
		//Specular: PBR Blinn-Phong
		half roughness = [[VALUE:Specular Roughness PBR]]*[[VALUE:Specular Roughness PBR]];
		half nh = saturate(dot(normal, halfDir));
		half spec = NDFBlinnPhong(nh, PercRoughnessToSpecPower(roughness));
	/// ELIF SPEC_PBR_GGX
		//Specular: GGX
		half roughness = [[VALUE:Specular Roughness PBR]]*[[VALUE:Specular Roughness PBR]];
		half nh = saturate(dot(normal, halfDir));
		half spec = GGX(nh, saturate(roughness));
		spec *= TCP2_PI * 0.05;
		#ifdef UNITY_COLORSPACE_GAMMA
			spec = max(0, sqrt(max(1e-4h, spec)));
			half surfaceReduction = 1.0 - 0.28 * roughness * [[VALUE:Specular Roughness PBR]];
		#else
			half surfaceReduction = 1.0 / (roughness*roughness + 1.0);
		#endif
		spec = max(0, spec * ndl);
		spec *= surfaceReduction;
	/// ELSE
		//Blinn-Phong Specular
		float ndh = max(0, dot (normal, halfDir));
		/// IF SPECULAR_RAMP
		float3 spec = tex2D(_SpecularRamp, (ndh*ndh).xx).rgb;
		/// ELIF SPECULAR_TOON
		float spec = smoothstep([[VALUE:Specular Toon Size]] + [[VALUE:Specular Toon Smoothness]], [[VALUE:Specular Toon Size]] - [[VALUE:Specular Toon Smoothness]],1 - (ndh / (1+[[VALUE:Specular Toon Smoothness]])));
		/// ELSE
		float spec = pow(ndh, 1e-4h + [[VALUE:Specular Smoothness]] * 128.0);
		///
		/// IF SPECULAR_TOON_BAND
		spec = floor(spec * [[VALUE:Specular Toon Bands]]) / [[VALUE:Specular Toon Bands]];
		///
		spec *= ndl;
	///
	/// IF SPECULAR_NO_ATTEN
		/// IF URP
		half specShadowAtten = saturate(light.shadowAttenuation * ndl + [[VALUE:Specular Shadow Attenuation]]) * light.distanceAttenuation;
		/// ELSE
		half specShadowAtten = saturate(atten * ndl + [[VALUE:Specular Shadow Attenuation]]);
		///
		/// IF SPECULAR_ANISOTROPIC_HAIR
		specColor1 *= specShadowAtten;
		specColor2 *= specShadowAtten;
		/// ELSE
		spec *= specShadowAtten;
		///
	/// ELSE
		/// IF SPECULAR_ANISOTROPIC_HAIR
		specColor1 *= atten;
		specColor2 *= atten;
		/// ELSE
		spec *= atten;
		///
	///

		//Apply specular
	/// IF SPECULAR_ANISOTROPIC_HAIR
		color.rgb += specColor1 + specColor2 * lightColor.rgb;
	/// ELSE
		color.rgb += spec * lightColor.rgb * [[VALUE:Specular Color]];
	///
	/// IF SPECULAR_SHADER_FEATURE
		#endif
	///
///
#END
