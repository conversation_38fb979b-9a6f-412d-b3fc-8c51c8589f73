// Toony Colors Pro+Mobile 2
// (c) 2014-2023 <PERSON>

#NAME=Default (Universal Pipeline)
#ID=TEMPLATE_URP
#TEMPLATE_KEYWORDS=TEMPLATE_LWRP
#SG2

#================================================================

#MODULES

# needed by Shader Generator 2:
NoTile Sampling
Triplanar Sampling
HSV
Screen Space UV
Hash Functions
Dithering

# lighting:
Ramp Shading LWRP
ShadowHSV
Specular
Rim Lighting
Reflection
Subsurface Scattering
MatCap
Custom Ambient

# surface:
AlbedoHSV
Normal Mapping
Texture Blending
Triplanar
Vertex Displacement
Terrain

# stylization:
NdotL Stylization
Sketch
Outline

# special effects:
Dissolve
Vertical Fog
Wind
Water
VertExmotion
CurvedWorld
Depth Texture

#END

#================================================================

#FEATURES

[[MODULE:FEATURES:Screen Space UV]]
dd_start	lbl="LIGHTING"
---
[[MODULE:FEATURES:Ramp Shading LWRP]]
#sngl	lbl="Bypass Additional Lights Falloff"		kw=BYPASS_LIGHT_FALLOFF								tt="Bypass the point and spot light falloff calculation and only use the TCP2 ramp shading instead"
---
mult	lbl="Shadow Color Shading"		kw=All Lights|,Main Directional Light|SHADOW_COLOR_MAIN_DIR		tt="How the shadow color is applied, either globally across all lights, or based on the main directional light only (same behavior as in the legacy render pipeline)"	help="featuresreference/lighting/shadowcolorshading"
mult	lbl="Shadow Color (other lights)"	kw=Off|,Enabled|ENABLE_SHADOW_2ND_LIGHTS,Enabled (clamped)|ENABLE_SHADOW_2ND_LIGHTS_SAT		needs=SHADOW_COLOR_MAIN_DIR		indent		tt="Enable shadow color for additive lights; this means that the model will get some tint in unlit areas when using secondary lights."
mult	lbl="Clamp Lights Intensities"	kw=Off|,All Lights|CLAMP_LIGHTS_INTENSITY,Additional Lights|CLAMP_LIGHTS_INTENSITY_ADD		excl=SHADOW_COLOR_MAIN_DIR	indent	visibleIf=!SHADOW_COLOR_MAIN_DIR		tt="Ensure that the illumination received by all lights combined will never exceed a specific value (1 by default)"
mult	lbl="Shadow Color Mode"			kw=Multiply|,Replace Color|SHADOW_COLOR_LERP					tt="How to blend the shadow color on the model"		help="featuresreference/lighting/shadowcolormode"
[[MODULE:FEATURES:ShadowHSV]]
sngl	lbl="Apply Shadows before Ramp"		kw=ATTEN_AT_NDL												tt="Apply the shadow map attenuation before calculating the shading ramp"	help="featuresreference/lighting/applyshadowsbeforeramp"
---
[[MODULE:FEATURES:Specular]]
---
sngl	lbl="Emission"					kw=EMISSION		help="featuresreference/lighting/emission"		tt="Adds emission to the shader.  Modify the Emission property to change the source input (color, texture, etc.) and possibly use a mask."
---
[[MODULE:FEATURES:Rim Lighting]]
---
[[MODULE:FEATURES:Subsurface Scattering]]
---
subh	lbl="Reflections"		help="featuresreference/lighting/reflections"
sngl	lbl="Reflection Probes"			kw=GLOSSY_REFLECTIONS																					tt="Enable reflection probes or skybox reflection support"
[[MODULE:FEATURES:Reflection]]
sngl	lbl="Make Reflections Optional"	kw=REFLECTION_SHADER_FEATURE		needsOr=REFLECTION_CUBEMAP,GLOSSY_REFLECTIONS,PLANAR_REFLECTION		tt="Will make reflections optional in the material inspector, using a shader keyword"
---
[[MODULE:FEATURES:MatCap]]
---
subh	lbl="Ambient Lighting/Indirect Diffuse"		help="featuresreference/lighting/ambientlighting/indirectdiffuse"
sngl	lbl="Disable Ambient Lighting"	kw=NO_AMBIENT				tt="Disable ambient lighting calculated by the engine (as defined in the Lighting Settings)"
[[MODULE:FEATURES:Custom Ambient]]
sngl	lbl="Occlusion"					kw=OCCLUSION		tt="Adds ambient lighting occlusion support.  Modify the Occlusion property to change the source input (texture, vertex color, etc.)."
sngl	lbl="Single Indirect Color"		kw=AMBIENT_VIEW_DIR		tt="Use the view direction to sample Light Probes (ambient), instead of the normal direction.  This makes the ambient color to always be the same for the whole model, which can be interesting to get a flat effect while still getting dynamic ambient from the skybox for example."
sngl	lbl="Make Ambient Optional"		kw=AMBIENT_SHADER_FEATURE	tt="Will make ambient/indirect diffuse lighting optional in the material inspector, using a shader keyword"
---
dd_end
dd_start	lbl="SURFACE"
---
[[MODULE:FEATURES:Vertex Displacement]]
---
[[MODULE:FEATURES:AlbedoHSV]]
---
[[MODULE:FEATURES:Normal Mapping]]
sngl	lbl="Sample Normal Map first"	kw=WORLD_NORMAL_FROM_BUMP		needs=BUMP	indent		tt="Sample the Normal Map before other Shader Properties, so that the ones using the 'World Normal' value will use the normal mapped version"
---
[[MODULE:FEATURES:Texture Blending]]
---
[[MODULE:FEATURES:Triplanar]]
dd_end
dd_start	lbl="STYLIZATION"
---
[[MODULE:FEATURES:NdotL Stylization]]
[[MODULE:FEATURES:Sketch]]
[[MODULE:FEATURES:Outline]]
dd_end
dd_start	lbl="TERRAIN"
---
[[MODULE:FEATURES:Terrain]]
dd_end
dd_start	lbl="SPECIAL EFFECTS"
---
sngl	lbl="Custom Time"				kw=CUSTOM_TIME		help="featuresreference/specialeffects/customtime"		tt="Use a custom time variable controlled by scripts for all timed-related features (this will disable Editor animation preview).  The custom time variable is a float4 named '_CustomTime'.  By default, the Y component should reflect the actual time, and other components are multiples of that value:  x: time * 0.05  y: time  z: time * 2  w: time * 3"
sngl	lbl="Local"		indent			kw=CUSTOM_TIME_LOCAL	needs=CUSTOM_TIME		help="featuresreference/specialeffects/customtime"		tt="Define the custom time value locally to the material, instead of globally.  Use Shader.SetGlobalVector if global, else material.SetVector if local."
---
sngl	lbl="Silhouette Pass"			kw=PASS_SILHOUETTE		help="featuresreference/specialeffects/silhouettepass"		tt="Adds a silhouette pass, to show the object when it is behind obstacles"
sngl	lbl="URP Renderer Feature"		kw=SILHOUETTE_URP_FEATURE		needs=PASS_SILHOUETTE	indent		tt="Set the silhouette pass to be used as a Renderer Feature (see documentation)"
sngl	lbl="Stencil Mask"				kw=SILHOUETTE_STENCIL			needs=PASS_SILHOUETTE	indent		tt="Use the Stencil Buffer as a mask for the silhouette, to prevent transparency issues with non-convex meshes or multiple meshes"
---
[[MODULE:FEATURES:Wind]]
---
[[MODULE:FEATURES:Dissolve]]
---
[[MODULE:FEATURES:Vertical Fog]]
---
[[MODULE:FEATURES:Water]]
dd_end
dd_start	lbl="TRANSPARENCY/BLENDING"
---
sngl	lbl="Auto Optional Transparency"	kw=AUTO_TRANSPARENT_BLENDING	help="featuresreference/transparency/blending/autooptionaltransparency"		tt="Automatically handle transparency and add rendering mode options in the material inspector (opaque, transparent, fade), similar to how it is handled in the Hybrid Shader"
---
mult	lbl="Blending"			excl=AUTO_TRANSPARENT_BLENDING		kw=Off|,Alpha Blending|ALPHA_BLENDING,Alpha Blending Premultiplied|ALPHA_BLENDING_PREMULT,Additive|ADDITIVE_BLENDING,Multiplicative|MULTIPLICATIVE_BLENDING,Custom Blending|CUSTOM_BLENDING	toggles=SHADER_BLENDING		tt="Enable blending on the shader"	help="featuresreference/transparency/blending/blending/blendoperation"
mult_fs		lbl="Blend Operation"		kw=BLEND_OP		options=Default|,Custom|Constant,Material Property|Material Property		shader_property="Blend Operation"		tt="Enable blend operation control"		help="featuresreference/transparency/blending/blending/blendoperation"
warning	msgType=info	needs=CUSTOM_BLENDING	lbl="Look at the <b>Shader Properties</b> tab to setup the custom blending states."
space	space=4			needs=CUSTOM_BLENDING
#---
#sngl	lbl="Depth pre-pass"		kw=DEPTH_PREPASS		tt="Adds a depth only shader pass, to prevent parts of the mesh from being visible through itself."
#NOTE: not working currently, the additional pass seems to always be drawn after the UniversalForward one, never before, so we can't draw to the depth buffer beforehand.
---
sngl	lbl="Alpha Testing (Cutout)"	kw=ALPHA_TESTING			help="featuresreference/transparency/blending/alphatesting"
sngl	lbl="Alpha to Coverage"			kw=ALPHA_TO_COVERAGE		needs=ALPHA_TESTING							indent		tt="Enables Alpha to Coverage, which allows MSAA anti-aliasing to be used with alpha testing"
sngl	lbl="Disable alpha sharpening"	kw=ALPHA_TO_COVERAGE_RAW	needs=ALPHA_TESTING,ALPHA_TO_COVERAGE		indent=2	tt="Disables screen-space alpha sharpening, which is used to get proper anti-aliasing with Alpha to Coverage"
sngl	lbl="Transparency Dithering"	kw=ALPHA_TESTING_DITHERING	needs=ALPHA_TESTING							indent		tt="Enables dithering for alpha testing, i.e. making the transparency transition pixel by pixel in screen space"
mult	lbl="Dithering Pattern"			kw=Procedural 4x4 Pixels|,Procedural 8x8 Pixels|ALPHA_DITHER_8x8,Lookup Texture|ALPHA_DITHER_TEXTURE	indent=2	needs=ALPHA_TESTING,ALPHA_TESTING_DITHERING	tt="The dithering pattern to use, either calculated with maths or using a lookup texture."
dd_end
dd_start	lbl="SHADER STATES"
---
subh		lbl="Shader States"		help="featuresreference/shaderstates"
---
mult_fs		lbl="Face Culling (Double-sided)"	excl=AUTO_TRANSPARENT_BLENDING	kw=CULLING	options=Default|,Custom|Constant,Material Property|Material Property	shader_property="Face Culling"	tt="Enable face culling control"
mult		lbl="Backface Lighting"		kw=Off|,Flip Normal (Z)|BACKFACE_LIGHTING_Z,Flip Normal (XYZ)|BACKFACE_LIGHTING_XYZ		tt="Invert the normals on backfaces for accurate lighting calculation (this may not work properly with shadows and introduce other artifacts)"
---
mult_fs		lbl="Depth Write"	excl=AUTO_TRANSPARENT_BLENDING		kw=ZWRITE	options=Default|,Custom|Constant,Material Property|Material Property	shader_property="Depth Write"	tt="Enable depth write (ZWrite) value control"
mult_fs		lbl="Depth Test"										kw=ZTEST	options=Default|,Custom|Constant,Material Property|Material Property	shader_property="Depth Test"	tt="Enable depth test (ZTest) control"
---
mult	lbl="Stencil"						kw=Off|,Custom|STENCIL				tt="Enable stencil control (see the Properties tab)"
warning	msgType=info	needs=STENCIL	lbl="Look at the <b>Shader Properties</b> tab to setup the stencil states."
space	space=4			needs=STENCIL
---
keyword	lbl="Shader Target"	kw=SHADER_TARGET	forceKeyword=true	values=2.0 (Old hardware)|2.0,2.5 (Low-end mobile devices)|2.5,3.0 (Recommended default)|3.0,3.5|3.5,4.0|4.0,4.5|4.5,4.6|4.6,5.0|5.0		default=2
warning	msgType=info		lbl="Use <b>Shader Target 2.5</b> for maximum compatibility across mobile devices (OpenGL ES 2.0 with no extensions).  Increase the number if the shader fails to compile (not enough instructions or interpolators)."
dd_end
dd_start	lbl="OPTIONS"
---
sngl		lbl="Disable Shadow Receiving"			kw=DISABLE_SHADOW_RECEIVING
sngl		lbl="Disable Shadow Casting"			kw=DISABLE_SHADOW_CASTING
sngl		lbl="Disable Additional Lights"			kw=DISABLE_ADDITIONAL_LIGHTS
sngl		lbl="Enable Decals (URP 12+)"			kw=ENABLE_DECALS		toggles=ENABLE_DEPTH_NORMALS_PASS
sngl		lbl="Enable Light Cookies (URP 12+)"	kw=ENABLE_COOKIES
sngl		lbl="Enable SSAO"						kw=SSAO
sngl		lbl="Enable Fog"						kw=FOG
sngl		lbl="Enable Lightmaps"					kw=ENABLE_LIGHTMAP
sngl		lbl="Enable LOD Dither Crossfade"		kw=ENABLE_DITHER_LOD
mult		lbl="Dithering Pattern"					kw=4x4 Pixels|,8x8 Pixels|DITHER_LOD_8x8	indent	tt="The size of the dithering pattern to use, 8x8 looks more progressive than 4x4."
sngl		lbl="Disable Dynamic Batching"			kw=DISABLE_BATCHING				tt="Disable dynamic batching support for this shader"
sngl		lbl="Enable Depth Normals Pass"			kw=ENABLE_DEPTH_NORMALS_PASS
sngl		lbl="Enable Meta Pass"					kw=ENABLE_META_PASS				tt="Enable meta pass: it is needed to be able to bake lighting but can prevent SRP Batcher compatibility."
sngl		lbl="Enable Light Layers (URP 12+)"		kw=ENABLE_LIGHT_LAYERS
sngl		lbl="Enable Rendering Layers (URP 14+)"	kw=ENABLE_RENDERING_LAYERS
sngl		lbl="Enable Forward+ (URP 14+)"			kw=ENABLE_FORWARD_PLUS
sngl		lbl="Enable DOTS Instancing (URP 14+)"	kw=ENABLE_DOTS_INSTANCING
space	space=4
subh	lbl="GPU Instancing Options"
flag	lbl="Assume Uniform Scaling"	kw=assumeuniformscaling		block="pragma_gpu_instancing"		tt="Use this to instruct Unity to assume that all the instances have uniform scalings (the same scale for all X, Y and Z axes)."
flag	lbl="No LOD Fade"				kw=nolodfade				block="pragma_gpu_instancing"		tt="Use this to prevent Unity from applying GPU Instancing to LOD fade values."
flag	lbl="No Light Probe"			kw=nolightprobe				block="pragma_gpu_instancing"		tt="Use this to prevent Unity from applying GPU Instancing to Light Probe values (including their occlusion data). This is useful for performance if you are absolutely sure that there are no GameObjects using both GPU Instancing and Light Probes."
flag	lbl="No Lightmap"				kw=nolightmap				block="pragma_gpu_instancing"		tt="Use this to prevent Unity from applying GPU Instancing to Lightmap ST (atlas information) values. This is useful for performance if you are absolutely sure that there are no GameObjects using both GPU Instancing and lightmaps."
mult	lbl="Max Count"					kw=Off|,Define|GPU_INSTANCING_MAX_COUNT_DEFINE,Force|GPU_INSTANCING_MAX_COUNT_FORCE		tt="Use this to prevent Unity from applying GPU Instancing to Lightmap ST (atlas information) values. This is useful for performance if you are absolutely sure that there are no GameObjects using both GPU Instancing and lightmaps."
int		lbl="Max Count Value"			kw=GPU_INSTANCING_MAX_COUNT_VALUE	indent	needsOr=GPU_INSTANCING_MAX_COUNT_DEFINE,GPU_INSTANCING_MAX_COUNT_FORCE	default=50	min=1	max=2147483647
dd_end
dd_start	lbl="THIRD PARTY PLUGINS"
---
[[MODULE:FEATURES:VertExmotion]]
---
[[MODULE:FEATURES:CurvedWorld]]
dd_end

#END

#================================================================

#PROPERTIES_NEW
header		Main Properties
/// IF !CUSTOM_ALBEDO
color_rgba	Albedo				fragment, imp(texture, label = "Albedo", variable = "_BaseMap", drawer = "[MainTexture]", default = white, tiling_offset = true, global = true)
///
[[MODULE:PROPERTIES_NEW:AlbedoHSV]]
color_rgba	Main Color			fragment, imp(color, label = "Color", variable = "_BaseColor", default = (1,1,1,1)), help = "An adjustable color multiplied with the final albedo color.  Set it to a white color constant if you don't plan on using it, to improve the shader performance."
/// IF !CUSTOM_ALBEDO
float		Alpha				fragment, imp(shader_property_ref, reference = Albedo, swizzle = A), imp(shader_property_ref, reference = Main Color, swizzle = A), help = "The output alpha value, generally only needed when using alpha blending transparency or alpha testing (cutout)",
///
/// IF ALPHA_TESTING
	/// IF ALPHA_TESTING_DITHERING
		/// IF ALPHA_DITHER_TEXTURE
float		Dithering Texture	fragment, imp(texture, label = "Alpha Dithering", variable = "_DitherTex", default = gray, uv_screenspace = true, scale_texel = true)
		///
	/// ELSE
float		Cutoff				fragment, imp(range, label = "Alpha Cutoff", default = 0.5, min = 0, max = 1), help = "The threshold value at which point pixels are discarded when using alpha testing (cutout)"
	///
///
float		Ambient Intensity	lighting, imp(constant, label = "Ambient Intensity", default = 1)

[[MODULE:PROPERTIES_NEW:Ramp Shading LWRP]]
color		Highlight Color		lighting, imp(color, label = "Highlight Color", variable = "_HColor", default = (0.75,0.75,0.75,1))
color		Shadow Color		lighting, imp(color, label = "Shadow Color", variable = "_SColor", default = (0.2,0.2,0.2,1))
[[MODULE:PROPERTIES_NEW:ShadowHSV]]
/// IF CLAMP_LIGHTS_INTENSITY || CLAMP_LIGHTS_INTENSITY_ADD
float3		Max Light Intensity				lighting, imp(constant, default = (1.0, 1.0, 1.0))
///

[[MODULE:PROPERTIES_NEW:Specular]]
[[MODULE:PROPERTIES_NEW:Rim Lighting]]
/// IF GLOSSY_REFLECTIONS || PLANAR_REFLECTION
	header		Reflections
	color		Reflection Color			lighting, imp(color, label = "Color", default = (1, 1, 1, 1))
	/// IF GLOSSY_REFLECTIONS
	float		Reflection Smoothness		lighting, imp(range, label = "Smoothness", default = 0.5, min = 0, max = 1)
	///
///
/// IF EMISSION
	header		Emission
	color		Emission									fragment, imp(color, label = "Emission Color", default = (0,0,0,1), hdr = true)
///
/// IF OCCLUSION
	header		Occlusion
	float		Occlusion									lighting, imp(shader_property_ref, reference = Albedo, swizzle = A)
///
[[MODULE:PROPERTIES_NEW:Subsurface Scattering]]
[[MODULE:PROPERTIES_NEW:Reflection]]
[[MODULE:PROPERTIES_NEW:MatCap]]
[[MODULE:PROPERTIES_NEW:Custom Ambient]]
[[MODULE:PROPERTIES_NEW:Terrain]]
[[MODULE:PROPERTIES_NEW:Vertex Displacement]]
[[MODULE:PROPERTIES_NEW:Normal Mapping]]
[[MODULE:PROPERTIES_NEW:Triplanar]]
[[MODULE:PROPERTIES_NEW:Texture Blending]]
[[MODULE:PROPERTIES_NEW:NdotL Stylization]]
[[MODULE:PROPERTIES_NEW:Sketch]]
[[MODULE:PROPERTIES_NEW:Outline]]
[[MODULE:PROPERTIES_NEW:Wind]]
[[MODULE:PROPERTIES_NEW:Water]]
[[MODULE:PROPERTIES_NEW:Depth Texture]]
[[MODULE:PROPERTIES_NEW:Dissolve]]
[[MODULE:PROPERTIES_NEW:Vertical Fog]]
/// IF PASS_SILHOUETTE
		header			Silhouette Pass
		color_rgba		Silhouette Color				lighting, imp(color, label = "Silhouette Color", default = (0,0,0,0.33))
	/// IF SILHOUETTE_STENCIL
		fixed_function_float	Silhouette Stencil Reference	fixed, imp(constant, label = "Silhouette Stencil Reference", default = 1)
	///
		fixed_function_enum		Silhouette Blend Source			fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.BlendFactor, default = "SrcAlpha")
		fixed_function_enum		Silhouette Blend Destination	fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.BlendFactor, default = "OneMinusSrcAlpha")
///
/// IF CUSTOM_BLENDING || BLEND_OP
header					Blending
///
/// IF CUSTOM_BLENDING
fixed_function_enum		Blend Source					fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.BlendFactor, default = "SrcAlpha")
fixed_function_enum		Blend Destination				fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.BlendFactor, default = "OneMinusSrcAlpha")
///
/// IF BLEND_OP
fixed_function_enum		Blend Operation					fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.BlendOperation, default = "Add")
///
/// IF ZWRITE || ZTEST || CULLING
header		Shader States
///
/// IF ZWRITE
fixed_function_enum		Depth Write						fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.DepthWrite, default = "On")
///
/// IF ZTEST
fixed_function_enum		Depth Test						fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.CompareFunction, default = "LEqual")
///
/// IF CULLING
fixed_function_enum		Face Culling					fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.Culling, default = "Back")
///
/// IF STENCIL
		header	Stencil
		fixed_function_float		Stencil Reference	fixed, imp(constant, label = "Reference", default = 0)
		fixed_function_float		Stencil Read Mask	fixed, imp(constant, label = "Read Mask", default = 255)
		fixed_function_float		Stencil Write Mask	fixed, imp(constant, label = "Write Mask", default = 255)
	/// IF !STENCIL_DOUBLE_SIDED
		fixed_function_enum			Stencil Comparison	fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.CompareFunction, default = "Always")
		fixed_function_enum			Stencil Pass		fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
		fixed_function_enum			Stencil Fail		fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
		fixed_function_enum			Stencil Depth Fail	fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
	/// ELSE
		fixed_function_enum			Stencil Front Comparison	fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.CompareFunction, default = "Always")
		fixed_function_enum			Stencil Front Pass			fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
		fixed_function_enum			Stencil Front Fail			fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
		fixed_function_enum			Stencil Front Depth Fail	fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")

		fixed_function_enum			Stencil Back Comparison		fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.CompareFunction, default = "Always")
		fixed_function_enum			Stencil Back Pass			fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
		fixed_function_enum			Stencil Back Fail			fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
		fixed_function_enum			Stencil Back Depth Fail		fixed, imp(enum, enum_type = ToonyColorsPro.ShaderGenerator.StencilOperation, default = "Keep")
	///
///
header	Third Party
[[MODULE:PROPERTIES_NEW:VertExmotion]]
[[MODULE:PROPERTIES_NEW:CurvedWorld]]
header			Hooks					"Hooks are special Shader Properties that expose variables from the shader code that can then be freely modified"
float3			Vertex Position			vertex, label = "Vertex Position (Object Space)", imp(hook, label = "input.vertex.xyz", toggles = HOOK_VERTEX_POSITION), help = "The object-space vertex position, e.g. to make your own vertex displacement function."
float3			Vertex Position World	vertex, label = "Vertex Position (World Space)", imp(hook, label = "worldPos.xyz", toggles = HOOK_VERTEX_POSITION_WORLD), help = "The world-space vertex position."
float3			Main Light Direction			lighting, label = "Main Light Direction", imp(hook, label = "lightDir", toggles = HOOK_MAIN_LIGHT_DIR), help = "The direction of the main directional light."
float3			Additional Lights Direction		lighting, label = "Additional Lights Direction", imp(hook, label = "lightDir", toggles = HOOK_OTHER_LIGHTS_DIR), help = "The direction of additional lights."
color			Main Light Color				lighting, label = "Main Light Color", imp(hook, label = "lightColor", toggles = HOOK_MAIN_LIGHT_COLOR), help = "The color of the main directional light."
color			Additional Lights Color			lighting, label = "Additional Lights Color", imp(hook, label = "lightColor", toggles = HOOK_OTHER_LIGHTS_COLOR), help = "The color of additional lights."
color			Shading Ramp					lighting, label = "Shading Ramp", imp(hook, label = "ramp", toggles = HOOK_RAMP), help = "The colored ramp calculated, depending on the ramp settings, with the highlight and shadow colors."
float			Main Light Attenuation			lighting, label = "Main Light Shadows Attenuation", imp(hook, label = "atten", toggles = HOOK_MAIN_LIGHT_ATTEN), help = "The attenuation of the main directional light (shadow map)."
float			Additional Lights Shadows		lighting, label = "Additional Lights Shadows Attenuation", imp(hook, label = "light.shadowAttenuation", toggles = HOOK_OTHER_LIGHTS_SHADOWMAP), help = "The shadow map part of the additional lights attenuation."
float			Additional Lights Distance		lighting, label = "Additional Lights Distance Attenuation", imp(hook, label = "light.distanceAttenuation", toggles = HOOK_OTHER_LIGHTS_DIST), help = "The distance part of the additional lights attenuation."
float			Additional Lights Attenuation	lighting, label = "Additional Lights Combined Attenuation", imp(hook, label = "atten", toggles = HOOK_OTHER_LIGHTS_ATTEN), help = "The attenuation of additional lights (shadow map and distance attenuation)."
color			Final Albedo			fragment, imp(hook, label = "albedo.rgb", toggles = HOOK_FINAL_ALBEDO), help = "The final albedo used by the shader before lighting."
color			Final Ambient			fragment, imp(hook, label = "indirectDiffuse.rgb", toggles = HOOK_FINAL_AMBIENT), help = "The final ambient color used by the shader, with any modifiers applied (e.g. Occlusion)"
color			Final Color				fragment, imp(hook, label = "color.rgb", toggles = HOOK_FINAL_COLOR), help = "The final color returned by the shader, after having processed all lighting and effects."
/// IF TERRAIN_SHADER
color_rgba		Terrain Splat Control	fragment, imp(hook, label = "terrain_splat_control.rgba", toggles = HOOK_SPLAT_CONTROL), help = "The terrain's internal splat control texture, before it is used to sample each layer."
///
/// IF TERRAIN_SHADER && TERRAIN_SHADER_8_LAYERS
color_rgba		Terrain Splat Control 1	fragment, imp(hook, label = "terrain_splat_control.rgba", toggles = HOOK_SPLAT_CONTROL_1), help = "The terrain's internal splat control 1 texture, before it is used to sample each layer."
///
header		Misc
/// IF USE_NDV_MIN_MAX_VERT
	float			NDV Min Vert		fragment, imp(range, label = "NDV Min (Vertex)", default = 0.5, min = 0, max = 2)
	float			NDV Max Vert		fragment, imp(range, label = "NDV Max (Vertex)", default = 1.0, min = 0, max = 2)
///
/// IF USE_NDV_MIN_MAX_FRAG
	float			NDV Min Frag		fragment, imp(range, label = "NDV Min", default = 0.5, min = 0, max = 2)
	float			NDV Max Frag		fragment, imp(range, label = "NDV Max", default = 1.0, min = 0, max = 2)
///
#END

#================================================================

#KEYWORDS

# SRP feature
feature_on		LWRP
feature_on		URP

# features
/// IF GLOSSY_REFLECTIONS || AMBIENT_VIEW_DIR
	feature_on		USE_VIEW_DIRECTION_FRAGMENT
///

/// IF ENABLE_DITHER_LOD || (ALPHA_TESTING && ALPHA_TESTING_DITHERING && !ALPHA_DITHER_TEXTURE)
	feature_on		USE_DITHERING_FUNCTION
///

/// IF USE_NDV_VERTEX
	feature_on		USE_WORLD_NORMAL_VERTEX
	feature_on		USE_VIEW_DIRECTION_VERTEX
///

/// IF USE_NDV_FRAGMENT
	feature_on		USE_WORLD_NORMAL_FRAGMENT
	feature_on		USE_VIEW_DIRECTION_FRAGMENT
	feature_on		USE_WORLD_POSITION_FRAGMENT
///

/// IF BACKFACE_LIGHTING_Z || BACKFACE_LIGHTING_XYZ
	feature_on		USE_VFACE
///

/// IF USE_VIEW_DIRECTION_FRAGMENT
	feature_on		USE_WORLD_POSITION_FRAGMENT
///

/// IF HOOK_VERTEX_POSITION_WORLD
	feature_on		APPLY_WORLD_POSITION
///

# queue
/// IF SHADER_BLENDING || OUTLINE_BLENDING
	feature_on		QUEUE_TRANSPARENT
///
/// IF ALPHA_TESTING
	feature_on		QUEUE_ALPHATEST
///

# rendertype
/// IF CURVED_WORLD
	/// IF ALPHA_TESTING
		set_keyword		RENDER_TYPE		CurvedWorld_TransparentCutout
	/// ELSE
		set_keyword		RENDER_TYPE		CurvedWorld_Opaque
	///
/// ELSE
	/// IF ALPHA_TESTING
		set_keyword		RENDER_TYPE		TransparentCutout
	/// ELSE
		set_keyword		RENDER_TYPE		Opaque
	///
///

# terrain
/// IF TERRAIN_SHADER
	feature_on		CUSTOM_ALBEDO
	flag_on			addshadow

	flag_on:pragma_gpu_instancing		assumeuniformscaling
	flag_on:pragma_gpu_instancing		nomatrices
	flag_on:pragma_gpu_instancing		nolightprobe
	flag_on:pragma_gpu_instancing		nolightmap
	flag_on:pragma_gpu_instancing		forwardadd

	/// IF TERRAIN_ADDPASS
	flag_off	alpha:blend
	flag_off	alpha:premul
	flag_on		decal:add
	flag_on		nometa
	///
///

[[MODULE:KEYWORDS]]

#END

#================================================================

/// IF TERRAIN_SHADER && TERRAIN_ADDPASS
// Terrain AddPass shader:
// This shader is used if your terrain uses more than 4 texture layers.
// It will draw the additional texture layers additively, by groups of 4 layers.

Shader "Hidden/@%SHADER_NAME%@-AddPass"
/// ELIF TERRAIN_SHADER && TERRAIN_BASEPASS
// Terrain BasePass shader:
// This shader is used when the terrain is viewed from the "Base Distance" setting.
// It uses low resolution generated textures from the "BaseGen" shader to draw the terrain entirely,
// thus preventing to perform the full splat map blending code to increase performances.

Shader "Hidden/@%SHADER_NAME%@-BasePass"
/// ELIF TERRAIN_SHADER && TERRAIN_BASEGEN
// Terrain BaseGen shader:
// This shader is used to generate full blended terrain maps at a low resolution, that will show if the camera is at the "Base Distance" setting of the terrain.
// This is a LOD-like system that prevents doing the full splat maps blending when the terrain is viewed from far away, and instead sample those generated maps only once.

Shader "Hidden/@%SHADER_NAME%@-BaseGen"
/// ELSE
Shader "@%SHADER_NAME%@"
///
{
/// IF (TERRAIN_SHADER && TERRAIN_BASEGEN && TERRAIN_BASEGEN_SAME_SHADER) || !TERRAIN_BASEGEN
	Properties
	{
/// IF AUTO_TRANSPARENT_BLENDING
		[Enum(Front, 2, Back, 1, Both, 0)] _Cull ("Render Face", Float) = 2.0
		[TCP2ToggleNoKeyword] _ZWrite ("Depth Write", Float) = 1.0
		[HideInInspector] _RenderingMode ("rendering mode", Float) = 0.0
		[HideInInspector] _SrcBlend ("blending source", Float) = 1.0
		[HideInInspector] _DstBlend ("blending destination", Float) = 0.0
		[TCP2Separator]

///
		[[INJECTION_POINT:Properties/Start]]
		[TCP2HeaderHelp(Base)]
		[[PROP:Main Color]]
		[[PROP:Highlight Color]]
		[[PROP:Shadow Color]]
		[[MODULE:PROPERTIES_BLOCK:ShadowHSV]]
/// IF !CUSTOM_ALBEDO
		[[PROP:Albedo]]
		[[PROP:Alpha]]
///
		[[MODULE:PROPERTIES_BLOCK:AlbedoHSV]]
/// IF ALPHA_TESTING
	/// IF ALPHA_TESTING_DITHERING
		/// IF ALPHA_DITHER_TEXTURE
		[[PROP:Dithering Texture]]
		///
	/// ELSE
		[[PROP:Cutoff]]
	///
///
/// IF OCCLUSION
		[[PROP:Occlusion]]
///
		[TCP2Separator]

		[[MODULE:PROPERTIES_BLOCK:Ramp Shading LWRP]]
		[[MODULE:PROPERTIES_BLOCK:Terrain]]
		[[MODULE:PROPERTIES_BLOCK:Specular]]
/// IF EMISSION

		[TCP2HeaderHelp(Emission)]
		[[PROP:Emission]]
		[TCP2Separator]
///
		[[MODULE:PROPERTIES_BLOCK:Rim Lighting]]
/// IF (GLOSSY_REFLECTIONS || REFLECTION_CUBEMAP || PLANAR_REFLECTION)

		[TCP2HeaderHelp(Reflections)]
	/// IF REFLECTION_SHADER_FEATURE
		[Toggle(TCP2_REFLECTIONS)] _UseReflections ("Enable Reflections", Float) = 0
	///
///
/// IF GLOSSY_REFLECTIONS || PLANAR_REFLECTION
		[[PROP:Reflection Color]]
///
/// IF GLOSSY_REFLECTIONS
		[[PROP:Reflection Smoothness]]
///
		[[MODULE:PROPERTIES_BLOCK:Reflection]]
/// IF (GLOSSY_REFLECTIONS || REFLECTION_CUBEMAP || PLANAR_REFLECTION)
		[TCP2Separator]
///
		[[MODULE:PROPERTIES_BLOCK:Subsurface Scattering]]
		[[MODULE:PROPERTIES_BLOCK:MatCap]]
	#if_not_empty
		[TCP2HeaderHelp(Ambient Lighting)]
	#start_not_empty_block
/// IF AMBIENT_SHADER_FEATURE
		[Toggle(TCP2_AMBIENT)] _UseAmbient ("Enable Ambient/Indirect Diffuse", Float) = 0
///
		[[PROP:Ambient Intensity]]
		[[MODULE:PROPERTIES_BLOCK:Custom Ambient]]
	#end_not_empty_block
		[TCP2Separator]
	#end_not_empty
		[[MODULE:PROPERTIES_BLOCK:Vertex Displacement]]
		[[MODULE:PROPERTIES_BLOCK:Triplanar]]
		[[MODULE:PROPERTIES_BLOCK:Normal Mapping]]
		[[MODULE:PROPERTIES_BLOCK:Texture Blending]]
		[[MODULE:PROPERTIES_BLOCK:NdotL Stylization]]
		[[MODULE:PROPERTIES_BLOCK:Sketch]]
		[[MODULE:PROPERTIES_BLOCK:Wind]]
		[[MODULE:PROPERTIES_BLOCK:Water]]
		[[MODULE:PROPERTIES_BLOCK:Dissolve]]
		[[MODULE:PROPERTIES_BLOCK:Vertical Fog]]
/// IF PASS_SILHOUETTE
		[TCP2HeaderHelp(Silhouette Pass)]
		[[PROP:Silhouette Color]]
		[[PROP:Silhouette Blend Source]]
		[[PROP:Silhouette Blend Destination]]
		[TCP2Separator]
///
		[[MODULE:PROPERTIES_BLOCK:Outline]]
		[[MODULE:PROPERTIES_BLOCK:NoTile Sampling]]
		[[MODULE:PROPERTIES_BLOCK:Triplanar Sampling]]
/// IF USE_NDV_MIN_MAX_VERT
		[[PROP:NDV Min Vert]]
		[[PROP:NDV Max Vert]]
		[TCP2Separator]
///
/// IF USE_NDV_MIN_MAX_FRAG
		[[PROP:NDV Min Frag]]
		[[PROP:NDV Max Frag]]
		[TCP2Separator]
///
	#if_not_empty
	#start_not_empty_block
		[[PROP:Vertex Position]]
		[[PROP:Vertex Position World]]
		[[PROP:Final Color]]
	#end_not_empty_block
		[TCP2Separator]
	#end_not_empty
/// IF STENCIL
		[TCP2HeaderHelp(Stencil)]
		[[PROP:Stencil Reference]]
		[[PROP:Stencil Read Mask]]
		[[PROP:Stencil Write Mask]]
	/// IF !STENCIL_DOUBLE_SIDED
		[[PROP:Stencil Comparison]]
		[[PROP:Stencil Pass]]
		[[PROP:Stencil Fail]]
		[[PROP:Stencil Depth Fail]]
	/// ELSE
		[[PROP:Stencil Front Comparison]]
		[[PROP:Stencil Front Pass]]
		[[PROP:Stencil Front Fail]]
		[[PROP:Stencil Front Depth Fail]]
		[[PROP:Stencil Back Comparison]]
		[[PROP:Stencil Back Pass]]
		[[PROP:Stencil Back Fail]]
		[[PROP:Stencil Back Depth Fail]]
	///
		[TCP2Separator]
///
		[[PROPERTIES]]

		[[MODULE:PROPERTIES_BLOCK:CurvedWorld]]

/// IF CUSTOM_TIME && CUSTOM_TIME_LOCAL
		_CustomTime ("Custom Time", Vector) = (0.05, 1, 2, 3)
///
		[[INJECTION_POINT:Properties/End]]

/// IF !DISABLE_SHADOW_RECEIVING
		[ToggleOff(_RECEIVE_SHADOWS_OFF)] _ReceiveShadowsOff ("Receive Shadows", Float) = 1

///
		// Avoid compile error if the properties are ending with a drawer
		[HideInInspector] __dummy__ ("unused", Float) = 0
	}

	SubShader
	{
		Tags
		{
/// IF DISABLE_BATCHING
			"DisableBatching" = "True"
///
			"RenderPipeline" = "UniversalPipeline"
# Queues are ordered from highest to lowest in terms of priority
/// IF TERRAIN_SHADER
			"RenderType" = "Opaque"
	/// IF TERRAIN_ADDPASS
			"Queue"="Geometry-99"
			"IgnoreProjector"="True"
	/// ELSE
			"Queue"="Geometry-100"
	///
/// ELIF QUEUE_TRANSPARENT
			"RenderType"="Transparent"
			"Queue"="Transparent"
			"IgnoreProjectors"="True"
/// ELIF QUEUE_ALPHATEST
			"RenderType"="@%RENDER_TYPE%@"
			"Queue"="AlphaTest"
/// ELIF PASS_SILHOUETTE
			"RenderType"="@%RENDER_TYPE%@"
			"Queue"="Geometry+10" // Make sure that the objects are rendered later to avoid sorting issues with the transparent silhouette
/// ELSE
			"RenderType"="@%RENDER_TYPE%@"
///
/// IF TERRAIN_SHADER
			"TerrainCompatible"="True"
	/// IF TERRAIN_SHADER_8_LAYERS
			"SplatCount"="8"
	///
///
			[[INJECTION_POINT:SubShader/Tags]]
		}

		[[INJECTION_POINT:SubShader/Shader States]]

		HLSLINCLUDE
		#define fixed half
		#define fixed2 half2
		#define fixed3 half3
		#define fixed4 half4

		#if UNITY_VERSION >= 202020
			#define URP_10_OR_NEWER
		#endif
		#if UNITY_VERSION >= 202120
			#define URP_12_OR_NEWER
		#endif
		#if UNITY_VERSION >= 202220
			#define URP_14_OR_NEWER
		#endif

/// IF UNITY_2019_4
		// Texture/Sampler abstraction
		#define TCP2_TEX2D_WITH_SAMPLER(tex)						TEXTURE2D(tex); SAMPLER(sampler##tex)
		#define TCP2_TEX2D_NO_SAMPLER(tex)							TEXTURE2D(tex)
		#define TCP2_TEX2D_SAMPLE(tex, samplertex, coord)			SAMPLE_TEXTURE2D(tex, sampler##samplertex, coord)
		#define TCP2_TEX2D_SAMPLE_LOD(tex, samplertex, coord, lod)	SAMPLE_TEXTURE2D_LOD(tex, sampler##samplertex, coord, lod)
///

/// IF ENABLE_DOTS_INSTANCING
		#include_with_pragmas "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DOTS.hlsl"
///
		#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
		#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
/// IF ENABLE_DECALS
		#if defined(URP_12_OR_NEWER)
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DBuffer.hlsl"
		#endif
///
		[[MODULE:VARIABLES:Depth Texture]]

/// IF TERRAIN_SHADER

		// Terrain
	/// IF BUMP
		#define TERRAIN_INSTANCED_PERPIXEL_NORMAL
	///
	/// IF TERRAIN_ADDPASS
		#define TERRAIN_SPLAT_ADDPASS
	///
	/// IF TERRAIN_BASEPASS
		#define TERRAIN_BASE_PASS
	///
///

/// IF CUSTOM_TIME

		// Custom time variable overriding the built-in one
		#define _Time _CustomTime
///

		[[INJECTION_POINT:Include Files]]

		// Uniforms

		[[VARIABLES_OUTSIDE_CBUFFER_INCLUDE]]
		[[MODULE:VARIABLES_OUTSIDE_CBUFFER]]
		[[INJECTION_POINT:Variables/Outside CBuffer]]

		CBUFFER_START(UnityPerMaterial)
/// IF CUSTOM_TIME
			float4 _CustomTime;
///
			[[VARIABLES_INCLUDE]]
			[[MODULE:VARIABLES]]
			[[INJECTION_POINT:Variables/Inside CBuffer]]
		CBUFFER_END

/// IF ENABLE_DOTS_INSTANCING
		#if defined(UNITY_DOTS_INSTANCING_ENABLED)

		[[VARIABLES_DOTS_INSTANCING_INCLUDE]]

		#endif
///

		#if defined(UNITY_INSTANCING_ENABLED) || defined(UNITY_DOTS_INSTANCING_ENABLED)
			#define unity_ObjectToWorld UNITY_MATRIX_M
			#define unity_WorldToObject UNITY_MATRIX_I_M
		#endif

		[[VARIABLES_GPU_INSTANCING_INCLUDE]]

		[[MODULE:FUNCTIONS]]

/// IF ENABLE_DECALS
		#if defined(_DBUFFER)
			// Derived from 'ApplyDecal' in URP's DBuffer.hlsl, directly fetch the decal data so that we can blend it accordingly
			DecalSurfaceData GetDecals(float4 positionCS)
			{
				FETCH_DBUFFER(DBuffer, _DBufferTexture, int2(positionCS.xy));

				DecalSurfaceData decalSurfaceData = (DecalSurfaceData)0;
				DECODE_FROM_DBUFFER(DBuffer, decalSurfaceData);

				#if !defined(_DBUFFER_MRT3)
					decalSurfaceData.MAOSAlpha = 0;
				#endif

				return decalSurfaceData;
			}
		#endif
///

		[[INJECTION_POINT:Functions]]

	#if_not_empty
		// Curved World 2020
	#start_not_empty_block
		[[MODULE:SHADER_FEATURES_BLOCK:CurvedWorld]]
	#end_not_empty_block
	#end_not_empty

		ENDHLSL

#PASS
/// IF OUTLINE

		// Outline Include
		HLSLINCLUDE

/// IF FOG
		#pragma multi_compile_fog
///
/// IF ENABLE_DITHER_LOD
		#pragma multi_compile _ LOD_FADE_CROSSFADE
///

		[[INJECTION_POINT:Outline Pass/Pragma]]

		struct appdata_outline
		{
			float4 vertex : POSITION;
			float3 normal : NORMAL;
			[[VERTEX_INPUT_TEXCOORDS]]
			[[VERTEX_INPUT_OUTLINE]]
	/// IF USE_VERTEX_COLORS_VERT
			fixed4 vertexColor : COLOR;
	/// ELSE
		#if TCP2_COLORS_AS_NORMALS
			float4 vertexColor : COLOR;
		#endif
	///
	/// IF !TERRAIN_SHADER
		/// IF !USE_TANGENT_VERT && !USE_TANGENT_FRAGMENT && !VERTEXMOTION_NORMAL && !CURVED_WORLD_NORMAL
		#if TCP2_TANGENT_AS_NORMALS
		///
			float4 tangent : TANGENT;
		/// IF !USE_TANGENT_VERT && !USE_TANGENT_FRAGMENT && !VERTEXMOTION_NORMAL && !CURVED_WORLD_NORMAL
		#endif
		///
	///
			[[INJECTION_POINT:Outline Pass/Attributes]]
			UNITY_VERTEX_INPUT_INSTANCE_ID
		};

		struct v2f_outline
		{
			float4 vertex : SV_POSITION;
			[[INPUT_STRUCT_SEMANTICS:0]]
#INPUT_VARIABLES
	/// IF USE_VERTEX_COLORS_FRAG
			fixed4 vertexColor;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT
			float4 screenPosition;
	///
	/// IF FOG
			float fogFactor;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
			float3 worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
			float3 worldNormal;
	///
	/// IF USE_OBJECT_POSITION_FRAGMENT
			float3 objPos;
	///
	/// IF USE_OBJECT_NORMAL_FRAGMENT
			float3 objNormal;
	///
			[[MODULE:INPUT:Outline]]
#END
			[[INJECTION_POINT:Outline Pass/Varyings]]
			UNITY_VERTEX_INPUT_INSTANCE_ID
			UNITY_VERTEX_OUTPUT_STEREO
		};

#INPUT = v
#OUTPUT = output
#VERTEX
		v2f_outline vertex_outline (appdata_outline v)
		{
			v2f_outline output = (v2f_outline)0;

			UNITY_SETUP_INSTANCE_ID(v);
			UNITY_TRANSFER_INSTANCE_ID(v, output);
			UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

			[[INJECTION_POINT:Outline Pass/Vertex Shader/Start]]

	/// IF USE_WORLD_POSITION_UV_VERTEX
			float3 worldPosUv = mul(unity_ObjectToWorld, v.vertex).xyz;
	///
	/// IF USE_WORLD_NORMAL_UV_VERTEX || USE_WORLD_NORMAL_FRAGMENT
			float3 worldNormalUv = mul(unity_ObjectToWorld, float4(v.normal, 1.0)).xyz;
	///
			[[VERTEX_TEXCOORDS]]
			[[SAMPLE_CUSTOM_PROPERTIES]]
			[[SAMPLE_SHADER_PROPERTIES]]

			[[MODULE:VERTEX:VertExmotion:LWRP(v.vertex, v.normal, v.tangent)]]
			[[MODULE:VERTEX:CurvedWorld(v.vertex, v.normal, v.tangent)]]

			[[MODULE:VERTEX:Vertex Displacement(v.vertex)]]
	/// IF HOOK_VERTEX_POSITION
			v.vertex.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position]];
	///
	/// IF HOOK_VERTEX_POSITION_WORLD || APPLY_WORLD_POSITION || USE_WORLD_POSITION_FRAGMENT
			float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
	///
			[[MODULE:VERTEX:Vertex Displacement:WORLD(worldPos)]]
	/// IF HOOK_VERTEX_POSITION_WORLD
			worldPos.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position World]];
	///
			[[MODULE:VERTEX:Wind(worldPos.xyz)]]
	/// IF APPLY_WORLD_POSITION
			v.vertex.xyz = mul(unity_WorldToObject, float4(worldPos, 1)).xyz;
	///
			[[MODULE:VERTEX:Water(v.vertex, worldPos, v.normal)]]
	/// IF USE_OBJECT_POSITION_FRAGMENT
			output.[[INPUT_VALUE:objPos]] = v.vertex.xyz;
	///
	/// IF USE_OBJECT_NORMAL_FRAGMENT
			output.[[INPUT_VALUE:objNormal]] = v.normal.xyz;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
			output.[[INPUT_VALUE:worldPos]] = worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
			output.[[INPUT_VALUE:worldNormal]] = worldNormalUv;
	///
	/// IF USE_VERTEX_COLORS_FRAG
			output.vertexColor = v.vertexColor;
	///
		[[MODULE:VERTEX:Outline(v, output, null)]]
	/// IF USE_CLIP_POSITION_VERTEX || USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX
			float4 clipPos = output.vertex;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX

			// Screen Position
			float4 screenPos = ComputeScreenPos(clipPos);
		/// IF USE_SCREEN_POSITION_FRAGMENT
			output.screenPosition = screenPos;
		///
	///
			[[MODULE:VERTEX:Depth Texture(output.screenPosition, clipPos)]]
			[[MODULE:VERTEX:Screen Space UV(screenPos, clipPos, output)]]
	/// IF FOG
			output.[[INPUT_VALUE:fogFactor]] = ComputeFogFactor(output.vertex.z);
	///

			[[INJECTION_POINT:Outline Pass/Vertex Shader/End]]

			return output;
		}

#INPUT = input
#OUTPUT = no_output
#FRAGMENT
		float4 fragment_outline (v2f_outline input) : SV_Target
		{
			[[INJECTION_POINT:Outline Pass/Fragment Shader/Start]]

			UNITY_SETUP_INSTANCE_ID(input);
			UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

/// IF USE_WORLD_POSITION_FRAGMENT
			float3 positionWS = input.[[INPUT_VALUE:worldPos]];
///
/// IF USE_WORLD_POSITION_FRAGMENT
			float3 normalWS = input.[[INPUT_VALUE:worldPos]];
///

			[[MODULE:FRAGMENT:Screen Space UV(input.screenPosition, input)]]
			[[SAMPLE_CUSTOM_PROPERTIES]]
			[[SAMPLE_SHADER_PROPERTIES]]
			[[MODULE:FRAGMENT:Outline(input)]]
			[[MODULE:FRAGMENT:Dissolve(outlineColor.rgb)]]
	/// IF FOG
			outlineColor.rgb = MixFog(outlineColor.rgb, input.[[INPUT_VALUE:fogFactor]]);
	///
			[[MODULE:FRAGMENT:Vertical Fog(outlineColor.rgb, positionWS, input.[[INPUT_VALUE:objPos]])]]

			[[INJECTION_POINT:Outline Pass/Fragment Shader/End]]
			return outlineColor;
		}

		ENDHLSL
		// Outline Include End
///
#PASS
/// IF PASS_SILHOUETTE
		// Silhouette Pass
		Pass
		{
			Name "Silhouette"
	/// IF SILHOUETTE_URP_FEATURE
			Tags { "LightMode" = "Silhouette" }
	///
			Tags
			{
				[[INJECTION_POINT:Silhouette Pass/Tags]]
			}
			Blend [[VALUE:Silhouette Blend Source]] [[VALUE:Silhouette Blend Destination]]
			ZTest Greater
			ZWrite Off
			[[INJECTION_POINT:Silhouette Pass/Shader States]]
	/// IF SILHOUETTE_STENCIL

			Stencil
			{
				Ref [[VALUE:Silhouette Stencil Reference]]
				Comp NotEqual
				Pass Replace
				ReadMask [[VALUE:Silhouette Stencil Reference]]
				WriteMask [[VALUE:Silhouette Stencil Reference]]
				[[INJECTION_POINT:Silhouette Pass/Stencil]]
			}
	///

			HLSLPROGRAM
			#pragma vertex vertex_silhouette
			#pragma fragment fragment_silhouette
			#pragma multi_compile_instancing
			[[GPU_INSTANCING_OPTIONS]]
			#pragma target @%SHADER_TARGET%@
			[[INJECTION_POINT:Silhouette Pass/Pragma]]

			struct appdata_sil
			{
				float4 vertex : POSITION;
				[[VERTEX_INPUT_TEXCOORDS]]
	/// IF USE_VERTEX_COLORS_VERT
				fixed4 vertexColor : COLOR;
	///
				float3 normal : NORMAL;
	/// IF !TERRAIN_SHADER
		/// IF VERTEXMOTION_NORMAL || CURVED_WORLD_NORMAL
				float4 tangent : TANGENT;
		///
	///
				[[INJECTION_POINT:Silhouette Pass/Attributes]]
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct v2f_sil
			{
				float4 vertex : SV_POSITION;
				[[INPUT_STRUCT_SEMANTICS:0]]
#INPUT_VARIABLES
	/// IF USE_VERTEX_COLORS_FRAG
				fixed4 vertexColor;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT
				float4 screenPosition;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
				float3 worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
				float3 worldNormal;
	///
	/// IF USE_OBJECT_POSITION_FRAGMENT
				float3 objPos;
	///
	/// IF USE_OBJECT_NORMAL_FRAGMENT
				float3 objNormal;
	///
				[[MODULE:INPUT]]
#END
				[[INJECTION_POINT:Silhouette Pass/Varyings]]
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

#INPUT = v
#OUTPUT = output
#VERTEX
			v2f_sil vertex_silhouette (appdata_sil v)
			{
				v2f_sil output = (v2f_sil)0;

				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, output);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

				[[INJECTION_POINT:Silhouette Pass/Vertex Shader/Start]]

	/// IF USE_WORLD_POSITION_UV_VERTEX
				float3 worldPosUv = mul(unity_ObjectToWorld, v.vertex).xyz;
	///
	/// IF USE_WORLD_NORMAL_UV_VERTEX || USE_WORLD_NORMAL_FRAGMENT
				float3 worldNormalUv = mul(unity_ObjectToWorld, float4(v.normal, 1.0)).xyz;
	///
				[[VERTEX_TEXCOORDS]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]

				[[MODULE:VERTEX:VertExmotion:LWRP(v.vertex, v.normal, v.tangent)]]
				[[MODULE:VERTEX:CurvedWorld(v.vertex, v.normal, v.tangent)]]

				[[MODULE:VERTEX:Vertex Displacement(v.vertex)]]
	/// IF HOOK_VERTEX_POSITION
				v.vertex.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position]];
	///
	/// IF HOOK_VERTEX_POSITION_WORLD || APPLY_WORLD_POSITION || USE_WORLD_POSITION_FRAGMENT || USE_WORLD_POSITION_VERTEX
				float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
	///
				[[MODULE:VERTEX:Vertex Displacement:WORLD(worldPos)]]
	/// IF HOOK_VERTEX_POSITION_WORLD
				worldPos.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position World]];
	///
				[[MODULE:VERTEX:Wind(worldPos.xyz)]]
	/// IF APPLY_WORLD_POSITION
				v.vertex.xyz = mul(unity_WorldToObject, float4(worldPos, 1)).xyz;
	///
				[[MODULE:VERTEX:Water(v.vertex, worldPos, v.normal)]]
	/// IF USE_OBJECT_POSITION_FRAGMENT
				output.[[INPUT_VALUE:objPos]] = v.vertex.xyz;
	///
	/// IF USE_OBJECT_NORMAL_FRAGMENT
				output.[[INPUT_VALUE:objNormal]] = v.normal.xyz;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
				output.[[INPUT_VALUE:worldPos]] = worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
				output.[[INPUT_VALUE:worldNormal]] = worldNormalUv;
	///
				output.vertex = TransformObjectToHClip(v.vertex.xyz);
	/// IF USE_VERTEX_COLORS_FRAG
				output.vertexColor = v.vertexColor;
	///
	/// IF USE_CLIP_POSITION_VERTEX || USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX
				float4 clipPos = output.vertex;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX

				// Screen Position
				float4 screenPos = ComputeScreenPos(clipPos);
		/// IF USE_SCREEN_POSITION_FRAGMENT
				output.screenPosition = screenPos;
		///
	///
				[[MODULE:VERTEX:Depth Texture(output.screenPosition, clipPos)]]
				[[MODULE:VERTEX:Screen Space UV(screenPos, clipPos, output)]]

				[[INJECTION_POINT:Silhouette Pass/Vertex Shader/End]]

				return output;
			}

#INPUT = input
#OUTPUT = no_output
#FRAGMENT
			half4 fragment_silhouette (v2f_sil input) : SV_Target
			{
				[[INJECTION_POINT:Silhouette Pass/Fragment Shader/Start]]

				UNITY_SETUP_INSTANCE_ID(input);
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

				[[MODULE:FRAGMENT:Screen Space UV(input.screenPosition, input)]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]

				[[INJECTION_POINT:Silhouette Pass/Fragment Shader/End]]

				return [[VALUE:Silhouette Color]];
			}
			ENDHLSL
		}

///
#PASS
/// IF DEPTH_PREPASS && ___DISABLED___

		// Depth pre-pass
		Pass
		{
			Name "Depth Prepass"
			ColorMask 0
			ZWrite On

			CGPROGRAM
			#pragma vertex vertex_depthprepass
			#pragma fragment fragment_depthprepass
			#pragma target @%SHADER_TARGET%@

			#include "UnityCG.cginc"
			#include "UnityLightingCommon.cginc"	// needed for LightColor

			struct appdata_depthprepass
			{
				float4 vertex : POSITION;
				[[VERTEX_INPUT_TEXCOORDS]]
	/// IF USE_VERTEX_COLORS_VERT
				fixed4 vertexColor : COLOR;
	///
				float3 normal : NORMAL;
	/// IF !TERRAIN_SHADER
		/// IF VERTEXMOTION_NORMAL || CURVED_WORLD_NORMAL
				float4 tangent : TANGENT;
		///
	///
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct v2f_depthprepass
			{
				float4 vertex : SV_POSITION;
				UNITY_VERTEX_OUTPUT_STEREO
				[[INPUT_STRUCT_SEMANTICS:0]]
#INPUT_VARIABLES
	/// IF USE_VERTEX_COLORS_FRAG
				fixed4 vertexColor;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT
				float4 screenPosition;
	///
	/// IF USE_WORLD_POSITION_FRAGMENT
				float3 worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
				float3 worldNormal;
	///
				[[MODULE:INPUT]]
#END
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

#INPUT = v
#OUTPUT = output
#VERTEX
			v2f_depthprepass vertex_depthprepass (appdata_depthprepass v)
			{
				v2f_depthprepass output;
				UNITY_INITIALIZE_OUTPUT(v2f_depthprepass, output);

				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, output);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

	/// IF USE_WORLD_POSITION_UV_VERTEX
				float3 worldPosUv = mul(unity_ObjectToWorld, v.vertex).xyz;
	///
	/// IF USE_WORLD_NORMAL_UV_VERTEX || USE_WORLD_NORMAL_FRAGMENT
				float3 worldNormalUv = mul(unity_ObjectToWorld, float4(v.normal, 1.0)).xyz;
	///
				[[VERTEX_TEXCOORDS]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]

				[[MODULE:VERTEX:VertExmotion(v.vertex, v.normal, v.tangent)]]
				[[MODULE:VERTEX:CurvedWorld(v.vertex, v.normal, v.tangent)]]

				[[MODULE:VERTEX:Vertex Displacement(v.vertex)]]
	/// IF HOOK_VERTEX_POSITION
				v.vertex.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position]];
	///
	/// IF HOOK_VERTEX_POSITION_WORLD || APPLY_WORLD_POSITION || USE_WORLD_POSITION_FRAGMENT || USE_WORLD_POSITION_VERTEX
				float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
	///
				[[MODULE:VERTEX:Vertex Displacement:WORLD(worldPos)]]
	/// IF HOOK_VERTEX_POSITION_WORLD
				worldPos.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position World]];
	///
				[[MODULE:VERTEX:Wind(worldPos.xyz)]]
	/// IF APPLY_WORLD_POSITION
				v.vertex.xyz = mul(unity_WorldToObject, float4(worldPos, 1)).xyz;
	///
				[[MODULE:VERTEX:Water(v.vertex, worldPos, v.normal)]]
	/// IF USE_WORLD_POSITION_FRAGMENT
				output.[[INPUT_VALUE:worldPos]] = worldPos;
	///
	/// IF USE_WORLD_NORMAL_FRAGMENT
				output.[[INPUT_VALUE:worldNormal]] = worldNormalUv;
	///
				output.vertex = UnityObjectToClipPos(v.vertex);
	/// IF USE_VERTEX_COLORS_FRAG
				output.vertexColor = v.vertexColor;
	///
	/// IF USE_CLIP_POSITION_VERTEX || USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX
				float4 clipPos = output.vertex;
	///
	/// IF USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX

				// Screen Position
				float4 screenPos = ComputeScreenPos(clipPos);
		/// IF USE_SCREEN_POSITION_FRAGMENT
				output.screenPosition = screenPos;
		///
	///
				[[MODULE:VERTEX:Depth Texture(output.screenPosition, clipPos)]]
				[[MODULE:VERTEX:Screen Space UV(screenPos, clipPos, output)]]
				return output;
			}

#INPUT = input
#OUTPUT = no_output
#FRAGMENT
			half4 fragment_depthprepass (v2f_depthprepass input) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(input);
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

				[[MODULE:FRAGMENT:Screen Space UV(input.screenPosition, input)]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]
				return 0;
			}
			ENDCG
		}
///
/// IF OUTLINE && OUTLINE_BEHIND_STENCIL

		Stencil
		{
			Ref [[VALUE:Outline Stencil Reference]]
			Comp Always
			Pass Replace
			[[INJECTION_POINT:Outline Pass/Stencil]]
		}
///
#PASS
/// IF !(TERRAIN_BASEGEN && TERRAIN_BASEGEN_SAME_SHADER)
		Pass
		{
			Name "Main"
			Tags
			{
				"LightMode"="UniversalForward"
				[[INJECTION_POINT:Main Pass/Tags]]
			}
/// IF TERRAIN_SHADER && TERRAIN_ADDPASS
		Blend One One
/// ELIF AUTO_TRANSPARENT_BLENDING
		Blend [_SrcBlend] [_DstBlend]
		Cull [_Cull]
		ZWrite [_ZWrite]
/// ELIF ALPHA_BLENDING
			Blend SrcAlpha OneMinusSrcAlpha
/// ELIF ALPHA_BLENDING_PREMULT
			Blend One OneMinusSrcAlpha
/// ELIF ADDITIVE_BLENDING
			Blend One One
/// ELIF MULTIPLICATIVE_BLENDING
			Blend DstColor Zero
/// ELIF CUSTOM_BLENDING
			Blend [[VALUE:Blend Source]] [[VALUE:Blend Destination]]
///
/// IF BLEND_OP
			BlendOp [[VALUE:Blend Operation]]
///
/// IF ALPHA_TESTING && ALPHA_TO_COVERAGE
			AlphaToMask On
///
/// IF ZWRITE && !AUTO_TRANSPARENT_BLENDING
			ZWrite [[VALUE:Depth Write]]
///
/// IF ZTEST
			ZTest [[VALUE:Depth Test]]
///
/// IF CULLING && !AUTO_TRANSPARENT_BLENDING
			Cull [[VALUE:Face Culling]]
///
			[[INJECTION_POINT:Main Pass/Shader States]]
/// IF STENCIL

			Stencil
			{
				Ref [[VALUE:Stencil Reference]]
				ReadMask [[VALUE:Stencil Read Mask]]
				WriteMask [[VALUE:Stencil Write Mask]]
	/// IF !STENCIL_DOUBLE_SIDED
				Comp [[VALUE:Stencil Comparison]]
				Pass [[VALUE:Stencil Pass]]
				Fail [[VALUE:Stencil Fail]]
				ZFail [[VALUE:Stencil Depth Fail]]
	/// ELSE
				CompFront [[VALUE:Stencil Front Comparison]]
				PassFront [[VALUE:Stencil Front Pass]]
				FailFront [[VALUE:Stencil Front Fail]]
				ZFailFront [[VALUE:Stencil Front Depth Fail]]

				CompBack [[VALUE:Stencil Back Comparison]]
				PassBack [[VALUE:Stencil Back Pass]]
				FailBack [[VALUE:Stencil Back Fail]]
				ZFailBack [[VALUE:Stencil Back Depth Fail]]
	///
				[[INJECTION_POINT:Main Pass/Stencil]]
			}
/// ELIF PASS_SILHOUETTE && SILHOUETTE_STENCIL

			// Stencil value used for Silhouette Pass to make sure we don't see a
			// silhouette when the same mesh occludes parts of itself
			Stencil
			{
				Ref [[VALUE:Silhouette Stencil Reference]]
				Pass Replace
				ReadMask [[VALUE:Silhouette Stencil Reference]]
				WriteMask [[VALUE:Silhouette Stencil Reference]]
			}
///

			HLSLPROGRAM
			// Required to compile gles 2.0 with standard SRP library
			// All shaders must be compiled with HLSLcc and currently only gles is not using HLSLcc by default
			#pragma prefer_hlslcc gles
			#pragma exclude_renderers d3d11_9x
			#pragma target @%SHADER_TARGET%@
			[[INJECTION_POINT:Main Pass/Pragma]]

			// -------------------------------------
			// Material keywords
/// IF DISABLE_SHADOW_RECEIVING
			#pragma multi_compile _RECEIVE_SHADOWS_OFF
/// ELSE
			#pragma shader_feature_local _ _RECEIVE_SHADOWS_OFF
///

			// -------------------------------------
			// Universal Render Pipeline keywords
			#pragma multi_compile _ _MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_CASCADE _MAIN_LIGHT_SHADOWS_SCREEN
			#pragma multi_compile _ _ADDITIONAL_LIGHTS_VERTEX _ADDITIONAL_LIGHTS
			#pragma multi_compile_fragment _ _ADDITIONAL_LIGHT_SHADOWS
			#pragma multi_compile_fragment _ _SHADOWS_SOFT
/// IF UNITY_2020_1
			#pragma multi_compile _ LIGHTMAP_SHADOW_MIXING
			#pragma multi_compile _ SHADOWS_SHADOWMASK
/// ELSE
			#pragma multi_compile _ _MIXED_LIGHTING_SUBTRACTIVE
///
/// IF ENABLE_DECALS
			#pragma multi_compile_fragment _ _DBUFFER_MRT1 _DBUFFER_MRT2 _DBUFFER_MRT3
///
/// IF ENABLE_COOKIES
			#pragma multi_compile_fragment _ _LIGHT_COOKIES
///
/// IF ENABLE_LIGHT_LAYERS
			#pragma multi_compile_fragment _ _LIGHT_LAYERS
///
/// IF ENABLE_FORWARD_PLUS
			#pragma multi_compile _ _FORWARD_PLUS
///
/// IF ENABLE_RENDERING_LAYERS
			#pragma multi_compile_fragment _ _WRITE_RENDERING_LAYERS
///
/// IF SSAO
			#pragma multi_compile_fragment _ _SCREEN_SPACE_OCCLUSION
///

			// -------------------------------------
/// IF ENABLE_LIGHTMAP
			#pragma multi_compile _ DIRLIGHTMAP_COMBINED
			#pragma multi_compile _ LIGHTMAP_ON
			#pragma multi_compile _ DYNAMICLIGHTMAP_ON
///
/// IF FOG
			#pragma multi_compile_fog
///
/// IF ENABLE_DITHER_LOD
			#pragma multi_compile _ LOD_FADE_CROSSFADE
///

			//--------------------------------------
			// GPU Instancing
			#pragma multi_compile_instancing
			[[GPU_INSTANCING_OPTIONS]]

			#pragma vertex Vertex
			#pragma fragment Fragment

	#if_not_empty
			//--------------------------------------
			// Toony Colors Pro 2 keywords
	#start_not_empty_block
			[[MODULE:SHADER_FEATURES_BLOCK:Terrain]]
			[[MODULE:SHADER_FEATURES_BLOCK:Specular]]
			[[MODULE:SHADER_FEATURES_BLOCK:Vertex Displacement]]
			[[MODULE:SHADER_FEATURES_BLOCK:Rim Lighting]]
/// IF (GLOSSY_REFLECTIONS || REFLECTION_CUBEMAP || PLANAR_REFLECTION) && REFLECTION_SHADER_FEATURE
			#pragma shader_feature_local_fragment TCP2_REFLECTIONS
///
/// IF AMBIENT_SHADER_FEATURE
			#pragma shader_feature_local_fragment TCP2_AMBIENT
///
/// IF AUTO_TRANSPARENT_BLENDING
		#pragma shader_feature_local _ _ALPHAPREMULTIPLY_ON
///
			[[MODULE:SHADER_FEATURES_BLOCK:MatCap]]
			[[MODULE:SHADER_FEATURES_BLOCK:Subsurface Scattering]]
			[[MODULE:SHADER_FEATURES_BLOCK:Normal Mapping]]
			[[MODULE:SHADER_FEATURES_BLOCK:Sketch]]
			[[MODULE:SHADER_FEATURES_BLOCK:NdotL Stylization]]
			[[MODULE:SHADER_FEATURES_BLOCK:Wind]]
			[[MODULE:SHADER_FEATURES_BLOCK:Water]]
			[[MODULE:SHADER_FEATURES_BLOCK:Dissolve]]
			[[MODULE:SHADER_FEATURES_BLOCK:Vertical Fog]]
	#end_not_empty_block
	#end_not_empty

#------------------------------------------------------------
# end of IF !(TERRAIN_BASEGEN && TERRAIN_BASEGEN_SAME_SHADER)
/// ELSE
[[MODULE:TERRAIN_BASEGEN_SHADER_URP:Terrain]]

			HLSLINCLUDE
///
#------------------------------------------------------------
			// vertex input
			struct Attributes
			{
				float4 vertex       : POSITION;
				float3 normal       : NORMAL;
/// IF (USE_TANGENT_VERT || USE_TANGENT_FRAGMENT) && !TERRAIN_SHADER
				float4 tangent      : TANGENT;
///
/// IF ENABLE_LIGHTMAP && !TERRAIN_SHADER
				#if defined(LIGHTMAP_ON)
				float2 texcoord1    : TEXCOORD1;
				#endif
				#if defined(DYNAMICLIGHTMAP_ON)
				float2 texcoord2    : TEXCOORD2;
				#endif
///
/// IF USE_VERTEX_COLORS_VERT
				half4 vertexColor   : COLOR;
///
				[[VERTEX_INPUT_TEXCOORDS]]
				[[INJECTION_POINT:Main Pass/Attributes]]
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			// vertex output / fragment input
			struct Varyings
			{
				float4 positionCS     : SV_POSITION;
				float3 normal         : NORMAL;
				float4 worldPosAndFog : TEXCOORD0;
			#if defined(REQUIRES_VERTEX_SHADOW_COORD_INTERPOLATOR)
				float4 shadowCoord    : TEXCOORD1; // compute shadow coord per-vertex for the main light
			#endif
			#ifdef _ADDITIONAL_LIGHTS_VERTEX
				half3 vertexLights : TEXCOORD2;
			#endif
/// IF ENABLE_LIGHTMAP
			#if defined(LIGHTMAP_ON) && defined(DYNAMICLIGHTMAP_ON)
				float4 lightmapUV  : TEXCOORD3;
				#define staticLightmapUV lightmapUV.xy
				#define dynamicLightmapUV lightmapUV.zw
			#elif defined(LIGHTMAP_ON) || defined(DYNAMICLIGHTMAP_ON)
				float2 lightmapUV  : TEXCOORD3;
				#define staticLightmapUV lightmapUV.xy
				#define dynamicLightmapUV lightmapUV.xy
			#endif
				[[INPUT_STRUCT_SEMANTICS:4]]
/// ELSE
				[[INPUT_STRUCT_SEMANTICS:3]]
///
#INPUT_VARIABLES
/// IF USE_OBJECT_POSITION_FRAGMENT
				float3 objPos;
///
/// IF USE_OBJECT_NORMAL_FRAGMENT
				float3 objNormal;
///
/// IF USE_VERTEX_COLORS_FRAG
				half4 vertexColor;
///
/// IF USE_TANGENT_FRAGMENT
				float3 tangent;
///
/// IF USE_BITANGENT_FRAGMENT
				float3 bitangent;
///
/// IF USE_SCREEN_POSITION_FRAGMENT
				float4 screenPosition;
///
/// IF FOG
				float fogFactor;
///
/// IF UV_SINE_ANIMATION_FRAGMENT
				float2 sinUvAnimVertexPos;
///
/// IF UV_SINE_ANIMATION_FRAGMENT_WORLD
				float2 sinUvAnimVertexWorldPos;
///
				[[MODULE:INPUT]]
#END
				[[INJECTION_POINT:Main Pass/Varyings]]
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

/// IF ENABLE_FORWARD_PLUS
			#if USE_FORWARD_PLUS
				// Fake InputData struct needed for Forward+ macro
				struct InputDataForwardPlusDummy
				{
					float3  positionWS;
					float2  normalizedScreenSpaceUV;
				};
			#endif
///

/// IF TERRAIN_SHADER && USE_TERRAIN_DATA_LIGHTING
		// Terrain data to pass to lighting function
		struct TerrainData
		{
			half4 splatControl;
			half4 specularColor;
			half3 mixedDiffuse;
			half smoothness;
			half metallic;
	/// IF TERRAIN_USE_MASKS
			half4 mask;
	///
		};
///

/// IF TERRAIN_SHADER
		//--------------------------------------
		// Terrain functions

		[[MODULE:FUNCTIONS:Terrain]]
///



#VERTEX, INPUT = input, OUTPUT = output
			Varyings Vertex(Attributes input)
			{
				Varyings output = (Varyings)0;

				UNITY_SETUP_INSTANCE_ID(input);
				UNITY_TRANSFER_INSTANCE_ID(input, output);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

				[[INJECTION_POINT:Main Pass/Vertex Shader/Start]]

				[[MODULE:VERTEX:Terrain(input.vertex, input.normal, input.texcoord0, _unused_)]]

/// IF USE_WORLD_POSITION_UV_VERTEX
				float3 worldPosUv = mul(unity_ObjectToWorld, input.vertex).xyz;
///
/// IF USE_WORLD_NORMAL_UV_VERTEX
				float3 worldNormalUv = mul(unity_ObjectToWorld, float4(input.normal, 1.0)).xyz;
///
/// IF UV_SINE_ANIMATION_VERTEX

				// Used for texture UV sine animation
				float2 sinUvAnimVertexPos = input.vertex.xy + input.vertex.yz;
	/// IF UV_SINE_ANIMATION_FRAGMENT
				output.[[INPUT_VALUE:sinUvAnimVertexPos]] = sinUvAnimVertexPos;
	///
///
/// IF UV_SINE_ANIMATION_VERTEX_WORLD

				// Used for texture UV sine animation (world space)
				float2 sinUvAnimVertexWorldPos = worldPosUv.xy + worldPosUv.yz;
	/// IF UV_SINE_ANIMATION_FRAGMENT_WORLD
				output.[[INPUT_VALUE:sinUvAnimVertexWorldPos]] = sinUvAnimVertexWorldPos;
	///
///

				[[VERTEX_TEXCOORDS]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]
/// IF ENABLE_LIGHTMAP
	/// IF TERRAIN_SHADER
				output.staticLightmapUV = input.texcoord0.xy * unity_LightmapST.xy + unity_LightmapST.zw;
	/// ELSE
				#if defined(LIGHTMAP_ON)
					output.staticLightmapUV = input.texcoord1.xy * unity_LightmapST.xy + unity_LightmapST.zw;
				#endif
				#if defined(DYNAMICLIGHTMAP_ON)
					output.dynamicLightmapUV = input.texcoord2.xy * unity_DynamicLightmapST.xy + unity_DynamicLightmapST.zw;
				#endif
	///
///

				[[MODULE:VERTEX:VertExmotion:LWRP(input.vertex, input.normal, input.tangent)]]
				[[MODULE:VERTEX:CurvedWorld(input.vertex, input.normal, input.tangent)]]

				[[MODULE:VERTEX:Vertex Displacement(input.vertex)]]
/// IF HOOK_VERTEX_POSITION
				input.vertex.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position]];
///
/// IF HOOK_VERTEX_POSITION_WORLD || APPLY_WORLD_POSITION || USE_WORLD_POSITION_FRAGMENT || USE_WORLD_POSITION_VERTEX
				float3 worldPos = mul(unity_ObjectToWorld, input.vertex).xyz;
///
				[[MODULE:VERTEX:Vertex Displacement:WORLD(worldPos)]]
/// IF HOOK_VERTEX_POSITION_WORLD
				worldPos.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position World]];
///
				[[MODULE:VERTEX:Wind(worldPos.xyz)]]
/// IF APPLY_WORLD_POSITION
				input.vertex.xyz = mul(unity_WorldToObject, float4(worldPos, 1)).xyz;
///
				[[MODULE:VERTEX:Water(input.vertex, worldPos, input.normal)]]
/// IF USE_OBJECT_POSITION_FRAGMENT
				output.[[INPUT_VALUE:objPos]] = input.vertex.xyz;
///
/// IF USE_OBJECT_NORMAL_FRAGMENT
				output.[[INPUT_VALUE:objNormal]] = input.normal.xyz;
///
				VertexPositionInputs vertexInput = GetVertexPositionInputs(input.vertex.xyz);
			#if defined(REQUIRES_VERTEX_SHADOW_COORD_INTERPOLATOR)
				output.shadowCoord = GetShadowCoord(vertexInput);
			#endif
/// IF USE_CLIP_POSITION_VERTEX || USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX
				float4 clipPos = vertexInput.positionCS;
///
/// IF USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX

				float4 screenPos = ComputeScreenPos(clipPos);
	/// IF USE_SCREEN_POSITION_FRAGMENT
				output.[[INPUT_VALUE:screenPosition]] = screenPos;
	///
///
				[[MODULE:VERTEX:Depth Texture(screenPos, clipPos)]]
				[[MODULE:VERTEX:Screen Space UV(screenPos, clipPos, output)]]

/// IF TERRAIN_SHADER
#NOTE: tangent here is inverted, as is the case in URP's terrain shaders... I haven't figured out why it's needed though
				float4 vertexTangent = -float4(cross(float3(0, 0, 1), input.normal), 1.0);
				VertexNormalInputs vertexNormalInput = GetVertexNormalInputs(input.normal, vertexTangent);
/// ELIF USE_TANGENT_VERT || USE_TANGENT_FRAGMENT
				VertexNormalInputs vertexNormalInput = GetVertexNormalInputs(input.normal, input.tangent);
/// ELSE
				VertexNormalInputs vertexNormalInput = GetVertexNormalInputs(input.normal);
///
			#ifdef _ADDITIONAL_LIGHTS_VERTEX
				// Vertex lighting
				output.vertexLights = VertexLighting(vertexInput.positionWS, vertexNormalInput.normalWS);
			#endif

				// world position
				output.worldPosAndFog = float4(vertexInput.positionWS.xyz, 0);
/// IF FOG

				// Computes fog factor per-vertex
				output.worldPosAndFog.w = ComputeFogFactor(vertexInput.positionCS.z);
///

				// normal
				output.normal = normalize(vertexNormalInput.normalWS);
/// IF USE_TANGENT_FRAGMENT

				// tangent
				output.[[INPUT_VALUE:tangent]] = vertexNormalInput.tangentWS;
///
/// IF USE_BITANGENT_FRAGMENT
				output.[[INPUT_VALUE:bitangent]] = vertexNormalInput.bitangentWS;
///

				// clip position
				output.positionCS = vertexInput.positionCS;

/// IF USE_VERTEX_COLORS_FRAG
				output.vertexColor = input.vertexColor;
///
/// IF USE_VIEW_DIRECTION_VERTEX
				half3 viewDirWS = SafeNormalize(GetCameraPositionWS() - vertexInput.positionWS);
///
#ENABLE_IMPL: float ndv, lbl = "Special/N·V (Vertex)", help = "The dot product between the normal and view direction.", toggles = "USE_NDV_VERTEX", options = "(Use Min/Max Properties,USE_NDV_MIN_MAX_VERT,config),(Invert,USE_NDV_INVERT_VERT)"
/// IF USE_NDV_VERTEX
				half ndv = abs(dot(viewDirWS, vertexNormalInput.normalWS));
				half ndvRaw = ndv;
	/// IF USE_NDV_INVERT_VERT
				ndv = 1 - ndv;
	///
	/// IF USE_NDV_MIN_MAX_VERT
				ndv = smoothstep([[VALUE:NDV Min Vert]], [[VALUE:NDV Max Vert]], ndv);
	///
///

				[[MODULE:VERTEX:Rim Lighting(ndvRaw, viewDirWS, input.normal, output)]]
				[[MODULE:VERTEX:MatCap(input.normal, screenPos, output)]]

				[[INJECTION_POINT:Main Pass/Vertex Shader/End]]

				return output;
#DISABLE_IMPL_ALL
			}

/// IF BYPASS_LIGHT_FALLOFF
			// Copy of LWRP's GetAdditionalLight() with different falloff calculation
			Light GetAdditionalLight_BypassFalloff(int i, float3 positionWS)
			{
				int perObjectLightIndex = GetPerObjectLightIndex(i);

				float3 lightPositionWS = _AdditionalLightsPosition[perObjectLightIndex].xyz;
				half4 distanceAndSpotAttenuation = _AdditionalLightsAttenuation[perObjectLightIndex];
				half4 spotDirection = _AdditionalLightsSpotDir[perObjectLightIndex];

				float3 lightVector = lightPositionWS - positionWS;
				float distanceSqr = max(dot(lightVector, lightVector), HALF_MIN);

				half3 lightDirection = half3(lightVector * rsqrt(distanceSqr));
				// original line:
				//half attenuation = DistanceAttenuation(distanceSqr, distanceAndSpotAttenuation.xy);
				half attenuation = saturate(1 - distanceSqr * distanceAndSpotAttenuation.x);
				attenuation *= AngleAttenuation(spotDirection.xyz, lightDirection, distanceAndSpotAttenuation.zw);

				Light light;
				light.direction = lightDirection;
				light.distanceAttenuation = attenuation;
				light.shadowAttenuation = AdditionalLightRealtimeShadow(perObjectLightIndex, positionWS);
				light.color = _AdditionalLightsColor[perObjectLightIndex].rgb;

				return light;
			}

///
#FRAGMENT, INPUT = input, OUTPUT = no_output
			half4 Fragment(Varyings input
/// IF USE_VFACE
				, half vFace : VFACE
///
/// IF ENABLE_RENDERING_LAYERS
			#ifdef _WRITE_RENDERING_LAYERS
				, out float4 outRenderingLayers : SV_Target1
			#endif
///
			) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(input);
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

				[[INJECTION_POINT:Main Pass/Fragment Shader/Start]]

				[[FRAGMENT_TEXCOORDS]]

/// IF ENABLE_DITHER_LOD
				#if defined(LOD_FADE_CROSSFADE)
	/// IF DITHER_LOD_8x8
					const float dither = Dither8x8(input.positionCS.xy);
	/// ELSE
					const float dither = Dither4x4(input.positionCS.xy);
	///
					const float ditherThreshold = unity_LODFade.x - CopySign(dither, unity_LODFade.x);
					clip(ditherThreshold);
				#endif
///

#ENABLE_IMPL: float vFace, lbl = "Special/VFACE (Face direction)", help = "Indicates if the current face is back or front-facing. Should be used with custom Face Culling.", toggles = "USE_VFACE", custom_code_compatible = true
				float3 positionWS = input.worldPosAndFog.xyz;
				float3 normalWS = normalize(input.normal);
/// IF USE_WORLD_NORMAL_FRAGMENT_PER_VERTEX
				float3 normalWS_Vertex = normalWS;
///
/// IF BACKFACE_LIGHTING_Z
				normalWS.z *= (vFace < 0) ? -1.0 : 1.0;
/// ELIF BACKFACE_LIGHTING_XYZ
				normalWS.xyz *= (vFace < 0) ? -1.0 : 1.0;
///
/// IF USE_VIEW_DIRECTION_FRAGMENT
				half3 viewDirWS = SafeNormalize(GetCameraPositionWS() - positionWS);
///
/// IF USE_TANGENT_FRAGMENT
				half3 tangentWS = input.[[INPUT_VALUE:tangent]];
///
/// IF USE_BITANGENT_FRAGMENT
				half3 bitangentWS = input.[[INPUT_VALUE:bitangent]];
///
#TODO implement POM from PerPixelDisplacement.hlsl instead of the old one?
/// IF BUMP
	/// IF BUMP_SHADER_FEATURE
				#if defined(_NORMALMAP)
	///
				[[MODULE:FRAGMENT:Normal Mapping:BUILD_TANGENT_MATRIX(tangentWS.xyz, bitangentWS.xyz, normalWS.xyz)]]
				[[MODULE:FRAGMENT:Normal Mapping:PARALLAX_SRP(viewDirWS, input.[[INPUT_VALUE:texcoord0]])]]
	/// IF WORLD_NORMAL_FROM_BUMP
				[[MODULE:FRAGMENT:Normal Mapping:BUMP_SAMPLE_BEFORE_URP()]]
				[[MODULE:FRAGMENT:Normal Mapping:APPLY_BUMP_SRP(normalWS, normalTS)]]
	///
	/// IF BUMP_SHADER_FEATURE
				#endif
	///
///

				[[MODULE:FRAGMENT:Screen Space UV(input.[[INPUT_VALUE:screenPosition]], input)]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]

				[[MODULE:FRAGMENT:Terrain:PREPASS_AND_NORMAL(normalWS, _unused_)]]

/// IF USE_NDV_FRAGMENT && USE_NDV_IGNORE_NORMAL_MAP
				half3 normalWSVertex = normalWS;
///
				[[MODULE:FRAGMENT:Texture Blending:INIT_UVS]]
/// IF BUMP && BUMP_SHADER_FEATURE
				#if defined(_NORMALMAP)
///
				[[MODULE:FRAGMENT:Normal Mapping:BUMP_SAMPLE()]]
				[[MODULE:FRAGMENT:Texture Blending:BUMP()]]
				[[MODULE:FRAGMENT:Normal Mapping:UNPACK_BUMP_SRP()]]
/// IF !TERRAIN_SHADER
				[[MODULE:FRAGMENT:Normal Mapping:APPLY_BUMP_SRP(normalWS, normalTS)]]
///
/// IF BUMP && BUMP_SHADER_FEATURE
				#endif
///

#ENABLE_IMPL: float ndv, lbl = "Special/N·V", help = "The dot product between the normal and view direction.", toggles = "USE_NDV_FRAGMENT", options = "(Use Min/Max Properties,USE_NDV_MIN_MAX_FRAG,config),(Invert,USE_NDV_INVERT_FRAG),(Ignore Normal Map,USE_NDV_IGNORE_NORMAL_MAP)"
/// IF USE_NDV_FRAGMENT
	/// IF USE_NDV_IGNORE_NORMAL_MAP
				half ndv = abs(dot(viewDirWS, normalWSVertex));
	/// ELSE
				half ndv = abs(dot(viewDirWS, normalWS));
	///
				half ndvRaw = ndv;
	/// IF USE_NDV_INVERT_FRAG
				ndv = 1 - ndv;
	///
	/// IF USE_NDV_MIN_MAX_FRAG
				ndv = smoothstep([[VALUE:NDV Min Frag]], [[VALUE:NDV Max Frag]], ndv);
	///

///
				[[MODULE:FRAGMENT:Depth Texture(input.[[INPUT_VALUE:screenPosition]], input.[[INPUT_VALUE:screenPosition]])]]

				// main texture
/// IF CUSTOM_ALBEDO
				half3 albedo = half3(1,1,1);
				half alpha = 1;
/// ELSE
				half3 albedo = [[VALUE:Albedo]].rgb;
				half alpha = [[VALUE:Alpha]];
///

/// IF TERRAIN_SHADER
				[[MODULE:FRAGMENT:Terrain(albedo, alpha, normalWS, _unused_)]]
				[[MODULE:FRAGMENT:Normal Mapping:APPLY_BUMP_SRP(normalWS, normalTS)]]

# Recalculate NDV to take terrain into account
	/// IF USE_NDV_FRAGMENT && BUMP
				// ndv with terrain normal taken into account
		/// IF USE_NDV_IGNORE_NORMAL_MAP
				ndv = abs(dot(viewDirWS, normalWSVertex));
		/// ELSE
				ndv = abs(dot(viewDirWS, normalWS));
		///
				ndvRaw = ndv;
		/// IF USE_NDV_INVERT_FRAG
				ndv = 1 - ndv;
		///
		/// IF USE_NDV_MIN_MAX_FRAG
				ndv = smoothstep([[VALUE:NDV Min Frag]], [[VALUE:NDV Max Frag]], ndv);
		///
	///

///

/// IF ENABLE_DECALS
				// URP Decals
				#if defined(_DBUFFER)
					#if defined(_DBUFFER_MRT2) || defined(_DBUFFER_MRT3)
						#define HAS_DECAL_NORMALS
					#endif
					#if defined(_DBUFFER_MRT3)
						#define HAS_DECAL_MAOS
					#endif

					DecalSurfaceData decals = GetDecals(input.positionCS);
					albedo.rgb = albedo.rgb * decals.baseColor.a + decals.baseColor.rgb;
					#if defined(HAS_DECAL_NORMALS)
						// Always test the normal as we can have decompression artifact
						if (decals.normalWS.w < 1.0)
						{
							normalWS.xyz = normalize(normalWS.xyz * decals.normalWS.w + decals.normalWS.xyz);
						}
					#endif
				#endif
///				

				half3 emission = half3(0,0,0);
				[[MODULE:FRAGMENT:Dissolve(emission)]]
/// IF ALPHA_TESTING
				// Alpha Testing
	/// IF ALPHA_TESTING_DITHERING
		/// IF ALPHA_DITHER_TEXTURE
				half cutoffValue = [[VALUE:Dithering Texture]];
		/// ELIF ALPHA_DITHER_8x8
				half cutoffValue = Dither8x8(input.positionCS.xy);
		/// ELSE
				half cutoffValue = Dither4x4(input.positionCS.xy);
		///
	/// ELSE
				half cutoffValue = [[VALUE:Cutoff]];
	///
	/// IF !ALPHA_TO_COVERAGE
				clip(alpha - cutoffValue);
	/// ELIF !ALPHA_TO_COVERAGE_RAW
				// Sharpen Alpha-to-Coverage
				alpha = (alpha - cutoffValue) / max(fwidth(alpha), 0.0001) + 0.5;
	///
///
/// IF TEXTURE_BLENDING || TRIPLANAR
				half4 albedoAlpha = half4(albedo, alpha);
				[[MODULE:FRAGMENT:Triplanar:INPUT(positionWS, normalWS_Vertex, input.[[INPUT_VALUE:objPos]], input.[[INPUT_VALUE:objNormal]])]]
				[[MODULE:FRAGMENT:Triplanar:SAMPLE_GROUND(albedoAlpha, input.[[INPUT_VALUE:objPos]])]]
				[[MODULE:FRAGMENT:Texture Blending(albedoAlpha)]]
				[[MODULE:FRAGMENT:Triplanar(albedoAlpha, positionWS, input.[[INPUT_VALUE:objPos]])]]
				albedo = albedoAlpha.rgb;
				alpha = albedoAlpha.a;
///
				[[MODULE:FRAGMENT:AlbedoHSV(albedo)]]
				albedo *= [[VALUE:Main Color]].rgb;
				[[MODULE:FRAGMENT:Water(albedo, alpha)]]
/// IF HOOK_FINAL_ALBEDO
				albedo.rgb = [[SAMPLE_VALUE_SHADER_PROPERTY:Final Albedo]];
///

				// main light: direction, color, distanceAttenuation, shadowAttenuation
			#if defined(REQUIRES_VERTEX_SHADOW_COORD_INTERPOLATOR)
				float4 shadowCoord = input.shadowCoord;
			#elif defined(MAIN_LIGHT_CALCULATE_SHADOWS)
				float4 shadowCoord = TransformWorldToShadowCoord(positionWS);
			#else
				float4 shadowCoord = float4(0, 0, 0, 0);
			#endif

			#if defined(URP_10_OR_NEWER)
				#if defined(SHADOWS_SHADOWMASK) && defined(LIGHTMAP_ON)
					half4 shadowMask = SAMPLE_SHADOWMASK(input.staticLightmapUV);
				#elif !defined (LIGHTMAP_ON)
					half4 shadowMask = unity_ProbesOcclusion;
				#else
					half4 shadowMask = half4(1, 1, 1, 1);
				#endif

/// IF ENABLE_RENDERING_LAYERS || ENABLE_LIGHT_LAYERS
				#if defined(URP_14_OR_NEWER)
					uint meshRenderingLayers = GetMeshRenderingLayer();
				#elif defined(URP_12_OR_NEWER)
					uint meshRenderingLayers = GetMeshRenderingLightLayer();
				#endif
///

				Light mainLight = GetMainLight(shadowCoord, positionWS, shadowMask);
			#else
				Light mainLight = GetMainLight(shadowCoord);
			#endif

/// IF SSAO || ENABLE_FORWARD_PLUS
			#if defined(_SCREEN_SPACE_OCCLUSION) || defined(USE_FORWARD_PLUS)
				float2 normalizedScreenSpaceUV = GetNormalizedScreenSpaceUV(input.positionCS);
			#endif
///
/// IF SSAO
			#if defined(_SCREEN_SPACE_OCCLUSION)
				AmbientOcclusionFactor aoFactor = GetScreenSpaceAmbientOcclusion(normalizedScreenSpaceUV);
				mainLight.color *= aoFactor.directAmbientOcclusion;
			#endif
///

				// ambient or lightmap
/// IF ENABLE_LIGHTMAP
			#if defined(LIGHTMAP_ON) && defined(DYNAMICLIGHTMAP_ON)
				// Static & Dynamic Lightmap
				half3 bakedGI = SampleLightmap(input.staticLightmapUV, input.dynamicLightmapUV, normalWS);
				MixRealtimeAndBakedGI(mainLight, normalWS, bakedGI, half4(0, 0, 0, 0));
			#elif defined(LIGHTMAP_ON)
				// Static Lightmap
				half3 bakedGI = SampleLightmap(input.staticLightmapUV, 0, normalWS);
				MixRealtimeAndBakedGI(mainLight, normalWS, bakedGI, half4(0, 0, 0, 0));
			#elif defined(DYNAMICLIGHTMAP_ON)
				// Dynamic Lightmap
				half3 bakedGI = SampleLightmap(0, input.dynamicLightmapUV, normalWS);
				MixRealtimeAndBakedGI(mainLight, normalWS, bakedGI, half4(0, 0, 0, 0));
			#else
///
/// IF NO_AMBIENT
				half3 bakedGI = half3(0,0,0);
/// ELSE
	/// IF AMBIENT_SHADER_FEATURE
			#if defined(TCP2_AMBIENT)
	///
				// Samples SH fully per-pixel. SampleSHVertex and SampleSHPixel functions
				// are also defined in case you want to sample some terms per-vertex.
		/// IF AMBIENT_VIEW_DIR
				half3 bakedGI = SampleSH(viewDirWS);
		/// ELSE
				half3 bakedGI = SampleSH(normalWS);
		///
	/// IF AMBIENT_SHADER_FEATURE
			#else
				half3 bakedGI = half3(0,0,0);
			#endif
	///
///
/// IF ENABLE_LIGHTMAP
			#endif
///
/// IF OCCLUSION
				half occlusion = [[VALUE:Occlusion]];
/// ELSE
				half occlusion = 1;
///

/// IF SSAO
			#if defined(_SCREEN_SPACE_OCCLUSION)
				occlusion = min(occlusion, aoFactor.indirectAmbientOcclusion);
			#endif
///

				half3 indirectDiffuse = bakedGI;
#ENABLE_IMPL: float3 bakedGI, lbl = "Special/Indirect Diffuse", help = "The raw indirect diffuse color, as calculated by Unity."
/// IF AMBIENT_SHADER_FEATURE
			#if defined(TCP2_AMBIENT)
///
				[[MODULE:FRAGMENT:Custom Ambient(indirectDiffuse.rgb, normalWS)]]
				indirectDiffuse *= occlusion * albedo * [[VALUE:Ambient Intensity]];
#ENABLE_IMPL: float3 indirectDiffuse, lbl = "Special/Ambient Color", help = "The indirect diffuse color, with albedo, occlusion and any custom ambient effects applied."
/// IF AMBIENT_SHADER_FEATURE
			#endif
///
/// IF EMISSION
				emission += [[VALUE:Emission]];
///

				[[MODULE:FRAGMENT:Triplanar:BUMP(normalWS, normalWS_Vertex)]]

/// IF USE_NDV_FRAGMENT && USE_SURFACE_CUSTOM_NORMAL && !USE_NDV_IGNORE_NORMAL_MAP
				// Recalculate NDV to take the triplanar normals into account:
				half ndv = abs(dot(viewDirWS, normalWS));
				half ndvRaw = ndv;
	/// IF USE_NDV_INVERT_FRAG
				ndv = 1 - ndv;
	///
	/// IF USE_NDV_MIN_MAX_FRAG
				ndv = smoothstep([[VALUE:NDV Min Frag]], [[VALUE:NDV Max Frag]], ndv);
	///
///

				[[MODULE:FRAGMENT:MatCap(albedo, emission, normalWS, input)]]

/// IF ENABLE_LIGHT_LAYERS
				#if defined(_LIGHT_LAYERS)
					half3 lightDir = half3(0, 1, 0);
					half3 lightColor = half3(0, 0, 0);
					if (IsMatchingLightLayer(mainLight.layerMask, meshRenderingLayers))
					{
						lightDir = mainLight.direction;
						lightColor = mainLight.color.rgb;
					}
				#else
					half3 lightDir = mainLight.direction;
					half3 lightColor = mainLight.color.rgb;
				#endif
/// ELSE
				half3 lightDir = mainLight.direction;
				half3 lightColor = mainLight.color.rgb;
///


/// IF HOOK_MAIN_LIGHT_DIR
				lightDir = normalize([[SAMPLE_VALUE_SHADER_PROPERTY:Main Light Direction]]);
///
/// IF HOOK_MAIN_LIGHT_COLOR
				lightColor = [[SAMPLE_VALUE_SHADER_PROPERTY:Main Light Color]];
///
#ENABLE_IMPL: float3 lightColor, lbl = "Special/Light Color", compat = "all", help = "The color of the current light used."

/// IF !NO_RAMP_UNLIT
				half atten = mainLight.shadowAttenuation * mainLight.distanceAttenuation;
/// ELSE
				half atten = mainLight.shadowAttenuation;
///
/// IF HOOK_MAIN_LIGHT_ATTEN
				atten = [[SAMPLE_VALUE_SHADER_PROPERTY:Main Light Attenuation]];
///
#ENABLE_IMPL: float atten, lbl = "Special/Shadow Map", compat = "all", help = "The shadow map value for the current light."

				half ndl = dot(normalWS, lightDir);
#ENABLE_IMPL: float ndl, lbl = "Special/N·L", compat = "all", help = "The dot product between the normal and light direction."
				[[MODULE:FRAGMENT:NdotL Stylization:AFTER_NDL(ndl)]]
/// IF ATTEN_AT_NDL
				// apply attenuation
				ndl *= atten;
///
				half3 ramp;
				[[MODULE:FRAGMENT:Ramp Shading LWRP:MAIN_LIGHT(ramp, ndl)]]
/// IF USE_NDL_GRAYSCALE
				fixed3 rampGrayscale = ramp;
///
#ENABLE_IMPL: float3 rampGrayscale, lbl = "Special/N·L Ramp (Black and White)", compat = "all", toggles = "USE_NDL_GRAYSCALE", help = "N·L with the ramp modification before the highlight/shadow colors are applied."

/// IF !ATTEN_AT_NDL
				// apply attenuation
				ramp *= atten;
///

/// IF SHADOW_COLOR_MAIN_DIR
				[[MODULE:FRAGMENT:ShadowHSV(albedo, ramp)]]
				[[MODULE:FRAGMENT:Sketch(ramp)]]
				[[MODULE:FRAGMENT:Sketch:APPLY_AMBIENT(indirectDiffuse.rgb)]]

				// highlight/shadow colors
	/// IF SHADOW_COLOR_LERP
				albedo = lerp([[VALUE:Shadow Color]], albedo, ramp);
				ramp = lerp(half3(1,1,1), [[VALUE:Highlight Color]], ramp);
	/// ELSE
				ramp = lerp([[VALUE:Shadow Color]], [[VALUE:Highlight Color]], ramp);
	///
	/// IF HOOK_RAMP
				ramp = [[SAMPLE_VALUE_SHADER_PROPERTY:Shading Ramp]];
	///
#ENABLE_IMPL: float3 ramp, lbl = "Special/N·L Ramp (With Colors)", compat = "all", help = "N·L with the ramp modification with the highlight/shadow colors applied."
				[[MODULE:FRAGMENT:NdotL Stylization:AFTER_RAMP(ndl, ramp)]]

				// output color
				half3 color = half3(0,0,0);
				[[MODULE:FRAGMENT:Rim Lighting(ndvRaw, emission, albedo, normalWS, viewDirWS, input.screenPosition, ndl, atten, input)]]
	/// IF NO_RAMP_UNLIT
				color += albedo * ramp;
	/// ELSE
				color += albedo * lightColor.rgb * ramp;
	///
				[[MODULE:FRAGMENT:Sketch:APPLY(color.rgb)]]
/// ELSE
	/// IF HOOK_RAMP
				ramp = [[SAMPLE_VALUE_SHADER_PROPERTY:Shading Ramp]];
	///
				[[MODULE:FRAGMENT:NdotL Stylization:AFTER_RAMP(ndl, ramp)]]
				half3 color = half3(0,0,0);
				[[MODULE:FRAGMENT:Rim Lighting(ndvRaw, emission, albedo, normalWS, viewDirWS, input.screenPosition, ndl, atten, input)]]
				half3 accumulatedRamp = ramp * max(lightColor.r, max(lightColor.g, lightColor.b));
				half3 accumulatedColors = ramp * lightColor.rgb;
///

				[[MODULE:FRAGMENT:Specular(emission, normalWS, tangentWS, lightDir, viewDirWS, ndl, ndvRaw, atten, mainLight)]]
				[[MODULE:FRAGMENT:Subsurface Scattering:LWRP_MAIN_LIGHT(color, normalWS, viewDirWS, albedo, lightColor, lightDir, atten)]]

				// Additional lights loop
/// IF !DISABLE_ADDITIONAL_LIGHTS
			#ifdef _ADDITIONAL_LIGHTS
	/// IF CLAMP_LIGHTS_INTENSITY_ADD
				half3 accumulatedColorsAdditional = half3(0, 0, 0);
	///
				uint pixelLightCount = GetAdditionalLightsCount();

	/// IF ENABLE_FORWARD_PLUS
				#if USE_FORWARD_PLUS
					// Additional directional lights in Forward+
					for (uint lightIndex = 0; lightIndex < min(URP_FP_DIRECTIONAL_LIGHTS_COUNT, MAX_VISIBLE_LIGHTS); lightIndex++)
					{
						FORWARD_PLUS_SUBTRACTIVE_LIGHT_CHECK

						Light light = GetAdditionalLight(lightIndex, positionWS, shadowMask);

#=========================================================================
#NOTE: SECTION DUPLICATED FOR FORWARD+ ADDITIONAL DIRECTIONAL LIGHTS BELOW
						#if defined(_LIGHT_LAYERS)
							if (IsMatchingLightLayer(light.layerMask, meshRenderingLayers))
						#endif
						{
		/// IF HOOK_OTHER_LIGHTS_SHADOWMAP
							light.shadowAttenuation = [[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Shadows]];
		///
		/// IF HOOK_OTHER_LIGHTS_DIST
							light.distanceAttenuation = [[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Distance]];
		///
							half atten = light.shadowAttenuation * light.distanceAttenuation;

		/// IF HOOK_OTHER_LIGHTS_ATTEN
							atten = [[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Attenuation]];
		///
		/// IF SSAO
							#if defined(_SCREEN_SPACE_OCCLUSION)
								light.color *= aoFactor.directAmbientOcclusion;
							#endif
		///

							#if defined(_LIGHT_LAYERS)
								half3 lightDir = half3(0, 1, 0);
								half3 lightColor = half3(0, 0, 0);
								if (IsMatchingLightLayer(light.layerMask, meshRenderingLayers))
								{
									lightColor = light.color.rgb;
									lightDir = light.direction;
								}
							#else
								half3 lightColor = light.color.rgb;
								half3 lightDir = light.direction;
							#endif

		/// IF HOOK_OTHER_LIGHTS_DIR
							lightDir = normalize([[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Direction]]);
		///
		/// IF HOOK_OTHER_LIGHTS_COLOR
							lightColor = [[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Color]];
		///

							half ndl = dot(normalWS, lightDir);
		/// IF BYPASS_LIGHT_FALLOFF
							ndl *= light.distanceAttenuation;
		///
							[[MODULE:FRAGMENT:NdotL Stylization:AFTER_NDL(ndl)]]
		/// IF ATTEN_AT_NDL
							// apply attenuation (shadowmaps & point/spot lights attenuation)
							ndl *= atten;
		///
							half3 ramp;
							[[MODULE:FRAGMENT:Ramp Shading LWRP:ADDITIONAL_LIGHT(ramp, ndl)]]

		/// IF !ENABLE_SHADOW_2ND_LIGHTS && !ENABLE_SHADOW_2ND_LIGHTS_SAT && !ATTEN_AT_NDL
							// apply attenuation (shadowmaps & point/spot lights attenuation)
							ramp *= atten;
		///

		/// IF SHADOW_COLOR_MAIN_DIR
							// apply highlight color
			/// IF ENABLE_SHADOW_2ND_LIGHTS || ENABLE_SHADOW_2ND_LIGHTS_SAT
							ramp = lerp([[VALUE:Shadow Color]], [[VALUE:Highlight Color]], ramp);
				/// IF !ATTEN_AT_NDL
					/// IF ENABLE_SHADOW_2ND_LIGHTS_SAT
							ramp *= saturate(atten);
					/// ELSE
							ramp *= atten;
					///
				///
			/// ELSE
							ramp = lerp(half3(0,0,0), [[VALUE:Highlight Color]], ramp);
			///
							[[MODULE:FRAGMENT:NdotL Stylization:AFTER_RAMP(ndl, ramp)]]
#							[[MODULE:FRAGMENT:Sketch(ramp)]]

							// output color
							color += albedo * lightColor.rgb * ramp;
#							[[MODULE:FRAGMENT:Sketch:APPLY(color.rgb)]]
		/// ELSE
							[[MODULE:FRAGMENT:NdotL Stylization:AFTER_RAMP(ndl, ramp)]]
							accumulatedRamp += ramp * max(lightColor.r, max(lightColor.g, lightColor.b));
			/// IF CLAMP_LIGHTS_INTENSITY_ADD
							accumulatedColorsAdditional += ramp * lightColor.rgb;
			/// ELSE
							accumulatedColors += ramp * lightColor.rgb;
			///
		///

							[[MODULE:FRAGMENT:Specular(emission, normalWS, tangentWS, lightDir, viewDirWS, ndl, ndvRaw, atten, light)]]
							[[MODULE:FRAGMENT:Subsurface Scattering:LWRP_ADDITIONAL_LIGHT(color, normalWS, viewDirWS, albedo, lightColor, lightDir, atten)]]
							[[MODULE:FRAGMENT:Rim Lighting:ADDITIONAL_LIGHT(ndvRaw, emission, normalWS, viewDirWS, input.screenPosition, ndl, atten, input)]]
						}
#=========================================================================
					}

					// Data with dummy struct used in Forward+ macro (LIGHT_LOOP_BEGIN)
					InputDataForwardPlusDummy inputData;
					inputData.normalizedScreenSpaceUV = normalizedScreenSpaceUV;
					inputData.positionWS = positionWS;
				#endif
	///

	/// IF UNITY_2021_2
				LIGHT_LOOP_BEGIN(pixelLightCount)
	/// ELSE
				for (uint lightIndex = 0u; lightIndex < pixelLightCount; ++lightIndex)
	///
				{
	/// IF BYPASS_LIGHT_FALLOFF
					Light light = GetAdditionalLight_BypassFalloff(lightIndex, positionWS);
					half atten = light.shadowAttenuation;
	/// ELSE
					#if defined(URP_10_OR_NEWER)
						Light light = GetAdditionalLight(lightIndex, positionWS, shadowMask);
					#else
						Light light = GetAdditionalLight(lightIndex, positionWS);
					#endif
#=========================================================================
#NOTE: SECTION DUPLICATED FOR FORWARD+ ADDITIONAL DIRECTIONAL LIGHTS ABOVE
	/// IF HOOK_OTHER_LIGHTS_SHADOWMAP
					light.shadowAttenuation = [[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Shadows]];
	///
	/// IF HOOK_OTHER_LIGHTS_DIST
					light.distanceAttenuation = [[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Distance]];
	///
					half atten = light.shadowAttenuation * light.distanceAttenuation;
	///
	/// IF HOOK_OTHER_LIGHTS_ATTEN
					atten = [[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Attenuation]];
	///
	/// IF SSAO
					#if defined(_SCREEN_SPACE_OCCLUSION)
						light.color *= aoFactor.directAmbientOcclusion;
					#endif
	///

					#if defined(_LIGHT_LAYERS)
						half3 lightDir = half3(0, 1, 0);
						half3 lightColor = half3(0, 0, 0);
						if (IsMatchingLightLayer(light.layerMask, meshRenderingLayers))
						{
							lightColor = light.color.rgb;
							lightDir = light.direction;
						}
					#else
						half3 lightColor = light.color.rgb;
						half3 lightDir = light.direction;
					#endif

	/// IF HOOK_OTHER_LIGHTS_DIR
					lightDir = normalize([[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Direction]]);
	///
	/// IF HOOK_OTHER_LIGHTS_COLOR
					lightColor = [[SAMPLE_VALUE_SHADER_PROPERTY:Additional Lights Color]];
	///

					half ndl = dot(normalWS, lightDir);
	/// IF BYPASS_LIGHT_FALLOFF
					ndl *= light.distanceAttenuation;
	///
					[[MODULE:FRAGMENT:NdotL Stylization:AFTER_NDL(ndl)]]
	/// IF ATTEN_AT_NDL
					// apply attenuation (shadowmaps & point/spot lights attenuation)
					ndl *= atten;
	///
					half3 ramp;
					[[MODULE:FRAGMENT:Ramp Shading LWRP:ADDITIONAL_LIGHT(ramp, ndl)]]

	/// IF !ENABLE_SHADOW_2ND_LIGHTS && !ENABLE_SHADOW_2ND_LIGHTS_SAT && !ATTEN_AT_NDL
					// apply attenuation (shadowmaps & point/spot lights attenuation)
					ramp *= atten;
	///

	/// IF SHADOW_COLOR_MAIN_DIR
					// apply highlight color
		/// IF ENABLE_SHADOW_2ND_LIGHTS || ENABLE_SHADOW_2ND_LIGHTS_SAT
					ramp = lerp([[VALUE:Shadow Color]], [[VALUE:Highlight Color]], ramp);
			/// IF !ATTEN_AT_NDL
				/// IF ENABLE_SHADOW_2ND_LIGHTS_SAT
					ramp *= saturate(atten);
				/// ELSE
					ramp *= atten;
				///
			///
		/// ELSE
					ramp = lerp(half3(0,0,0), [[VALUE:Highlight Color]], ramp);
		///
					[[MODULE:FRAGMENT:NdotL Stylization:AFTER_RAMP(ndl, ramp)]]
#					[[MODULE:FRAGMENT:Sketch(ramp)]]

					// output color
					color += albedo * lightColor.rgb * ramp;
#					[[MODULE:FRAGMENT:Sketch:APPLY(color.rgb)]]
	/// ELSE
					[[MODULE:FRAGMENT:NdotL Stylization:AFTER_RAMP(ndl, ramp)]]
					accumulatedRamp += ramp * max(lightColor.r, max(lightColor.g, lightColor.b));
		/// IF CLAMP_LIGHTS_INTENSITY_ADD
					accumulatedColorsAdditional += ramp * lightColor.rgb;
		/// ELSE
					accumulatedColors += ramp * lightColor.rgb;
		///
	///

					[[MODULE:FRAGMENT:Specular(emission, normalWS, tangentWS, lightDir, viewDirWS, ndl, ndvRaw, atten, light)]]
					[[MODULE:FRAGMENT:Subsurface Scattering:LWRP_ADDITIONAL_LIGHT(color, normalWS, viewDirWS, albedo, lightColor, lightDir, atten)]]
					[[MODULE:FRAGMENT:Rim Lighting:ADDITIONAL_LIGHT(ndvRaw, emission, normalWS, viewDirWS, input.screenPosition, ndl, atten, input)]]
#=========================================================================
				}
	/// IF UNITY_2021_2
				LIGHT_LOOP_END
	///
			#endif
			#ifdef _ADDITIONAL_LIGHTS_VERTEX
				color += input.vertexLights * albedo;
			#endif
///
/// IF !SHADOW_COLOR_MAIN_DIR

				accumulatedRamp = saturate(accumulatedRamp);
	/// IF CLAMP_LIGHTS_INTENSITY_ADD && !DISABLE_ADDITIONAL_LIGHTS
			#ifdef _ADDITIONAL_LIGHTS
				accumulatedColorsAdditional = min(accumulatedColorsAdditional, [[VALUE:Max Light Intensity]]);
				accumulatedColors += accumulatedColorsAdditional;
			#endif
	/// ELIF CLAMP_LIGHTS_INTENSITY
				accumulatedColors = min(accumulatedColors, [[VALUE:Max Light Intensity]]);
	///
				[[MODULE:FRAGMENT:ShadowHSV(albedo,accumulatedRamp)]];
				[[MODULE:FRAGMENT:Sketch(accumulatedRamp)]]
	/// IF SHADOW_COLOR_LERP
				albedo = lerp([[VALUE:Shadow Color]], albedo, accumulatedRamp);
	///
				half3 shadowColor = (1 - accumulatedRamp.rgb) * [[VALUE:Shadow Color]];
				accumulatedRamp = accumulatedColors.rgb * [[VALUE:Highlight Color]] + shadowColor;
				color += albedo * accumulatedRamp;
				[[MODULE:FRAGMENT:Sketch:APPLY(color.rgb)]]
///

				// apply ambient
/// IF HOOK_FINAL_AMBIENT
				indirectDiffuse.rgb = [[SAMPLE_VALUE_SHADER_PROPERTY:Final Ambient]];
///
				color += indirectDiffuse;

/// IF AUTO_TRANSPARENT_BLENDING
				// Premultiply blending
				#if defined(_ALPHAPREMULTIPLY_ON)
					color.rgb *= alpha;
				#endif

///
/// IF GLOSSY_REFLECTIONS
	/// IF REFLECTION_SHADER_FEATURE
				#if defined(TCP2_REFLECTIONS)
	///
				half3 reflections = half3(0, 0, 0);

				// World reflection
				half reflectionRoughness = 1 - [[VALUE:Reflection Smoothness]];
				half3 reflectVector = reflect(-viewDirWS, normalWS);
				
	/// IF ENABLE_FORWARD_PLUS
				#if USE_FORWARD_PLUS
					half3 indirectSpecular = GlossyEnvironmentReflection(reflectVector, positionWS, reflectionRoughness, occlusion, normalizedScreenSpaceUV);
				#else
					half3 indirectSpecular = GlossyEnvironmentReflection(reflectVector, reflectionRoughness, occlusion);
				#endif
	/// ELSE
				half3 indirectSpecular = GlossyEnvironmentReflection(reflectVector, reflectionRoughness, occlusion);
	///
				half reflectionRoughness4 = max(pow(reflectionRoughness, 4), 6.103515625e-5);
				float surfaceReductionRefl = 1.0 / (reflectionRoughness4 + 1.0);
				reflections += indirectSpecular * surfaceReductionRefl;

				[[MODULE:FRAGMENT:Reflection(reflections, normalWS, viewDirWS, ndvRaw, input.[[INPUT_VALUE:screenPosition]], normalTS)]]

				reflections *= [[VALUE:Reflection Color]];
				color.rgb += reflections;
	/// IF REFLECTION_SHADER_FEATURE
				#endif
	///
/// ELSE
	#if_not_empty

				half3 reflections = half3(0, 0, 0);
	#start_not_empty_block
				[[MODULE:FRAGMENT:Reflection(reflections, normalWS, viewDirWS, ndvRaw, input.[[INPUT_VALUE:screenPosition]], normalTS)]]
	#end_not_empty_block
	/// IF PLANAR_REFLECTION
				reflections *= [[VALUE:Reflection Color]];
	///
				color.rgb += reflections;
	#end_not_empty
///

				color += emission;
/// IF FOG

				// Mix the pixel color with fogColor. You can optionally use MixFogColor to override the fogColor with a custom one.
				float fogFactor = input.worldPosAndFog.w;
				color = MixFog(color, fogFactor);
///
				[[MODULE:FRAGMENT:Vertical Fog(color.rgb, input.worldPosAndFog.xyz, input.[[INPUT_VALUE:objPos]])]]
/// IF HOOK_FINAL_COLOR

				color.rgb = [[SAMPLE_VALUE_SHADER_PROPERTY:Final Color]];
///

				[[MODULE:FRAGMENT:Terrain:FINAL_COLOR(terrain_weight, terrain_weight_1, color)]]
				
				[[INJECTION_POINT:Main Pass/Fragment Shader/End]]

/// IF ENABLE_RENDERING_LAYERS
				#if defined(URP_14_OR_NEWER) && defined(_WRITE_RENDERING_LAYERS)
					outRenderingLayers = float4(EncodeMeshRenderingLayer(meshRenderingLayers), 0, 0, 0);
				#endif
///

				return half4(color, alpha);
#DISABLE_IMPL_ALL
			}
			ENDHLSL
/// IF !(TERRAIN_SHADER && TERRAIN_BASEGEN && TERRAIN_BASEGEN_SAME_SHADER)
		}
///

/// IF OUTLINE
		// Outline
		Pass
		{
			Name "Outline"
	/// IF OUTLINE_URP_FEATURE
			Tags { "LightMode" = "Outline" }
	///
			Tags
			{
				[[INJECTION_POINT:Outline Pass/Tags]]
			}
			Cull Front
	/// IF OUTLINE_ZSMOOTH
			Offset [[VALUE:Outline Offset Factor]],[[VALUE:Outline Offset Units]]
	///
	/// IF OUTLINE_BLENDING
			Blend [[VALUE:Outline Blend Source]] [[VALUE:Outline Blend Destination]]
	/// ELIF OUTLINE_OPAQUE
			Blend Off
	///
			[[INJECTION_POINT:Outline Pass/Shader States]]
	/// IF OUTLINE_BEHIND_STENCIL
			Stencil
			{
				Ref [[VALUE:Outline Stencil Reference]]
				Comp NotEqual
				Pass Keep
				[[INJECTION_POINT:Outline Pass/Stencil]]
			}
	///

			HLSLPROGRAM

			#pragma vertex vertex_outline
			#pragma fragment fragment_outline

			#pragma target @%SHADER_TARGET%@

			#pragma multi_compile _ TCP2_COLORS_AS_NORMALS TCP2_TANGENT_AS_NORMALS TCP2_UV1_AS_NORMALS TCP2_UV2_AS_NORMALS TCP2_UV3_AS_NORMALS TCP2_UV4_AS_NORMALS
			#pragma multi_compile _ TCP2_UV_NORMALS_FULL TCP2_UV_NORMALS_ZW
			#pragma multi_compile_instancing
			[[GPU_INSTANCING_OPTIONS]]
			
	#if_not_empty
			//--------------------------------------
			// Toony Colors Pro 2 keywords
	#start_not_empty_block
			[[MODULE:SHADER_FEATURES_BLOCK:Wind]]
			[[MODULE:SHADER_FEATURES_BLOCK:Dissolve]]
			[[MODULE:SHADER_FEATURES_BLOCK:Vertex Displacement]]
	#end_not_empty_block
	#end_not_empty

			ENDHLSL
		}
///
#PASS
/// IF !(TERRAIN_SHADER && TERRAIN_BASEGEN && TERRAIN_BASEGEN_SAME_SHADER)
		// Depth & Shadow Caster Passes
		HLSLINCLUDE

		#if defined(SHADOW_CASTER_PASS) || defined(DEPTH_ONLY_PASS)

			#define fixed half
			#define fixed2 half2
			#define fixed3 half3
			#define fixed4 half4

			float3 _LightDirection;
			float3 _LightPosition;

			struct Attributes
			{
				float4 vertex   : POSITION;
				float3 normal   : NORMAL;
/// IF VERTEXMOTION_NORMAL || CURVED_WORLD_NORMAL || CURVED_WORLD_2020
				float4 tangent : TANGENT;
///
				[[VERTEX_INPUT_TEXCOORDS]]
/// IF USE_VERTEX_COLORS_VERT
				half4 vertexColor : COLOR;
///
				[[INJECTION_POINT:Depth + Shadow Caster Pass/Attributes]]
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct Varyings
			{
				float4 positionCS     : SV_POSITION;
/// IF CURVED_WORLD || USE_WORLD_NORMAL_FRAGMENT
				float3 normal         : NORMAL;
///
/// IF ENABLE_DEPTH_NORMALS_PASS
			#if defined(DEPTH_NORMALS_PASS)
				float3 normalWS : TEXCOORD0;
			#endif
///
				[[INPUT_STRUCT_SEMANTICS:1]]
#INPUT_VARIABLES
/// IF USE_VERTEX_COLORS_FRAG
				half4 vertexColor;
///
/// IF USE_SCREEN_POSITION_FRAGMENT
				float4 screenPosition;
///
/// IF USE_WORLD_POSITION_FRAGMENT
				float3 positionWS;
///
/// IF USE_OBJECT_POSITION_FRAGMENT
				float3 objPos;
///
/// IF USE_OBJECT_NORMAL_FRAGMENT
				float3 objNormal;
///
/// IF UV_SINE_ANIMATION_FRAGMENT
				float2 sinUvAnimVertexPos;
///
/// IF UV_SINE_ANIMATION_FRAGMENT_WORLD
				float2 sinUvAnimVertexWorldPos;
///
				[[MODULE:INPUT]]
#END
				[[INJECTION_POINT:Depth + Shadow Caster Pass/Varyings]]
			#if defined(DEPTH_ONLY_PASS)
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			#endif
			};

			float4 GetShadowPositionHClip(Attributes input)
			{
				float3 positionWS = TransformObjectToWorld(input.vertex.xyz);
				float3 normalWS = TransformObjectToWorldNormal(input.normal);

/// IF UNITY_2021_1
				#if _CASTING_PUNCTUAL_LIGHT_SHADOW
					float3 lightDirectionWS = normalize(_LightPosition - positionWS);
				#else
					float3 lightDirectionWS = _LightDirection;
				#endif
				float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, lightDirectionWS));
/// ELSE
				float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, _LightDirection));
///

				#if UNITY_REVERSED_Z
					positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
				#else
					positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
				#endif

				return positionCS;
			}

#VERTEX, INPUT = input, OUTPUT = output
			Varyings ShadowDepthPassVertex(Attributes input)
			{
				Varyings output = (Varyings)0;
				UNITY_SETUP_INSTANCE_ID(input);
				#if defined(DEPTH_ONLY_PASS)
					UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);
				#endif

				[[INJECTION_POINT:Depth + Shadow Caster Pass/Vertex Shader/Start]]

/// IF USE_WORLD_POSITION_UV_VERTEX
				float3 worldPosUv = mul(unity_ObjectToWorld, input.vertex).xyz;
///
/// IF USE_WORLD_NORMAL_UV_VERTEX || USE_WORLD_NORMAL_FRAGMENT || USE_NDV_VERTEX
				float3 worldNormalUv = mul(unity_ObjectToWorld, float4(input.normal, 1.0)).xyz;
///
/// IF UV_SINE_ANIMATION_VERTEX

				// Used for texture UV sine animation
				float2 sinUvAnimVertexPos = input.vertex.xy + input.vertex.yz;
	/// IF UV_SINE_ANIMATION_FRAGMENT
				output.[[INPUT_VALUE:sinUvAnimVertexPos]] = sinUvAnimVertexPos;
	///
///
/// IF UV_SINE_ANIMATION_VERTEX_WORLD

				// Used for texture UV sine animation (world space)
				float2 sinUvAnimVertexWorldPos = worldPosUv.xy + worldPosUv.yz;
	/// IF UV_SINE_ANIMATION_FRAGMENT_WORLD
				output.[[INPUT_VALUE:sinUvAnimVertexWorldPos]] = sinUvAnimVertexWorldPos;
	///
///

				[[VERTEX_TEXCOORDS]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]

				[[MODULE:VERTEX:VertExmotion:LWRP(input.vertex, input.normal, input.tangent)]]
				[[MODULE:VERTEX:CurvedWorld(input.vertex, input.normal, input.tangent)]]

				[[MODULE:VERTEX:Vertex Displacement(input.vertex)]]
/// IF HOOK_VERTEX_POSITION
				input.vertex.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position]];
///
/// IF HOOK_VERTEX_POSITION_WORLD || APPLY_WORLD_POSITION || USE_WORLD_POSITION_FRAGMENT || USE_WORLD_POSITION_VERTEX
				float3 worldPos = mul(unity_ObjectToWorld, input.vertex).xyz;
///
				[[MODULE:VERTEX:Vertex Displacement:WORLD(worldPos)]]
/// IF HOOK_VERTEX_POSITION_WORLD
				worldPos.xyz = [[SAMPLE_VALUE_SHADER_PROPERTY:Vertex Position World]];
///
				[[MODULE:VERTEX:Wind(worldPos.xyz)]]
/// IF APPLY_WORLD_POSITION
				input.vertex.xyz = mul(unity_WorldToObject, float4(worldPos, 1)).xyz;
///
				[[MODULE:VERTEX:Water(input.vertex, worldPos, input.normal)]]
/// IF USE_VIEW_DIRECTION_VERTEX || USE_CLIP_POSITION_VERTEX || USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX || USE_WORLD_POSITION_FRAGMENT
				VertexPositionInputs vertexInput = GetVertexPositionInputs(input.vertex.xyz);
///
/// IF USE_VIEW_DIRECTION_VERTEX
				half3 viewDirWS = SafeNormalize(GetCameraPositionWS() - vertexInput.positionWS);
///
/// IF USE_NDV_VERTEX
				half ndv = abs(dot(viewDirWS, worldNormalUv));
				half ndvRaw = ndv;
	/// IF USE_NDV_INVERT_VERT
				ndv = 1 - ndv;
	///
	/// IF USE_NDV_MIN_MAX_VERT
				ndv = smoothstep([[VALUE:NDV Min Vert]], [[VALUE:NDV Max Vert]], ndv);
	///
///
/// IF USE_SCREEN_POSITION_FRAGMENT || USE_SCREEN_POSITION_VERTEX

				// Screen Space UV
				float4 screenPos = ComputeScreenPos(vertexInput.positionCS);
	/// IF USE_SCREEN_POSITION_FRAGMENT
				output.[[INPUT_VALUE:screenPosition]] = screenPos;
	///
///
				[[MODULE:VERTEX:Depth Texture(output.[[INPUT_VALUE:screenPosition]], clipPos)]]
				[[MODULE:VERTEX:Screen Space UV(screenPos, vertexInput.positionCS, output)]]
/// IF USE_VERTEX_COLORS_FRAG
				output.vertexColor = input.vertexColor;
///
/// IF USE_WORLD_NORMAL_FRAGMENT
				output.normal = normalize(worldNormalUv);
///
/// IF USE_WORLD_POSITION_FRAGMENT
				output.[[INPUT_VALUE:positionWS]] = vertexInput.positionWS;
///
/// IF USE_OBJECT_POSITION_FRAGMENT
				output.[[INPUT_VALUE:objPos]] = input.vertex.xyz;
///
/// IF USE_OBJECT_NORMAL_FRAGMENT
				output.[[INPUT_VALUE:objNormal]] = input.normal.xyz;
///

				#if defined(DEPTH_ONLY_PASS)
					output.positionCS = TransformObjectToHClip(input.vertex.xyz);
/// IF ENABLE_DEPTH_NORMALS_PASS
					#if defined(DEPTH_NORMALS_PASS)
						float3 normalWS = TransformObjectToWorldNormal(input.normal);
						output.normalWS = normalWS; // already normalized in TransformObjectToWorldNormal
					#endif
///
				#elif defined(SHADOW_CASTER_PASS)
					output.positionCS = GetShadowPositionHClip(input);
				#else
					output.positionCS = float4(0,0,0,0);
				#endif

				[[INJECTION_POINT:Depth + Shadow Caster Pass/Vertex Shader/End]]

				return output;
			}

#FRAGMENT, INPUT = input, OUTPUT = no_output
			half4 ShadowDepthPassFragment(
				Varyings input
/// IF ENABLE_DEPTH_NORMALS_PASS
	#if defined(DEPTH_NORMALS_PASS) && defined(_WRITE_RENDERING_LAYERS)
				, out float4 outRenderingLayers : SV_Target1
	#endif
///
			) : SV_TARGET
			{
				#if defined(DEPTH_ONLY_PASS)
					UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);
				#endif

				[[INJECTION_POINT:Depth + Shadow Caster Pass/Fragment Shader/Start]]

/// IF USE_WORLD_POSITION_FRAGMENT
				float3 positionWS = input.[[INPUT_VALUE:positionWS]];
///
/// IF USE_WORLD_NORMAL_FRAGMENT
				float3 normalWS = normalize(input.normal);
///

				[[MODULE:FRAGMENT:Screen Space UV(input.[[INPUT_VALUE:screenPosition]], input)]]
				[[SAMPLE_CUSTOM_PROPERTIES]]
				[[SAMPLE_SHADER_PROPERTIES]]

/// IF USE_VIEW_DIRECTION_FRAGMENT
				half3 viewDirWS = SafeNormalize(GetCameraPositionWS() - positionWS);
///
/// IF USE_NDV_FRAGMENT
#		/// IF USE_NDV_IGNORE_NORMAL_MAP
#				half ndv = abs(dot(viewDirWS, normalWSVertex));
#		/// ELSE
				half ndv = abs(dot(viewDirWS, normalWS));
#		///
				half ndvRaw = ndv;
	/// IF USE_NDV_INVERT_FRAG
				ndv = 1 - ndv;
	///
	/// IF USE_NDV_MIN_MAX_FRAG
				ndv = smoothstep([[VALUE:NDV Min Frag]], [[VALUE:NDV Max Frag]], ndv);
	///

///
/// IF CUSTOM_ALBEDO
				half3 albedo = half3(1,1,1);
				half alpha = 1;
/// ELSE
# we don't care about color for the depth/shadow pass
				half3 albedo = half3(1,1,1);
				half alpha = [[VALUE:Alpha]];
///
				half3 emission = half3(0,0,0);
				[[MODULE:FRAGMENT:Dissolve(emission)]]
/// IF ALPHA_TESTING
				// Alpha Testing
	/// IF ALPHA_TESTING_DITHERING
		/// IF ALPHA_DITHER_TEXTURE
				half cutoffValue = [[VALUE:Dithering Texture]];
		/// ELIF ALPHA_DITHER_8x8
				half cutoffValue = Dither8x8(input.positionCS.xy);
		/// ELSE
				half cutoffValue = Dither4x4(input.positionCS.xy);
		///
	/// ELSE
				half cutoffValue = [[VALUE:Cutoff]];
	///
	/// IF !ALPHA_TO_COVERAGE
		/// IF ALPHA_TESTING_DITHERING
				#if defined(DEPTH_ONLY_PASS)
					clip(alpha - cutoffValue);
				#endif
		/// ELSE
				clip(alpha - cutoffValue);
		///
	/// ELIF !ALPHA_TO_COVERAGE_RAW
				// Sharpen Alpha-to-Coverage
				alpha = (alpha - cutoffValue) / max(fwidth(alpha), 0.0001) + 0.5;
	///
///

				[[INJECTION_POINT:Depth + Shadow Caster Pass/Fragment Shader/End]]

/// IF ENABLE_DEPTH_NORMALS_PASS
				#if defined(DEPTH_NORMALS_PASS)
					#if defined(_WRITE_RENDERING_LAYERS)
						uint meshRenderingLayers = GetMeshRenderingLayer();
						outRenderingLayers = float4(EncodeMeshRenderingLayer(meshRenderingLayers), 0, 0, 0);
					#endif

					#if defined(URP_12_OR_NEWER)
						return float4(input.normalWS.xyz, 0.0);
					#else
						return float4(PackNormalOctRectEncode(TransformWorldToViewDir(input.normalWS, true)), 0.0, 0.0);
					#endif
				#endif
///


/// IF ALPHA_TESTING && ALPHA_TO_COVERAGE
				return alpha;
/// ELSE
				return 0;
///
			}

		#endif
		ENDHLSL
/// IF !DISABLE_SHADOW_CASTING

		Pass
		{
			Name "ShadowCaster"
			Tags
			{
				"LightMode" = "ShadowCaster"
				[[INJECTION_POINT:Shadow Caster Pass/Tags]]
			}

	/// IF ALPHA_TESTING && ALPHA_TO_COVERAGE
			AlphaToMask On
	///
			ZWrite On
			ZTest LEqual
	/// IF CULLING
			Cull [[VALUE:Face Culling]]
	///
			[[INJECTION_POINT:Shadow Caster Pass/Shader States]]

			HLSLPROGRAM
			// Required to compile gles 2.0 with standard srp library
			#pragma prefer_hlslcc gles
			#pragma exclude_renderers d3d11_9x
			#pragma target 2.0
			[[INJECTION_POINT:Shadow Caster Pass/Pragma]]

			// using simple #define doesn't work, we have to use this instead
			#pragma multi_compile SHADOW_CASTER_PASS

			//--------------------------------------
			// GPU Instancing
			#pragma multi_compile_instancing
			[[GPU_INSTANCING_OPTIONS]]
	/// IF UNITY_2021_1
			#pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW
	///

			#pragma vertex ShadowDepthPassVertex
			#pragma fragment ShadowDepthPassFragment

	#if_not_empty
			//--------------------------------------
			// Toony Colors Pro 2 keywords
	#start_not_empty_block
			[[MODULE:SHADER_FEATURES_BLOCK:Wind]]
			[[MODULE:SHADER_FEATURES_BLOCK:Dissolve]]
			[[MODULE:SHADER_FEATURES_BLOCK:Vertex Displacement]]
	#end_not_empty_block
	#end_not_empty

			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Shadows.hlsl"

			ENDHLSL
		}
///

/// IF !USE_DEPTH_BUFFER
		Pass
		{
			Name "DepthOnly"
			Tags
			{
				"LightMode" = "DepthOnly"
				[[INJECTION_POINT:Depth Pass/Tags]]
			}

/// IF ALPHA_TESTING && ALPHA_TO_COVERAGE
			AlphaToMask On
///
			ZWrite On
			ColorMask 0
/// IF AUTO_TRANSPARENT_BLENDING
			Cull [_Cull]
/// ELIF CULLING
			Cull [[VALUE:Face Culling]]
///
			[[INJECTION_POINT:Depth Pass/Shader States]]

			HLSLPROGRAM

			// Required to compile gles 2.0 with standard srp library
			#pragma prefer_hlslcc gles
			#pragma exclude_renderers d3d11_9x
			#pragma target 2.0
			[[INJECTION_POINT:Depth Pass/Pragma]]

			//--------------------------------------
			// GPU Instancing
			#pragma multi_compile_instancing
			[[GPU_INSTANCING_OPTIONS]]

			// using simple #define doesn't work, we have to use this instead
			#pragma multi_compile DEPTH_ONLY_PASS

			#pragma vertex ShadowDepthPassVertex
			#pragma fragment ShadowDepthPassFragment


	#if_not_empty
			//--------------------------------------
			// Toony Colors Pro 2 keywords
	#start_not_empty_block
			[[MODULE:SHADER_FEATURES_BLOCK:Wind]]
			[[MODULE:SHADER_FEATURES_BLOCK:Dissolve]]
			[[MODULE:SHADER_FEATURES_BLOCK:Vertex Displacement]]
	#end_not_empty_block
	#end_not_empty

			ENDHLSL
		}
///

/// IF ENABLE_DEPTH_NORMALS_PASS
		Pass
		{
			Name "DepthNormals"
			Tags
			{
				"LightMode" = "DepthNormals"
				[[INJECTION_POINT:Depth Normals Pass/Tags]]
			}

			ZWrite On
	/// IF CULLING
			Cull [[VALUE:Face Culling]]
	///
			[[INJECTION_POINT:Depth Normals Pass/Shader States]]

			HLSLPROGRAM
			#pragma exclude_renderers gles gles3 glcore
			#pragma target 2.0
			[[INJECTION_POINT:Depth Normals Pass/Pragma]]

			#pragma multi_compile_instancing
			[[GPU_INSTANCING_OPTIONS]]

	/// IF ENABLE_RENDERING_LAYERS
			#pragma multi_compile_fragment _ _WRITE_RENDERING_LAYERS
	///

			// using simple #define doesn't work, we have to use this instead
			#pragma multi_compile DEPTH_ONLY_PASS
			#pragma multi_compile DEPTH_NORMALS_PASS

			#pragma vertex ShadowDepthPassVertex
			#pragma fragment ShadowDepthPassFragment

			ENDHLSL
		}
///

/// IF ENABLE_META_PASS
		// Used for Baking GI. This pass is stripped from build.
		UsePass "Universal Render Pipeline/Lit/Meta"
///
/// IF TERRAIN_SHADER

		// Scene picking for terrain shader
		UsePass "Hidden/Nature/Terrain/Utilities/PICKING"
///

/// IF CURVED_WORLD_2020
		// Curved World 2020 - Scene Picking passes
		Pass
		{
			Name "ScenePickingPass"
			Tags { "LightMode" = "Picking" }

			BlendOp Add
			Blend One Zero
			ZWrite On
			Cull Off

			CGPROGRAM

			#include "HLSLSupport.cginc"
			#include "UnityShaderVariables.cginc"
			#include "UnityShaderUtilities.cginc"

			#pragma target 3.0

			#pragma shader_feature_local _ALPHATEST_ON
			#pragma shader_feature_local _ALPHAPREMULTIPLY_ON
			#pragma multi_compile_instancing

			[[MODULE:SHADER_FEATURES_BLOCK:CurvedWorld]]

			#pragma vertex vertEditorPass
			#pragma fragment fragScenePickingPass

			#include "@%CURVED_WORLD_2020_INCLUDE%@/SceneSelection.cginc"

			ENDCG
		}

		Pass
		{
			Name "SceneSelectionPass"
			Tags { "LightMode" = "SceneSelectionPass" }

			BlendOp Add
			Blend One Zero
			ZWrite On
			Cull Off

			CGPROGRAM

			#include "HLSLSupport.cginc"
			#include "UnityShaderVariables.cginc"
			#include "UnityShaderUtilities.cginc"

			#pragma target 3.0

			#pragma shader_feature_local _ALPHATEST_ON
			#pragma shader_feature_local _ALPHAPREMULTIPLY_ON
			#pragma multi_compile_instancing

			[[MODULE:SHADER_FEATURES_BLOCK:CurvedWorld]]

			#pragma vertex vertEditorPass
			#pragma fragment fragSceneHighlightPass

			#include "@%CURVED_WORLD_2020_INCLUDE%@/SceneSelection.cginc"

			ENDCG
		}
///
#------------------------------------------------------------
///
# end of IF !(TERRAIN_SHADER && TERRAIN_BASEGEN && TERRAIN_BASEGEN_SAME_SHADER)
#------------------------------------------------------------
	}

/// IF TERRAIN_SHADER && !TERRAIN_ADDPASS && !TERRAIN_BASEPASS && !TERRAIN_BASEGEN
	/// IF !TERRAIN_SHADER_8_LAYERS
	Dependency "AddPassShader"    = "Hidden/@%SHADER_NAME%@-AddPass"
	///
	Dependency "BaseMapShader"    = "Hidden/@%SHADER_NAME%@-BasePass"
	Dependency "BaseMapGenShader" = "Hidden/@%SHADER_NAME%@-BaseGen"
///

	FallBack "Hidden/InternalErrorShader"
	CustomEditor "ToonyColorsPro.ShaderGenerator.MaterialInspector_SG2"
/// ELSE
#IF TERRAIN_BASEGEN:
[[MODULE:TERRAIN_BASEGEN_SHADER:Terrain]]
///
}