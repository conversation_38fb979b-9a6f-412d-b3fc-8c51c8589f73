1

Common_ReserveGrid 模块的使用方法可以分为以下几个部分：
首先，任何需要放入格子的物体都需要实现 ICommon_ReserveItem 接口：
public class MyItem : MonoBehaviour, ICommon_ReserveItem
{
    // MonoBehaviour 已经自带 gameObject 属性，所以不需要额外实现
}

2

创建格子对象，继承 Common_ReserveGrid：

public class MyGrid : Common_ReserveGrid
{
    // 可以添加获取当前物品的便捷方法
    public MyItem CurrentItem => currentItem?.gameObject.GetComponent<MyItem>();
}


3

在场景中使用：

// 初始化格子系统
List<Common_ReserveGrid> grids = new List<Common_ReserveGrid>();
// ... 添加场景中的格子
Common_ReserveGridManager.Instance.InitGrids(grids);

// 获取空格子
Common_ReserveGrid emptyGrid = Common_ReserveGridManager.Instance.GetFirstEmptyGrid();

// 放置物品
MyItem item = someGameObject.GetComponent<MyItem>();
if (emptyGrid != null)
{
    Common_ReserveGridManager.Instance.TryPlaceItemInGrid(item, emptyGrid);
}

// 检查是否所有格子都被占用
bool allOccupied = Common_ReserveGridManager.Instance.AreAllGridsOccupiedOrLocked();

// 监听所有格子被占用的事件
Common_ReserveGridManager.OnAllGridsOccupied += () => 
{
    Debug.Log("All grids are occupied!");
};



4

具体示例，比如在一个卡牌游戏中：

// 卡牌类
public class Card : MonoBehaviour, ICommon_ReserveItem
{
    public int cardId;
}

// 卡牌格子类
public class CardGrid : Common_ReserveGrid
{
    public Card CurrentCard => currentItem?.gameObject.GetComponent<Card>();
}

// 卡牌管理器
public class CardManager : MonoBehaviour
{
    void Start()
    {
        // 初始化格子
        var grids = GetComponentsInChildren<CardGrid>().ToList<Common_ReserveGrid>();
        Common_ReserveGridManager.Instance.InitGrids(grids);
        
        // 监听格子全满事件
        Common_ReserveGridManager.OnAllGridsOccupied += OnAllGridsFull;
    }

    public void TryPlaceCard(Card card)
    {
        var emptyGrid = Common_ReserveGridManager.Instance.GetFirstEmptyGrid();
        if (emptyGrid != null)
        {
            Common_ReserveGridManager.Instance.TryPlaceItemInGrid(card, emptyGrid);
        }
    }

    private void OnAllGridsFull()
    {
        Debug.Log("No more space for cards!");
    }
}
