using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 通用格子控制器，用于管理一组格子
/// </summary>
/// <typeparam name="T">格子类型，必须继承自Common_ReserveGrid</typeparam>
public class Common_ReserveGridController<T> where T : Common_ReserveGrid
{
    private List<T> currentGrids;
    
    // 当所有格子都被实际占用或锁定时触发
    public event Action OnAllGridsOccupied;
    
    // 当所有格子都被预留或锁定时触发
    public event Action OnAllGridsReserved;
    
    // 格子组的标识符
    public string GroupId { get; private set; }

    /// <summary>
    /// 创建一个新的格子控制器
    /// </summary>
    /// <param name="grids">要管理的格子列表</param>
    /// <param name="groupId">格子组的标识符</param>
    public Common_ReserveGridController(List<T> grids, string groupId)
    {
        if (string.IsNullOrEmpty(groupId))
        {
            Debug.LogError("组名不能为空");
            groupId = "未命名组_" + Guid.NewGuid().ToString().Substring(0, 8);
        }
        
        currentGrids = grids ?? new List<T>();
        GroupId = groupId;
        
        // 设置格子的组ID
        foreach (var grid in currentGrids)
        {
            if (grid != null)
            {
                grid.GroupId = GroupId;
            }
        }
    }

    /// <summary>
    /// 获取当前管理的所有格子
    /// </summary>
    public List<T> CurrentGrids => currentGrids ?? new List<T>();

    /// <summary>
    /// 获取第一个完全空闲的格子（既没有被占用也没有被预留）
    /// </summary>
    public T GetFirstFreeGrid()
    {
        return CurrentGrids.FirstOrDefault(grid => grid.IsFree && !grid.IsLocked);
    }
    
    /// <summary>
    /// 获取第一个未被实际占用的格子（可能被预留）
    /// </summary>
    // public T GetFirstEmptyGrid()
    // {
    //     return CurrentGrids.FirstOrDefault(grid => !grid.IsOccupied && !grid.IsLocked);
    // }

    /// <summary>
    /// 检查是否所有格子都被实际占用或锁定
    /// </summary>
    public bool AreAllGridsOccupiedOrLocked()
    {
        if (CurrentGrids.Count == 0) return false;
        return CurrentGrids.All(grid => grid.IsOccupied || grid.IsLocked);
    }
    
    /// <summary>
    /// 检查是否所有格子都被预留或锁定
    /// </summary>
    public bool AreAllGridsReservedOrLocked()
    {
        if (CurrentGrids.Count == 0) return false;
        return CurrentGrids.All(grid => grid.IsReserved || grid.IsLocked);
    }
    
    public bool AreAllGridsReservedOrLockedOrOccupided()
    {
        if (CurrentGrids.Count == 0) return false;
        return CurrentGrids.All(grid => grid.IsReserved || grid.IsLocked || grid.IsOccupied);
    }
    
    /// <summary>
    /// 检查是否所有格子都是空的（未被实际占用）
    /// </summary>
    // public bool AreAllGridsEmpty()
    // {
    //     if (CurrentGrids.Count == 0) return true;
    //     return CurrentGrids.All(grid => !grid.IsOccupied || grid.IsLocked);
    // }
    
    /// <summary>
    /// 检查是否所有格子都是完全空闲的（既没有被占用也没有被预留）
    /// </summary>
    public bool AreAllGridsCompletelyFree()
    {
        if (CurrentGrids.Count == 0) return true;
        return CurrentGrids.All(grid => grid.IsFree && !grid.IsLocked);
    }

    /// <summary>
    /// 尝试在指定格子中放置物体（实际占用）
    /// </summary>
    public bool TryPlaceItemInGrid(GameObject item, T grid)
    {
        if (grid == null || !CurrentGrids.Contains(grid)) return false;
        
        if (grid.TryPlaceItem(item))
        {
            CheckAndTriggerEvents();
            return true;
        }
        return false;
    }
    
    /// <summary>
    /// 尝试预留指定格子
    /// </summary>
    public bool TryReserveGrid(GameObject item, T grid)
    {
        if (grid == null || !CurrentGrids.Contains(grid)) return false;
        
        if (grid.TryReserveGrid(item))
        {
            CheckAndTriggerEvents();
            return true;
        }
        return false;
    }
    
    /// <summary>
    /// 将预留状态升级为实际占用状态
    /// </summary>
    public bool UpgradeReservationToOccupation(T grid)
    {
        if (grid == null || !CurrentGrids.Contains(grid)) return false;
        
        if (grid.UpgradeReservationToOccupation())
        {
            CheckAndTriggerEvents();
            return true;
        }
        return false;
    }
    
    /// <summary>
    /// 检查并触发相应事件
    /// </summary>
    private void CheckAndTriggerEvents()
    {
        if (AreAllGridsOccupiedOrLocked())
        {
            OnAllGridsOccupied?.Invoke();
        }
        
        if (AreAllGridsReservedOrLocked())
        {
            OnAllGridsReserved?.Invoke();
        }
    }

    /// <summary>
    /// 获取指定格子中的物体
    /// </summary>
    public GameObject GetItemFromGrid(T grid)
    {
        if (grid == null || !CurrentGrids.Contains(grid)) return null;
        return grid.GetItem();
    }
    
    /// <summary>
    /// 通过物体查找对应的格子（实际占用或预留）
    /// </summary>
    /// <param name="item">要查找的物体</param>
    /// <param name="includeReserved">是否包含预留状态的格子</param>
    /// <returns>找到的格子，如果未找到则返回null</returns>
    public T FindGridByItem(GameObject item, bool includeReserved)
    {
        if (item == null) return null;
        
        // 先查找实际占用的格子
        T occupiedGrid = CurrentGrids.FirstOrDefault(grid => 
            grid.IsOccupied && grid.GetItem() == item);
            
        if (occupiedGrid != null)
        {
            return occupiedGrid;
        }
        
        // 如果需要，再查找预留的格子
        if (includeReserved)
        {
            return CurrentGrids.FirstOrDefault(grid => 
                grid.IsReserved && grid.GetItem() == item);
        }
        
        return null;
    }
    
    /// <summary>
    /// 查找物体实际占用的格子
    /// </summary>
    /// <param name="item">要查找的物体</param>
    /// <returns>找到的格子，如果未找到则返回null</returns>
    public T FindOccupiedGridByItem(GameObject item)
    {
        if (item == null) return null;
        return CurrentGrids.FirstOrDefault(grid => 
            grid.IsOccupied && grid.GetItem() == item);
    }
    
    /// <summary>
    /// 查找物体预留的格子
    /// </summary>
    /// <param name="item">要查找的物体</param>
    /// <returns>找到的格子，如果未找到则返回null</returns>
    public T FindReservedGridByItem(GameObject item)
    {
        if (item == null) return null;
        return CurrentGrids.FirstOrDefault(grid => 
            grid.IsReserved && grid.GetItem() == item);
    }

    /// <summary>
    /// 添加新格子
    /// </summary>
    public bool AddGrid(T grid)
    {
        if (grid == null) return false;
        
        // 检查格子是否已经在列表中
        if (CurrentGrids.Contains(grid)) return false;
        
        // 设置格子的组ID
        grid.GroupId = GroupId;
        
        // 添加格子到当前格子列表
        currentGrids.Add(grid);
        return true;
    }
    
    /// <summary>
    /// 移除格子
    /// </summary>
    public bool RemoveGrid(T grid)
    {
        if (grid == null) return false;
        return currentGrids.Remove(grid);
    }
    
    /// <summary>
    /// 清除指定格子的预留状态
    /// </summary>
    public bool ClearGridReservation(T grid)
    {
        if (grid == null || !CurrentGrids.Contains(grid)) return false;
        return grid.ClearReservation();
    }
    
    /// <summary>
    /// 清除所有格子的预留状态
    /// </summary>
    public void ClearAllReservations()
    {
        foreach (var grid in CurrentGrids)
        {
            if (grid.IsReserved)
            {
                grid.ClearReservation();
            }
        }
    }
    
    /// <summary>
    /// 清除所有格子（移除所有物体和状态）
    /// </summary>
    public void ClearAllGrids()
    {
        foreach (var grid in CurrentGrids)
        {
            grid.ClearGrid();
        }
    }
}