using UnityEngine;

/// <summary>
/// 格子占用状态枚举
/// </summary>
public enum OccupationState
{
    Free,       // 完全空闲
    Reserved,   // 已预留
    Occupied    // 已占用
}

/// <summary>
/// 通用格子类，可以被预留和占用
/// </summary>
public class Common_ReserveGrid : MonoBehaviour
{
    // 当前物体引用
    public GameObject currentItem;

    // 当前占用状态
    public OccupationState currentState = OccupationState.Free;

    // 格子是否被锁定
    [SerializeField] protected bool isLocked = false;

    // 格子所属的组ID
    [SerializeField] protected string groupId;

    /// <summary>
    /// 获取或设置格子所属的组ID
    /// </summary>
    public string GroupId
    {
        get => groupId;
        set
        {
            if (string.IsNullOrEmpty(value))
            {
                Debug.LogError("格子组ID不能为空");
                return;
            }
            groupId = value;
        }
    }

    /// <summary>
    /// 获取或设置格子的占用状态
    /// </summary>
    public OccupationState CurrentState
    {
        get => currentState;
        protected set => currentState = value;
    }

    /// <summary>
    /// 获取或设置格子是否被锁定
    /// </summary>
    public bool IsLocked
    {
        get => isLocked;
        set => isLocked = value;
    }

    /// <summary>
    /// 是否预留或占用
    /// </summary>
    public bool IsOccOrRese => CurrentState == OccupationState.Occupied || CurrentState == OccupationState.Reserved;

    /// <summary>
    /// 格子是否被实际占用
    /// </summary>
    public bool IsOccupied => CurrentState == OccupationState.Occupied;

    /// <summary>
    /// 格子是否被预留
    /// </summary>
    public bool IsReserved => CurrentState == OccupationState.Reserved;

    /// <summary>
    /// 格子是否完全空闲（既没有被占用也没有被预留）
    /// </summary>
    public bool IsFree => CurrentState == OccupationState.Free;

    /// <summary>
    /// 初始化组件
    /// </summary>
    protected virtual void Awake()
    {
        // 确保组ID不为空
        if (string.IsNullOrEmpty(groupId))
        {
            Debug.LogWarning($"格子 {gameObject.name} 的组ID为空，请设置组ID");
        }
    }

    /// <summary>
    /// 尝试在格子中放置物体（实际占用）
    /// </summary>
    public virtual bool TryPlaceItem(GameObject item)
    {
        if (IsOccupied || IsLocked) return false;
        currentItem = item;
        CurrentState = OccupationState.Occupied;
        return true;
    }

    /// <summary>
    /// 尝试预留格子
    /// </summary>
    public virtual bool TryReserveGrid(GameObject item)
    {
        if (!IsFree) return false;
        currentItem = item;
        CurrentState = OccupationState.Reserved;
        return true;
    }

    /// <summary>
    /// 将预留状态升级为实际占用状态
    /// </summary>
    public virtual bool UpgradeReservationToOccupation()
    {
        if (!IsReserved || IsLocked) return false;
        CurrentState = OccupationState.Occupied;
        return true;
    }

    /// <summary>
    /// 清除预留状态
    /// </summary>
    public virtual bool ClearReservation()
    {
        if (!IsReserved || IsLocked) return false;
        currentItem = null;
        CurrentState = OccupationState.Free;
        return true;
    }

    /// <summary>
    /// 清除格子（移除物体和状态）
    /// </summary>
    public virtual void ClearGrid()
    {
        if (IsLocked) return;
        currentItem = null;
        CurrentState = OccupationState.Free;
    }

    /// <summary>
    /// 获取格子中的物体
    /// </summary>
    public virtual GameObject GetItem()
    {
        return currentItem;
    }
}