using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class Common_ReserveGridManager : MonoBehaviour
{
    private static Common_ReserveGridManager _instance;
    public static Common_ReserveGridManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<Common_ReserveGridManager>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("Common_ReserveGridManager");
                    _instance = go.AddComponent<Common_ReserveGridManager>();
                    //DontDestroyOnLoad(go);
                }
            }
            return _instance;
        }
    }

    // 使用字典存储不同组的控制器
    private Dictionary<string, Common_ReserveGridController<Common_ReserveGrid>> gridControllers = 
        new Dictionary<string, Common_ReserveGridController<Common_ReserveGrid>>();
    
    // 事件字典，每个组有自己的事件
    private Dictionary<string, Action> onAllGridsOccupiedEvents = new Dictionary<string, Action>();
    private Dictionary<string, Action> onAllGridsReservedEvents = new Dictionary<string, Action>();
    
    /// <summary>
    /// 初始化指定组的格子
    /// </summary>
    /// <param name="grids">格子列表</param>
    /// <param name="groupName">组名</param>
    public void InitGrids(List<Common_ReserveGrid> grids, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return;
        }
        
        // 如果已存在该组的gridController，先取消事件订阅
        if (gridControllers.TryGetValue(groupName, out var existingController))
        {
            existingController.OnAllGridsOccupied -= () => InvokeGroupEvent(onAllGridsOccupiedEvents, groupName);
            existingController.OnAllGridsReserved -= () => InvokeGroupEvent(onAllGridsReservedEvents, groupName);
        }
        
        // 创建新的控制器
        var controller = new Common_ReserveGridController<Common_ReserveGrid>(grids, groupName);
        
        // 订阅事件
        controller.OnAllGridsOccupied += () => InvokeGroupEvent(onAllGridsOccupiedEvents, groupName);
        controller.OnAllGridsReserved += () => InvokeGroupEvent(onAllGridsReservedEvents, groupName);
        
        // 更新或添加控制器
        gridControllers[groupName] = controller;
    }
    
    /// <summary>
    /// 触发指定组的事件
    /// </summary>
    private void InvokeGroupEvent(Dictionary<string, Action> eventDict, string groupName)
    {
        if (eventDict.TryGetValue(groupName, out var action))
        {
            action?.Invoke();
        }
    }
    
    /// <summary>
    /// 订阅指定组的所有格子被占用事件
    /// </summary>
    public void SubscribeToAllGridsOccupied(Action callback, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return;
        }
        
        if (!onAllGridsOccupiedEvents.ContainsKey(groupName))
        {
            onAllGridsOccupiedEvents[groupName] = callback;
        }
        else
        {
            onAllGridsOccupiedEvents[groupName] += callback;
        }
    }
    
    /// <summary>
    /// 取消订阅指定组的所有格子被占用事件
    /// </summary>
    public void UnsubscribeFromAllGridsOccupied(Action callback, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return;
        }
        
        if (onAllGridsOccupiedEvents.ContainsKey(groupName))
        {
            onAllGridsOccupiedEvents[groupName] -= callback;
        }
    }
    
    /// <summary>
    /// 订阅指定组的所有格子被预留事件
    /// </summary>
    public void SubscribeToAllGridsReserved(Action callback, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return;
        }
        
        if (!onAllGridsReservedEvents.ContainsKey(groupName))
        {
            onAllGridsReservedEvents[groupName] = callback;
        }
        else
        {
            onAllGridsReservedEvents[groupName] += callback;
        }
    }
    
    /// <summary>
    /// 取消订阅指定组的所有格子被预留事件
    /// </summary>
    public void UnsubscribeFromAllGridsReserved(Action callback, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return;
        }
        
        if (onAllGridsReservedEvents.ContainsKey(groupName))
        {
            onAllGridsReservedEvents[groupName] -= callback;
        }
    }
    
    /// <summary>
    /// 获取指定组的所有格子
    /// </summary>
    public List<Common_ReserveGrid> GetCurrentGrids(string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return new List<Common_ReserveGrid>();
        }
        
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.CurrentGrids 
            : new List<Common_ReserveGrid>();
    }
    
    /// <summary>
    /// 获取所有组的名称
    /// </summary>
    public List<string> GetAllGroupNames()
    {
        return gridControllers.Keys.ToList();
    }
    
    /// <summary>
    /// 移除指定组
    /// </summary>
    public bool RemoveGroup(string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return false;
        }
        
        if (gridControllers.ContainsKey(groupName))
        {
            gridControllers.Remove(groupName);
            onAllGridsOccupiedEvents.Remove(groupName);
            onAllGridsReservedEvents.Remove(groupName);
            return true;
        }
        
        return false;
    }

    /// <summary>
    /// 获取指定组的第一个完全空闲的格子（既没有被占用也没有被预留）
    /// </summary>
    public Common_ReserveGrid GetFirstFreeGrid(string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return null;
        }
        
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.GetFirstFreeGrid() 
            : null;
    }

    /// <summary>
    /// 获取指定组的第一个未被实际占用的格子（可能被预留）
    /// </summary>
    // public Common_ReserveGrid GetFirstEmptyGrid(string groupName)
    // {
    //     if (string.IsNullOrEmpty(groupName))
    //     {
    //         Debug.LogError("组名不能为空");
    //         return null;
    //     }
    //     
    //     return gridControllers.TryGetValue(groupName, out var controller) 
    //         ? controller.GetFirstEmptyGrid() 
    //         : null;
    // }

    /// <summary>
    /// 检查指定组是否所有格子都被实际占用或锁定
    /// </summary>
    public bool AreAllGridsOccupiedOrLocked(string groupName)
    {

        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return false;
        }
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.AreAllGridsOccupiedOrLocked() 
            : false;
    }
    
    public bool AreAllGridsReservedOrLockedOrOccupied(string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return false;
        }
        
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.AreAllGridsReservedOrLockedOrOccupided() 
            : false;
    }
    
    /// <summary>
    /// 检查指定组是否所有格子都被预留或锁定
    /// </summary>
    public bool AreAllGridsReservedOrLocked(string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return false;
        }
        
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.AreAllGridsReservedOrLocked() 
            : false;
    }

    /// <summary>
    /// 检查指定组是否所有格子都是空的（未被实际占用）
    /// </summary>
    // public bool AreAllGridsEmpty(string groupName)
    // {
    //     if (string.IsNullOrEmpty(groupName))
    //     {
    //         Debug.LogError("组名不能为空");
    //         return false;
    //     }
    //     
    //     return gridControllers.TryGetValue(groupName, out var controller) 
    //         ? controller.AreAllGridsEmpty() 
    //         : false;
    // }
    
    /// <summary>
    /// 检查指定组是否所有格子都是完全空闲的（既没有被占用也没有被预留）
    /// </summary>
    public bool AreAllGridsCompletelyFree(string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return false;
        }
        
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.AreAllGridsCompletelyFree() 
            : false;
    }

    /// <summary>
    /// 尝试在指定组的格子中放置物体（实际占用）
    /// </summary>
    public bool TryPlaceItemInGrid(GameObject item, Common_ReserveGrid grid, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return false;
        }
        
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.TryPlaceItemInGrid(item, grid) 
            : false;
    }
    
    /// <summary>
    /// 尝试预留指定组的格子
    /// </summary>
    public bool TryReserveGrid(GameObject item, Common_ReserveGrid grid, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return false;
        }
        
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.TryReserveGrid(item, grid) 
            : false;
    }
    
    /// <summary>
    /// 将指定组的格子预留状态升级为实际占用状态
    /// </summary>
    public bool UpgradeReservationToOccupation(Common_ReserveGrid grid, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return false;
        }
        
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.UpgradeReservationToOccupation(grid) 
            : false;
    }

    /// <summary>
    /// 获取指定组的格子中的物体
    /// </summary>
    public GameObject GetItemFromGrid(Common_ReserveGrid grid, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return null;
        }
        
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.GetItemFromGrid(grid) 
            : null;
    }
    
    /// <summary>
    /// 通过物体查找指定组中对应的格子（实际占用或预留）
    /// </summary>
    public Common_ReserveGrid FindGridByItem(GameObject item, bool includeReserved, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return null;
        }
        
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.FindGridByItem(item, includeReserved) 
            : null;
    }
    
    /// <summary>
    /// 在所有组中查找物体对应的格子
    /// </summary>
    public (Common_ReserveGrid grid, string groupName) FindGridByItemInAllGroups(GameObject item, bool includeReserved = true)
    {
        foreach (var pair in gridControllers)
        {
            var grid = pair.Value.FindGridByItem(item, includeReserved);
            if (grid != null)
            {
                return (grid, pair.Key);
            }
        }
        
        return (null, null);
    }
    
    /// <summary>
    /// 查找指定组中物体实际占用的格子
    /// </summary>
    public Common_ReserveGrid FindOccupiedGridByItem(GameObject item, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return null;
        }
        
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.FindOccupiedGridByItem(item) 
            : null;
    }
    
    /// <summary>
    /// 在所有组中查找物体实际占用的格子
    /// </summary>
    public (Common_ReserveGrid grid, string groupName) FindOccupiedGridByItemInAllGroups(GameObject item)
    {
        foreach (var pair in gridControllers)
        {
            var grid = pair.Value.FindOccupiedGridByItem(item);
            if (grid != null)
            {
                return (grid, pair.Key);
            }
        }
        
        return (null, null);
    }
    
    /// <summary>
    /// 查找指定组中物体预留的格子
    /// </summary>
    public Common_ReserveGrid FindReservedGridByItem(GameObject item, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return null;
        }
        
        return gridControllers.TryGetValue(groupName, out var controller) 
            ? controller.FindReservedGridByItem(item) 
            : null;
    }
    
    /// <summary>
    /// 在所有组中查找物体预留的格子
    /// </summary>
    public (Common_ReserveGrid grid, string groupName) FindReservedGridByItemInAllGroups(GameObject item)
    {
        foreach (var pair in gridControllers)
        {
            var grid = pair.Value.FindReservedGridByItem(item);
            if (grid != null)
            {
                return (grid, pair.Key);
            }
        }
        
        return (null, null);
    }

    /// <summary>
    /// 向指定组添加新格子
    /// </summary>
    public bool AddGrid(Common_ReserveGrid grid, string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
        {
            Debug.LogError("组名不能为空");
            return false;
        }
        
        if (grid == null) return false;
        
        // 确保格子未被锁定
        grid.IsLocked = false;
        
        // 如果组不存在，创建新组
        if (!gridControllers.ContainsKey(groupName))
        {
            InitGrids(new List<Common_ReserveGrid>(), groupName);
        }
        
        // 添加格子到控制器
        return gridControllers[groupName].AddGrid(grid);
    }
    
    /// <summary>
    /// 清除指定组中格子的预留状态
    /// </summary>
    // public bool ClearGridReservation(Common_ReserveGrid grid, string groupName)
    // {
    //     if (string.IsNullOrEmpty(groupName))
    //     {
    //         Debug.LogError("组名不能为空");
    //         return false;
    //     }
    //     
    //     return gridControllers.TryGetValue(groupName, out var controller) 
    //         ? controller.ClearGridReservation(grid) 
    //         : false;
    // }
    
    /// <summary>
    /// 清除指定组中所有格子的预留状态
    /// </summary>
    // public void ClearAllReservations(string groupName)
    // {
    //     if (string.IsNullOrEmpty(groupName))
    //     {
    //         Debug.LogError("组名不能为空");
    //         return;
    //     }
    //     
    //     if (gridControllers.TryGetValue(groupName, out var controller))
    //     {
    //         controller.ClearAllReservations();
    //     }
    // }
    
    /// <summary>
    /// 清除所有组中所有格子的预留状态
    /// </summary>
    // public void ClearAllReservationsInAllGroups()
    // {
    //     foreach (var controller in gridControllers.Values)
    //     {
    //         controller.ClearAllReservations();
    //     }
    // }

    /// <summary>
    /// 获取指定组中已占用的格子数量
    /// </summary>
    public int GetOccupiedGridCount(string groupName)
    {
        if (!gridControllers.ContainsKey(groupName))
        {
            Debug.LogWarning($"尝试获取不存在的格子组 {groupName} 的已占用格子数量");
            return 0;
        }
        
        int count = 0;
        foreach (var grid in gridControllers[groupName].CurrentGrids)
        {
            if (grid.IsOccupied)
            {
                count++;
            }
        }
        
        return count;
    }
    
    /// <summary>
    /// 清除指定组中的所有格子
    /// </summary>
    public void ClearAllGrids(string groupName)
    {
        if (!gridControllers.ContainsKey(groupName))
        {
            Debug.LogWarning($"尝试清除不存在的格子组 {groupName} 的所有格子");
            return;
        }
        
        foreach (var grid in gridControllers[groupName].CurrentGrids)
        {
            grid.ClearGrid();
        }
    }
}