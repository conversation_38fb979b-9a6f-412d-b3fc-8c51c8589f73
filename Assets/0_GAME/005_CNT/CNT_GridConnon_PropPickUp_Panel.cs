using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class CNT_GridConnon_PropPickUp_Panel : MatchUI_BasePanel<CNT_GridConnon_PropPickUp_Panel>
{

    public static void ShowPanel()
    {



        GameObject prefab = Resources.Load<GameObject>("CNT_GridConnon_PropPickUp_Panel");
        if (prefab != null)
        {
            GameObject panelObj = GameObject.Instantiate(prefab);
            Canvas canvas = GameObject.Find("Canvas").GetComponent<Canvas>();
            if (canvas != null)
            {
                panelObj.transform.SetParent(canvas.transform, false);
            }
            else
            {
                Debug.LogError("No Canvas found in scene");
            }
        }

        // 播放显示动画
        CNT_GridConnon_PropPickUp_Panel.Instance.PlayShowAnimation();
    }

    public static void ClosePanel()
    {

        CNT_GridConnon_PropPickUp_Panel panel = FindObjectOfType<CNT_GridConnon_PropPickUp_Panel>();
        if (panel != null)
        {
            // 播放隐藏动画，完成后销毁
            panel.PlayHideAnimation(() =>
            {
                Destroy(panel.gameObject);
            });
        }
    }

    public void On_BtnClose_Click()
    {
        GridConnon_Controller.Instance.Prop_PickOut_Exit();

        ClosePanel();
    }



}