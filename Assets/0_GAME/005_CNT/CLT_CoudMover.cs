using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CLT_CoudMover : MonoBehaviour
{
    public Vector3 StartPos;
    public Vector3 EndPos;
    public float moveSpeed = 1f;

    private bool isFirstMove = true; // 是否是第一次移动
    private Vector3 initialPos; // 初始位置
    private float journeyLength; // 路径总长度
    private float journeyTime = 0f; // 当前移动时间

    void Start()
    {
        // 记录物体的初始位置
        initialPos = transform.position;
        // 计算第一次移动的路径长度（从当前位置到EndPos）
        journeyLength = Vector3.Distance(initialPos, EndPos);
    }

    void Update()
    {
        // 如果路径长度为0，不进行移动
        if (journeyLength == 0) return;

        // 计算移动进度
        journeyTime += Time.deltaTime * moveSpeed;
        float fractionOfJourney = journeyTime / journeyLength;

        if (isFirstMove)
        {
            // 第一次移动：从初始位置移动到EndPos
            transform.position = Vector3.Lerp(initialPos, EndPos, fractionOfJourney);

            // 到达终点时开始循环移动
            if (fractionOfJourney >= 1f)
            {
                isFirstMove = false;
                journeyTime = 0f;
                // 直接设置到StartPos开始循环
                transform.position = StartPos;
                // 重新计算路径长度（现在是StartPos和EndPos之间的距离）
                journeyLength = Vector3.Distance(StartPos, EndPos);
            }
        }
        else
        {
            // 循环移动阶段：始终从StartPos移动到EndPos
            transform.position = Vector3.Lerp(StartPos, EndPos, fractionOfJourney);

            // 到达终点时直接回到起点
            if (fractionOfJourney >= 1f)
            {
                journeyTime = 0f;
                transform.position = StartPos;
            }
        }
    }
}
