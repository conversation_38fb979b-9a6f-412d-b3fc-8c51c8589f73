using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

public class Game_CNT_Start : Game_Base_Start
{


    protected override void Awake()
    {
        base.Awake();

        QualitySettings.shadowDistance = 5;

        // 监听三个控制器的On_PlaySound事件
        TileCube_Controller.On_PlaySound += OnPlaySound;
        GridConnon_Controller.On_PlaySound += OnPlaySound;
        MatchUI_Controller.On_PlaySound += OnPlaySound;

        // 监听音乐播放和停止事件
        MatchUI_Controller.On_PlayMusic += OnPlayMusic;
        MatchUI_Controller.On_StopMusic += OnStopMusic;

        MatchUI_Controller.On_MainEvent += On_MtachUI_MainEvent;


    }

    protected override void OnDestroy()
    {
        base.OnDestroy();

        // 取消监听三个控制器的On_PlaySound事件
        TileCube_Controller.On_PlaySound -= OnPlaySound;
        GridConnon_Controller.On_PlaySound -= OnPlaySound;
        MatchUI_Controller.On_PlaySound -= OnPlaySound;

        // 取消监听音乐播放和停止事件
        MatchUI_Controller.On_PlayMusic -= OnPlayMusic;
        MatchUI_Controller.On_StopMusic -= OnStopMusic;

        MatchUI_Controller.On_MainEvent -= On_MtachUI_MainEvent;


    }

    private string MatachUI_MainEvent_Name;
    void On_MtachUI_MainEvent(string eventname, int level, int Level_Display)
    {

        MatachUI_MainEvent_Name = eventname;

        if (eventname == "3StartPanel")
        {

        }

    }



    private void Update()
    {

#if UNITY_EDITOR
        // 测试功能
        if (Input.GetKeyDown(KeyCode.X))
        {
            MatchUI_UserData_Manager.Instance.Set.Level_Current += 1;
            SceneManager.LoadScene("1Start");
        }
        if (Input.GetKeyDown(KeyCode.Z))
        {
            MatchUI_UserData_Manager.Instance.Set.Level_Current -= 1;
            SceneManager.LoadScene("1Start");
        }
#endif
    }





}