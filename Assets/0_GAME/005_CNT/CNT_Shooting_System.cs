using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using System;

/// <summary>
/// 射击系统 - 静态类，负责管理所有射击相关逻辑
/// 性能优化：只检查添加了GridConnon_Bullet_CollisionBody的对象，而非全部TileCube对象
/// </summary>
public static class CNT_Shooting_System
{
    #region 系统组件

    private static CNT_Shooting_System_ShootingManager shootingManager;
    private static CNT_Shooting_System_BulletTracker bulletTracker;
    private static CNT_Shooting_System_TargetDetector targetDetector;
    private static CNT_Shooting_System_PathChecker pathChecker;

    #endregion

    #region 配置参数

    [System.Serializable]
    public class TargetOffsetConfig
    {
        [Tooltip("目标物体类型")]
        public TileCube_Object.TileCube_ObjectType objectType;

        [Tooltip("射击位置偏移（相对于目标物体中心的世界坐标偏移）")]
        public Vector3 positionOffset = Vector3.zero;
    }

    [System.Serializable]
    public class ShootingConfig
    {
        [Header("射击系统时间配置")]
        [Tooltip("射击检测和执行间隔（秒）- 统一控制射击频率")]
        public float shootingInterval = 0.5f;

        [Header("目标类型优先级")]
        [Tooltip("目标类型优先级配置（数字越小优先级越高）")]
        public TileCube_Object.TileCube_ObjectType[] targetPriorities = {
            TileCube_Object.TileCube_ObjectType.SingleBlock,
            TileCube_Object.TileCube_ObjectType.LargeBlock,
            TileCube_Object.TileCube_ObjectType.GoldPig,
            TileCube_Object.TileCube_ObjectType.Barrier
        };

        [Header("射击偏移配置")]
        [Tooltip("不同类型目标的射击位置偏移")]
        public TargetOffsetConfig[] targetOffsets = {
            new TargetOffsetConfig { objectType = TileCube_Object.TileCube_ObjectType.SingleBlock, positionOffset = Vector3.zero },
            new TargetOffsetConfig { objectType = TileCube_Object.TileCube_ObjectType.LargeBlock, positionOffset = Vector3.zero },
            new TargetOffsetConfig { objectType = TileCube_Object.TileCube_ObjectType.GoldPig, positionOffset = Vector3.zero },
            new TargetOffsetConfig { objectType = TileCube_Object.TileCube_ObjectType.Barrier, positionOffset = Vector3.zero }
        };

        [Header("调试设置")]
        [Tooltip("是否显示射击系统调试信息")]
        public bool showDebugInfo = false;
    }

    private static ShootingConfig config = new ShootingConfig();
    private static bool isInitialized = false;

    /// <summary>
    /// 检查射击系统是否已初始化
    /// </summary>
    public static bool IsInitialized => isInitialized;

    #endregion

    #region 初始化与生命周期

    /// <summary>
    /// 初始化射击系统
    /// </summary>
    public static void Initialize(ShootingConfig shootingConfig = null)
    {
        if (isInitialized) return;

        if (shootingConfig != null)
            config = shootingConfig;

        shootingManager = new CNT_Shooting_System_ShootingManager();
        bulletTracker = new CNT_Shooting_System_BulletTracker();
        targetDetector = new CNT_Shooting_System_TargetDetector();
        pathChecker = new CNT_Shooting_System_PathChecker();

        // 初始化各个组件
        CNT_Shooting_System_ShootingManager.Initialize(config);
        CNT_Shooting_System_BulletTracker.Initialize();
        CNT_Shooting_System_TargetDetector.Initialize(config.targetPriorities);
        CNT_Shooting_System_PathChecker.Initialize(config);

        // 注册事件监听
        RegisterEvents();

        isInitialized = true;

        //if (config.showDebugInfo)
        //Debug.Log("[CNT_Shooting_System] 射击系统初始化完成");
    }

    /// <summary>
    /// 更新射击系统（由Game_CNT_Game调用）
    /// </summary>
    public static void Update()
    {
        if (!isInitialized) return;

        CNT_Shooting_System_ShootingManager.Update();
    }

    /// <summary>
    /// 清理射击系统
    /// </summary>
    public static void Cleanup()
    {
        if (!isInitialized) return;

        UnregisterEvents();

        CNT_Shooting_System_ShootingManager.Cleanup();
        CNT_Shooting_System_BulletTracker.Cleanup();
        CNT_Shooting_System_TargetDetector.Cleanup();
        CNT_Shooting_System_PathChecker.Cleanup();

        isInitialized = false;
    }

    /// <summary>
    /// 注册事件监听
    /// </summary>
    private static void RegisterEvents()
    {
        GridConnon_Controller.Act_On_Bullet_Targeted += OnBulletTargeted;
        GridConnon_Controller.Act_On_Bullet_Thorw += OnBulletPenetrated;
        GridConnon_Controller.Act_On_Bullet_HitTarget += OnBulletHitTarget;
    }

    /// <summary>
    /// 注销事件监听
    /// </summary>
    private static void UnregisterEvents()
    {
        GridConnon_Controller.Act_On_Bullet_Targeted -= OnBulletTargeted;
        GridConnon_Controller.Act_On_Bullet_Thorw -= OnBulletPenetrated;
        GridConnon_Controller.Act_On_Bullet_HitTarget -= OnBulletHitTarget;
    }

    #endregion

    #region 事件处理

    private static void OnBulletTargeted(GridConnon_Bullet bullet, GameObject target)
    {
        // 性能优化：提前检查target有效性
        if (target == null || target.gameObject == null)
        {
            //Debug.LogWarning("[CNT_Shooting_System] OnBulletTargeted: 目标为空!");
            return;
        }

        CNT_Shooting_System_BulletTracker.OnBulletTargeted(bullet, target);

        // 减少重复的GetComponent调用
        var tileCubeObject = target.GetComponent<TileCube_Object>();
        if (tileCubeObject != null)
        {
            TileCube_Controller.Instance?.TriggerObjectTargeted(tileCubeObject, bullet.gameObject);
        }
    }

    private static void OnBulletPenetrated(GridConnon_Bullet bullet, GameObject penetratedObj)
    {
        // 性能优化：提前检查penetratedObj有效性
        if (penetratedObj == null || penetratedObj.gameObject == null)
        {
            //Debug.LogWarning("[CNT_Shooting_System] OnBulletPenetrated: 穿透对象为空!");
            return;
        }

        var tileCubeObject = penetratedObj.GetComponent<TileCube_Object>();
        if (tileCubeObject != null)
        {
            TileCube_Controller.Instance?.TriggerObjectPenetrated(tileCubeObject, bullet.gameObject);
        }
    }

    private static void OnBulletHitTarget(GridConnon_Bullet bullet, GameObject target)
    {
        // 性能优化：提前检查target有效性
        if (target == null || target.gameObject == null)
        {
            //Debug.LogWarning("[CNT_Shooting_System] OnBulletHitTarget: 目标为空!");
            return;
        }

        CNT_Shooting_System_BulletTracker.OnBulletHitTarget(bullet, target);

        var tileCubeObject = target.GetComponent<TileCube_Object>();
        if (tileCubeObject != null)
        {
            TileCube_Controller.Instance?.TriggerObjectHit(tileCubeObject, bullet.gameObject);
        }
    }

    #endregion

    #region 公共接口

    /// <summary>
    /// 设置配置
    /// </summary>
    public static void SetConfig(ShootingConfig newConfig)
    {
        config = newConfig;
        if (isInitialized)
        {
            CNT_Shooting_System_ShootingManager.UpdateConfig(config);
            CNT_Shooting_System_TargetDetector.UpdatePriorities(config.targetPriorities);
        }
    }

    /// <summary>
    /// 获取当前配置
    /// </summary>
    public static ShootingConfig GetConfig()
    {
        return config;
    }

    #endregion
}

/// <summary>
/// 射击管理器 - 负责射击的时间控制和执行
/// </summary>
public class CNT_Shooting_System_ShootingManager
{
    private static CNT_Shooting_System.ShootingConfig config;
    private static float lastShootingTime = 0f;

    public static void Initialize(CNT_Shooting_System.ShootingConfig shootingConfig)
    {
        config = shootingConfig;
        lastShootingTime = 0f;
    }

    public static void Update()
    {
        // 统一的射击检测和执行：按射击间隔执行
        if (Time.time - lastShootingTime >= config.shootingInterval)
        {
            lastShootingTime = Time.time;

            // 清理已被销毁的目标
            CNT_Shooting_System_BulletTracker.CleanupDestroyedTargets();

            // 性能优化：定期清理已销毁的射击炮台
            GridConnon_Controller.Instance?.CleanupDestroyedShootingCannons();

            // 执行射击
            ExecuteSingleShot();
        }
    }

    public static void UpdateConfig(CNT_Shooting_System.ShootingConfig newConfig)
    {
        config = newConfig;
    }

    public static void Cleanup()
    {
        // 清理工作已简化
    }

    /// <summary>
    /// 执行射击检测（允许多炮台同时射击）
    /// </summary>
    private static void ExecuteSingleShot()
    {
        var shootingCannons = GetShootingCannons();

        // 先获取全局“第一排目标”的有序列表（已按X->高度->Z排序）
        var orderedTargets = CNT_Shooting_System_TargetDetector.GetFirstRowTargets();
        if (orderedTargets == null || orderedTargets.Count == 0)
            return;

        // 记录本轮已分配的目标（仅对SingleBlock预留，避免重复射击）
        var reservedTargets = new HashSet<GameObject>();

        // 按炮台Z排序（保持你原有的炮台优先顺序）
        var sortedCannons = shootingCannons.OrderBy(c => c.transform.position.z).ToList();

        foreach (var cannon in sortedCannons)
        {
            if (cannon == null || cannon.gameObject == null) continue;

            TileCube_Object chosen = null;

            // 按全局顺序为该炮台挑选第一个“可打”的目标
            for (int i = 0; i < orderedTargets.Count; i++)
            {
                var target = orderedTargets[i];
                if (target == null || target.gameObject == null) continue;

                // 仅对SingleBlock检查本轮预留，允许多击打目标被多炮台同时选中
                if (target.ObjectType == TileCube_Object.TileCube_ObjectType.SingleBlock && reservedTargets.Contains(target.gameObject))
                    continue;

                // 颜色匹配
                if (!CNT_Shooting_System_BulletTracker.IsColorMatched(cannon, target))
                    continue;

                // 路径无阻挡
                if (!CNT_Shooting_System_PathChecker.IsPathClear(cannon, target))
                    continue;

                // 存在性检测（是否还需要子弹）
                if (!CNT_Shooting_System_BulletTracker.ShouldShootTarget(target))
                    continue;

                chosen = target;
                break;
            }

            if (chosen == null) continue;

            // 偏移
            Vector3 targetOffset = CNT_Shooting_System_PathChecker.GetTargetOffset(chosen.ObjectType);

            bool fireSuccess = cannon.FireBulletWithOffset(chosen.gameObject, targetOffset);
            if (fireSuccess)
            {
                // 仅对SingleBlock做本轮预留，避免重复射击
                if (chosen.ObjectType == TileCube_Object.TileCube_ObjectType.SingleBlock)
                {
                    reservedTargets.Add(chosen.gameObject);
                }
            }
        }
    }

    /// <summary>
    /// 获取所有处于射击状态的炮台
    /// 性能优化：直接使用GridConnon_Controller维护的射击炮台列表，避免遍历和状态检查
    /// </summary>
    private static List<GridConnon_Cannon> GetShootingCannons()
    {
        var shootingCannons = new List<GridConnon_Cannon>();

        if (GridConnon_Controller.Instance?.ShootingCannons != null)
        {
            // 性能优化：直接使用已维护的射击炮台列表
            foreach (var cannon in GridConnon_Controller.Instance.ShootingCannons)
            {
                // 只需要检查对象有效性，状态由GridConnon_Controller保证
                if (cannon != null && cannon.gameObject != null)
                {
                    shootingCannons.Add(cannon);
                }
            }

            // 如果发现无效炮台，通知GridConnon_Controller清理
            if (shootingCannons.Count < GridConnon_Controller.Instance.ShootingCannons.Count)
            {
                GridConnon_Controller.Instance.CleanupDestroyedShootingCannons();
            }
        }

        return shootingCannons;
    }


}

/// <summary>
/// 子弹追踪器 - 追踪飞行中的子弹，避免重复射击
/// </summary>
public class CNT_Shooting_System_BulletTracker
{
    // 追踪每个目标有多少子弹正在飞向它（保留用于调试信息）
    private static Dictionary<GameObject, int> flyingBulletsToTarget = new Dictionary<GameObject, int>();

    // 追踪已被锁定的SingleBlock，防止重复射击
    private static HashSet<GameObject> lockedSingleBlocks = new HashSet<GameObject>();

    // 新增：虚拟生命值系统 - 为LargeBlock、GoldPig、Barrier维护虚拟生命值
    private static Dictionary<GameObject, int> virtualHealthMap = new Dictionary<GameObject, int>();

    public static void Initialize()
    {
        flyingBulletsToTarget.Clear();
        lockedSingleBlocks.Clear();
        virtualHealthMap.Clear();
    }

    public static void Cleanup()
    {
        flyingBulletsToTarget.Clear();
        lockedSingleBlocks.Clear();
        virtualHealthMap.Clear();
    }

    /// <summary>
    /// 清理已被销毁的目标
    /// </summary>
    public static void CleanupDestroyedTargets()
    {
        // 性能优化：使用预分配的列表避免频繁内存分配
        var keysToRemove = new List<GameObject>(flyingBulletsToTarget.Count);

        // 清理飞行子弹字典中的无效目标
        foreach (var kvp in flyingBulletsToTarget)
        {
            if (kvp.Key == null || kvp.Key.gameObject == null)
            {
                keysToRemove.Add(kvp.Key);
            }
        }

        // 批量删除无效键
        for (int i = 0; i < keysToRemove.Count; i++)
        {
            var key = keysToRemove[i];
            int bulletCount = flyingBulletsToTarget.ContainsKey(key) ? flyingBulletsToTarget[key] : 0;
            flyingBulletsToTarget.Remove(key);
            //Debug.Log($"[BulletTracker] 清理已销毁的目标引用，飞行子弹数: {bulletCount}");
        }

        // 重用列表以减少GC压力
        keysToRemove.Clear();

        // 清理锁定的SingleBlock中的无效目标
        foreach (var lockedBlock in lockedSingleBlocks)
        {
            if (lockedBlock == null || lockedBlock.gameObject == null)
            {
                keysToRemove.Add(lockedBlock);
            }
        }

        // 批量删除无效的锁定块
        for (int i = 0; i < keysToRemove.Count; i++)
        {
            var lockedBlock = keysToRemove[i];
            lockedSingleBlocks.Remove(lockedBlock);
            //Debug.Log($"[BulletTracker] 清理已销毁的锁定SingleBlock");
        }

        // 清理虚拟生命值字典中的无效目标
        keysToRemove.Clear();
        foreach (var kvp in virtualHealthMap)
        {
            if (kvp.Key == null || kvp.Key.gameObject == null)
            {
                keysToRemove.Add(kvp.Key);
            }
        }

        for (int i = 0; i < keysToRemove.Count; i++)
        {
            var key = keysToRemove[i];
            virtualHealthMap.Remove(key);
            //Debug.Log($"[BulletTracker] 清理已销毁的虚拟生命值记录");
        }
    }

    /// <summary>
    /// 子弹瞄准目标时调用
    /// </summary>
    public static void OnBulletTargeted(GridConnon_Bullet bullet, GameObject target)
    {
        if (target == null)
        {
            //Debug.LogWarning("[BulletTracker] OnBulletTargeted: 目标为空!");
            return;
        }

        var tileCubeObject = target.GetComponent<TileCube_Object>();
        if (tileCubeObject == null)
        {
            //Debug.LogWarning($"[BulletTracker] 目标 {target.name} 没有TileCube_Object组件");
            return;
        }

        // 增加飞行子弹计数（保留用于调试信息）
        if (!flyingBulletsToTarget.ContainsKey(target))
            flyingBulletsToTarget[target] = 0;

        flyingBulletsToTarget[target]++;

        // 根据目标类型处理
        if (tileCubeObject.ObjectType == TileCube_Object.TileCube_ObjectType.SingleBlock)
        {
            // SingleBlock：立即锁定，防止其他子弹射击
            lockedSingleBlocks.Add(target);
            //Debug.Log($"[BulletTracker] SingleBlock {target.name} 被锁定，飞行子弹数: {flyingBulletsToTarget[target]}");
        }
        else if (IsMultiHitTarget(tileCubeObject.ObjectType))
        {
            // LargeBlock、GoldPig、Barrier：使用虚拟生命值系统
            EnsureVirtualHealthInitialized(target, tileCubeObject);

            // 虚拟生命值减1
            if (virtualHealthMap.ContainsKey(target))
            {
                virtualHealthMap[target]--;
                //Debug.Log($"[BulletTracker] {tileCubeObject.ObjectType} {target.name} 虚拟生命值-1，当前虚拟生命: {virtualHealthMap[target]}, 飞行子弹数: {flyingBulletsToTarget[target]}");
            }
        }
    }

    /// <summary>
    /// 子弹击中目标时调用
    /// </summary>
    public static void OnBulletHitTarget(GridConnon_Bullet bullet, GameObject target)
    {
        if (target == null)
        {
            //Debug.LogWarning("[BulletTracker] OnBulletHitTarget: 目标为空!");
            return;
        }

        var tileCubeObject = target.GetComponent<TileCube_Object>();

        // 减少飞行子弹计数（保留用于调试信息）
        if (flyingBulletsToTarget.ContainsKey(target))
        {
            flyingBulletsToTarget[target]--;
            //Debug.Log($"[BulletTracker] 子弹击中目标 {target.name}, 剩余飞行子弹数: {flyingBulletsToTarget[target]}");

            if (flyingBulletsToTarget[target] <= 0)
            {
                flyingBulletsToTarget.Remove(target);
                //Debug.Log($"[BulletTracker] 目标 {target.name} 飞行子弹计数清零，从字典中移除");
            }
        }
        else
        {
            //Debug.LogWarning($"[BulletTracker] 子弹击中目标 {target.name}，但字典中没有记录!");
        }

        // 根据目标类型处理击中逻辑
        if (tileCubeObject != null)
        {
            if (tileCubeObject.ObjectType == TileCube_Object.TileCube_ObjectType.SingleBlock)
            {
                // SingleBlock击中后保持锁定状态，防止后续误射
                //Debug.Log($"[BulletTracker] SingleBlock {target.name} 被击中，保持锁定状态");
            }
            else if (IsMultiHitTarget(tileCubeObject.ObjectType))
            {
                // LargeBlock、GoldPig、Barrier：检查虚拟生命值和实际生命值同步
                SyncVirtualHealthWithActual(target, tileCubeObject);
            }
        }
    }

    /// <summary>
    /// 检查目标是否还需要子弹 - 基于存在性判断
    /// SingleBlock: 被锁定即当成不存在
    /// LargeBlock/GoldPig/Barrier: 虚拟生命值>0 即当成存在
    /// </summary>
    public static bool ShouldShootTarget(TileCube_Object target)
    {
        if (target == null || target.gameObject == null)
        {
            //Debug.Log($"[BulletTracker] 目标无效，跳过");
            return false;
        }

        var targetObj = target.gameObject;

        if (target.ObjectType == TileCube_Object.TileCube_ObjectType.SingleBlock)
        {
            // SingleBlock：被锁定即当成不存在
            bool isLocked = lockedSingleBlocks.Contains(targetObj);
            bool exists = !isLocked;  // 未被锁定就存在
            //Debug.Log($"[BulletTracker] SingleBlock {target.name} 存在性: {exists} (被锁定: {isLocked})");
            return exists;
        }
        else if (IsMultiHitTarget(target.ObjectType))
        {
            // LargeBlock、GoldPig、Barrier：使用虚拟生命值判断
            EnsureVirtualHealthInitialized(targetObj, target);

            int virtualHealth = virtualHealthMap.ContainsKey(targetObj) ? virtualHealthMap[targetObj] : 0;
            bool exists = virtualHealth > 0;

            //Debug.Log($"[BulletTracker] {target.ObjectType} {target.name} 存在性: {exists} (虚拟生命: {virtualHealth})");
            return exists;
        }

        return false;
    }

    /// <summary>
    /// 检查炮台和目标的颜色是否匹配
    /// </summary>
    public static bool IsColorMatched(GridConnon_Cannon cannon, TileCube_Object target)
    {
        // GoldPig可以被任何颜色射击
        if (target.ObjectType == TileCube_Object.TileCube_ObjectType.GoldPig)
        {
            return true;
        }

        // 其他类型需要颜色匹配
        int cannonColorId = GetCannonColorId(cannon);
        int targetColorId = GetTargetColorId(target);

        return cannonColorId > 0 && targetColorId > 0 && cannonColorId == targetColorId;
    }

    /// <summary>
    /// 获取炮台颜色ID
    /// </summary>
    private static int GetCannonColorId(GridConnon_Cannon cannon)
    {
        if (cannon == null) return 0;

        // 根据GridConnon_Cannon的实际颜色字段获取
        return cannon.ColorId;
    }

    /// <summary>
    /// 获取目标颜色ID
    /// </summary>
    private static int GetTargetColorId(TileCube_Object target)
    {
        if (target == null) return 0;

        switch (target.ObjectType)
        {
            case TileCube_Object.TileCube_ObjectType.SingleBlock:
                var singleBlock = target as TileCube_SingleBlock;
                return singleBlock?.blockData?.ColorId ?? 0;

            case TileCube_Object.TileCube_ObjectType.LargeBlock:
                var largeBlock = target as TileCube_LargeBlock;
                return largeBlock?.blockData?.ColorId ?? 0;

            case TileCube_Object.TileCube_ObjectType.Barrier:
                var barrier = target as TileCube_Barrier;
                return barrier?.barrierData?.ColorId ?? 0;

            case TileCube_Object.TileCube_ObjectType.GoldPig:
                // GoldPig没有颜色限制，返回特殊值
                return -1;

            default:
                return 0;
        }
    }

    /// <summary>
    /// 检查目标是否被锁定（有飞行子弹）
    /// </summary>
    public static bool IsTargetLocked(GameObject target)
    {
        if (target == null) return false;

        var tileCubeObject = target.GetComponent<TileCube_Object>();
        if (tileCubeObject != null && tileCubeObject.ObjectType == TileCube_Object.TileCube_ObjectType.SingleBlock)
        {
            // SingleBlock使用专门的锁定机制
            return lockedSingleBlocks.Contains(target);
        }

        // 其他类型使用飞行子弹数量判断
        return flyingBulletsToTarget.ContainsKey(target) && flyingBulletsToTarget[target] > 0;
    }

    /// <summary>
    /// 判断是否为多次击打类型目标
    /// </summary>
    private static bool IsMultiHitTarget(TileCube_Object.TileCube_ObjectType objectType)
    {
        return objectType == TileCube_Object.TileCube_ObjectType.LargeBlock ||
               objectType == TileCube_Object.TileCube_ObjectType.GoldPig ||
               objectType == TileCube_Object.TileCube_ObjectType.Barrier;
    }

    /// <summary>
    /// 确保虚拟生命值已初始化
    /// </summary>
    private static void EnsureVirtualHealthInitialized(GameObject targetObj, TileCube_Object target)
    {
        if (!virtualHealthMap.ContainsKey(targetObj))
        {
            int actualHealth = GetActualHealth(target);
            virtualHealthMap[targetObj] = actualHealth;
            //Debug.Log($"[BulletTracker] 初始化 {target.ObjectType} {target.name} 虚拟生命值: {actualHealth}");
        }
    }

    /// <summary>
    /// 同步虚拟生命值与实际生命值
    /// 当子弹击中目标后，确保虚拟生命值不超过实际生命值
    /// </summary>
    private static void SyncVirtualHealthWithActual(GameObject targetObj, TileCube_Object target)
    {
        if (!virtualHealthMap.ContainsKey(targetObj)) return;

        int actualHealth = GetActualHealth(target);
        int virtualHealth = virtualHealthMap[targetObj];

        // 如果虚拟生命值大于实际生命值，同步到实际生命值
        if (virtualHealth > actualHealth)
        {
            virtualHealthMap[targetObj] = actualHealth;
            //Debug.Log($"[BulletTracker] 同步 {target.ObjectType} {target.name} 虚拟生命值: {virtualHealth} -> {actualHealth}");
        }

        // 如果实际生命值为0，虚拟生命值也设为0
        if (actualHealth <= 0)
        {
            virtualHealthMap[targetObj] = 0;
            //Debug.Log($"[BulletTracker] {target.ObjectType} {target.name} 实际生命值为0，虚拟生命值设为0");
        }
    }

    /// <summary>
    /// 获取目标实际生命值
    /// </summary>
    private static int GetActualHealth(TileCube_Object target)
    {
        switch (target.ObjectType)
        {
            case TileCube_Object.TileCube_ObjectType.SingleBlock:
                // SingleBlock通常是1击即毁，如果没被锁定说明还活着
                return lockedSingleBlocks.Contains(target.gameObject) ? 0 : 1;

            case TileCube_Object.TileCube_ObjectType.LargeBlock:
                var largeBlock = target as TileCube_LargeBlock;
                return largeBlock != null ? (largeBlock.RequiredHits - largeBlock.CurrentHits) : 0;

            case TileCube_Object.TileCube_ObjectType.GoldPig:
                var goldPig = target as TileCube_GoldPig;
                return goldPig != null ? (goldPig.RequiredHits - goldPig.CurrentHits) : 0;

            case TileCube_Object.TileCube_ObjectType.Barrier:
                var barrier = target as TileCube_Barrier;
                return barrier?.CurrentHitValues ?? 0;
        }

        return 0;
    }

    /// <summary>
    /// 重置目标的虚拟生命值（用于目标重新生成或恢复时）
    /// </summary>
    public static void ResetVirtualHealth(GameObject targetObj, TileCube_Object target)
    {
        // 如果射击系统还没有初始化，不执行操作
        if (!CNT_Shooting_System.IsInitialized) return;

        if (targetObj == null || target == null) return;

        if (IsMultiHitTarget(target.ObjectType))
        {
            int actualHealth = GetActualHealth(target);
            virtualHealthMap[targetObj] = actualHealth;
            //Debug.Log($"[BulletTracker] 重置 {target.ObjectType} {target.name} 虚拟生命值: {actualHealth}");
        }
    }
}

/// <summary>
/// 目标检测器 - 检测第一排可射击目标并排序
/// 性能优化：只检查添加了GridConnon_Bullet_CollisionBody碰撞体的对象，避免遍历全部TileCube对象
/// </summary>
public class CNT_Shooting_System_TargetDetector
{
    private static Dictionary<TileCube_Object.TileCube_ObjectType, int> typePriorities =
        new Dictionary<TileCube_Object.TileCube_ObjectType, int>();

    // 性能优化：缓存已添加碰撞体的可射击目标，避免重复遍历
    private static List<TileCube_Object> cachedShootableTargets = new List<TileCube_Object>();
    private static float lastCacheUpdateTime = 0f;
    private static readonly float CACHE_UPDATE_INTERVAL = 0.1f; // 缓存更新间隔（秒）

    // 性能优化：缓存GameObject到TileCube_Object的映射，减少GetComponent开销
    private static readonly Dictionary<GameObject, TileCube_Object> goToTileCache = new Dictionary<GameObject, TileCube_Object>(1024);

    // 临时缓存：本轮计算中各目标的“存在性”快照，避免重复调用ShouldShootTarget
    private static readonly Dictionary<TileCube_Object, bool> existenceSnapshot = new Dictionary<TileCube_Object, bool>(1024);

    public static void Initialize(TileCube_Object.TileCube_ObjectType[] priorityArray)
    {
        UpdatePriorities(priorityArray);
        // 清空缓存
        cachedShootableTargets.Clear();
        lastCacheUpdateTime = 0f;
        goToTileCache.Clear();
        existenceSnapshot.Clear();
    }

    public static void UpdatePriorities(TileCube_Object.TileCube_ObjectType[] priorityArray)
    {
        typePriorities.Clear();
        for (int i = 0; i < priorityArray.Length; i++)
        {
            typePriorities[priorityArray[i]] = i;
        }
    }

    public static void Cleanup()
    {
        typePriorities.Clear();
        cachedShootableTargets.Clear();
        goToTileCache.Clear();
        existenceSnapshot.Clear();
    }

    /// <summary>
    /// 为指定炮台获取最佳射击目标
    /// </summary>
    public static TileCube_Object GetBestTargetForCannon(GridConnon_Cannon cannon)
    {
        var firstRowTargets = GetFirstRowTargets();

        if (firstRowTargets.Count == 0) return null;

        // 返回优先级最高的目标
        return firstRowTargets[0];
    }

    /// <summary>
    /// 为指定炮台获取第一个符合所有条件的有效射击目标
    /// 依次检查：目标有效性 -> 颜色匹配 -> 路径清晰 -> 子弹需求
    /// </summary>
    public static TileCube_Object GetBestValidTargetForCannon(GridConnon_Cannon cannon)
    {
        var firstRowTargets = GetFirstRowTargets();

        if (firstRowTargets.Count == 0)
        {
            //Debug.Log($"[TargetDetector] 炮台 {cannon.ObjectId} 没有找到第一排目标");
            return null;
        }

        //Debug.Log($"[TargetDetector] 炮台 {cannon.ObjectId}[颜色{cannon.ColorId}] 开始检查 {firstRowTargets.Count} 个第一排目标");

        // 遍历所有第一排目标，找到第一个符合所有条件的
        foreach (var target in firstRowTargets)
        {
            var gridInfo = target.GetGridInfo();
            //Debug.Log($"[TargetDetector] 检查目标 {target.name}[{gridInfo?.gridX},{gridInfo?.gridZ}]");

            // 0. 目标有效性检测 - 确保目标仍然存在且未被销毁
            if (target == null || target.gameObject == null)
            {
                //Debug.LogWarning($"[TargetDetector] 目标 {target?.name} 已被销毁，跳过");
                continue;
            }

            // 1. 颜色匹配检测
            bool colorMatched = CNT_Shooting_System_BulletTracker.IsColorMatched(cannon, target);
            //Debug.Log($"[TargetDetector] 颜色匹配检测: {colorMatched}");
            if (!colorMatched) continue;

            // 2. 路径检测
            bool pathClear = CNT_Shooting_System_PathChecker.IsPathClear(cannon, target);
            //Debug.Log($"[TargetDetector] 路径清晰检测: {pathClear}");
            if (!pathClear) continue;

            // 3. 存在性检测（根据类型判断目标是否"存在"）
            bool targetExists = CNT_Shooting_System_BulletTracker.ShouldShootTarget(target);
            //Debug.Log($"[TargetDetector] 存在性检测: {targetExists}");
            if (!targetExists) continue;

            // 4. 最终检查：确保在即将射击时目标仍然有效
            if (target == null || target.gameObject == null)
            {
                //Debug.LogWarning($"[TargetDetector] 目标 {target?.name} 在检查过程中被销毁，跳过");
                continue;
            }

            // 找到第一个符合所有条件的目标
            //Debug.Log($"[TargetDetector] 炮台 {cannon.ObjectId} 选中目标 {target.name}[{gridInfo?.gridX},{gridInfo?.gridZ}]");
            return target;
        }

        // 没有找到符合条件的目标
        //Debug.Log($"[TargetDetector] 炮台 {cannon.ObjectId} 没有找到符合条件的目标");
        return null;
    }

    /// <summary>
    /// 获取第一排可射击目标（已排序）- 优化版本，使用缓存避免重复计算
    /// </summary>
    public static List<TileCube_Object> GetFirstRowTargets()
    {
        // 更新可射击目标缓存
        UpdateShootableTargetsCache();

        // 本轮存在性快照，避免后续重复计算
        existenceSnapshot.Clear();
        for (int i = 0; i < cachedShootableTargets.Count; i++)
        {
            var t = cachedShootableTargets[i];
            if (t == null || t.gameObject == null) continue;
            existenceSnapshot[t] = CNT_Shooting_System_BulletTracker.ShouldShootTarget(t);
        }

        // 按列索引，降低第一排判定复杂度
        // key: 列X -> 该列上的目标列表
        var columnToTargets = new Dictionary<int, List<TileCube_Object>>();
        for (int i = 0; i < cachedShootableTargets.Count; i++)
        {
            var target = cachedShootableTargets[i];
            if (target == null || target.gameObject == null) continue;

            var occupied = GetObjectOccupiedPositions_ForDetector(target);
            for (int k = 0; k < occupied.Count; k++)
            {
                int x = occupied[k].x;
                if (!columnToTargets.TryGetValue(x, out var list))
                {
                    list = new List<TileCube_Object>();
                    columnToTargets[x] = list;
                }
                list.Add(target);
            }
        }

        List<TileCube_Object> firstRowTargets = new List<TileCube_Object>();
        //Debug.Log($"[TargetDetector] 总共找到 {cachedShootableTargets.Count} 个可射击目标");

        foreach (var target in cachedShootableTargets)
        {
            // 检查目标有效性
            if (target == null || target.gameObject == null)
            {
                //Debug.LogWarning($"[TargetDetector] 发现无效目标，跳过");
                continue;
            }

            var gridInfo = target.GetGridInfo();
            bool isMoving = IsTargetMoving(target);
            //Debug.Log($"[TargetDetector] 检查目标 {target.name} 网格位置[{gridInfo?.gridX},{gridInfo?.gridZ},{gridInfo?.height}] 移动状态:{isMoving}");

            // 优化：使用分列后的列表进行第一排检测，仅比较同列（含多格占位列）
            if (IsInFirstRow_ByColumnIndex(target, columnToTargets))
            {
                firstRowTargets.Add(target);
                //Debug.Log($"[TargetDetector] 目标 {target.name} 在第一排");
            }
            else
            {
                //Debug.Log($"[TargetDetector] 目标 {target.name} 不在第一排");
            }
        }

        // 按优先级排序
        var sortedTargets = SortTargetsByPriority(firstRowTargets);
        //Debug.Log($"[TargetDetector] 第一排目标排序后: {string.Join(", ", sortedTargets.Select(t => $"{t.name}[{t.GetGridInfo()?.gridX},移动:{IsTargetMoving(t)}]"))}");

        return sortedTargets;
    }

    /// <summary>
    /// 更新可射击目标缓存 - 优化版本：只检查添加了碰撞体的对象
    /// 性能优化：使用GridConnon_Bullet_CollisionBody的碰撞体列表，避免遍历所有TileCube对象
    /// </summary>
    private static void UpdateShootableTargetsCache()
    {
        // 如果缓存还新鲜，直接返回
        if (Time.time - lastCacheUpdateTime < CACHE_UPDATE_INTERVAL)
        {
            return;
        }

        lastCacheUpdateTime = Time.time;
        cachedShootableTargets.Clear();

        // 性能优化：只检查已添加碰撞体的对象，避免遍历所有TileCube对象
        var allCollisionBodies = GridConnon_Bullet_CollisionBody.GetAllCollisionBodies();
        if (allCollisionBodies != null)
        {
            // 性能调试：记录优化前后的对象数量对比
            int collisionBodyCount = allCollisionBodies.Count;

            foreach (var collisionBody in allCollisionBodies)
            {
                // 检查碰撞体和其游戏对象的有效性
                if (collisionBody == null || collisionBody.gameObject == null)
                    continue;

                // 获取TileCube_Object组件
                var go = collisionBody.gameObject;
                if (!goToTileCache.TryGetValue(go, out var tileCubeObject) || tileCubeObject == null)
                {
                    tileCubeObject = go.GetComponent<TileCube_Object>();
                    // 仅当非空时写入缓存，防止缓存无用空引用
                    if (tileCubeObject != null)
                    {
                        goToTileCache[go] = tileCubeObject;
                    }
                }
                if (tileCubeObject != null && IsShootableType(tileCubeObject.ObjectType))
                {
                    cachedShootableTargets.Add(tileCubeObject);
                }
            }

            // 仅在配置开启调试信息时显示性能优化效果
            if (CNT_Shooting_System.GetConfig().showDebugInfo && cachedShootableTargets.Count > 0)
            {
                // Debug.Log($"[TargetDetector] 性能优化：检查了{collisionBodyCount}个碰撞体对象，找到{cachedShootableTargets.Count}个可射击目标");
            }
        }
    }

    /// <summary>
    /// 获取所有可射击目标 - 优化版本：只返回添加了碰撞体的对象
    /// 性能优化：通过GridConnon_Bullet_CollisionBody列表获取，避免遍历全部TileCube对象
    /// </summary>
    private static List<TileCube_Object> GetAllShootableTargets()
    {
        UpdateShootableTargetsCache();
        return new List<TileCube_Object>(cachedShootableTargets);
    }

    /// <summary>
    /// 判断目标是否在"第一排"（前方无任何"存在"的可射击物体）- 优化版本
    /// 基于存在性判断，而非销毁状态，使用传入的目标列表避免重复查询
    /// </summary>
    private static bool IsInFirstRowOptimized(TileCube_Object target, List<TileCube_Object> allTargets)
    {
        var gridInfo = target.GetGridInfo();
        if (gridInfo == null)
        {
            //Debug.Log($"[TargetDetector] 目标 {target.name} 网格信息为空");
            return false;
        }

        //Debug.Log($"[TargetDetector] 检查 {target.name}[{gridInfo.gridX},{gridInfo.gridZ}] 是否在第一排");

        // 检查是否有其他"存在"的可射击目标在当前目标前方（z更小）
        foreach (var otherTarget in allTargets)
        {
            if (otherTarget == target) continue; // 跳过自己

            var otherGridInfo = otherTarget.GetGridInfo();
            if (otherGridInfo == null) continue;

            // 检查其他目标是否"存在"（根据类型使用不同判断条件）
            bool otherExists = CNT_Shooting_System_BulletTracker.ShouldShootTarget(otherTarget);

            // 如果其他目标在当前目标前方（Z更小）且"存在"
            if (otherGridInfo.gridZ < gridInfo.gridZ && otherExists)
            {
                //Debug.Log($"[TargetDetector] {target.name}[{gridInfo.gridX},{gridInfo.gridZ}] 前方有存在的目标 {otherTarget.name}[{otherGridInfo.gridX},{otherGridInfo.gridZ}]");
                return false; // 有前方存在的目标，当前目标不在第一排
            }
        }

        //Debug.Log($"[TargetDetector] {target.name}[{gridInfo.gridX},{gridInfo.gridZ}] 在第一排");
        return true; // 前方无存在的可射击目标，属于第一排
    }

    /// <summary>
    /// 使用“按列索引”的方式判断是否在第一排
    /// 规则：在目标占用的每一列X上，都不存在更靠前(Z更小)且“存在”的目标
    /// </summary>
    private static bool IsInFirstRow_ByColumnIndex(TileCube_Object target, Dictionary<int, List<TileCube_Object>> columnToTargets)
    {
        var gridInfo = target.GetGridInfo();
        if (gridInfo == null) return false;

        var occupied = GetObjectOccupiedPositions_ForDetector(target);
        for (int i = 0; i < occupied.Count; i++)
        {
            int x = occupied[i].x;
            if (!columnToTargets.TryGetValue(x, out var list) || list == null) continue;

            for (int k = 0; k < list.Count; k++)
            {
                var other = list[k];
                if (other == null || other == target) continue;
                var otherInfo = other.GetGridInfo();
                if (otherInfo == null) continue;

                // 仅比较同列中更靠前(Z更小)且存在的目标
                if (otherInfo.gridZ < gridInfo.gridZ)
                {
                    bool exists = existenceSnapshot.TryGetValue(other, out var ex) ? ex : CNT_Shooting_System_BulletTracker.ShouldShootTarget(other);
                    if (exists)
                    {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /// <summary>
    /// 复制一份路径检测器中的占位计算逻辑，避免跨类调用私有方法
    /// </summary>
    private static List<Vector2Int> GetObjectOccupiedPositions_ForDetector(TileCube_Object obj)
    {
        List<Vector2Int> positions = new List<Vector2Int>();
        if (obj == null || obj.GetGridInfo() == null)
            return positions;

        switch (obj.ObjectType)
        {
            case TileCube_Object.TileCube_ObjectType.LargeBlock:
                var largeBlock = obj as TileCube_LargeBlock;
                if (largeBlock != null)
                {
                    positions = largeBlock.GetOccupiedPositions();
                }
                break;
            case TileCube_Object.TileCube_ObjectType.Barrier:
                var barrier = obj as TileCube_Barrier;
                if (barrier != null)
                {
                    positions = barrier.GetOccupiedPositions();
                }
                break;
            default:
                var gridInfo = obj.GetGridInfo();
                positions.Add(new Vector2Int(gridInfo.gridX, gridInfo.gridZ));
                break;
        }
        return positions;
    }

    /// <summary>
    /// 判断目标是否在"第一排"（前方无任何"存在"的可射击物体）
    /// 基于存在性判断，而非销毁状态
    /// </summary>
    private static bool IsInFirstRow(TileCube_Object target)
    {
        var gridInfo = target.GetGridInfo();
        if (gridInfo == null)
        {
            //Debug.Log($"[TargetDetector] 目标 {target.name} 网格信息为空");
            return false;
        }

        Tool_TileGrid tileGrid = TileCube_Controller.Instance?.TileGrid;
        if (tileGrid == null) return false;

        //Debug.Log($"[TargetDetector] 检查 {target.name}[{gridInfo.gridX},{gridInfo.gridZ}] 是否在第一排");

        // 获取所有可射击目标
        var allShootableTargets = GetAllShootableTargets();

        // 检查是否有其他"存在"的可射击目标在当前目标前方（z更小）
        foreach (var otherTarget in allShootableTargets)
        {
            if (otherTarget == target) continue; // 跳过自己

            var otherGridInfo = otherTarget.GetGridInfo();
            if (otherGridInfo == null) continue;

            // 检查其他目标是否"存在"（根据类型使用不同判断条件）
            bool otherExists = CNT_Shooting_System_BulletTracker.ShouldShootTarget(otherTarget);

            // 如果其他目标在当前目标前方（Z更小）且"存在"
            if (otherGridInfo.gridZ < gridInfo.gridZ && otherExists)
            {
                //Debug.Log($"[TargetDetector] {target.name}[{gridInfo.gridX},{gridInfo.gridZ}] 前方有存在的目标 {otherTarget.name}[{otherGridInfo.gridX},{otherGridInfo.gridZ}]");
                return false; // 有前方存在的目标，当前目标不在第一排
            }
        }

        //Debug.Log($"[TargetDetector] {target.name}[{gridInfo.gridX},{gridInfo.gridZ}] 在第一排");
        return true; // 前方无存在的可射击目标，属于第一排
    }

    /// <summary>
    /// 检查是否为可射击类型
    /// </summary>
    private static bool IsShootableType(TileCube_Object.TileCube_ObjectType objectType)
    {
        return objectType == TileCube_Object.TileCube_ObjectType.SingleBlock ||
               objectType == TileCube_Object.TileCube_ObjectType.LargeBlock ||
               objectType == TileCube_Object.TileCube_ObjectType.GoldPig ||
               objectType == TileCube_Object.TileCube_ObjectType.Barrier;
    }

    /// <summary>
    /// 按优先级排序目标
    /// 排序规则：移动状态(已到位优先) -> 类型优先级 -> 视觉深度(WorldZ，小→大优先) -> 横向(X，左到右) -> 高度(height，高到低)
    /// 以世界坐标Z作为前后判断，确保Z越小越优先
    /// </summary>
    private static List<TileCube_Object> SortTargetsByPriority(List<TileCube_Object> targets)
    {
        return targets.OrderBy(t => IsTargetMoving(t) ? 1 : 0)    // 移动状态优先：已到位(0) > 移动中(1)
                     .ThenBy(t => GetTypePriority(t.ObjectType))  // 类型优先级
                     .ThenBy(t => t.transform.position.z)          // 视觉深度优先：世界Z 小→大
                     .ThenBy(t => t.GetGridInfo()?.gridX ?? 0)     // 其次：横向 左到右
                     .ThenByDescending(t => t.GetGridInfo()?.height ?? 0)    // 最后：同列内 高到低
                     .ToList();
    }

    /// <summary>
    /// 检查目标是否正在移动
    /// </summary>
    private static bool IsTargetMoving(TileCube_Object target)
    {
        var gridInfo = target.GetGridInfo();
        return gridInfo?.isMoving ?? false;
    }

    /// <summary>
    /// 获取类型优先级
    /// </summary>
    private static int GetTypePriority(TileCube_Object.TileCube_ObjectType objectType)
    {
        return typePriorities.ContainsKey(objectType) ? typePriorities[objectType] : 999;
    }
}

/// <summary>
/// 路径检测器 - 检测射击路径是否有遮挡
/// </summary>
public class CNT_Shooting_System_PathChecker
{
    private static CNT_Shooting_System.ShootingConfig pathConfig;

    // 短期路径检测缓存：减少同一帧/短时间内对同一目标的重复网格扫描
    private class PathCacheEntry
    {
        public Vector3Int gridPos;
        public int height;
        public float time;
        public bool result;
        public bool wasMoving;
        // 新增：缓存命中所使用的X走廊范围，避免不同炮台位置导致误用
        public int corridorMinX;
        public int corridorMaxX;
    }
    private static readonly Dictionary<int, PathCacheEntry> pathCache = new Dictionary<int, PathCacheEntry>(256);
    private static readonly float PATH_CACHE_TTL = 0.08f; // 80ms缓存窗口，足以覆盖同一射击周期的重复查询

    // 新增：生成缓存键（将目标实例ID与走廊范围、目标高度混合）
    private static int MakePathCacheKey(int targetId, int corridorMinX, int corridorMaxX, int height)
    {
        unchecked
        {
            int key = targetId;
            key = (key * 397) ^ corridorMinX;
            key = (key * 397) ^ corridorMaxX;
            key = (key * 397) ^ height;
            return key;
        }
    }

    public static void Initialize(CNT_Shooting_System.ShootingConfig config)
    {
        pathConfig = config;
    }

    public static void Cleanup()
    {
        pathConfig = null;
        pathCache.Clear();
    }

    /// <summary>
    /// 检查炮台到目标的射击路径是否清晰
    /// 炮台在射击格上，到所有TileCube无物理阻挡
    /// 只需检测目标前方是否有其他未锁定的TileCube阻挡
    /// </summary>
    public static bool IsPathClear(GridConnon_Cannon cannon, TileCube_Object target)
    {
        var targetGridPos = target.GetGridPosition();

        // 验证网格位置是否有效（不能简单检查是否为zero，因为[0,0,0]是有效位置）
        var gridInfo = target.GetGridInfo();
        if (gridInfo == null)
        {
            //Debug.LogWarning($"[PathChecker] 目标 {target.name} 获取网格信息失败");
            return false;
        }

        bool result = IsTargetPathClear_Cached(cannon, target, gridInfo);

        if (!result)
        {
            //Debug.Log($"[PathChecker] 目标被阻挡: 炮台{cannon.ObjectId} -> 目标{target.name}[{targetGridPos}]");
        }
        else
        {
            //Debug.Log($"[PathChecker] 路径清晰: 炮台{cannon.ObjectId} -> 目标{target.name}[{targetGridPos}]");
        }

        return result;
    }

    /// <summary>
    /// 路径检测带短期缓存（包含炮台-目标组合）
    /// - 目标未移动且在TTL内：直接返回缓存
    /// - 否则重新计算并写回缓存
    /// </summary>
    private static bool IsTargetPathClear_Cached(GridConnon_Cannon cannon, TileCube_Object target, GridTileObject targetGridInfo)
    {
        // 计算炮台与目标之间的X走廊范围
        Tool_TileGrid tileGrid = TileCube_Controller.Instance?.TileGrid;
        if (tileGrid == null)
        {
            return false;
        }

        Vector2Int cannonGrid = tileGrid.WorldToGridPosition(cannon.transform.position);

        // 目标占用的所有X列
        List<Vector2Int> occupied = GetObjectOccupiedPositions(target);
        int corridorMinX = cannonGrid.x;
        int corridorMaxX = cannonGrid.x;
        for (int i = 0; i < occupied.Count; i++)
        {
            int x = occupied[i].x;
            if (x < corridorMinX) corridorMinX = x;
            if (x > corridorMaxX) corridorMaxX = x;
        }

        int key = MakePathCacheKey(target.GetInstanceID(), corridorMinX, corridorMaxX, targetGridInfo.height);
        Vector3Int gridPos = new Vector3Int(targetGridInfo.gridX, targetGridInfo.gridZ, targetGridInfo.height);
        bool isMoving = targetGridInfo.isMoving;

        if (pathCache.TryGetValue(key, out var entry))
        {
            if (!isMoving && Time.time - entry.time <= PATH_CACHE_TTL &&
                entry.gridPos == gridPos && entry.height == targetGridInfo.height &&
                entry.corridorMinX == corridorMinX && entry.corridorMaxX == corridorMaxX)
            {
                return entry.result;
            }
        }

        bool fresh = IsTargetPathClear_WithCorridor(target, corridorMinX, corridorMaxX);
        pathCache[key] = new PathCacheEntry
        {
            gridPos = gridPos,
            height = targetGridInfo.height,
            wasMoving = isMoving,
            time = Time.time,
            result = fresh,
            corridorMinX = corridorMinX,
            corridorMaxX = corridorMaxX
        };
        return fresh;
    }

    /// <summary>
    /// 检查目标射击路径是否清晰（按X走廊范围检测）
    /// 从目标网格位置向前方检查是否有阻挡
    /// 修复：考虑炮台与目标之间的横向走廊，而非仅目标所占列
    /// 修复：正确处理多格物体（LargeBlock、Barrier）的占位遮挡
    /// 修复：正确处理高度遮挡 - 只有高度≥目标高度的物体才能阻挡
    /// </summary>
    private static bool IsTargetPathClear_WithCorridor(TileCube_Object target, int corridorMinX, int corridorMaxX)
    {
        Tool_TileGrid tileGrid = TileCube_Controller.Instance?.TileGrid;
        if (tileGrid == null)
        {
            return false;
        }

        var targetGridPos = target.GetGridPosition();
        var targetGridInfo = target.GetGridInfo();
        if (targetGridInfo == null)
        {
            return false;
        }

        int targetHeight = targetGridInfo.height;

        // 从目标前方逐层检查阻挡（从targetGridPos.y-1到0）
        for (int checkZ = targetGridPos.y - 1; checkZ >= 0; checkZ--)
        {
            // 在炮台与目标之间的X走廊范围内逐列检查
            for (int targetX = corridorMinX; targetX <= corridorMaxX; targetX++)
            {
                // 只检查高度 >= 目标高度的物体
                for (int h = targetHeight; h < tileGrid.maxHeightLayers; h++)
                {
                    var obj = tileGrid.GetObjectAtPosition(targetX, checkZ, h);
                    if (obj != null)
                    {
                        TileCube_Object tileCubeObj = obj.GetComponent<TileCube_Object>();
                        if (tileCubeObj != null && IsShootableType(tileCubeObj.ObjectType))
                        {
                            if (tileCubeObj == null || tileCubeObj.gameObject == null)
                            {
                                continue;
                            }

                            bool objExists = CNT_Shooting_System_BulletTracker.ShouldShootTarget(tileCubeObj);
                            if (objExists)
                            {
                                // 判断该物体是否真的阻挡走廊内的该格位
                                if (IsObjectBlockingPath(tileCubeObj, targetX, checkZ))
                                {
                                    return false; // 有实体遮挡
                                }
                            }
                        }
                    }
                }
            }
        }

        return true; // 路径清晰
    }

    /// <summary>
    /// 检查目标射击路径是否清晰
    /// 从目标网格位置向前方检查是否有阻挡
    /// 修复：正确处理多格物体（LargeBlock、Barrier）的占位遮挡
    /// 修复：正确处理高度遮挡 - 只有高度≥目标高度的物体才能阻挡
    /// </summary>
    private static bool IsTargetPathClear(TileCube_Object target)
    {
        Tool_TileGrid tileGrid = TileCube_Controller.Instance?.TileGrid;
        if (tileGrid == null)
        {
            return false;
        }

        var targetGridPos = target.GetGridPosition();
        var targetGridInfo = target.GetGridInfo();
        if (targetGridInfo == null)
        {
            return false;
        }

        int targetHeight = targetGridInfo.height; // 目标的高度

        // 获取目标的所有占位位置（对于多格物体很重要）
        List<Vector2Int> targetOccupiedPositions = GetObjectOccupiedPositions(target);

        // 从目标前方逐层检查阻挡（从targetGridPos.y-1到0）
        for (int checkZ = targetGridPos.y - 1; checkZ >= 0; checkZ--)
        {
            // 对于目标占用的每个X坐标，都要检查对应的前方位置
            foreach (var targetPos in targetOccupiedPositions)
            {
                int targetX = targetPos.x;

                // 关键修复：只检查高度 >= 目标高度的物体
                // 低于目标高度的物体不会阻挡射击
                for (int h = targetHeight; h < tileGrid.maxHeightLayers; h++)
                {
                    var obj = tileGrid.GetObjectAtPosition(targetX, checkZ, h);
                    if (obj != null)
                    {
                        // 性能优化：减少GetComponent调用
                        TileCube_Object tileCubeObj = obj.GetComponent<TileCube_Object>();
                        if (tileCubeObj != null && IsShootableType(tileCubeObj.ObjectType))
                        {
                            // 再次检查组件有效性，防止在多线程或销毁过程中出现问题
                            if (tileCubeObj == null || tileCubeObj.gameObject == null)
                            {
                                continue;
                            }

                            bool objExists = CNT_Shooting_System_BulletTracker.ShouldShootTarget(tileCubeObj);

                            // 如果是可射击类型且"存在"，需要进一步检查是否真的阻挡
                            if (objExists)
                            {
                                // 关键修复：检查阻挡物体是否真的占用了射击路径上的位置
                                if (IsObjectBlockingPath(tileCubeObj, targetX, checkZ))
                                {
                                    return false; // 有实体遮挡
                                }
                            }
                        }
                    }
                }
            }
        }

        return true; // 路径清晰
    }

    /// <summary>
    /// 获取物体占用的所有网格位置（适配不同类型的多格物体）
    /// </summary>
    private static List<Vector2Int> GetObjectOccupiedPositions(TileCube_Object obj)
    {
        List<Vector2Int> positions = new List<Vector2Int>();

        if (obj == null || obj.GetGridInfo() == null)
            return positions;

        switch (obj.ObjectType)
        {
            case TileCube_Object.TileCube_ObjectType.LargeBlock:
                // LargeBlock有GetOccupiedPositions方法
                var largeBlock = obj as TileCube_LargeBlock;
                if (largeBlock != null)
                {
                    positions = largeBlock.GetOccupiedPositions();
                }
                break;

            case TileCube_Object.TileCube_ObjectType.Barrier:
                // Barrier有GetOccupiedPositions方法
                var barrier = obj as TileCube_Barrier;
                if (barrier != null)
                {
                    positions = barrier.GetOccupiedPositions();
                }
                break;

            case TileCube_Object.TileCube_ObjectType.SingleBlock:
            case TileCube_Object.TileCube_ObjectType.GoldPig:
            case TileCube_Object.TileCube_ObjectType.Key:
            case TileCube_Object.TileCube_ObjectType.Spawner:
            default:
                // 单格物体，只占用自己的位置
                var gridInfo = obj.GetGridInfo();
                positions.Add(new Vector2Int(gridInfo.gridX, gridInfo.gridZ));
                break;
        }

        return positions;
    }

    /// <summary>
    /// 检查物体是否阻挡了指定的射击路径位置
    /// </summary>
    private static bool IsObjectBlockingPath(TileCube_Object blockingObj, int pathX, int pathZ)
    {
        if (blockingObj == null) return false;

        switch (blockingObj.ObjectType)
        {
            case TileCube_Object.TileCube_ObjectType.LargeBlock:
                // LargeBlock有专门的IsBlockingPosition方法
                var largeBlock = blockingObj as TileCube_LargeBlock;
                return largeBlock?.IsBlockingPosition(pathX, pathZ) ?? false;

            case TileCube_Object.TileCube_ObjectType.Barrier:
                // Barrier有专门的IsBlockingPosition方法
                var barrier = blockingObj as TileCube_Barrier;
                return barrier?.IsBlockingPosition(pathX, pathZ) ?? false;

            case TileCube_Object.TileCube_ObjectType.SingleBlock:
            case TileCube_Object.TileCube_ObjectType.GoldPig:
            case TileCube_Object.TileCube_ObjectType.Key:
            case TileCube_Object.TileCube_ObjectType.Spawner:
            default:
                // 单格物体，检查是否在相同位置
                var gridInfo = blockingObj.GetGridInfo();
                return gridInfo != null && gridInfo.gridX == pathX && gridInfo.gridZ == pathZ;
        }
    }


    /// <summary>
    /// 获取目标类型的射击位置偏移
    /// </summary>
    public static Vector3 GetTargetOffset(TileCube_Object.TileCube_ObjectType objectType)
    {
        if (pathConfig?.targetOffsets != null)
        {
            foreach (var offsetConfig in pathConfig.targetOffsets)
            {
                if (offsetConfig.objectType == objectType)
                    return offsetConfig.positionOffset;
            }
        }
        return Vector3.zero; // 默认无偏移
    }



    /// <summary>
    /// 检查是否为可射击类型
    /// </summary>
    private static bool IsShootableType(TileCube_Object.TileCube_ObjectType objectType)
    {
        return objectType == TileCube_Object.TileCube_ObjectType.SingleBlock ||
               objectType == TileCube_Object.TileCube_ObjectType.LargeBlock ||
               objectType == TileCube_Object.TileCube_ObjectType.GoldPig ||
               objectType == TileCube_Object.TileCube_ObjectType.Barrier;
    }
}