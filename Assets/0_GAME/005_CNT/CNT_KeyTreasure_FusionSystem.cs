using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 钥匙与宝箱融合系统
/// 静态类，不继承MonoBehaviour，负责钥匙与宝箱的匹配和融合逻辑
/// </summary>
public static class CNT_KeyTreasure_FusionSystem
{
    #region 配置参数

    [System.Serializable]
    public class FusionConfig
    {
        [Header("宝箱位置偏移配置")]
        [Tooltip("钥匙飞向宝箱时的目标位置偏移量")]
        public Vector3 treasurePositionOffset = Vector3.zero;

        [Header("调试设置")]
        [Tooltip("是否启用调试日志")]
        public bool enableDebugLog = true;
    }

    // 静态配置实例
    private static FusionConfig _config;
    public static FusionConfig Config
    {
        get
        {
            if (_config == null)
            {
                _config = new FusionConfig();
            }
            return _config;
        }
        set { _config = value; }
    }

    #endregion

    #region 私有变量

    /// <summary>
    /// 到达前排可以被查找的钥匙列表（按到达顺序排列）
    /// </summary>
    private static List<TileCube_Key> availableKeys = new List<TileCube_Key>();

    /// <summary>
    /// 正在进行融合的钥匙集合（防止重复融合）
    /// </summary>
    private static HashSet<TileCube_Key> fusingKeys = new HashSet<TileCube_Key>();

    /// <summary>
    /// 钥匙与宝箱的配对记录（用于开锁时找到对应的宝箱）
    /// Key: 钥匙对象, Value: 对应的宝箱炮对象
    /// </summary>
    private static Dictionary<TileCube_Key, GridConnon_Cannon> keyTreasurePairs = new Dictionary<TileCube_Key, GridConnon_Cannon>();

    /// <summary>
    /// 正在被某把钥匙目标中的宝箱炮集合（防止多个钥匙同时指向同一宝箱）
    /// </summary>
    private static HashSet<GridConnon_Cannon> busyTreasureCannons = new HashSet<GridConnon_Cannon>();

    /// <summary>
    /// 系统是否已初始化
    /// </summary>
    private static bool isInitialized = false;

    #endregion

    #region 初始化和清理

    /// <summary>
    /// 初始化融合系统
    /// </summary>
    /// <param name="config">配置参数</param>
    public static void Initialize(FusionConfig config = null)
    {
        if (isInitialized)
        {
            Debug.LogWarning("[CNT_KeyTreasure_FusionSystem] 系统已经初始化过了");
            return;
        }

        // 设置配置
        if (config != null)
        {
            Config = config;
        }

        // 注册事件
        RegisterEvents();

        // 清空之前的数据（以防万一）
        availableKeys.Clear();
        fusingKeys.Clear();
        keyTreasurePairs.Clear();
        busyTreasureCannons.Clear();

        isInitialized = true;

        if (Config.enableDebugLog)
        {
            Debug.Log("[CNT_KeyTreasure_FusionSystem] 钥匙宝箱融合系统已初始化");
        }
    }

    /// <summary>
    /// 清理融合系统
    /// </summary>
    public static void Cleanup()
    {
        if (!isInitialized)
            return;

        // 取消事件注册
        UnregisterEvents();

        // 清空数据
        availableKeys.Clear();
        fusingKeys.Clear();
        keyTreasurePairs.Clear();
        busyTreasureCannons.Clear();

        isInitialized = false;

        if (Config.enableDebugLog)
        {
            Debug.Log("[CNT_KeyTreasure_FusionSystem] 钥匙宝箱融合系统已清理");
        }
    }

    #endregion

    #region 事件注册管理

    /// <summary>
    /// 注册事件监听
    /// </summary>
    private static void RegisterEvents()
    {
        // 监听钥匙到达前排事件
        TileCube_Controller.OnKeyReachedFront += OnKeyReachedFront;

        // 监听宝箱炮寻找钥匙事件
        GridConnon_Controller.Act_On_TreasureCannonFindKey += OnTreasureCannonFindKey;

        // 监听钥匙开始开锁事件
        TileCube_Controller.OnKeyStartUnlock += OnKeyStartUnlock;
    }

    /// <summary>
    /// 取消事件监听
    /// </summary>
    private static void UnregisterEvents()
    {
        // 取消监听钥匙到达前排事件
        TileCube_Controller.OnKeyReachedFront -= OnKeyReachedFront;

        // 取消监听宝箱炮寻找钥匙事件
        GridConnon_Controller.Act_On_TreasureCannonFindKey -= OnTreasureCannonFindKey;

        // 取消监听钥匙开始开锁事件
        TileCube_Controller.OnKeyStartUnlock -= OnKeyStartUnlock;
    }

    #endregion

    #region 事件处理方法

    /// <summary>
    /// 处理钥匙到达前排事件
    /// 把到达前排可以被查找的钥匙添加到表中
    /// </summary>
    /// <param name="key">到达前排的钥匙</param>
    private static void OnKeyReachedFront(TileCube_Key key)
    {
        if (key == null)
        {
            Debug.LogError("[CNT_KeyTreasure_FusionSystem] 钥匙为空，无法添加到可用钥匙表");
            return;
        }

        // 检查钥匙是否已经在表中
        if (availableKeys.Contains(key))
        {
            if (Config.enableDebugLog)
            {
                Debug.LogWarning($"[CNT_KeyTreasure_FusionSystem] 钥匙 {key.KeyId} 已经在可用钥匙表中");
            }
            return;
        }

        // 添加钥匙到可用表
        availableKeys.Add(key);

        if (Config.enableDebugLog)
        {
            Debug.Log($"[CNT_KeyTreasure_FusionSystem] 钥匙 {key.KeyId} 已到达前排，添加到可用钥匙表中。当前可用钥匙数量: {availableKeys.Count}");
        }
    }

    /// <summary>
    /// 处理宝箱炮寻找钥匙事件
    /// 如果表内有任意钥匙，则进行融合处理（优先发现优先使用）
    /// </summary>
    /// <param name="treasureCannon">寻找钥匙的宝箱炮</param>
    private static void OnTreasureCannonFindKey(GridConnon_Cannon treasureCannon)
    {


        if (treasureCannon == null)
        {
            Debug.LogError("[CNT_KeyTreasure_FusionSystem] 宝箱炮为空，无法处理寻找钥匙事件");
            return;
        }

        // 检查宝箱炮是否已经解锁
        if (treasureCannon.TreasureCannon_IsUnlocked)
        {
            return; // 已解锁的宝箱不需要钥匙
        }

        // 如果该宝箱炮已经被某把钥匙占用，则不再分配新的钥匙
        if (busyTreasureCannons.Contains(treasureCannon))
        {
            return;
        }

        // 检查是否有可用的钥匙（优先使用最早到达的钥匙）
        if (availableKeys.Count == 0)
        {
            return; // 没有可用的钥匙
        }

        // 获取第一个可用的钥匙（最早到达的）
        TileCube_Key firstAvailableKey = availableKeys[0];

        // 检查钥匙是否正在融合中
        if (fusingKeys.Contains(firstAvailableKey))
        {
            // 如果第一个钥匙正在融合，从列表中移除并尝试下一个
            availableKeys.RemoveAt(0);
            OnTreasureCannonFindKey(treasureCannon); // 递归尝试下一个钥匙
            return;
        }

        // 检查钥匙对象是否还存在
        if (firstAvailableKey == null)
        {
            // 钥匙对象已被销毁，从表中移除并尝试下一个
            availableKeys.RemoveAt(0);
            OnTreasureCannonFindKey(treasureCannon); // 递归尝试下一个钥匙
            return;
        }

        // 开始融合处理
        StartKeyTreasureFusion(firstAvailableKey, treasureCannon);
    }

    /// <summary>
    /// 处理钥匙开始开锁事件
    /// 调用目标GridConnon的TreasureCannon_Unlock方法
    /// </summary>
    /// <param name="key">开始开锁的钥匙</param>
    /// <param name="targetPosition">目标位置</param>
    private static void OnKeyStartUnlock(TileCube_Key key, Vector3 targetPosition)
    {
        if (key == null)
        {
            Debug.LogError("[CNT_KeyTreasure_FusionSystem] 钥匙为空，无法处理开锁事件");
            return;
        }

        // 查找对应的宝箱炮
        GridConnon_Cannon targetTreasureCannon = null;
        if (keyTreasurePairs.TryGetValue(key, out targetTreasureCannon))
        {
            if (targetTreasureCannon == null)
            {
                Debug.LogError($"[CNT_KeyTreasure_FusionSystem] 配对记录中ID为 {key.KeyId} 的宝箱炮为空");
                return;
            }
        }
        else
        {
            Debug.LogError($"[CNT_KeyTreasure_FusionSystem] 未找到ID为 {key.KeyId} 的钥匙对应的宝箱炮");
            return;
        }

        // 调用宝箱炮的解锁方法
        targetTreasureCannon.TreasureCannon_Unlock();

        // 清理融合状态
        fusingKeys.Remove(key);
        keyTreasurePairs.Remove(key); // 移除配对记录
        busyTreasureCannons.Remove(targetTreasureCannon); // 释放该宝箱的占用

        if (Config.enableDebugLog)
        {
            Debug.Log($"[CNT_KeyTreasure_FusionSystem] 钥匙 {key.KeyId} 开始开锁，宝箱炮已解锁");
        }
    }

    #endregion

    #region 融合处理方法

    /// <summary>
    /// 开始钥匙与宝箱的融合处理
    /// </summary>
    /// <param name="key">匹配的钥匙</param>
    /// <param name="treasureCannon">目标宝箱炮</param>
    private static void StartKeyTreasureFusion(TileCube_Key key, GridConnon_Cannon treasureCannon)
    {
        if (key == null || treasureCannon == null)
        {
            Debug.LogError("[CNT_KeyTreasure_FusionSystem] 钥匙或宝箱炮为空，无法开始融合");
            return;
        }

        // 标记钥匙正在融合中
        fusingKeys.Add(key);

        // 从可用钥匙表中移除（防止重复使用）
        availableKeys.Remove(key);

        // 记录钥匙与宝箱的配对
        keyTreasurePairs[key] = treasureCannon;

        // 标记该宝箱炮已被占用
        busyTreasureCannons.Add(treasureCannon);

        // 计算宝箱的目标位置（包含偏移）
        Vector3 treasurePosition = treasureCannon.transform.position + Config.treasurePositionOffset;

        // 调用钥匙的飞行方法
        key.FlyToTarget(treasurePosition);

        if (Config.enableDebugLog)
        {
            Debug.Log($"[CNT_KeyTreasure_FusionSystem] 开始融合 - 钥匙 {key.KeyId} 飞向宝箱炮 (位置: {treasurePosition})");
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 获取当前可用钥匙数量
    /// </summary>
    /// <returns>可用钥匙数量</returns>
    public static int GetAvailableKeyCount()
    {
        return availableKeys.Count;
    }

    /// <summary>
    /// 获取正在融合的钥匙数量
    /// </summary>
    /// <returns>正在融合的钥匙数量</returns>
    public static int GetFusingKeyCount()
    {
        return fusingKeys.Count;
    }

    /// <summary>
    /// 检查指定ID的钥匙是否可用
    /// </summary>
    /// <param name="keyId">钥匙ID</param>
    /// <returns>钥匙是否可用</returns>
    public static bool IsKeyAvailable(int keyId)
    {
        return availableKeys.Exists(key => key.KeyId == keyId);
    }

    /// <summary>
    /// 检查指定ID的钥匙是否正在融合中
    /// </summary>
    /// <param name="keyId">钥匙ID</param>
    /// <returns>钥匙是否正在融合中</returns>
    public static bool IsKeyFusing(int keyId)
    {
        return fusingKeys.Any(key => key.KeyId == keyId);
    }

    /// <summary>
    /// 强制清理指定钥匙（用于异常情况处理）
    /// </summary>
    /// <param name="keyId">要清理的钥匙ID</param>
    public static void ForceCleanupKey(int keyId)
    {
        availableKeys.RemoveAll(key => key.KeyId == keyId);
        fusingKeys.RemoveWhere(key => key.KeyId == keyId);

        // 清理配对记录
        var keysToRemove = new List<TileCube_Key>();
        var cannonsToFree = new List<GridConnon_Cannon>();
        foreach (var pair in keyTreasurePairs)
        {
            if (pair.Key.KeyId == keyId)
            {
                keysToRemove.Add(pair.Key);
                cannonsToFree.Add(pair.Value);
            }
        }
        foreach (var keyToRemove in keysToRemove)
        {
            keyTreasurePairs.Remove(keyToRemove);
        }
        foreach (var cannon in cannonsToFree)
        {
            busyTreasureCannons.Remove(cannon);
        }

        if (Config.enableDebugLog)
        {
            Debug.Log($"[CNT_KeyTreasure_FusionSystem] 强制清理钥匙ID {keyId}");
        }
    }

    #endregion
}