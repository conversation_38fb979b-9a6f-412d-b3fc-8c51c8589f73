using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// 金猪融合系统 - 处理金猪破碎时的金币飞行动画
/// </summary>
public static class CNT_GoldPig_FusionSystem
{
    #region 系统配置

    /// <summary>
    /// 融合系统配置
    /// </summary>
    [System.Serializable]
    public class FusionConfig
    {
        [Header("金币UI配置")]
        [Tooltip("金币UI预制体")]
        public GameObject coinUIPrefab;

        [Tooltip("目标飞行位置RectTransform")]
        public RectTransform targetRectTransform;

        [Tooltip("缩放目标大小")]
        public float targetScale = 1.0f;

        [Tooltip("飞行时间")]
        public float flightTime = 1.0f;

        [Header("动画配置")]
        [Tooltip("初始缩放动画时间")]
        public float initialScaleTime = 0.3f;

        [Tooltip("飞行过程中的目标缩放大小")]
        public float flightTargetScale = 0.8f;

        [Tooltip("到达目标后缩小动画时间")]
        public float finalScaleTime = 0.2f;

        [Tooltip("抛物线飞行高度偏移")]
        public float flightHeightOffset = 100f;

        [Tooltip("抛物线偏移方向（1=右侧，-1=左侧，0=无偏移）")]
        public float parabolicDirection = 1f;

        [Tooltip("旋转速度（度/秒）")]
        public float rotationSpeed = 360f;

        [Header("金币奖励配置")]
        [Tooltip("单个金猪给予的金币数量")]
        public int coinsPerPig = 10;

        [Header("位置偏移配置")]
        [Tooltip("金猪位置创建金币的偏移值")]
        public Vector3 pigPositionOffset = Vector3.zero;

        [Header("调试设置")]
        [Tooltip("启用调试日志")]
        public bool enableDebugLog = false;
    }

    #endregion

    #region 私有变量

    private static FusionConfig config;
    private static Canvas targetCanvas;
    private static RectTransform coinTargetRect; // 金币飞行目标位置
    private static int currentSessionCoins = 0; // 当局累加金猪金币
    private static MonoBehaviour coroutineRunner; // 协程运行器

    #endregion

    #region 系统初始化与清理

    /// <summary>
    /// 初始化金猪融合系统
    /// </summary>
    /// <param name="fusionConfig">融合配置</param>
    public static void Initialize(FusionConfig fusionConfig)
    {
        config = fusionConfig;
        currentSessionCoins = 0;

        // 找到Canvas
        FindTargetCanvas();

        // 找到金币目标位置
        FindCoinTarget();

        // 设置协程运行器
        SetupCoroutineRunner();

        // 监听金猪破碎事件
        TileCube_Controller.OnPigBroke += OnPigBroke;

        if (config.enableDebugLog)
        {
            Debug.Log("[CNT_GoldPig_FusionSystem] 系统初始化完成");
        }
    }

    /// <summary>
    /// 清理系统
    /// </summary>
    public static void Cleanup()
    {
        // 取消监听事件
        TileCube_Controller.OnPigBroke -= OnPigBroke;

        // 重置数据
        currentSessionCoins = 0;

        // 调试日志
        if (config != null && config.enableDebugLog)
        {
            Debug.Log("[CNT_GoldPig_FusionSystem] 系统清理完成");
        }

        // 置空静态引用，避免跨场景悬挂/泄漏
        coinTargetRect = null;           // 金币目标位置
        targetCanvas = null;              // 目标Canvas
        coroutineRunner = null;          // 协程运行器
        config = null;                   // 配置对象
    }

    #endregion

    #region 核心功能

    /// <summary>
    /// 处理金猪破碎事件
    /// </summary>
    /// <param name="goldPig">破碎的金猪</param>
    private static void OnPigBroke(TileCube_GoldPig goldPig)
    {
        if (config == null)
        {
            Debug.LogWarning("[CNT_GoldPig_FusionSystem] 系统未初始化");
            return;
        }

        if (goldPig == null)
        {
            Debug.LogWarning("[CNT_GoldPig_FusionSystem] 金猪对象为空");
            return;
        }

        // 创建并播放金币飞行动画
        CreateAndPlayCoinAnimation(goldPig.transform.position);

        // 增加当局金币计数
        currentSessionCoins += config.coinsPerPig;

        if (config.enableDebugLog)
        {
            Debug.Log($"[CNT_GoldPig_FusionSystem] 金猪破碎，当局累计金币: {currentSessionCoins}");
        }
    }

    /// <summary>
    /// 创建并播放金币飞行动画
    /// </summary>
    /// <param name="worldPosition">金猪世界坐标位置</param>
    private static void CreateAndPlayCoinAnimation(Vector3 worldPosition)
    {
        if (targetCanvas == null || coinTargetRect == null || config.coinUIPrefab == null)
        {
            Debug.LogWarning("[CNT_GoldPig_FusionSystem] 缺少必要组件，无法创建金币动画");
            return;
        }

        // 应用金猪位置偏移
        Vector3 adjustedWorldPosition = worldPosition + config.pigPositionOffset;

        // 创建金币UI对象 - 让金币和目标在同一个父级下
        Transform coinParent = coinTargetRect.parent != null ? coinTargetRect.parent : targetCanvas.transform;
        GameObject coinUI = Object.Instantiate(config.coinUIPrefab, coinParent);
        RectTransform coinRect = coinUI.GetComponent<RectTransform>();

        if (coinRect == null)
        {
            Debug.LogError("[CNT_GoldPig_FusionSystem] 金币预制体缺少RectTransform组件");
            Object.Destroy(coinUI);
            return;
        }

        // 使用项目统一的世界坐标转UI坐标工具
        Vector2 uiPosition = Tool_WorldPos_To_UIPos.WorldPosToUIPos(adjustedWorldPosition, targetCanvas, Camera.main);

        // 检查转换是否成功
        if (uiPosition.x < 0 || uiPosition.y < 0)
        {
            Debug.LogWarning("[CNT_GoldPig_FusionSystem] 金猪位置不在屏幕范围内或转换失败");
            Object.Destroy(coinUI);
            return;
        }

        // 将世界坐标转换结果设置为金币的position（世界坐标）
        coinRect.position = uiPosition;
        coinRect.localScale = Vector3.zero;

        if (config.enableDebugLog)
        {
            Debug.Log($"[CNT_GoldPig_FusionSystem] 世界位置: {adjustedWorldPosition}, 金币UI位置: {uiPosition}, 目标位置: {coinTargetRect.position}");
            Debug.Log($"[CNT_GoldPig_FusionSystem] 金币父级: {coinRect.parent.name}, 目标父级: {coinTargetRect.parent.name}");
            Debug.Log($"[CNT_GoldPig_FusionSystem] Canvas相机: {targetCanvas.worldCamera}, Canvas类型: {targetCanvas.renderMode}");
        }

        // 开始动画序列
        if (coroutineRunner != null)
        {
            coroutineRunner.StartCoroutine(CoinAnimationSequence(coinRect));
        }
    }

    /// <summary>
    /// 金币动画序列
    /// </summary>
    /// <param name="coinRect">金币RectTransform</param>
    private static IEnumerator CoinAnimationSequence(RectTransform coinRect)
    {
        // 阶段1：从小缩放到目标大小
        yield return ScaleAnimation(coinRect, Vector3.zero, Vector3.one * config.targetScale, config.initialScaleTime);

        // 阶段2：抛物线飞行到目标位置，同时缩小到飞行目标大小
        yield return FlightWithScaleAnimation(coinRect);

        TileCube_Controller.On_PlaySound?.Invoke("CNT_金猪金币到位");
        // 阶段3：到达目标位置后快速缩小到0
        yield return ScaleAnimation(coinRect, Vector3.one * config.flightTargetScale, Vector3.zero, config.finalScaleTime);


        Object.Destroy(coinRect.gameObject);
    }

    /// <summary>
    /// 缩放动画
    /// </summary>
    /// <param name="coinRect">金币RectTransform</param>
    /// <param name="startScale">起始缩放</param>
    /// <param name="endScale">结束缩放</param>
    /// <param name="duration">持续时间</param>
    private static IEnumerator ScaleAnimation(RectTransform coinRect, Vector3 startScale, Vector3 endScale, float duration)
    {
        float elapsedTime = 0f;

        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / duration;

            // 使用EaseOut曲线
            float easeProgress = 1f - (1f - progress) * (1f - progress);

            coinRect.localScale = Vector3.Lerp(startScale, endScale, easeProgress);

            yield return null;
        }

        coinRect.localScale = endScale;
    }

    /// <summary>
    /// 抛物线飞行动画（保留原版本，备用）
    /// </summary>
    /// <param name="coinRect">金币RectTransform</param>
    private static IEnumerator FlightAnimation(RectTransform coinRect)
    {
        Vector3 startPosition = coinRect.position;

        // 获取目标位置 - 使用世界坐标系统保证一致性
        Vector3 endPosition = coinTargetRect.position;

        Vector3 startScale = coinRect.localScale;

        if (config.enableDebugLog)
        {
            Debug.Log($"[CNT_GoldPig_FusionSystem] 飞行开始位置: {startPosition}, 目标位置: {endPosition}");
        }

        float elapsedTime = 0f;
        float currentRotation = 0f;

        while (elapsedTime < config.flightTime)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / config.flightTime;

            // 渐加速运动曲线 - 使用EaseInQuad曲线实现渐加速效果
            float easeProgress = progress * progress;

            // 抛物线飞行（在中点达到最高点）
            Vector3 currentPosition = Vector3.Lerp(startPosition, endPosition, easeProgress);
            // 使用抛物线公式：在progress=0.5时达到最高点，但这里使用原始progress而不是easeProgress来保持抛物线形状
            float height = 4f * config.flightHeightOffset * progress * (1f - progress);

            // 使用固定的左右方向进行抛物线偏移
            currentPosition.x += height * config.parabolicDirection;

            coinRect.position = currentPosition;

            // 飞行过程中保持目标缩放，不缩小
            coinRect.localScale = startScale;

            // 旋转效果
            currentRotation += config.rotationSpeed * Time.deltaTime;
            coinRect.rotation = Quaternion.Euler(0, 0, currentRotation);

            yield return null;
        }

        // 确保最终位置
        coinRect.position = endPosition;
    }

    /// <summary>
    /// 抛物线飞行动画同时缩放
    /// </summary>
    /// <param name="coinRect">金币RectTransform</param>
    private static IEnumerator FlightWithScaleAnimation(RectTransform coinRect)
    {
        Vector3 startPosition = coinRect.position;

        // 获取目标位置 - 使用世界坐标系统保证一致性
        Vector3 endPosition = coinTargetRect.position;

        Vector3 startScale = coinRect.localScale; // 应该是 targetScale
        Vector3 endScale = Vector3.one * config.flightTargetScale;

        if (config.enableDebugLog)
        {
            Debug.Log($"[CNT_GoldPig_FusionSystem] 飞行开始位置: {startPosition}, 目标位置: {endPosition}, 开始缩放: {startScale}, 结束缩放: {endScale}");
        }

        float elapsedTime = 0f;
        float currentRotation = 0f;

        while (elapsedTime < config.flightTime)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / config.flightTime;

            // 渐加速运动曲线 - 使用EaseInQuad曲线实现渐加速效果
            float easeProgress = progress * progress;

            // 抛物线飞行（在中点达到最高点）
            Vector3 currentPosition = Vector3.Lerp(startPosition, endPosition, easeProgress);
            // 使用抛物线公式：在progress=0.5时达到最高点，但这里使用原始progress而不是easeProgress来保持抛物线形状
            float height = 4f * config.flightHeightOffset * progress * (1f - progress);

            // 使用固定的左右方向进行抛物线偏移
            currentPosition.x += height * config.parabolicDirection;

            coinRect.position = currentPosition;

            // 飞行过程中同时缩放到目标大小
            coinRect.localScale = Vector3.Lerp(startScale, endScale, progress);

            // 旋转效果
            currentRotation += config.rotationSpeed * Time.deltaTime;
            coinRect.rotation = Quaternion.Euler(0, 0, currentRotation);

            yield return null;
        }

        // 确保最终位置和缩放
        coinRect.position = endPosition;
        coinRect.localScale = endScale;
    }



    #endregion

    #region 辅助方法

    /// <summary>
    /// 查找目标Canvas
    /// </summary>
    private static void FindTargetCanvas()
    {
        // 搜索名为"Canvas"的对象
        GameObject canvasObject = GameObject.Find("Canvas");
        if (canvasObject != null)
        {
            targetCanvas = canvasObject.GetComponent<Canvas>();
        }

        if (targetCanvas == null)
        {
            Debug.LogWarning("[CNT_GoldPig_FusionSystem] 未找到Canvas对象");
        }
    }

    /// <summary>
    /// 查找金币目标位置
    /// </summary>
    private static void FindCoinTarget()
    {
        // 优先使用配置的目标位置
        if (config.targetRectTransform != null)
        {
            coinTargetRect = config.targetRectTransform;
            if (config.enableDebugLog)
            {
                Debug.Log("[CNT_GoldPig_FusionSystem] 使用配置的目标飞行位置");
            }
            return;
        }

        if (targetCanvas == null) return;

        // 自动查找游戏面板中的金币图标作为目标
        GameObject gamePanel = GameObject.Find("3MatchUI_GamePanel(Clone)");
        if (gamePanel == null)
        {
            // 尝试在Canvas下查找
            Transform gamePanelTransform = targetCanvas.transform.Find("3MatchUI_GamePanel(Clone)");
            if (gamePanelTransform != null)
            {
                gamePanel = gamePanelTransform.gameObject;
            }
        }

        if (gamePanel != null)
        {
            // 查找imgCoin对象
            Transform coinTransform = gamePanel.transform.Find("imgCoin");
            if (coinTransform == null)
            {
                // 递归查找imgCoin
                coinTransform = FindChildByName(gamePanel.transform, "imgCoin");
            }

            if (coinTransform != null)
            {
                coinTargetRect = coinTransform.GetComponent<RectTransform>();
            }
        }

        if (coinTargetRect == null)
        {
            Debug.LogWarning("[CNT_GoldPig_FusionSystem] 未找到金币目标位置，请在配置中手动指定targetRectTransform");
        }
        else if (config.enableDebugLog)
        {
            Debug.Log("[CNT_GoldPig_FusionSystem] 自动找到金币目标位置: " + coinTargetRect.name);
        }
    }

    /// <summary>
    /// 设置协程运行器
    /// </summary>
    private static void SetupCoroutineRunner()
    {
        // 使用Game_CNT_Game作为协程运行器
        coroutineRunner = Object.FindObjectOfType<Game_CNT_Game>();

        if (coroutineRunner == null)
        {
            Debug.LogWarning("[CNT_GoldPig_FusionSystem] 未找到协程运行器");
        }
    }

    /// <summary>
    /// 递归查找子对象
    /// </summary>
    /// <param name="parent">父对象</param>
    /// <param name="name">目标名称</param>
    /// <returns>找到的Transform，未找到返回null</returns>
    private static Transform FindChildByName(Transform parent, string name)
    {
        for (int i = 0; i < parent.childCount; i++)
        {
            Transform child = parent.GetChild(i);
            if (child.name == name)
            {
                return child;
            }

            Transform result = FindChildByName(child, name);
            if (result != null)
            {
                return result;
            }
        }
        return null;
    }

    #endregion

    #region 公共接口

    /// <summary>
    /// 获取当局累加的金猪金币
    /// </summary>
    /// <returns>当局金猪金币数量</returns>
    public static int GetCurrentSessionCoins()
    {
        return currentSessionCoins;
    }

    /// <summary>
    /// 重置当局金币计数
    /// </summary>
    public static void ResetSessionCoins()
    {
        currentSessionCoins = 0;

        if (config != null && config.enableDebugLog)
        {
            Debug.Log("[CNT_GoldPig_FusionSystem] 重置当局金币计数");
        }
    }

    /// <summary>
    /// 获取系统是否已初始化
    /// </summary>
    /// <returns>是否已初始化</returns>
    public static bool IsInitialized()
    {
        return config != null;
    }

    #endregion
}