using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.SceneManagement;

public class Game_CNT_Game : Game_Base_Game, GridConnon_IInputChecker, TileCube_IInputChecker
{

    public bool IsGameOver = false;


    #region Unity生命周期

    protected override void Awake()
    {


        base.Awake();

        QualitySettings.shadowDistance = 25;

        // 初始化摄像机
        InitializeCamera();

        RegisterEvents();

        // 初始化射击系统
        InitializeShootingSystem();

        // 初始化钥匙宝箱融合系统
        InitializeKeyTreasureFusionSystem();

        // 初始化金猪融合系统
        InitializeGoldPigFusionSystem();

    }

    protected override void OnDestroy()
    {
        base.OnDestroy();

        // 清理射击系统
        CNT_Shooting_System.Cleanup();

        // 清理钥匙宝箱融合系统
        CNT_KeyTreasure_FusionSystem.Cleanup();

        // 清理金猪融合系统
        CNT_GoldPig_FusionSystem.Cleanup();

        UnregisterEvents();
    }

    private void Update()
    {
#if UNITY_EDITOR
        // 测试功能
        if (Input.GetKeyDown(KeyCode.X))
        {
            MatchUI_UserData_Manager.Instance.Set.Level_Current += 1;
            SceneManager.LoadScene("2Game");
        }

        if (Input.GetKeyDown(KeyCode.Z))
        {
            MatchUI_UserData_Manager.Instance.Set.Level_Current -= 1;
            SceneManager.LoadScene("2Game");
        }
#endif

        if (CanUpdateShootingSys())
            // 更新射击系统
            CNT_Shooting_System.Update();

        // 游戏失败检测
        CheckGameOverCondition();
    }

    #endregion

    #region 射击系统配置

    [Header("射击系统配置")]
    [SerializeField] private CNT_Shooting_System.ShootingConfig shootingConfig = new CNT_Shooting_System.ShootingConfig();

    /// <summary>
    /// 初始化射击系统
    /// </summary>
    private void InitializeShootingSystem()
    {

        CNT_Shooting_System.Initialize(shootingConfig);

        shootingConfig.shootingInterval = MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data.ShootingSpeed_CheckInterval;
    }

    private bool CanUpdateShootingSys()
    {
        if (IsGameOver)
            return false;

        // 直接检查静态实例是否存在且激活，最高效的方式
        if (MatchUI_GetPropPanel._instance != null && MatchUI_GetPropPanel._instance.gameObject.activeInHierarchy)
            return false;

        if (MatchUI_UnlockVacantPanel._instance != null && MatchUI_UnlockVacantPanel._instance.gameObject.activeInHierarchy)
            return false;

        if (MatchUI_NoAdsPanel._instance != null && MatchUI_NoAdsPanel._instance.gameObject.activeInHierarchy)
            return false;

        if (MatchUI_SettingPanel._instance != null && MatchUI_SettingPanel._instance.gameObject.activeInHierarchy)
            return false;

        if (MatchUI_ShopPanel._instance != null && MatchUI_ShopPanel._instance.gameObject.activeInHierarchy)
            return false;

        if (CNT_GridConnon_PropPickUp_Panel._instance != null && CNT_GridConnon_PropPickUp_Panel._instance.gameObject.activeInHierarchy)
            return false;

        if (CNT_TileCube_ClearColor_Panel._instance != null && CNT_TileCube_ClearColor_Panel._instance.gameObject.activeInHierarchy)
            return false;

        if (TileCube_Controller.Instance.currentState != TileCube_Controller.ControllerState.Normal || GridConnon_Controller.Instance.currentControllerState != GridConnon_Controller_State.Normal)
            return false;



        // 所有指定面板都没有打开，可以更新射击系统
        return true;
    }

    #endregion

    #region 钥匙宝箱融合系统配置

    [Header("钥匙宝箱融合系统配置")]
    [SerializeField] private CNT_KeyTreasure_FusionSystem.FusionConfig keyTreasureFusionConfig;

    /// <summary>
    /// 初始化钥匙宝箱融合系统
    /// </summary>
    private void InitializeKeyTreasureFusionSystem()
    {
        // 如果没有配置，创建默认配置
        if (keyTreasureFusionConfig == null)
        {
            keyTreasureFusionConfig = new CNT_KeyTreasure_FusionSystem.FusionConfig();
            keyTreasureFusionConfig.treasurePositionOffset = new Vector3(0, 0.5f, 0); // 默认宝箱位置向上偏移0.5单位
            keyTreasureFusionConfig.enableDebugLog = true; // 启用调试日志
        }

        CNT_KeyTreasure_FusionSystem.Initialize(keyTreasureFusionConfig);
    }

    #endregion



    #region 私有变量

    private string matachUI_MainEvent_Name;

    // 道具使用完成回调存储
    private System.Action currentPropAction;

    // 游戏失败检测相关变量
    [Header("游戏失败检测配置")]
    [Tooltip("满格无进度变化多少秒后游戏失败")]
    public float GameOverWaitTime = 1.0f;

    private float lastProgressCheckTime = 0f; // 上次进度检查时间
    private int lastProgressValue = -1; // 上次进度值（-1表示未初始化）
    private bool isCheckingGameOver = false; // 是否正在检测游戏失败
    private bool wasUIDisplayingLastFrame = false; // 上一帧是否有UI显示

    #endregion

    #region 金猪融合系统配置

    [Header("金猪融合系统配置")]
    [SerializeField] private CNT_GoldPig_FusionSystem.FusionConfig goldPigFusionConfig;

    /// <summary>
    /// 初始化金猪融合系统
    /// </summary>
    private void InitializeGoldPigFusionSystem()
    {
        // 如果没有配置，创建默认配置
        if (goldPigFusionConfig == null)
        {
            goldPigFusionConfig = new CNT_GoldPig_FusionSystem.FusionConfig();
            goldPigFusionConfig.targetScale = 1.0f; // 默认目标缩放
            goldPigFusionConfig.flightTime = 1.0f; // 默认飞行时间
            goldPigFusionConfig.initialScaleTime = 0.3f; // 默认初始缩放时间
            goldPigFusionConfig.finalScaleTime = 0.2f; // 默认最终缩放时间
            goldPigFusionConfig.flightHeightOffset = 100f; // 默认抛物线高度偏移
            goldPigFusionConfig.parabolicDirection = 1f; // 默认向右偏移
            goldPigFusionConfig.rotationSpeed = 360f; // 默认旋转速度
            goldPigFusionConfig.coinsPerPig = 10; // 默认每个金猪给予10金币
            goldPigFusionConfig.pigPositionOffset = Vector3.zero; // 默认无偏移
            goldPigFusionConfig.enableDebugLog = true; // 启用调试日志
        }

        CNT_GoldPig_FusionSystem.Initialize(goldPigFusionConfig);
    }

    #endregion

    #region 调试功能配置

    [Header("颜色统计调试输出")]
    [SerializeField]
    [Tooltip("是否启用颜色统计调试输出")]
    public bool enableColorStatsDebug = false;

    /// <summary>
    /// 调试输出 - 对比TileCube和GridConnon的颜色统计差额
    /// </summary>
    private void DebugColorStatsComparison()
    {
        if (!enableColorStatsDebug) return;

        // 获取TileCube模块的颜色统计
        Dictionary<int, int> tileCubeColorStats = GetTileCubeColorStats();

        // 获取GridConnon模块的颜色统计
        Dictionary<int, int> gridConnonColorStats = GetGridConnonColorStats();

        Debug.Log("=== 颜色统计对比调试 ===");
        Debug.Log($"TileCube颜色统计: {ColorStatsToString(tileCubeColorStats)}");
        Debug.Log($"GridConnon颜色统计: {ColorStatsToString(gridConnonColorStats)}");

        // 计算差额
        Dictionary<int, int> colorDifferences = CalculateColorDifferences(tileCubeColorStats, gridConnonColorStats);

        if (colorDifferences.Count > 0)
        {
            Debug.LogWarning($"[颜色差额检测] 发现差额: {ColorStatsToString(colorDifferences)}");
        }
        else
        {
            Debug.Log("[颜色差额检测] 颜色统计完全一致，无差额");
        }
    }

    /// <summary>
    /// 获取TileCube模块的颜色统计
    /// </summary>
    /// <returns>颜色ID到数量的字典</returns>
    private Dictionary<int, int> GetTileCubeColorStats()
    {
        Dictionary<int, int> colorStats = new Dictionary<int, int>();

        if (TileCube_Controller.Instance?.Current_Objects == null)
            return colorStats;

        foreach (var obj in TileCube_Controller.Instance.Current_Objects)
        {
            if (obj == null) continue;

            // 只统计有颜色的物体，使用数据源来获取颜色和击打次数
            if (obj is TileCube_SingleBlock singleBlock && singleBlock.blockData != null && singleBlock.blockData.ColorId > 0)
            {
                if (colorStats.ContainsKey(singleBlock.blockData.ColorId))
                    colorStats[singleBlock.blockData.ColorId]++;
                else
                    colorStats[singleBlock.blockData.ColorId] = 1;
            }
            else if (obj is TileCube_LargeBlock largeBlock && largeBlock.blockData != null && largeBlock.blockData.ColorId > 0)
            {
                // LargeBlock按剩余击打次数统计
                int remainingHits = largeBlock.RequiredHits - largeBlock.CurrentHits;
                if (colorStats.ContainsKey(largeBlock.blockData.ColorId))
                    colorStats[largeBlock.blockData.ColorId] += remainingHits;
                else
                    colorStats[largeBlock.blockData.ColorId] = remainingHits;
            }
            else if (obj is TileCube_Barrier barrier && barrier.barrierData != null && barrier.barrierData.ColorId > 0)
            {
                // Barrier按剩余击打次数统计
                int remainingHits = barrier.CurrentHitValues;
                if (colorStats.ContainsKey(barrier.barrierData.ColorId))
                    colorStats[barrier.barrierData.ColorId] += remainingHits;
                else
                    colorStats[barrier.barrierData.ColorId] = remainingHits;
            }
            else if (obj is TileCube_GoldPig goldPig)
            {
                // GoldPig按剩余击打次数统计，金猪没有颜色统计，跳过
                // GoldPig通常没有颜色分类，这里跳过
            }
            else if (obj is TileCube_Spawner spawner && spawner.CurrentSpawnColorSingleBlocks != null)
            {
                // Spawner按库存中的颜色块统计
                foreach (int colorId in spawner.CurrentSpawnColorSingleBlocks)
                {
                    if (colorId > 0)
                    {
                        if (colorStats.ContainsKey(colorId))
                            colorStats[colorId]++;
                        else
                            colorStats[colorId] = 1;
                    }
                }
            }
        }

        return colorStats;
    }

    /// <summary>
    /// 获取GridConnon模块的颜色统计
    /// </summary>
    /// <returns>颜色ID到数量的字典</returns>
    private Dictionary<int, int> GetGridConnonColorStats()
    {
        Dictionary<int, int> colorStats = new Dictionary<int, int>();

        if (GridConnon_Controller.Instance?.CurrentCannons == null)
            return colorStats;

        foreach (var cannon in GridConnon_Controller.Instance.CurrentCannons)
        {
            if (cannon == null || cannon.ColorId <= 0) continue;

            // 排除TreasureCannon（无颜色）
            if (cannon.ObjectType == GridConnon_ObjectType.TreasureCannon) continue;

            // 获取击打次数
            int hitCount = 1; // 默认值
            if (cannon is GridConnon_Cannon gridCannon)
            {
                hitCount = gridCannon.GetCurrentHitCount();
            }

            if (colorStats.ContainsKey(cannon.ColorId))
                colorStats[cannon.ColorId] += hitCount;
            else
                colorStats[cannon.ColorId] = hitCount;
        }

        return colorStats;
    }

    /// <summary>
    /// 计算两个颜色统计字典的差额
    /// </summary>
    /// <param name="tileCubeStats">TileCube颜色统计</param>
    /// <param name="gridConnonStats">GridConnon颜色统计</param>
    /// <returns>差额字典（正数表示TileCube多，负数表示GridConnon多）</returns>
    private Dictionary<int, int> CalculateColorDifferences(Dictionary<int, int> tileCubeStats, Dictionary<int, int> gridConnonStats)
    {
        Dictionary<int, int> differences = new Dictionary<int, int>();

        // 获取所有涉及的颜色ID
        HashSet<int> allColorIds = new HashSet<int>();
        foreach (var colorId in tileCubeStats.Keys) allColorIds.Add(colorId);
        foreach (var colorId in gridConnonStats.Keys) allColorIds.Add(colorId);

        // 计算每种颜色的差额
        foreach (var colorId in allColorIds)
        {
            int tileCubeCount = tileCubeStats.ContainsKey(colorId) ? tileCubeStats[colorId] : 0;
            int gridConnonCount = gridConnonStats.ContainsKey(colorId) ? gridConnonStats[colorId] : 0;
            int difference = tileCubeCount - gridConnonCount;

            if (difference != 0)
            {
                differences[colorId] = difference;
            }
        }

        return differences;
    }

    /// <summary>
    /// 将颜色统计字典转换为可读字符串
    /// </summary>
    /// <param name="colorStats">颜色统计字典</param>
    /// <returns>格式化字符串</returns>
    private string ColorStatsToString(Dictionary<int, int> colorStats)
    {
        if (colorStats.Count == 0) return "无颜色数据";

        List<string> statStrings = new List<string>();
        foreach (var kvp in colorStats.OrderBy(x => x.Key))
        {
            statStrings.Add($"颜色{kvp.Key}:{kvp.Value}");
        }

        return string.Join(", ", statStrings);
    }

    /// <summary>
    /// 延迟执行颜色统计调试，确保所有物体都已创建完成
    /// </summary>
    /// <returns></returns>
    private IEnumerator DelayedColorStatsDebug()
    {
        // 等待一帧，确保所有物体都已经创建完成
        yield return null;

        // 执行颜色统计调试
        DebugColorStatsComparison();
    }

    #endregion

    #region 碰撞体配置

    [System.Serializable]
    public class CollisionBodyConfig
    {
        [Header("碰撞框配置")]
        [Tooltip("碰撞框大小")]
        public Vector3 boundsSize = Vector3.one * 0.2f;

        [Tooltip("碰撞框相对于物体的偏移量")]
        public Vector3 boundsOffset = Vector3.zero;
    }

    [Header("GridConnon子弹碰撞体配置")]
    [SerializeField]
    private CollisionBodyConfig singleBlockConfig = new CollisionBodyConfig();

    [SerializeField]
    private CollisionBodyConfig largeBlockConfig = new CollisionBodyConfig();

    [SerializeField]
    private CollisionBodyConfig goldPigConfig = new CollisionBodyConfig();

    [SerializeField]
    private CollisionBodyConfig barrierConfig = new CollisionBodyConfig();

    [Header("调试设置")]
    [Tooltip("是否显示调试碰撞框")]
    public bool showDebugBounds = true;

    [Header("触发行配置")]
    [Tooltip("触发添加碰撞体的行数（从0开始计数，3表示第四行）")]
    public int triggerRow = 3;

    #endregion

    #region 摄像机Z偏移配置

    [Header("摄像机Z偏移配置")]
    [Tooltip("进入道具拾取状态时的摄像机Z偏移值")]
    public float cameraZOffset = 2f;

    [Tooltip("摄像机过渡动画持续时间")]
    public float cameraTransitionDuration = 0.5f;

    [Tooltip("摄像机过渡动画曲线")]
    public AnimationCurve cameraTransitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    // 私有变量
    private Vector3 originalCameraPosition; // 摄像机初始位置
    private Camera mainCamera; // 主摄像机引用
    private Coroutine cameraTransitionCoroutine; // 摄像机过渡协程
    private bool hasSavedOriginalPosition = false; // 是否已保存初始位置

    #endregion

    #region 事件注册与注销

    private void RegisterEvents()
    {
        GridConnon_InputManager.RegisterChecker(this);
        TileCube_InputManager.RegisterChecker(this);

        MatchUI_Controller.On_MainEvent += On_MtachUI_MainEvent;
        MatchUI_Controller.On_Ask_UseProp += On_Ask_UseProp;

        // 监听三个控制器的On_PlaySound事件
        TileCube_Controller.On_PlaySound += OnPlaySound;
        GridConnon_Controller.On_PlaySound += OnPlaySound;
        MatchUI_Controller.On_PlaySound += OnPlaySound;

        TileCube_Controller.On_PlayVib += On_PlayVib;
        GridConnon_Controller.On_PlayVib += On_PlayVib;
        MatchUI_Controller.On_PlayVib += On_PlayVib;


        // 监听音乐播放和停止事件
        MatchUI_Controller.On_PlayMusic += OnPlayMusic;
        MatchUI_Controller.On_StopMusic += OnStopMusic;

        // 监听Tool_TileGrid的推进完成事件
        Tool_TileGrid.OnPushCompleted += OnTileGridPushCompleted;

        TileCube_Controller.On_Enter_PropClearColor_State += On_Enter_PropClearColor_State;
        TileCube_Controller.On_Exit_PropClearColor_State += On_Exit_PropClearColor_State;
        GridConnon_Controller.OnControllerStateChanged += OnGridConnon_ControllerStateChanged;

        // 监听道具使用成功事件
        GridConnon_Controller.OnPropPickOutUsedSuccessfully += OnPropPickOutUsedSuccessfully;
        TileCube_Controller.OnPropClearColorUsedSuccessfully += OnPropClearColorUsedSuccessfully;

        // 监听奖励射击格点击询问事件
        GridConnon_ShotGrid_Controller.Action_RewardShotGrid_AskClick += OnRewardShotGrid_AskClick;

        // 监听道具清除目标颜色事件
        TileCube_Controller.OnPropClearColor_ClearTargetColor += OnPropClearColor_ClearTargetColor;

        // 监听进度清除事件
        TileCube_Controller.On_Progress_Clear += OnProgressClear;

        // 监听重新游戏成功事件
        MatchUI_Controller.On_ReGameSuccess += OnReGameSuccess;

        // 监听关卡创建完成事件
        TileCube_Controller.OnLevelCreationCompleted += OnLevelCreationCompleted;


        TileCube_Controller.OnSpanwer_BlockLand += OnSpanwer_BlockLand;

    }

    private void UnregisterEvents()
    {
        GridConnon_InputManager.UnregisterChecker(this);
        TileCube_InputManager.UnregisterChecker(this);

        MatchUI_Controller.On_MainEvent -= On_MtachUI_MainEvent;
        MatchUI_Controller.On_Ask_UseProp -= On_Ask_UseProp;

        // 监听三个控制器的On_PlaySound事件
        TileCube_Controller.On_PlaySound -= OnPlaySound;
        GridConnon_Controller.On_PlaySound -= OnPlaySound;
        MatchUI_Controller.On_PlaySound -= OnPlaySound;

        TileCube_Controller.On_PlayVib -= On_PlayVib;
        GridConnon_Controller.On_PlayVib -= On_PlayVib;
        MatchUI_Controller.On_PlayVib -= On_PlayVib;


        // 监听音乐播放和停止事件
        MatchUI_Controller.On_PlayMusic -= OnPlayMusic;
        MatchUI_Controller.On_StopMusic -= OnStopMusic;

        // 取消监听Tool_TileGrid的推进完成事件
        Tool_TileGrid.OnPushCompleted -= OnTileGridPushCompleted;

        TileCube_Controller.On_Enter_PropClearColor_State -= On_Enter_PropClearColor_State;
        TileCube_Controller.On_Exit_PropClearColor_State -= On_Exit_PropClearColor_State;
        GridConnon_Controller.OnControllerStateChanged -= OnGridConnon_ControllerStateChanged;

        // 取消监听道具使用成功事件
        GridConnon_Controller.OnPropPickOutUsedSuccessfully -= OnPropPickOutUsedSuccessfully;
        TileCube_Controller.OnPropClearColorUsedSuccessfully -= OnPropClearColorUsedSuccessfully;

        // 取消监听奖励射击格点击询问事件
        GridConnon_ShotGrid_Controller.Action_RewardShotGrid_AskClick -= OnRewardShotGrid_AskClick;

        // 取消监听道具清除目标颜色事件
        TileCube_Controller.OnPropClearColor_ClearTargetColor -= OnPropClearColor_ClearTargetColor;

        // 取消监听进度清除事件
        TileCube_Controller.On_Progress_Clear -= OnProgressClear;

        // 取消监听重新游戏成功事件
        MatchUI_Controller.On_ReGameSuccess -= OnReGameSuccess;

        // 取消监听关卡创建完成事件
        TileCube_Controller.OnLevelCreationCompleted -= OnLevelCreationCompleted;

        TileCube_Controller.OnSpanwer_BlockLand -= OnSpanwer_BlockLand;

    }

    #endregion

    #region 关卡创建完成处理

    /// <summary>
    /// 关卡创建完成回调 - 处理难度关卡换色
    /// </summary>
    private void OnLevelCreationCompleted()
    {
        // 获取当前关卡索引
        int currentLevel = MatchUI_UserData_Manager.Instance.Get.Level_Current;

        // 检查是否为难度关卡
        if (IsHardLevel(currentLevel))
        {
            // 如果是难度关卡，换色为红色
            if (TileCube_SceneColorer.Instance != null)
            {
                TileCube_SceneColorer.Instance.SetSceneColor("Red");
                //Debug.Log($"[Game_CNT_Game] 检测到难度关卡 {currentLevel}，已切换为红色主题");
            }
        }
    }

    /// <summary>
    /// 检查指定关卡是否为难度关卡
    /// </summary>
    /// <param name="levelIndex">关卡索引</param>
    /// <returns>是否为难度关卡</returns>
    private bool IsHardLevel(int levelIndex)
    {
        // 从配置数据获取难度关卡列表
        var hardLevels = MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.HardLevel_TargetLevel;

        if (hardLevels != null)
        {
            return hardLevels.Contains(levelIndex + 1);
        }

        return false;
    }

    #endregion

    #region 摄像机控制功能

    /// <summary>
    /// 初始化摄像机引用并保存初始位置
    /// </summary>
    private void InitializeCamera()
    {
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
            {
                //Debug.LogWarning("[Game_CNT_Game] 未找到主摄像机");
                return;
            }
        }

        // 只在第一次保存位置
        if (!hasSavedOriginalPosition)
        {
            originalCameraPosition = mainCamera.transform.position;
            hasSavedOriginalPosition = true;
            ////Debug.Log($"[Game_CNT_Game] 已保存摄像机初始位置: {originalCameraPosition}");
        }
    }

    /// <summary>
    /// 摄像机移动到偏移位置（带过渡动画）
    /// </summary>
    private void MoveCameraToOffset()
    {
        InitializeCamera();
        if (mainCamera == null) return;

        Vector3 targetPosition = originalCameraPosition + new Vector3(0, 0, cameraZOffset);
        StartCameraTransition(targetPosition);
    }

    /// <summary>
    /// 摄像机回到原始位置（带过渡动画）
    /// </summary>
    private void MoveCameraToOriginal()
    {
        InitializeCamera();
        if (mainCamera == null) return;

        StartCameraTransition(originalCameraPosition);
    }

    /// <summary>
    /// 开始摄像机位置过渡动画
    /// </summary>
    /// <param name="targetPosition">目标位置</param>
    private void StartCameraTransition(Vector3 targetPosition)
    {
        if (cameraTransitionCoroutine != null)
        {
            StopCoroutine(cameraTransitionCoroutine);
        }

        cameraTransitionCoroutine = StartCoroutine(CameraTransitionCoroutine(targetPosition));
    }

    /// <summary>
    /// 摄像机位置过渡动画协程
    /// </summary>
    /// <param name="targetPosition">目标位置</param>
    /// <returns>协程</returns>
    private IEnumerator CameraTransitionCoroutine(Vector3 targetPosition)
    {
        if (mainCamera == null) yield break;

        Vector3 startPosition = mainCamera.transform.position;
        float elapsedTime = 0f;

        while (elapsedTime < cameraTransitionDuration)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / cameraTransitionDuration;

            // 使用动画曲线计算过渡进度
            float curveProgress = cameraTransitionCurve.Evaluate(progress);

            // 计算当前位置
            Vector3 currentPosition = Vector3.Lerp(startPosition, targetPosition, curveProgress);
            mainCamera.transform.position = currentPosition;

            yield return null;
        }

        // 确保到达目标位置
        mainCamera.transform.position = targetPosition;
        cameraTransitionCoroutine = null;
    }

    #endregion

    #region 关卡创建功能

    /// <summary>
    /// 创建游戏关卡（包括GridConnon和TileCube）
    /// 计算GridConnon关卡的颜色总数传递给TileCube关卡
    /// </summary>
    /// <param name="level">关卡编号</param>
    /// <param name="levelExceed">附加关卡序号（默认为1，表示主关卡）</param>
    public void CreateGameLevel(int level, int levelExceed = 1)
    {
        // 添加控制器实例空引用检查
        if (GridConnon_Controller.Instance == null)
        {
            //Debug.LogError("[Game_CNT_Game] GridConnon_Controller.Instance 为空，无法创建关卡");
            return;
        }

        if (TileCube_Controller.Instance == null)
        {
            //Debug.LogError("[Game_CNT_Game] TileCube_Controller.Instance 为空，无法创建关卡");
            return;
        }

        // 1. 创建GridConnon关卡
        var gridConnonLevel = GridConnon_LevelConverter.GetLevel(level, levelExceed);
        if (gridConnonLevel != null)
        {
            GridConnon_Controller.Instance.CreateLevel(gridConnonLevel);
        }

        // 2. 计算GridConnon关卡的颜色总数（排除TreasureCannon）
        Dictionary<int, int> colorDict = CalculateGridConnonColorCounts(gridConnonLevel);

        // 3. 创建TileCube关卡，使用计算出的颜色字典
        var tileCubeLevel = TileCube_LevelConverter.GetLevel(
            levelIndex: level,                    // 关卡索引
            totalColorDict: colorDict,            // 颜色配置字典
            levelExceed: levelExceed              // 附加关卡序号
        );

        if (tileCubeLevel != null)
        {
            TileCube_Controller.Instance.CreateObjectsFromLevelData(tileCubeLevel);
        }

        // 关卡创建完成后，手动检查第三行的物体并添加碰撞体脚本
        StartCoroutine(CheckAndAddCollisionBodiesAfterLevelCreation());

        // 调试输出 - 延迟一帧确保所有物体都已创建完成
        StartCoroutine(DelayedColorStatsDebug());
    }

    /// <summary>
    /// 计算GridConnon关卡中每种颜色的总击打值
    /// TreasureCannon不计入统计
    /// </summary>
    /// <param name="gridConnonLevel">GridConnon关卡数据</param>
    /// <returns>颜色ID到总击打值的字典</returns>
    private Dictionary<int, int> CalculateGridConnonColorCounts(GridConnon_Data_Level gridConnonLevel)
    {
        Dictionary<int, int> colorDict = new Dictionary<int, int>();

        if (gridConnonLevel?.cannons == null)
        {
            return colorDict;
        }

        foreach (var cannon in gridConnonLevel.cannons)
        {
            // 跳过TreasureCannon（无颜色或colorId为-1）
            if (cannon.objectType == GridConnon_ObjectType.TreasureCannon || cannon.colorId <= 0)
            {
                continue;
            }

            // 累加相同颜色的击打值
            if (colorDict.ContainsKey(cannon.colorId))
            {
                colorDict[cannon.colorId] += cannon.hitCount;
            }
            else
            {
                colorDict[cannon.colorId] = cannon.hitCount;
            }
        }

        return colorDict;
    }

    #endregion

    #region 碰撞体管理功能

    /// <summary>
    /// 关卡创建完成后延迟检查并添加碰撞体脚本
    /// </summary>
    private IEnumerator CheckAndAddCollisionBodiesAfterLevelCreation()
    {
        // 等待一帧，确保所有物体都已经创建完成
        yield return null;

        // 手动检查指定行范围的所有物体
        CheckAndAddCollisionBodiesForTargetRows();
    }

    /// <summary>
    /// 检查指定行范围的所有物体并添加碰撞体脚本
    /// 初始创建时检查第一行到触发行，推进事件时只检查触发行
    /// </summary>
    private void CheckAndAddCollisionBodiesForTargetRows()
    {
        // 获取TileGrid实例
        Tool_TileGrid tileGrid = FindObjectOfType<Tool_TileGrid>();
        if (tileGrid == null)
        {
            //Debug.LogWarning("[Game_CNT_Game] 未找到Tool_TileGrid实例，无法检查指定行物体");
            return;
        }

        // 遍历TileCube_Controller中的所有当前物体
        if (TileCube_Controller.Instance?.Current_Objects != null)
        {
            foreach (var tileCubeObject in TileCube_Controller.Instance.Current_Objects)
            {
                if (tileCubeObject?.gameObject == null) continue;

                // 获取物体的网格信息
                GridTileObject gridInfo = Tool_TileGrid.GetMyGridInfo(tileCubeObject.gameObject);
                if (gridInfo == null) continue;

                // 初始创建时：检查第一行（gridZ == 1）到触发行（triggerRow）的所有物体
                // 注意：gridZ == 0 通常是最前面的位置，gridZ == 1 是第一行
                if (gridInfo.gridZ >= 0 && gridInfo.gridZ <= triggerRow)
                {
                    // 检查物体类型是否为目标类型
                    if (IsTargetObjectType(tileCubeObject.ObjectType))
                    {
                        // 检查是否已经存在GridConnon_Bullet_CollisionBody脚本
                        GridConnon_Bullet_CollisionBody existingCollisionBody = tileCubeObject.gameObject.GetComponent<GridConnon_Bullet_CollisionBody>();
                        if (existingCollisionBody == null)
                        {
                            // 添加GridConnon_Bullet_CollisionBody脚本
                            GridConnon_Bullet_CollisionBody collisionBody = tileCubeObject.gameObject.AddComponent<GridConnon_Bullet_CollisionBody>();
                            UpdateCollisionBodyConfig(collisionBody, tileCubeObject.ObjectType);

                            //                            //Debug.Log($"[Game_CNT_Game] 为第{gridInfo.gridZ + 1}行的 {tileCubeObject.ObjectType} 物体添加了碰撞体脚本");
                        }
                    }
                }
            }
        }
    }

    private void OnSpanwer_BlockLand(TileCube_Spawner spawner, TileCube_SingleBlock block)
    {
        GridConnon_Bullet_CollisionBody existingCollisionBody = block.gameObject.GetComponent<GridConnon_Bullet_CollisionBody>();
        if (existingCollisionBody != null)
            return;

        // 添加GridConnon_Bullet_CollisionBody脚本
        GridConnon_Bullet_CollisionBody collisionBody = block.gameObject.AddComponent<GridConnon_Bullet_CollisionBody>();
        UpdateCollisionBodyConfig(collisionBody, TileCube_Object.TileCube_ObjectType.SingleBlock);

    }

    /// <summary>
    /// 处理TileGrid推进完成事件
    /// 当物体到达第三行时，为指定类型添加GridConnon_Bullet_CollisionBody脚本
    /// </summary>
    /// <param name="stoppedTileObject">停下的格子对象</param>
    private void OnTileGridPushCompleted(GridTileObject stoppedTileObject)
    {
        if (stoppedTileObject?.gameObject == null) return;

        // 检查是否到达触发行（使用配置的triggerRow值）
        if (stoppedTileObject.gridZ != triggerRow) return;


        GridConnon_Bullet_CollisionBody existingCollisionBody = stoppedTileObject.gameObject.GetComponent<GridConnon_Bullet_CollisionBody>();
        if (existingCollisionBody != null)
            return;

        // 获取TileCube_Object组件
        TileCube_Object tileCubeObject = stoppedTileObject.gameObject.GetComponent<TileCube_Object>();
        if (tileCubeObject == null) return;

        // 检查物体类型是否为目标类型
        TileCube_Object.TileCube_ObjectType objectType = tileCubeObject.ObjectType;
        if (!IsTargetObjectType(objectType)) return;

        // 添加GridConnon_Bullet_CollisionBody脚本
        GridConnon_Bullet_CollisionBody collisionBody = stoppedTileObject.gameObject.AddComponent<GridConnon_Bullet_CollisionBody>();
        UpdateCollisionBodyConfig(collisionBody, objectType);

    }

    /// <summary>
    /// 检查是否为目标物体类型
    /// </summary>
    /// <param name="objectType">物体类型</param>
    /// <returns>是否为目标类型</returns>
    private bool IsTargetObjectType(TileCube_Object.TileCube_ObjectType objectType)
    {
        return objectType == TileCube_Object.TileCube_ObjectType.SingleBlock ||
               objectType == TileCube_Object.TileCube_ObjectType.LargeBlock ||
               objectType == TileCube_Object.TileCube_ObjectType.GoldPig ||
               objectType == TileCube_Object.TileCube_ObjectType.Barrier;
    }

    /// <summary>
    /// 根据物体类型更新碰撞体配置
    /// </summary>
    /// <param name="collisionBody">碰撞体组件</param>
    /// <param name="objectType">物体类型</param>
    private void UpdateCollisionBodyConfig(GridConnon_Bullet_CollisionBody collisionBody, TileCube_Object.TileCube_ObjectType objectType)
    {
        CollisionBodyConfig config = GetConfigForObjectType(objectType);

        if (config != null)
        {
            collisionBody.BoundsSize = config.boundsSize;
            collisionBody.BoundsOffset = config.boundsOffset;
            collisionBody.ShowDebugBounds = showDebugBounds;
        }
    }

    /// <summary>
    /// 根据物体类型获取对应的配置
    /// </summary>
    /// <param name="objectType">物体类型</param>
    /// <returns>对应的配置</returns>
    private CollisionBodyConfig GetConfigForObjectType(TileCube_Object.TileCube_ObjectType objectType)
    {
        switch (objectType)
        {
            case TileCube_Object.TileCube_ObjectType.SingleBlock:
                return singleBlockConfig;
            case TileCube_Object.TileCube_ObjectType.LargeBlock:
                return largeBlockConfig;
            case TileCube_Object.TileCube_ObjectType.GoldPig:
                return goldPigConfig;
            case TileCube_Object.TileCube_ObjectType.Barrier:
                return barrierConfig;
            default:
                return null;
        }
    }

    #endregion

    #region 主要事件处理

    private string MatachUI_MainEvent_Name;

    void On_MtachUI_MainEvent(string eventname, int level, int Level_Display)
    {
        MatachUI_MainEvent_Name = eventname;

        if (eventname == "4GamePlay")
        {
            // 确保链接系统的场景标志被正确重置
            GridConnon_LinkSystem.ResetSceneFlags();

            CreateGameLevel(level + 1);
        }
    }

    #endregion

    #region 道具使用功能

    private void On_Ask_UseProp(int index, System.Action action)
    {
        if (!Can_UseProp())
            return;

        if (index == 0)
        {
            if (GridConnon_Controller.Instance.Prop_PickOut_EnterState())
            {
                MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
                // 存储回调，等道具使用完成后再调用
                currentPropAction = action;

                ResetGameOverTimer();
            }
            else
            {
                MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
                //MXR_BRIGE.MessageTip_Show(Tool_LanguageManager.GetText("无法使用"));
            }
        }
        else if (index == 1)
        {
            if (GridConnon_Controller.Instance.Prop_Random())
            {
                MXR_BRIGE.MessageTip_Show(Tool_LanguageManager.GetText("打乱成功！"));

                action?.Invoke(); // 随机重排道具立即完成，可以直接调用
                ResetGameOverTimer(); // 使用prop_random成功后重置失败检测计时
            }
            else
            {
                MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
                //MXR_BRIGE.MessageTip_Show(Tool_LanguageManager.GetText("无法使用"));
            }
        }
        else if (index == 2)
        {
            if (TileCube_Controller.Instance.PropClearColor_EnterGridState())
            {
                MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
                // 存储回调，等道具使用完成后再调用
                currentPropAction = action;

                ResetGameOverTimer();
            }
            else
            {
                MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
            }
        }
        else if (index == 3)
        {
            // 箭矢道具：无需点击，使用面板配置的坐标直接发射
            // 进入前确保状态可用已由 Can_UseProp() 保证
            bool entered = TileCube_Controller.Instance.PropArrow_Enter();
            if (entered)
            {
                MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
                MXR_BRIGE.MessageTip_Show(Tool_LanguageManager.GetText("射击成功！"));

                action?.Invoke();
                ResetGameOverTimer(); // 使用prop_arrow成功后重置失败检测计时
            }
            else
            {
                // 进入失败（如未配置prefab等），仍反馈点击音
                MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
            }
        }
    }

    /// <summary>
    /// 道具拾取使用成功回调
    /// </summary>
    private void OnPropPickOutUsedSuccessfully()
    {
        // 道具使用成功，立即恢复游戏UI
        if (!IsGameOver)
            MatchUI_GamePanel.Instance.gameObject.SetActive(true);
        CNT_GridConnon_PropPickUp_Panel.ClosePanel();
        MoveCameraToOriginal();

        // 调用道具使用完成回调
        if (currentPropAction != null)
        {
            currentPropAction.Invoke();
            currentPropAction = null; // 清空回调
        }
    }

    /// <summary>
    /// 道具清除颜色使用成功回调
    /// </summary>
    private void OnPropClearColorUsedSuccessfully()
    {



        // 道具使用成功，立即恢复游戏UI
        if (!IsGameOver)
            MatchUI_GamePanel.Instance.gameObject.SetActive(true);
        CNT_TileCube_ClearColor_Panel.ClosePanel();

        // 调用道具使用完成回调
        if (currentPropAction != null)
        {
            currentPropAction.Invoke();
            currentPropAction = null; // 清空回调
        }
    }

    /// <summary>
    /// 处理道具清除目标颜色事件
    /// </summary>
    /// <param name="colorId">颜色ID</param>
    /// <param name="destroyedCount">销毁的方块数量</param>
    private void OnPropClearColor_ClearTargetColor(int colorId, int destroyedCount)
    {
        if (GridConnon_Controller.Instance == null)
        {
            //Debug.LogWarning("[Game_CNT_Game] GridConnon_Controller.Instance 为空，无法清除对应颜色的炮台");
            return;
        }

        // 创建清除要求列表
        List<GridConnon_Controller.CannonClearRequirement> clearRequirements = new List<GridConnon_Controller.CannonClearRequirement>
        {
            new GridConnon_Controller.CannonClearRequirement(colorId, destroyedCount)
        };

        // 调用GridConnon_Controller的清除方法
        GridConnon_Controller.Instance.ClearCannonsByColorValue(clearRequirements);

        ////Debug.Log($"[Game_CNT_Game] 清除颜色ID {colorId} 的炮台，数量: {destroyedCount}");
    }

    /// <summary>
    /// 处理进度清除事件
    /// </summary>
    /// <param name="currentValue">当前剩余值</param>
    /// <param name="totalValue">总目标值</param>
    private void OnProgressClear(int currentValue, int totalValue)
    {
        // 计算完成进度百分比
        float progress = 1f - ((float)currentValue / totalValue);

        MatchUI_GamePanel.Instance.UpdateLevelProgressBar(progress);

        // 重置游戏失败检测计时器（因为有进度变化）
        ResetGameOverTimer();

        //        Debug.LogError(progress);

        // 示例：当进度达到100%时
        if (currentValue <= 0)
        {
            if (!IsGameOver)
                GameWin();
        }
    }

    #endregion

    #region 游戏失败检测功能

    /// <summary>
    /// 检查指定的UI界面是否有任何一个正在显示
    /// </summary>
    /// <returns>如果有UI界面正在显示返回true，否则返回false</returns>
    private bool IsAnyUIDisplaying()
    {
        // 检查指定的UI界面是否有任何一个正在显示
        if (MatchUI_UnlockVacantPanel._instance != null && MatchUI_UnlockVacantPanel._instance.gameObject.activeInHierarchy)
            return true;

        if (MatchUI_NoAdsPanel._instance != null && MatchUI_NoAdsPanel._instance.gameObject.activeInHierarchy)
            return true;

        if (MatchUI_SettingPanel._instance != null && MatchUI_SettingPanel._instance.gameObject.activeInHierarchy)
            return true;

        if (MatchUI_ShopPanel._instance != null && MatchUI_ShopPanel._instance.gameObject.activeInHierarchy)
            return true;

        if (CNT_GridConnon_PropPickUp_Panel._instance != null && CNT_GridConnon_PropPickUp_Panel._instance.gameObject.activeInHierarchy)
            return true;

        if (CNT_TileCube_ClearColor_Panel._instance != null && CNT_TileCube_ClearColor_Panel._instance.gameObject.activeInHierarchy)
            return true;

        return false;
    }

    /// <summary>
    /// 检查游戏失败条件
    /// </summary>
    private void CheckGameOverCondition()
    {
        if (IsGameOver) return;

        // 检查当前帧是否有UI显示
        bool isUIDisplayingNow = IsAnyUIDisplaying();

        // 如果上一帧有UI显示，但当前帧没有，说明UI刚关闭，重置计时
        if (wasUIDisplayingLastFrame && !isUIDisplayingNow && isCheckingGameOver)
        {
            lastProgressCheckTime = Time.time; // 重置计时时间
            // Debug.Log("[Game_CNT_Game] UI界面关闭，重置失败检测计时");
        }

        // 更新UI显示状态记录
        wasUIDisplayingLastFrame = isUIDisplayingNow;

        if (isUIDisplayingNow)
        {
            return;
        }

        // 检查是否满格
        if (GridConnon_ShotGrid_Controller.Instance != null &&
            GridConnon_ShotGrid_Controller.Instance.AreAllShotGridsOccupied(GridConnon_ShotGrid_Type.DirectShot))
        {
            // 获取当前进度值
            int currentProgressValue = GetCurrentProgressValue();

            // 如果是第一次检测或进度有变化，重置计时
            if (lastProgressValue != currentProgressValue)
            {
                lastProgressValue = currentProgressValue;
                lastProgressCheckTime = Time.time;
                isCheckingGameOver = true;
                return;
            }

            // 如果之前不在检测状态（比如刚重新满格），开始计时
            if (!isCheckingGameOver)
            {
                lastProgressCheckTime = Time.time;
                isCheckingGameOver = true;
                return;
            }

            // 如果正在检测且超过设定时间无进度变化，游戏失败
            if (isCheckingGameOver && Time.time - lastProgressCheckTime >= GameOverWaitTime)
            {
                GameLose();
            }
        }
        else
        {
            // 未满格时重置检测状态
            isCheckingGameOver = false;
        }
    }

    /// <summary>
    /// 获取当前进度值
    /// </summary>
    /// <returns>当前进度值</returns>
    private int GetCurrentProgressValue()
    {
        // 从TileCube_Controller获取当前进度值
        if (TileCube_Controller.Instance != null)
        {
            var (current, total) = TileCube_Controller.Instance.GetProgressInfo();
            return current;
        }
        return 0;
    }

    /// <summary>
    /// 重置游戏失败检测计时器（当有进度变化时调用）
    /// </summary>
    public void ResetGameOverTimer()
    {
        lastProgressCheckTime = Time.time;
        isCheckingGameOver = false;
    }

    #endregion

    #region 奖励射击格解锁功能

    /// <summary>
    /// 处理奖励射击格点击询问事件
    /// </summary>
    /// <param name="unlockAction">解锁回调</param>
    private void OnRewardShotGrid_AskClick(System.Action unlockAction)
    {
        if (!Can_UseProp())
        {
            MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
            MXR_BRIGE.MessageTip_Show(Tool_LanguageManager.GetText("请稍候"));
            return;
        }

        MatchUI_Controller.On_PlaySound?.Invoke("MatchUI_按钮点击");
        MatchUI_UnlockVacantPanel.ShowPanel(0, unlockAction);
    }

    #endregion

    #region 输入检查接口实现

    public override bool CanProcessInput()
    {
        return true;
    }

    public bool Can_UseProp()
    {
        if (IsGameOver)
            return false;

        if (TileCube_Controller.Instance.currentState != TileCube_Controller.ControllerState.Normal || GridConnon_Controller.Instance.currentControllerState != GridConnon_Controller_State.Normal)
            return false;



        return true;
    }

    public bool Can_Connon_Click()
    {
        if (IsGameOver)
            return false;
        if (!Tool_InputManager.CanProcessInput() || Tool_IsPointerOverUI.Check())
            return false;

        if (TileCube_Controller.Instance.currentState != TileCube_Controller.ControllerState.Normal)
            return false;

        return true;
    }

    public bool Can_Prop_PickOut_Click()
    {
        if (IsGameOver)
            return false;
        if (!Tool_InputManager.CanProcessInput() || Tool_IsPointerOverUI.Check())
            return false;

        if (TileCube_Controller.Instance.currentState != TileCube_Controller.ControllerState.Normal)
            return false;

        return true;
    }

    public bool Can_Prop_ClearColor_Click()
    {
        if (IsGameOver)
            return false;
        if (!Tool_InputManager.CanProcessInput() || Tool_IsPointerOverUI.Check())
            return false;

        if (GridConnon_Controller.Instance.currentControllerState != GridConnon_Controller_State.Normal)
            return false;

        return true;
    }

    public bool Can_UnlockGrid_Click()
    {
        if (IsGameOver)
            return false;
        if (!Tool_InputManager.CanProcessInput() || Tool_IsPointerOverUI.Check())
            return false;
        if (TileCube_Controller.Instance.currentState != TileCube_Controller.ControllerState.Normal)
            return false;
        if (GridConnon_Controller.Instance.currentControllerState != GridConnon_Controller_State.Normal)
            return false;
        return true;
    }

    #endregion

    #region 状态变化事件处理

    public void On_Enter_PropClearColor_State()
    {
        MatchUI_GamePanel.Instance.gameObject.SetActive(false);
        CNT_TileCube_ClearColor_Panel.ShowPanel();
    }

    public void On_Exit_PropClearColor_State()
    {
        // 只有在UI还显示时才处理（避免与道具使用成功的处理重复）
        if (!MatchUI_GamePanel.Instance.gameObject.activeInHierarchy)
        {
            if (!IsGameOver)
                MatchUI_GamePanel.Instance.gameObject.SetActive(true);
            CNT_TileCube_ClearColor_Panel.ClosePanel();
        }

        // 清空未使用的回调（手动关闭时取消道具使用）
        currentPropAction = null;
    }

    public void OnGridConnon_ControllerStateChanged(GridConnon_Controller_State oldstate, GridConnon_Controller_State newstate)
    {
        // 当退出道具拾取状态时（手动关闭），恢复UI和摄像机
        if (oldstate == GridConnon_Controller_State.Prop_PickUp)
        {
            // 只有在UI还显示时才处理（避免与道具使用成功的处理重复）
            if (!MatchUI_GamePanel.Instance.gameObject.activeInHierarchy)
            {
                if (!IsGameOver)
                    MatchUI_GamePanel.Instance.gameObject.SetActive(true);
                CNT_GridConnon_PropPickUp_Panel.ClosePanel();
                MoveCameraToOriginal();
            }

            return;
        }

        // 当进入道具拾取状态时，摄像机添加Z偏移
        if (newstate == GridConnon_Controller_State.Prop_PickUp)
        {
            MatchUI_GamePanel.Instance.gameObject.SetActive(false);
            CNT_GridConnon_PropPickUp_Panel.ShowPanel();
            MoveCameraToOffset();
            return;
        }
    }

    #endregion

    #region 胜利失败

    /// <summary>
    /// 处理重新游戏成功事件
    /// </summary>
    private void OnReGameSuccess()
    {
        // 重置游戏结束状态
        IsGameOver = false;

        MatchUI_GamePanel.Instance.gameObject.SetActive(true);

        // 调用射击格控制器分配炮台到复活解锁格子
        if (GridConnon_ShotGrid_Controller.Instance != null)
        {
            GridConnon_ShotGrid_Controller.Instance.AssignCannonsToReviveUnlockGrids();
        }
    }

    public void GameWin()
    {

        if (IsGameOver)
            return;
        MXR_BRIGE.Action_OnEnterEndScene();
        MXR_BRIGE.Action_OnGameOver(()=>{});
        IsGameOver = true;
        StartCoroutine("DelayShowGameWinUI");
    }

    IEnumerator DelayShowGameWinUI()
    {
        yield return new WaitForSeconds(0.25f);
        MatchUI_Controller.GameWin(CNT_GoldPig_FusionSystem.GetCurrentSessionCoins());

    }

    public void GameLose()
    {
        if (IsGameOver)
            return;
        MXR_BRIGE.Action_OnEnterEndScene();
        MXR_BRIGE.Action_OnGameOver(()=>{});
        IsGameOver = true;
        MatchUI_GamePanel.Instance.gameObject.SetActive(false);
        StartCoroutine("DelayShowGameLostUI");
    }

    IEnumerator DelayShowGameLostUI()
    {

        yield return new WaitForSeconds(0f);

        if (GridConnon_ShotGrid_Controller.Instance != null &&
            GridConnon_ShotGrid_Controller.Instance.AreAllShotGridsReservedOrOcc(GridConnon_ShotGrid_Type.ReviveUnlock))
            MatchUI_Controller.GameLose(false);
        else
            MatchUI_Controller.GameLose(true);


    }

    #endregion
}