using UnityEngine;

/// <summary>
/// Shooting状态 - 射击状态
/// 进入：关闭描边，关闭膜层，停止呼吸，数字透明度1
/// 持续：留空等待后续实现
/// </summary>
public class GridConnon_CannonState_Shooting : GridConnon_CannonStateBase
{
    public override GridConnon_Cannon_State GetStateType()
    {
        return GridConnon_Cannon_State.Shooting;
    }

    public override void OnEnter()
    {
        stateManager.cannon.tool_JRJELLY_Simple.enabled = false;

        // 1. 设置描边效果 - 使用淡化颜色和较小宽度
        Color fadedOutlineColor = stateManager.GetFadedCannonColor(cannon.shooting_outlineFadePercent);
        stateManager.SetOutlineEffect(
            color: fadedOutlineColor,
            width: cannon.shooting_outlineWidth
        );

        // 2. 关闭颜色膜层效果（设置透明度为0）
        stateManager.SetColorOverlayEffect(
            color: Color.white,
            alpha: 0f
        );

        // 3. 停止呼吸效果（立即停止，避免果冻效果残留）
        stateManager.StopBreathe(true);

        // 4. 设置数字透明度为1（完全不透明）
        stateManager.SetTextAlpha(1f);

        // 5. 性能优化：通知GridConnon_Controller炮台进入Shooting状态
        GridConnon_Controller.Instance?.OnCannonEnterShooting(cannon);
    }

    public override void OnExit()
    {
        // 性能优化：通知GridConnon_Controller炮台退出Shooting状态
        GridConnon_Controller.Instance?.OnCannonExitShooting(cannon);

        //Debug.Log($"[Shooting状态] 炮台 {cannon.ObjectId} 退出Shooting状态");
    }

    public override void OnUpdate()
    {
        // GridConnon_Controller.Act_On_Cannon_Shooting_Update?.Invoke(stateManager.cannon);

        stateManager.cannon.UpdateRotationSystem();
        stateManager.cannon.UpdateRecoilSystem();

        // 检查击打值是否为0，如果是则切换到Destroying状态
        if (cannon.IsHitCountZero())
        {
            // 使用智能切换方法，自动根据当前状态（Shooting）设置Zoff
            cannon.ToDestroy(GridConnon_Cannon_State.Shooting);
        }
    }

    public override void OnClick()
    {

    }
}