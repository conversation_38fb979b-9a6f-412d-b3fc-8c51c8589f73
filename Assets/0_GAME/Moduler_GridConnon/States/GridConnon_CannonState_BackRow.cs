using UnityEngine;

/// <summary>
/// BackRow状态 - 后排状态
/// 进入：打开描边，打开膜层，呼吸，颜色膜层颜色为当前cannon的color的淡化，数字透明化，描边颜色和宽度
/// 持续：监测点击，被点击：一个旋转表现，旋转Y轴，先旋转到左边，再旋转到右边，最后回到中间
/// </summary>
public class GridConnon_CannonState_BackRow : GridConnon_CannonStateBase
{

    public override GridConnon_Cannon_State GetStateType()
    {
        return GridConnon_Cannon_State.BackRow;
    }


    public override void OnEnter()
    {



        // 1. 设置描边效果 - 使用淡化颜色
        Color fadedOutlineColor = stateManager.GetFadedCannonColor(cannon.backRow_outlineFadePercent);
        stateManager.SetOutlineEffect(
            color: fadedOutlineColor,
            width: cannon.backRow_outlineWidth
        );

        // 2. 设置颜色膜层效果
        Color fadedOverlayColor = stateManager.GetFadedCannonColor(cannon.backRow_overlayFadePercent);
        stateManager.SetColorOverlayEffect(
            color: fadedOverlayColor,
            alpha: cannon.backRow_overlayAlpha
        );

        // 3. 设置数字透明化
        stateManager.SetTextAlpha(cannon.backRow_textAlpha);

        // 4. 开始呼吸效果（带随机延迟）
        stateManager.TriggerBreatheWithRandomDelay();

        ////Debug.Log($"[BackRow状态] 炮台 {cannon.ObjectId} 进入BackRow状态");
        ////Debug.Log($"  - 描边: 颜色={fadedOutlineColor}, 宽度={cannon.backRow_outlineWidth}, 淡化/加深比例={cannon.backRow_outlineFadePercent}");
        ////Debug.Log($"  - 膜层: 淡化/加深比例={cannon.backRow_overlayFadePercent}, 颜色={fadedOverlayColor}, 透明度={cannon.backRow_overlayAlpha}");
        ////Debug.Log($"  - 数字透明度: {cannon.backRow_textAlpha}");
    }

    public override void OnExit()
    {

    }

    public override void OnUpdate()
    {



        // BackRow状态下的持续逻辑
        // 监测GridConnon_Controller Z+1 的自身前方网格是否占用
        int frontGridX = cannon.GridX;
        int frontGridZ = cannon.GridZ + 1; // Z+1是前方网格

        // 检查前方网格是否没有被占用
        if (!GridConnon_Controller.Instance.IsGridReserveOrOccupied(frontGridX, frontGridZ))
        {
            var beginEang = stateManager.cannon.transform.eulerAngles;
            // 使用封装的方法进行网格间移动
            stateManager.ControllerGridToControllerGrid(frontGridX, frontGridZ, () =>
            {
                var grid = GridConnon_Controller.Instance.GetGridAt(frontGridX, frontGridZ);

                stateManager.OnReachedControllerGridToControllerGrid(grid, frontGridX, frontGridZ);

                stateManager.cannon.transform.eulerAngles = beginEang;
            });
        }
    }



    public override void OnClick()
    {
        if (!GridConnon_InputManager.Can_Connon_Click())
            return;
        // 被点击时执行旋转动画
        //////Debug.Log($"[BackRow状态] 炮台 {cannon.ObjectId} 被点击，执行旋转动画");
        stateManager.PlayRotationAnimation();


    }
}