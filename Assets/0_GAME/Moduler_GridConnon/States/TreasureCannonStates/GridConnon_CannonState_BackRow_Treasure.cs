using UnityEngine;

/// <summary>
/// 宝箱炮BackRow状态 - 继承基础BackRow状态，去除颜色膜层设置
/// 区别：没有颜色膜层，其他都一致
/// </summary>
public class GridConnon_CannonState_BackRow_Treasure : GridConnon_CannonState_BackRow
{
    public override void OnEnter()
    {
        stateManager.TriggerBreatheWithRandomDelay();
        stateManager.SetTextAlpha(0);
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[TreasureCannon BackRow状态] 炮台 {cannon.ObjectId} 退出宝箱炮BackRow状态");
        // TODO: 宝箱炮特殊的BackRow退出逻辑
    }

    public override void OnUpdate()
    {
        base.OnUpdate();
        // TODO: 宝箱炮特殊的BackRow更新逻辑
    }

    public override void OnClick()
    {
        base.OnClick();
        // TODO: 宝箱炮特殊的BackRow点击逻辑
    }
}