using UnityEngine;

/// <summary>
/// 宝箱炮Moving状态 - 继承基础Moving状态
/// 留空等待后续实现
/// </summary>
public class GridConnon_CannonState_Moving_Treasure : GridConnon_CannonState_Moving
{
    public override void OnEnter()
    {
        base.OnEnter();
        //Debug.Log($"[TreasureCannon Moving状态] 炮台 {cannon.ObjectId} 进入宝箱炮Moving状态");
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[TreasureCannon Moving状态] 炮台 {cannon.ObjectId} 退出宝箱炮Moving状态");
    }

    public override void OnUpdate()
    {
        base.OnUpdate();
        // TODO: 宝箱炮特殊的Moving更新逻辑
    }

    public override void OnClick()
    {
        base.OnClick();
        // TODO: 宝箱炮特殊的Moving点击逻辑
    }
}