using UnityEngine;

/// <summary>
/// 宝箱炮FirstRow状态 - 继承基础FirstRow状态，禁用点击并添加持续监测逻辑
/// 区别：在FirstRow不可点击，在FirstRow持续监测待实现
/// </summary>
public class GridConnon_CannonState_FirstRow_Treasure : GridConnon_CannonState_FirstRow
{

    public override void OnEnter()
    {
        stateManager.TriggerBreatheWithRandomDelay();
        stateManager.SetTextAlpha(0);
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[TreasureCannon FirstRow状态] 炮台 {cannon.ObjectId} 退出宝箱炮FirstRow状态");
        // TODO: 宝箱炮特殊的FirstRow退出逻辑
    }

    public override void OnUpdate()
    {
        base.OnUpdate();

        // 检查是否为宝箱炮且未解锁
        if (cannon.ObjectType == GridConnon_ObjectType.TreasureCannon &&
            !cannon.TreasureCannon_IsUnlocked)
        {
            // 持续发送寻找钥匙事件
            GridConnon_Controller.Act_On_TreasureCannonFindKey?.Invoke(cannon);
        }
    }

    public override void OnClick()
    {
        if (!GridConnon_InputManager.Can_Connon_Click())
            return;
        stateManager.PlayRotationAnimation();
    }
}