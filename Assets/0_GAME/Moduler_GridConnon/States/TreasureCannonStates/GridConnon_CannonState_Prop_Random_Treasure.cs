using UnityEngine;

/// <summary>
/// 宝箱炮Prop_Random状态 - 继承基础Prop_Random状态
/// 留空等待后续实现
/// </summary>
public class GridConnon_CannonState_Prop_Random_Treasure : GridConnon_CannonState_Prop_Random
{


    public override void OnEnter()
    {
        base.OnEnter();
        //Debug.Log($"[TreasureCannon Prop_Random状态] 炮台 {cannon.ObjectId} 进入宝箱炮Prop_Random状态");
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[TreasureCannon Prop_Random状态] 炮台 {cannon.ObjectId} 退出宝箱炮Prop_Random状态");
    }

    public override void OnUpdate()
    {
        base.OnUpdate();
        // TODO: 宝箱炮特殊的Prop_Random更新逻辑
    }

    public override void OnClick()
    {
        base.OnClick();
        // TODO: 宝箱炮特殊的Prop_Random点击逻辑
    }
}