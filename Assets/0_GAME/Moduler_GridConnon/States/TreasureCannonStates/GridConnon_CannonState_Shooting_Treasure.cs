using UnityEngine;

/// <summary>
/// 宝箱炮Shooting状态 - 继承基础Shooting状态
/// 留空等待后续实现
/// </summary>
public class GridConnon_CannonState_Shooting_Treasure : GridConnon_CannonState_Shooting
{
    public override void OnEnter()
    {
        base.OnEnter();
        //Debug.Log($"[TreasureCannon Shooting状态] 炮台 {cannon.ObjectId} 进入宝箱炮Shooting状态");
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[TreasureCannon Shooting状态] 炮台 {cannon.ObjectId} 退出宝箱炮Shooting状态");
    }

    public override void OnUpdate()
    {
        base.OnUpdate();
        // TODO: 宝箱炮特殊的Shooting更新逻辑
    }

    public override void OnClick()
    {
        base.OnClick();
        // TODO: 宝箱炮特殊的Shooting点击逻辑
    }
}