using UnityEngine;
using System.Collections;

/// <summary>
/// 宝箱炮Destroying状态 - 继承基础Destroying状态
/// 重写OnEnter方法，添加解锁动画和特效
/// </summary>
public class GridConnon_CannonState_Destroying_Treasure : GridConnon_CannonState_Destroying
{
    public override void OnEnter()
    {
        //Debug.Log($"[TreasureCannon Destroying状态] 炮台 {cannon.ObjectId} 进入宝箱炮Destroying状态");

        stateManager.StopBreathe(true);

        cannon.tool_JRJELLY_Simple.enabled = false;

        ExecuteTreasureUnlockSequence();

    }



    /// <summary>
    /// 执行宝箱解锁序列
    /// 1. 先让TreasureCannon_UnlockAssemble位移到目标位置
    /// 2. 然后让TreasureCannon_UnlockQuad缩放到目标大小
    /// 3. 最后调用GoToDes
    /// </summary>
    private void ExecuteTreasureUnlockSequence()
    {
        // 启动解锁动画协程
        cannon.StartCoroutine(UnlockAnimationSequence());
    }



    /// <summary>
    /// 解锁动画序列协程
    /// </summary>
    /// <returns></returns>
    private IEnumerator UnlockAnimationSequence()
    {
        // 第一步：TreasureCannon_UnlockAssemble位移动画
        if (cannon.TreasureCannon_UnlockAssemble != null)
        {
            yield return cannon.StartCoroutine(AnimateAssemblePosition());
        }

        stateManager.PlayCompressionAnimation(false, cannon.destroying_animDuration, cannon.destroying_scaleSize);
        //stateManager.PlayWhiteFlashEffect(cannon.destroying_animDuration, 0.8f);
         // 第二步：TreasureCannon_UnlockQuad缩放动画
        if (cannon.TreasureCannon_UnlockQuad != null)
        {

          yield return  cannon.StartCoroutine(AnimateQuadScale());
        }
        
    
    GoToDes();


    }

    /// <summary>
    /// 装配组件位移动画
    /// </summary>
    /// <returns></returns>
    private IEnumerator AnimateAssemblePosition()
    {
        GameObject assembleObj = cannon.TreasureCannon_UnlockAssemble;
        Vector3 startPos = assembleObj.transform.localPosition;
        Vector3 targetPos = cannon.TreasureCannon_UnlockAssemble_ToLocalPos;
        float duration = cannon.TreasureCannon_UnlockAssemble_Duration;

        float elapsedTime = 0f;
        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float t = elapsedTime / duration;

            // 使用SmoothStep实现平滑动画
            float smoothT = Mathf.SmoothStep(0f, 1f, t);
            assembleObj.transform.localPosition = Vector3.Lerp(startPos, targetPos, smoothT);

            yield return null;
        }

        // 确保最终位置准确
        assembleObj.transform.localPosition = targetPos;
    }

    /// <summary>
    /// 四边形缩放动画（同时包含透明度动画）
    /// </summary>
    /// <returns></returns>
    private IEnumerator AnimateQuadScale()
    {

        SpriteRenderer quadObj = cannon.TreasureCannon_UnlockQuad;
        quadObj.gameObject.SetActive(true);

        // 记录初始状态
        Vector3 startScale = quadObj.transform.localScale;
        Vector3 targetScale = Vector3.one * cannon.TreasureCannon_UnlockQuad_ScaleTo;
        Color startColor = quadObj.color;
        float startAlpha = startColor.a; // 记录一开始的透明度
        float duration = cannon.TreasureCannon_UnlockQuad_Duration;

        float elapsedTime = 0f;
        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float t = elapsedTime / duration;

            // 使用SmoothStep实现平滑动画
            float smoothT = Mathf.SmoothStep(0f, 1f, t);

            // 缩放动画
            quadObj.transform.localScale = Vector3.Lerp(startScale, targetScale, smoothT);

            // 透明度动画：从初始透明度渐变到0
            Color currentColor = quadObj.color;
            currentColor.a = Mathf.Lerp(startAlpha, 0f, smoothT);
            quadObj.color = currentColor;

            yield return null;
        }

        // 确保最终状态准确
        quadObj.transform.localScale = targetScale;
        Color finalColor = quadObj.color;
        finalColor.a = 0f;
        quadObj.color = finalColor;
    }

    /// <summary>
    /// 动画完成回调
    /// </summary>
    private void GoToDes()
    {
        cannon.ExecuteDestroyingGoToDes(Zoff);
    }


}