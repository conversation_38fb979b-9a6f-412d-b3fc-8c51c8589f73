using UnityEngine;

/// <summary>
/// 宝箱炮ShootingSynthesis状态 - 继承基础ShootingSynthesis状态
/// 留空等待后续实现
/// </summary>
public class GridConnon_CannonState_ShootingSynthesis_Treasure : GridConnon_CannonState_ShootingSynthesis
{
    public override void OnEnter()
    {
        base.OnEnter();
        //Debug.Log($"[TreasureCannon ShootingSynthesis状态] 炮台 {cannon.ObjectId} 进入宝箱炮ShootingSynthesis状态");
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[TreasureCannon ShootingSynthesis状态] 炮台 {cannon.ObjectId} 退出宝箱炮ShootingSynthesis状态");
    }

    public override void OnUpdate()
    {
        base.OnUpdate();
        // TODO: 宝箱炮特殊的ShootingSynthesis更新逻辑
    }

    public override void OnClick()
    {
        base.OnClick();
        // TODO: 宝箱炮特殊的ShootingSynthesis点击逻辑
    }
}