using UnityEngine;

/// <summary>
/// 炮台状态基类 - 所有状态的抽象基类
/// </summary>
public abstract class GridConnon_CannonStateBase
{
    protected GridConnon_Cannon cannon;
    protected GridConnon_CannonStateManager stateManager;

    /// <summary>
    /// 初始化状态
    /// </summary>
    public virtual void Initialize(GridConnon_Cannon cannon, GridConnon_CannonStateManager stateManager)
    {
        this.cannon = cannon;
        this.stateManager = stateManager;
    }

    /// <summary>
    /// 进入状态时调用
    /// </summary>
    public abstract void OnEnter();

    /// <summary>
    /// 退出状态时调用
    /// </summary>
    public abstract void OnExit();

    /// <summary>
    /// 状态持续期间每帧调用
    /// </summary>
    public virtual void OnUpdate() { }

    /// <summary>
    /// 处理点击事件
    /// </summary>
    public virtual void OnClick() { }

    /// <summary>
    /// 获取当前状态类型
    /// </summary>
    public abstract GridConnon_Cannon_State GetStateType();
}