using System.Collections.Generic;

/// <summary>
/// 三角炮状态工厂 - 创建包含特殊Shooting状态的状态集合
/// </summary>
public class GridConnon_TriangleCannonStateFactory : IGridConnon_StateFactory
{
    public Dictionary<GridConnon_Cannon_State, GridConnon_CannonStateBase> CreateStates(
        GridConnon_Cannon cannon, GridConnon_CannonStateManager manager)
    {
        return new Dictionary<GridConnon_Cannon_State, GridConnon_CannonStateBase>
        {
            // 三角炮专用状态
            { GridConnon_Cannon_State.BackRow, new GridConnon_CannonState_BackRow() },
            { GridConnon_Cannon_State.FirstRow, new GridConnon_CannonState_FirstRow() },
            { GridConnon_Cannon_State.Shooting, new GridConnon_CannonState_Shooting() },
            { GridConnon_Cannon_State.ShootingSynthesis, new GridConnon_CannonState_ShootingSynthesis() },
            { GridConnon_Cannon_State.Moving, new GridConnon_CannonState_Moving() },
            { GridConnon_Cannon_State.Destroying, new GridConnon_CannonState_Destroying() },
            { GridConnon_Cannon_State.Prop_PickOut, new GridConnon_CannonState_Prop_PickOut() },
            { GridConnon_Cannon_State.Prop_Random, new GridConnon_CannonState_Prop_Random() },
            { GridConnon_Cannon_State.ShootingSynthesisFinish, new GridConnon_CannonState_ShootingSynthesisFinish() }
        };
    }
}