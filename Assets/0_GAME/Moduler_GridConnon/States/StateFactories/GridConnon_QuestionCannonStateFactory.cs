using System.Collections.Generic;

/// <summary>
/// 问号炮状态工厂 - 创建包含特殊BackRow和FirstRow状态的状态集合
/// </summary>
public class GridConnon_QuestionCannonStateFactory : IGridConnon_StateFactory
{
    public Dictionary<GridConnon_Cannon_State, GridConnon_CannonStateBase> CreateStates(
        GridConnon_Cannon cannon, GridConnon_CannonStateManager manager)
    {
        return new Dictionary<GridConnon_Cannon_State, GridConnon_CannonStateBase>
        {
            // 问号炮专用状态
            { GridConnon_Cannon_State.BackRow, new GridConnon_CannonState_BackRow_Question() },
            { GridConnon_Cannon_State.FirstRow, new GridConnon_CannonState_FirstRow_Question() },
            { GridConnon_Cannon_State.Shooting, new GridConnon_CannonState_Shooting_Question() },
            { GridConnon_Cannon_State.ShootingSynthesis, new GridConnon_CannonState_ShootingSynthesis_Question() },
            { GridConnon_Cannon_State.Moving, new GridConnon_CannonState_Moving_Question() },
            { GridConnon_Cannon_State.Destroying, new GridConnon_CannonState_Destroying_Question() },
            { GridConnon_Cannon_State.Prop_PickOut, new GridConnon_CannonState_Prop_PickOut_Question() },
            { GridConnon_Cannon_State.Prop_Random, new GridConnon_CannonState_Prop_Random_Question() },
            { GridConnon_Cannon_State.ShootingSynthesisFinish, new GridConnon_CannonState_ShootingSynthesisFinish_Question() }
        };
    }
}