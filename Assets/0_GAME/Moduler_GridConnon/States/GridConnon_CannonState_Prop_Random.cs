using UnityEngine;
using System.Collections.Generic;
using System;

/// <summary>
/// Prop_Random状态 - 道具随机重排状态
/// 进入：在指定Z行内寻找空位，然后移动过去
/// 持续：留空
/// </summary>
public class GridConnon_CannonState_Prop_Random : GridConnon_CannonStateBase
{
    private int targetMoveGridX = 0; // 目标Z行数
    private int targetMoveGridZ = 0; // 目标Z行数

    private Action reachAct = null;

    public override GridConnon_Cannon_State GetStateType()
    {
        return GridConnon_Cannon_State.Prop_Random;
    }

    /// <summary>
    /// 设置目标行数
    /// </summary>
    /// <param name="row">目标Z行数（0为第一行，-1为第二行，以此类推）</param>
    public void SetTargetAndReachAct(int gx, int gz, Action reachAct)
    {
        targetMoveGridX = gx;
        targetMoveGridZ = gz;
        this.reachAct = reachAct;
    }



    //public override void OnEnter() { //Debug.LogError("enter"); }

    public override void OnEnter()
    {
        // 设置描边效果：纯白色，使用参数宽度
        stateManager.SetOutlineEffect(
            color: cannon.firstRow_outlineColor,
            width: cannon.firstRow_outlineWidth
        );

        // 设置膜层透明度为0（完全透明）
        stateManager.SetColorOverlayEffect(
            color: Color.white,
            alpha: 0f
        );

        stateManager.SetTextAlpha(1);

        //Debug.Log($"[Prop_Random状态] 炮台 {cannon.ObjectId} 开始移动到目标位置 ({targetMoveGridX}, {targetMoveGridZ})");

        // 尝试移动到目标位置
        stateManager.ControllerGridToControllerGrid(targetMoveGridX, targetMoveGridZ, reachAct);

    }

    public override void OnExit()
    {
        //Debug.Log($"[Prop_Random状态] 炮台 {cannon.ObjectId} 退出Prop_Random状态");
    }

    public override void OnUpdate()
    {
        // 持续期间无需特殊处理
    }

    public override void OnClick()
    {
        // Prop_Random状态下不响应点击
        //Debug.Log($"[Prop_Random状态] 炮台 {cannon.ObjectId} 在Prop_Random状态下不响应点击");
    }
}