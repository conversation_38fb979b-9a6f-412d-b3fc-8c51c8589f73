using UnityEngine;

/// <summary>
/// Moving状态 - 移动状态
/// 进入：停止呼吸，其他留空
/// 持续：留空
/// </summary>
public class GridConnon_CannonState_Moving : GridConnon_CannonStateBase
{
    public override GridConnon_Cannon_State GetStateType()
    {
        return GridConnon_Cannon_State.Moving;
    }

    public override void OnEnter()
    {
        if (cannon.GridZ > -GridConnon_Controller.Instance.optimizeZ)
            stateManager.cannon.foot_MeshObject.gameObject.SetActive(true);
    }

    public override void OnExit()
    {
        if (cannon.GridZ > -GridConnon_Controller.Instance.optimizeZ)
            stateManager.cannon.foot_MeshObject.gameObject.SetActive(false);
    }

    public override void OnUpdate()
    {

    }

    public override void OnClick()
    {

    }
}