using System.Linq;
using UnityEngine;

/// <summary>
/// Destroying状态 - 销毁状态
/// 进入：1.颜色膜层宽度设定为0，如果有在执行呼吸效果则停掉（仅GridConnon_Controller格子），判断在GridConnon_ShotGrid_Controller还是GridConnon_Controller的格子上，清除占用
///      2.执行一个动画：放大connon的connon_MeshObject到参数大小，然后回缩到原大小
///      3.动画执行完之后，判断是处于GridConnon_Controller的X网格的最中间格的左边还是右边（世界坐标），如果是左边向左边相同Z的-目标销毁X移动过去，如果是右边向右边相同Z的目标销毁X移动过去，中间随机
///      4.移动到目标位之后，销毁
/// 持续：留空
/// </summary>
public class GridConnon_CannonState_Destroying : GridConnon_CannonStateBase
{
    /// <summary>
    /// Z偏移数组（由外部设置，用于销毁时的Z偏移）
    /// </summary>
    public float[] Zoff;

    /// <summary>
    /// 是否暂停销毁进程（用于链接组同步销毁）
    /// </summary>
    private bool isPaused = false;

    /// <summary>
    /// 是否已完成销毁动画
    /// </summary>
    private bool isAnimationComplete = false;

    public override GridConnon_Cannon_State GetStateType()
    {
        return GridConnon_Cannon_State.Destroying;
    }

    public override void OnEnter()
    {
        //Debug.Log($"[Destroying状态] 炮台 {cannon.ObjectId} 进入Destroying状态");

        // 重置状态
        isPaused = false;
        isAnimationComplete = false;

        // 1. 颜色膜层宽度设定为0，停止呼吸效果，清除占用
        HandleOverlayAndBreathe();

        // 2. 执行放大回缩动画
        ExecuteDestroyingAnimation();
    }

    public override void OnExit()
    {
        //Debug.Log($"[Destroying状态] 炮台 {cannon.ObjectId} 退出Destroying状态");
    }

    public override void OnUpdate()
    {
        stateManager.cannon.UpdateRecoilSystem();
    }

    public override void OnClick()
    {
        // Destroying状态下一般不响应点击
        //Debug.Log($"[Destroying状态] 炮台 {cannon.ObjectId} 在销毁状态下被点击");
    }

    /// <summary>
    /// 暂停销毁进程（用于链接组同步销毁）
    /// </summary>
    public void PauseDestruction()
    {
        isPaused = true;
        //Debug.Log($"[Destroying状态] 炮台 {cannon.ObjectId} 销毁进程已暂停");
    }

    /// <summary>
    /// 恢复销毁进程（用于链接组同步销毁）
    /// </summary>
    public void ResumeDestruction()
    {
        if (isPaused)
        {
            isPaused = false;
            //Debug.Log($"[Destroying状态] 炮台 {cannon.ObjectId} 销毁进程已恢复");

            // 如果动画已完成且被暂停，立即继续销毁流程
            if (isAnimationComplete)
            {
                ContinueAfterAnimation();
            }
        }
    }

    /// <summary>
    /// 检查是否处于暂停状态
    /// </summary>
    /// <returns>是否暂停</returns>
    public bool IsPaused()
    {
        return isPaused;
    }

    /// <summary>
    /// 检查是否已完成动画并在等待GoToDes执行
    /// </summary>
    /// <returns>是否已完成动画且在等待状态</returns>
    public bool IsAnimationCompleteAndWaiting()
    {
        return isAnimationComplete && isPaused;
    }

    /// <summary>
    /// 处理膜层和呼吸效果
    /// </summary>
    private void HandleOverlayAndBreathe()
    {
        // 设置膜层宽度为0来隐藏膜层
        stateManager.SetColorOverlayEffect(Color.clear, 0f, 0f);

        // 判断是否在GridConnon_Controller的格子上，如果是则停止呼吸
        if (stateManager.GetCurrentGridType() == GridConnon_CannonStateManager.GridType.GridConnon)
        {
            // 马上停止呼吸效果，不要过渡
            stateManager.StopBreathe(true);
        }


    }



    /// <summary>
    /// 执行销毁动画
    /// </summary>
    private void ExecuteDestroyingAnimation()
    {
        // 使用炮台配置的销毁动画参数
        float animDuration = cannon.destroying_animDuration;
        float scaleSize = cannon.destroying_scaleSize;
        float whiteFlashDuration = cannon.destroying_whiteFlashDuration;

        // 同时执行白色闪烁效果和放大回缩动画，使用不同的时间参数
        stateManager.PlayWhiteFlashEffect(whiteFlashDuration, 0.8f);
        stateManager.PlayCompressionAnimation(false, animDuration, scaleSize, GoToDes);

        //Debug.Log($"[Destroying状态] 炮台 {cannon.ObjectId} 开始执行销毁动画，缩放时间:{animDuration}秒，放大比例:{scaleSize}，白色闪烁时间:{whiteFlashDuration}秒");
    }

    /// <summary>
    /// 动画完成回调
    /// </summary>
    private void GoToDes()
    {
        isAnimationComplete = true;

        // 通知链接系统检查同步状态
        stateManager.NotifyAnimationComplete();

        // 检查是否被暂停，如果暂停则等待恢复
        if (isPaused)
        {
            //Debug.Log($"[Destroying状态] 炮台 {cannon.ObjectId} 动画完成但被暂停，等待恢复");
            return;
        }

        ContinueAfterAnimation();
    }

    /// <summary>
    /// 动画完成后继续销毁流程
    /// </summary>
    private void ContinueAfterAnimation()
    {
        //Debug.Log($"[Destroying状态] 炮台 {cannon.ObjectId} 动画完成，继续销毁流程");
        // 调用炮台的通用销毁逻辑方法，传入Z偏移数组
        cannon.ExecuteDestroyingGoToDes(Zoff);
    }


}