using UnityEngine;
using System.Collections;

/// <summary>
/// ShootingSynthesis状态 - 射击合成状态
/// 进入：留空等待后续实现
/// 持续：留空等待后续实现
/// </summary>
public class GridConnon_CannonState_ShootingSynthesisFinish : GridConnon_CannonStateBase
{
    public override GridConnon_Cannon_State GetStateType()
    {
        return GridConnon_Cannon_State.ShootingSynthesisFinish;
    }

    public override void OnEnter()
    {
        // 1. 颜色膜层宽度设定为0，停止呼吸效果，清除占用
        HandleOverlayAndBreathe();

        // 2. 延时执行放大回缩动画
        float delayTime = cannon.SynthesisFinish_delayTime;
        cannon.StartCoroutine(DelayExecuteAnimation(delayTime));
    }

    public override void OnExit()
    {

    }

    public override void OnUpdate()
    {

    }

    public override void OnClick()
    {

    }

    /// <summary>
    /// 处理膜层和呼吸效果
    /// </summary>
    private void HandleOverlayAndBreathe()
    {
        // 设置膜层宽度为0来隐藏膜层
        stateManager.SetColorOverlayEffect(Color.clear, 0f, 0f);


    }



    /// <summary>
    /// 延时执行动画协程
    /// </summary>
    private IEnumerator DelayExecuteAnimation(float delayTime)
    {
        yield return new WaitForSeconds(delayTime);
        ExecuteSynthesisFinishAnimation();
    }

    /// <summary>
    /// 执行合成完成动画
    /// </summary>
    private void ExecuteSynthesisFinishAnimation()
    {
        // 使用炮台配置的合成完成动画参数
        float animDuration = cannon.SynthesisFinish_animDuration;
        float scaleSize = cannon.SynthesisFinish_scaleSize;
        float whiteFlashDuration = cannon.SynthesisFinish_whiteFlashDuration;

        // 同时执行白色闪烁效果和放大回缩动画，使用不同的时间参数
        stateManager.PlayWhiteFlashEffect(whiteFlashDuration, 0.8f);
        stateManager.PlayCompressionAnimation(false, animDuration, scaleSize, OnAnimationComplete);

        //Debug.Log($"[ShootingSynthesisFinish状态] 炮台 {cannon.ObjectId} 开始执行合成完成动画，缩放时间:{animDuration}秒，放大比例:{scaleSize}，白色闪烁时间:{whiteFlashDuration}秒");
    }

    /// <summary>
    /// 动画完成回调
    /// </summary>
    private void OnAnimationComplete()
    {

        stateManager.ChangeState(GridConnon_Cannon_State.Shooting);

    }


}