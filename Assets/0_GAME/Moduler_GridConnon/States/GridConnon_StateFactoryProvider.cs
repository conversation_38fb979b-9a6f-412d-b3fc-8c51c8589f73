using UnityEngine;

/// <summary>
/// 状态工厂提供者 - 根据Cannon类型返回对应的状态工厂
/// </summary>
public static class GridConnon_StateFactoryProvider
{
    /// <summary>
    /// 根据Cannon类型获取对应的状态工厂
    /// </summary>
    /// <param name="cannonType">炮台类型</param>
    /// <returns>对应的状态工厂</returns>
    public static IGridConnon_StateFactory GetFactory(GridConnon_ObjectType cannonType)
    {
        switch (cannonType)
        {
            case GridConnon_ObjectType.NormalCannon:
                return new GridConnon_NormalCannonStateFactory();

            case GridConnon_ObjectType.QuestionNormalCannon:
                return new GridConnon_QuestionCannonStateFactory();

            case GridConnon_ObjectType.TreasureCannon:
                return new GridConnon_TreasureCannonStateFactory();

            case GridConnon_ObjectType.TriangleCannon:
                return new GridConnon_TriangleCannonStateFactory();

            case GridConnon_ObjectType.QuestionTriangleCannon:
                return new GridConnon_QuestionTriangleCannonStateFactory();

            default:
                //Debug.LogWarning($"未知的Cannon类型: {cannonType}，使用默认NormalCannon工厂");
                return new GridConnon_NormalCannonStateFactory();
        }
    }
}