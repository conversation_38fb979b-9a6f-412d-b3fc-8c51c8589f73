using UnityEngine;

/// <summary>
/// FirstRow状态 - 前排状态
/// 进入：打开描边，关闭膜层，呼吸，数字透明度1，描边颜色和宽度
/// 持续：监测点击，被点击留空等待后续实现
/// </summary>
public class GridConnon_CannonState_FirstRow : GridConnon_CannonStateBase
{
    public override GridConnon_Cannon_State GetStateType()
    {
        return GridConnon_Cannon_State.FirstRow;
    }

    public override void OnEnter()
    {
        // 1. 设置描边效果
        stateManager.SetOutlineEffect(
            color: cannon.firstRow_outlineColor,
            width: cannon.firstRow_outlineWidth
        );

        // 2. 关闭颜色膜层效果（设置透明度为0）
        stateManager.SetColorOverlayEffect(
            color: Color.white,
            alpha: 0f
        );

        // 3. 设置数字透明度为1（完全不透明）
        stateManager.SetTextAlpha(1f);

        // 4. 开始呼吸效果（带随机延迟）
        stateManager.TriggerBreatheWithRandomDelay();

        //Debug.Log($"[FirstRow状态] 炮台 {cannon.ObjectId} 进入FirstRow状态");
        //Debug.Log($"  - 描边: 颜色={cannon.firstRow_outlineColor}, 宽度={cannon.firstRow_outlineWidth}");
        //Debug.Log($"  - 膜层: 关闭（透明度=0）");
        //Debug.Log($"  - 数字透明度: 1.0");
    }

    public override void OnExit()
    {


        //Debug.Log($"[FirstRow状态] 炮台 {cannon.ObjectId} 退出FirstRow状态");
    }

    public override void OnUpdate()
    {
        // FirstRow状态下的持续逻辑
        // 这里可以添加持续监测的逻辑
    }

    public override void OnClick()
    {
        if (!GridConnon_InputManager.Can_Connon_Click())
            return;
        // 检查是否有链接组
        var linkSystem = GridConnon_LinkSystem.Instance;
        if (linkSystem != null)
        {
            var linkGroup = linkSystem.GetLinkGroup(cannon);

            if (linkGroup != null && linkGroup.Count > 1)
            {
                // 有链接组，检查链接条件
                if (!CheckLinkGroupConditions(linkGroup, linkSystem))
                {
                    // 条件不满足，触发压缩动画
                    stateManager.PlayCompressionAnimation(false);
                    //Debug.Log($"[FirstRow状态] 炮台 {cannon.ObjectId} 链接组条件不满足，播放压缩动画");
                    return;
                }

                // 条件满足，整组移动到射击格
                MoveLinkedGroupToShotGrid(linkGroup, linkSystem);

                GridConnon_Controller.On_PlaySound?.Invoke("CNT_正确点击");

                return;
            }
        }

        // 没有链接，使用原有逻辑
        bool moveStarted = stateManager.ControllerGridToShotGrid();
        GridConnon_Controller.On_PlaySound?.Invoke("CNT_正确点击");


    }

    /// <summary>
    /// 检查链接组的移动条件
    /// </summary>
    /// <param name="linkGroup">链接组</param>
    /// <param name="linkSystem">链接系统</param>
    /// <returns>是否满足条件</returns>
    private bool CheckLinkGroupConditions(System.Collections.Generic.HashSet<GridConnon_Cannon> linkGroup, GridConnon_LinkSystem linkSystem)
    {
        // 1. 检查所有炮台是否都准备好移动到射击格
        if (!linkSystem.IsAllInGroupReadyForShotGrid(cannon))
        {
            //Debug.Log($"[FirstRow状态] 链接组中部分炮台未准备好移动到射击格");
            return false;
        }

        // 2. 检查DirectShot格子是否有足够的空位（根据开关决定是否需要连续）
        if (!linkSystem.HasEnoughConsecutiveDirectShotGrids(linkGroup.Count))
        {
            var controller = GridConnon_Controller.Instance;
            string checkType = (controller != null && controller.enableLinkConsecutiveGridCheck) ? "连续" : "非连续";
            //Debug.Log($"[FirstRow状态] DirectShot格子{checkType}空位不足，需要 {linkGroup.Count} 个");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 移动链接组到射击格
    /// </summary>
    /// <param name="linkGroup">链接组</param>
    /// <param name="linkSystem">链接系统</param>
    private void MoveLinkedGroupToShotGrid(System.Collections.Generic.HashSet<GridConnon_Cannon> linkGroup, GridConnon_LinkSystem linkSystem)
    {
        // 获取射击格（根据开关决定是否需要连续）
        var grids = linkSystem.GetConsecutiveDirectShotGrids(linkGroup.Count);
        if (grids == null)
        {
            var controller = GridConnon_Controller.Instance;
            string checkType = (controller != null && controller.enableLinkConsecutiveGridCheck) ? "连续" : "可用";
            //Debug.LogError($"[FirstRow状态] 无法获取 {linkGroup.Count} 个{checkType}射击格");
            stateManager.PlayCompressionAnimation(false);
            return;
        }

        // 将链接组转换为列表并排序（按X坐标排序以保持相对位置）
        var sortedCannons = new System.Collections.Generic.List<GridConnon_Cannon>(linkGroup);
        sortedCannons.Sort((a, b) => a.GridX.CompareTo(b.GridX));

        //Debug.Log($"[FirstRow状态] 开始为链接组分配射击格：");
        for (int i = 0; i < sortedCannons.Count; i++)
        {
            //Debug.Log($"[FirstRow状态]   炮台 {sortedCannons[i].ObjectId} -> 格子索引 {GetGridIndex(grids[i])}");
        }

        // 为每个炮台分配指定的射击格并开始移动
        for (int i = 0; i < sortedCannons.Count; i++)
        {
            var linkedCannon = sortedCannons[i];
            var targetGrid = grids[i];

            // 使用自定义逻辑移动到指定格子，而不是调用ControllerGridToShotGrid
            MoveCannonToSpecificShotGrid(linkedCannon, targetGrid);
        }

        //Debug.Log($"[FirstRow状态] 链接组开始移动到射击格，共 {linkGroup.Count} 个炮台");
    }

    /// <summary>
    /// 移动炮台到指定的射击格
    /// </summary>
    /// <param name="targetCannon">目标炮台</param>
    /// <param name="targetGrid">目标格子</param>
    private void MoveCannonToSpecificShotGrid(GridConnon_Cannon targetCannon, GridConnon_ShotGrid_Grid targetGrid)
    {
        // 检查炮台是否正在移动
        if (targetCannon.IsMoving())
        {
            //Debug.LogWarning($"[FirstRow状态] 炮台 {targetCannon.ObjectId} 正在移动，跳过");
            return;
        }

        // 1. 解除占用 GridConnon_Controller 的格子
        targetCannon.StateManager.ClearGridConnon_ControllerGrid();

        // 2. 预占用目标射击格
        if (targetGrid.TryReserveGrid(targetCannon.gameObject))
        {
            //Debug.Log($"[FirstRow状态] 炮台 {targetCannon.ObjectId} 成功预占用射击格索引 {GetGridIndex(targetGrid)}");

            // 停止呼吸效果
            targetCannon.StateManager.StopBreathe(true);

            // 3. 触发压缩动画
            targetCannon.StateManager.PlayCompressionAnimation(false);

            // 4. 移动到目标位置
            Vector3 targetPosition = targetGrid.transform.position;
            targetPosition.y = targetCannon.transform.position.y; // 保持Y轴不变

            // 开始移动
            bool moveStarted = targetCannon.MoveToPosition(targetPosition, () =>
            {
                //Debug.Log($"[FirstRow状态] 炮台 {targetCannon.ObjectId} 到达射击格 {GetGridIndex(targetGrid)}");

                // 1. 设置炮台Y旋转为0（停止旋转）
                Vector3 currentRotation = targetCannon.transform.eulerAngles;
                currentRotation.y = 0f;
                targetCannon.transform.eulerAngles = currentRotation;
                //Debug.Log($"[FirstRow状态] 炮台 {targetCannon.ObjectId} 重置旋转角度为0");

                // 2. 将预留状态升级为占用状态
                if (targetGrid.UpgradeReservationToOccupation())
                {
                    //Debug.Log($"[FirstRow状态] 炮台 {targetCannon.ObjectId} 成功占用射击格");

                    // 3. 更新炮台的格子引用
                    targetCannon.StateManager.UpdateGridReference(targetGrid);

                    // 4. 切换到Shooting状态
                    targetCannon.ChangeState(GridConnon_Cannon_State.Shooting);

                    //Debug.Log($"[FirstRow状态] 炮台 {targetCannon.ObjectId} 完成移动到射击格流程");
                }
                else
                {
                    //Debug.LogError($"[FirstRow状态] 炮台 {targetCannon.ObjectId} 升级占用状态失败");
                    // 如果升级失败，清除预占状态
                    targetGrid.ClearReservation();
                }
            });

            if (moveStarted)
            {
                // 进入Moving状态
                targetCannon.ChangeState(GridConnon_Cannon_State.Moving);
                //Debug.Log($"[FirstRow状态] 炮台 {targetCannon.ObjectId} 开始移动到指定射击格");
            }
            else
            {
                //Debug.LogError($"[FirstRow状态] 炮台 {targetCannon.ObjectId} 移动失败");
                // 释放格子预占用
                targetGrid.ClearReservation();
            }
        }
        else
        {
            //Debug.LogError($"[FirstRow状态] 炮台 {targetCannon.ObjectId} 无法预占用目标射击格");
        }
    }

    /// <summary>
    /// 获取格子在数组中的索引（用于调试）
    /// </summary>
    /// <param name="grid">格子</param>
    /// <returns>索引</returns>
    private int GetGridIndex(GridConnon_ShotGrid_Grid grid)
    {
        var shotController = GridConnon_ShotGrid_Controller.Instance;
        if (shotController?.DirectShotGrids != null)
        {
            for (int i = 0; i < shotController.DirectShotGrids.Length; i++)
            {
                if (shotController.DirectShotGrids[i] == grid)
                    return i;
            }
        }
        return -1;
    }
}