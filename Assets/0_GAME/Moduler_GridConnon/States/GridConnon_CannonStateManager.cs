using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 炮台状态管理器 - 负责状态切换和管理
/// </summary>
public class GridConnon_CannonStateManager
{
    public GridConnon_Cannon cannon;
    private GridConnon_CannonStateBase currentState;
    private Dictionary<GridConnon_Cannon_State, GridConnon_CannonStateBase> states;
    private IGridConnon_StateFactory stateFactory;

    // 材质属性ID缓存
    private static readonly int EnableOutlineID = Shader.PropertyToID("_EnableOutline");
    private static readonly int OutlineColorID = Shader.PropertyToID("_OutlineColor");
    private static readonly int OutlineWidthID = Shader.PropertyToID("_OutlineWidth");
    private static readonly int EnableColorOverlayID = Shader.PropertyToID("_EnableColorOverlay");
    private static readonly int OverlayColorID = Shader.PropertyToID("_OverlayColor");
    private static readonly int OverlayAlphaID = Shader.PropertyToID("_OverlayAlpha");
    private static readonly int OverlayWidthScaleID = Shader.PropertyToID("_OverlayWidthScale");

    // 协程管理
    private Coroutine breatheDelayCoroutine;
    private Coroutine rotationCoroutine;
    private Coroutine compressionCoroutine;
    private Coroutine whiteFlashCoroutine;

    /// <summary>
    /// 构造函数
    /// </summary>
    public GridConnon_CannonStateManager(GridConnon_Cannon cannon)
    {
        this.cannon = cannon;
        // 根据Cannon类型获取对应的状态工厂
        this.stateFactory = GridConnon_StateFactoryProvider.GetFactory(cannon.ObjectType);
        InitializeStates();

        // 初始化状态显示字段
        cannon.UpdateStateDisplay(GridConnon_Cannon_State.BackRow);
    }

    /// <summary>
    /// 初始化所有状态
    /// </summary>
    private void InitializeStates()
    {
        // 使用工厂创建对应类型的状态集合
        states = stateFactory.CreateStates(cannon, this);

        // 初始化所有状态
        foreach (var state in states.Values)
        {
            state.Initialize(cannon, this);
        }

        //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 类型 {cannon.ObjectType} 使用工厂 {stateFactory.GetType().Name} 初始化状态");
    }

    /// <summary>
    /// 切换状态
    /// </summary>
    public void ChangeState(GridConnon_Cannon_State newState)
    {
        // 记录旧状态
        var oldState = currentState?.GetStateType();

        // 退出当前状态
        if (currentState != null)
        {
            currentState.OnExit();
        }

        // 切换到新状态
        if (states.TryGetValue(newState, out GridConnon_CannonStateBase nextState))
        {
            currentState = nextState;
            currentState.OnEnter();

            // 同步更新炮台的状态显示字段
            cannon.UpdateStateDisplay(newState);

            //Debug.Log($"炮台 {cannon.ObjectId} 切换到状态: {newState}");

            // 处理状态切换后的链接系统逻辑
            HandleLinkSystemOnStateChange(oldState, newState);
        }
        else
        {
            //Debug.LogError($"未找到状态: {newState}");
        }
    }

    /// <summary>
    /// 处理状态切换后的链接系统逻辑
    /// </summary>
    /// <param name="oldState">旧状态</param>
    /// <param name="newState">新状态</param>
    private void HandleLinkSystemOnStateChange(GridConnon_Cannon_State? oldState, GridConnon_Cannon_State newState)
    {
        var linkSystem = GridConnon_LinkSystem.Instance;
        if (linkSystem == null)
        {
            //Debug.LogWarning($"[状态管理器] 炮台 {cannon.ObjectId} 链接系统实例为null，无法处理状态变化");
            return;
        }

        //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 状态变化: {oldState} → {newState}");

        // 检查是否为问号炮台类型
        bool isQuestionCannon = cannon.ObjectType == GridConnon_ObjectType.QuestionNormalCannon ||
                               cannon.ObjectType == GridConnon_ObjectType.QuestionTriangleCannon;

        // 问号炮台在BackRow和FirstRow状态变化时更新链接棍颜色，Moving状态时不改变颜色
        if (isQuestionCannon)
        {
            if (newState == GridConnon_Cannon_State.BackRow || newState == GridConnon_Cannon_State.FirstRow)
            {
                // 更新当前炮台的所有链接棍颜色
                cannon.UpdateAllLinkStickColors();

                //Debug.Log($"[状态管理器] 问号炮台 {cannon.ObjectId} 进入{newState}状态，已更新链接棍颜色");
            }
            // Moving状态时不改变颜色，其他状态也不改变颜色
        }

        // 当进入FirstRow状态时，检查同列前进
        if (newState == GridConnon_Cannon_State.FirstRow)
        {
            //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 进入FirstRow状态，开始检查同列前进");
            linkSystem.CheckSameColumnAdvancement(cannon);
        }

        // 当进入Destroying状态时，检查链接组同步销毁
        if (newState == GridConnon_Cannon_State.Destroying)
        {
            //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 进入Destroying状态，开始检查链接组同步销毁");
            CheckLinkedGroupDestroySync();
        }
    }

    /// <summary>
    /// 检查链接组同步销毁
    /// </summary>
    private void CheckLinkedGroupDestroySync()
    {
        var linkSystem = GridConnon_LinkSystem.Instance;
        if (linkSystem == null) return;

        var linkGroup = linkSystem.GetLinkGroup(cannon);
        if (linkGroup == null || linkGroup.Count <= 1) return;

        // 检查是否所有链接炮台都处于Destroying状态
        bool allDestroying = linkSystem.IsAllInGroupDestroying(cannon);

        if (!allDestroying)
        {
            // 不是所有炮台都在销毁状态，暂停当前炮台的销毁进程
            var destroyingState = GetStateByType(GridConnon_Cannon_State.Destroying) as GridConnon_CannonState_Destroying;
            if (destroyingState != null)
            {
                destroyingState.PauseDestruction();
                //Debug.Log($"[链接系统] 炮台 {cannon.ObjectId} 等待链接组其他炮台进入销毁状态");
            }
        }
        else
        {
            // 所有炮台都在销毁状态，检查是否都完成了动画并在等待GoToDes
            bool allAnimationCompleteAndWaiting = linkSystem.IsAllInGroupAnimationCompleteAndWaiting(cannon);

            if (allAnimationCompleteAndWaiting)
            {
                // 所有炮台都完成了动画且在等待状态，恢复所有暂停的销毁进程
                foreach (var linkedCannon in linkGroup)
                {
                    if (linkedCannon != null && linkedCannon.GetCurrentState() == GridConnon_Cannon_State.Destroying)
                    {
                        var destroyingState = linkedCannon.StateManager.GetStateByType(GridConnon_Cannon_State.Destroying) as GridConnon_CannonState_Destroying;
                        destroyingState?.ResumeDestruction();
                    }
                }
                //Debug.Log($"[链接系统] 链接组所有炮台都完成销毁动画并在等待状态，开始执行GoToDes");
            }
            else
            {
                // 还有炮台未完成动画，确保当前炮台保持暂停状态
                var destroyingState = GetStateByType(GridConnon_Cannon_State.Destroying) as GridConnon_CannonState_Destroying;
                if (destroyingState != null && !destroyingState.IsPaused())
                {
                    destroyingState.PauseDestruction();
                    //Debug.Log($"[链接系统] 炮台 {cannon.ObjectId} 等待链接组其他炮台完成销毁动画");
                }
            }
        }
    }

    /// <summary>
    /// 通知动画完成，触发链接组同步检查
    /// </summary>
    public void NotifyAnimationComplete()
    {
        CheckLinkedGroupDestroySync();
    }

    /// <summary>
    /// 更新当前状态
    /// </summary>
    public void Update()
    {
        currentState?.OnUpdate();
    }

    /// <summary>
    /// 处理点击事件
    /// </summary>
    public void HandleClick()
    {
        // 如果正在旋转中，则不响应点击
        if (IsRotating())
        {
            //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 正在旋转中，不响应点击");
            return;
        }

        // 如果正在压缩动画中，则不响应点击
        if (IsCompressing())
        {
            //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 正在压缩动画中，不响应点击");
            return;
        }

        currentState?.OnClick();
    }

    /// <summary>
    /// 获取当前状态
    /// </summary>
    public GridConnon_Cannon_State GetCurrentState()
    {
        return currentState?.GetStateType() ?? GridConnon_Cannon_State.BackRow;
    }

    /// <summary>
    /// 根据状态类型获取状态实例
    /// </summary>
    /// <param name="stateType">状态类型</param>
    /// <returns>状态实例，如果不存在返回null</returns>
    public GridConnon_CannonStateBase GetStateByType(GridConnon_Cannon_State stateType)
    {
        return states.TryGetValue(stateType, out GridConnon_CannonStateBase state) ? state : null;
    }

    /// <summary>
    /// 检查是否正在旋转
    /// </summary>
    public bool IsRotating()
    {
        return rotationCoroutine != null;
    }

    /// <summary>
    /// 检查是否正在压缩动画
    /// </summary>
    public bool IsCompressing()
    {
        return compressionCoroutine != null;
    }

    /// <summary>
    /// 停止旋转动画
    /// </summary>
    public void StopRotation()
    {
        if (rotationCoroutine != null)
        {
            cannon.StopCoroutine(rotationCoroutine);
            rotationCoroutine = null;
            //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 旋转动画已停止");
        }
    }

    /// <summary>
    /// 停止压缩动画
    /// </summary>
    public void StopCompression()
    {
        if (compressionCoroutine != null)
        {
            cannon.StopCoroutine(compressionCoroutine);
            compressionCoroutine = null;
            //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 压缩动画已停止");
        }
    }

    #region 格子管理方法

    /// <summary>
    /// 格子类型枚举
    /// </summary>
    public enum GridType
    {
        None,                   // 未占用任何格子
        GridConnon,            // GridConnon_Controller的格子
        ShotGrid               // GridConnon_ShotGrid_Controller的格子
    }

    /// <summary>
    /// 解除占用GridConnon_Controller的格子
    /// </summary>
    public void ClearGridConnon_ControllerGrid()
    {
        if (cannon.reserveGrid != null && IsInGridConnon_ControllerGrid())
        {
            cannon.reserveGrid.ClearGrid();
            cannon.reserveGrid = null;
            //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 解除占用GridConnon_Controller格子");
        }
    }

    /// <summary>
    /// 解除占用射击格子
    /// </summary>
    public void ClearShotGrid()
    {
        if (cannon.reserveGrid != null && IsInShotGrid())
        {
            cannon.reserveGrid.ClearGrid();
            cannon.reserveGrid = null;
            //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 解除占用射击格子");
        }
    }

    /// <summary>
    /// 判断当前占用的是哪种格子
    /// </summary>
    /// <returns>格子类型</returns>
    public GridType GetCurrentGridType()
    {
        if (cannon.reserveGrid == null)
            return GridType.None;

        string groupId = cannon.reserveGrid.GroupId;
        switch (groupId)
        {
            case "GridConnon":
                return GridType.GridConnon;
            case "GridConnon_ShotGrid":
                return GridType.ShotGrid;
            default:
                //Debug.LogWarning($"[状态管理器] 炮台 {cannon.ObjectId} 占用了未知类型的格子: {groupId}");
                return GridType.None;
        }
    }

    /// <summary>
    /// 判断炮台当前是否在GridConnon_Controller的格子中
    /// </summary>
    /// <returns>true表示在GridConnon_Controller格子中</returns>
    public bool IsInGridConnon_ControllerGrid()
    {
        if (cannon.reserveGrid == null) return false;
        return cannon.reserveGrid.GroupId == "GridConnon";
    }

    /// <summary>
    /// 判断炮台当前是否在射击格子中
    /// </summary>
    /// <returns>true表示在射击格子中</returns>
    public bool IsInShotGrid()
    {
        if (cannon.reserveGrid == null) return false;
        return cannon.reserveGrid.GroupId == "GridConnon_ShotGrid";
    }

    /// <summary>
    /// 更新炮台的格子引用
    /// </summary>
    /// <param name="newGrid">新的格子引用</param>
    public void UpdateGridReference(Common_ReserveGrid newGrid)
    {
        cannon.reserveGrid = newGrid;
        //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 格子引用已更新: {newGrid?.name ?? "null"}");
    }

    #endregion

    /// <summary>
    /// 通用方法：从 Controller 格子移动到射击格子
    /// 从 GridConnon_Controller 的格子移动到 GridConnon_ShotGrid 的第一个空闲格子
    /// </summary>
    /// <param name="onReachDestination">到达目标后的回调方法</param>
    /// <returns>是否成功开始移动</returns>
    public bool ControllerGridToShotGrid(System.Action<GridConnon_ShotGrid_Grid> onReachDestination = null)
    {
        // 检索 GridConnon_ShotGrid_Controller 是否有 DirectShot 空格
        GridConnon_ShotGrid_Grid targetShotGrid = GridConnon_Controller.Instance.GetFirstFreeDirectShotGrid();

        if (targetShotGrid != null)
        {
            // 如果炮台正在移动，不允许重复点击
            if (cannon.IsMoving())
            {
                return false;
            }

            // 1. 解除占用 GridConnon_Controller 的格子
            ClearGridConnon_ControllerGrid();

            // 2. 预占有目标 DirectShot 格子
            if (targetShotGrid.TryReserveGrid(cannon.gameObject))
            {
                // 停止呼吸效果
                StopBreathe(true);

                // 3. 触发 PlayCompressionAnimation(false)
                PlayCompressionAnimation(false);

                // 4. 通过 GridConnon_Navigation 移动到目标格子坐标（忽略Y）
                Vector3 targetPosition = targetShotGrid.transform.position;
                targetPosition.y = cannon.transform.position.y; // 忽略Y轴

                // 开始移动，移动完成后的回调处理
                bool moveStarted = cannon.MoveToPosition(targetPosition, () =>
                {
                    // 到达后的处理
                    OnReachedShotGridFromController(targetShotGrid, onReachDestination);
                });

                if (moveStarted)
                {
                    // 进入 Moving 状态（停止呼吸效果）
                    ChangeState(GridConnon_Cannon_State.Moving);
                    return true;
                }
                else
                {
                    // 移动失败，恢复格子状态
                    targetShotGrid.ClearReservation();
                    if (cannon.reserveGrid != null)
                    {
                        cannon.reserveGrid.TryPlaceItem(cannon.gameObject);
                    }
                    return false;
                }
            }
            else
            {
                // 预占失败，恢复原格子占用
                if (cannon.reserveGrid != null)
                {
                    cannon.reserveGrid.TryPlaceItem(cannon.gameObject);
                }
                // 触发 PlayCompressionAnimation(true)
                PlayCompressionAnimation(true);
                return false;
            }
        }
        else
        {
            // 无空格：触发 PlayCompressionAnimation(true)
            //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 没有空闲DirectShot格子");
            PlayCompressionAnimation(true);
            return false;
        }
    }

    /// <summary>
    /// 到达射击格子后的处理（从 Controller 格子移动过来）
    /// </summary>
    /// <param name="shotGrid">目标射击格子</param>
    /// <param name="onReachDestination">到达目标后的额外回调</param>
    private void OnReachedShotGridFromController(GridConnon_ShotGrid_Grid shotGrid, System.Action<GridConnon_ShotGrid_Grid> onReachDestination = null)
    {
        //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 到达射击格子 {shotGrid.name}");

        // 1. 设置 cannon Y旋转为0
        Vector3 currentRotation = cannon.transform.eulerAngles;
        currentRotation.y = 0f;
        cannon.transform.eulerAngles = currentRotation;

        // 2. 将预占状态升级为实际占用
        if (shotGrid.UpgradeReservationToOccupation())
        {
            //Debug.Log($"[状态管理器] 实际占用射击格子成功");

            // 3. 更新炮台的格子引用
            UpdateGridReference(shotGrid);

            // 4. 执行自定义回调（如果有）
            onReachDestination?.Invoke(shotGrid);

            // 5. 默认进入 Shooting 状态（可被自定义回调覆盖）
            if (GetCurrentState() == GridConnon_Cannon_State.Moving)
            {
                ChangeState(GridConnon_Cannon_State.Shooting);
            }
        }
        else
        {
            //Debug.LogError($"[状态管理器] 升级占用状态失败");
            // 如果升级失败，清除预占状态并回到原位置（这种情况理论上不应该发生）
            shotGrid.ClearReservation();
        }
    }

    /// <summary>
    /// 从Controller格子移动到另一个Controller格子（通用方法）
    /// </summary>
    /// <param name="targetGridX">目标格子X坐标</param>
    /// <param name="targetGridZ">目标格子Z坐标</param>
    /// <param name="onReachDestination">到达目标后的额外回调</param>
    /// <returns>是否成功开始移动</returns>
    public bool ControllerGridToControllerGrid(int targetGridX, int targetGridZ, System.Action onReached)
    {
        // 检查目标格子是否空闲
        if (GridConnon_Controller.Instance.IsGridReserveOrOccupied(targetGridX, targetGridZ))
        {

            return false;
        }

        // 获取目标格子对象
        Common_ReserveGrid targetGrid = GridConnon_Controller.Instance.GetGridAt(targetGridX, targetGridZ);
        if (targetGrid == null)
        {

            return false;
        }

        // 如果炮台正在移动，不允许重复移动
        if (cannon.IsMoving())
        {

            return false;
        }

        //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 开始移动到Controller格子 ({targetGridX}, {targetGridZ})");

        // 1. 解除占用当前格子
        ClearGridConnon_ControllerGrid();

        // 2. 预占有目标格子
        if (targetGrid.TryReserveGrid(cannon.gameObject))
        {
            UpdateGridReference(targetGrid);
            cannon.UpdateGridPosition(targetGridX, targetGridZ);

            // 3. 通过GridConnon_Navigation移动到目标格子坐标（忽略Y）
            Vector3 targetPosition = targetGrid.transform.position;
            targetPosition.y = cannon.transform.position.y; // 忽略Y轴

            // 开始移动，移动完成后的回调处理
            bool moveStarted = cannon.MoveToPosition(targetPosition, () =>
            {
                onReached();
            });

            if (moveStarted)
            {
                // 进入Moving状态
                ChangeState(GridConnon_Cannon_State.Moving);
                return true;
            }
            else
            {
                // 移动失败，恢复格子状态
                targetGrid.ClearReservation();
                if (cannon.reserveGrid != null)
                {
                    cannon.reserveGrid.TryPlaceItem(cannon.gameObject);
                }
                return false;
            }
        }
        else
        {
            // 预占失败，恢复原格子占用
            if (cannon.reserveGrid != null)
            {
                cannon.reserveGrid.TryPlaceItem(cannon.gameObject);
            }
            return false;
        }
    }

    /// <summary>
    /// 到达Controller格子后的处理（从另一个Controller格子移动过来）
    /// </summary>
    /// <param name="targetGrid">目标格子</param>
    /// <param name="newGridX">新的网格X坐标</param>
    /// <param name="newGridZ">新的网格Z坐标</param>
    /// <param name="onReachDestination">到达目标后的额外回调</param>
    public void OnReachedControllerGridToControllerGrid(Common_ReserveGrid targetGrid, int newGridX, int newGridZ)
    {
        //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 到达Controller格子 ({newGridX}, {newGridZ})");

        // 1. 将预占状态升级为实际占用
        if (targetGrid.UpgradeReservationToOccupation())
        {
            //Debug.Log($"[状态管理器] 实际占用Controller格子成功");

            // 2. 检查是否应该进入FirstRow状态
            GridConnon_Cannon_State targetState = DetermineStateByPositionAndLinks(newGridZ);

            //Debug.Log($"[状态管理器] 炮台 {cannon.ObjectId} 根据位置和链接关系确定状态: {targetState}");
            ChangeState(targetState);
        }
        else
        {
            //Debug.LogError($"[状态管理器] 升级占用状态失败");
            // 如果升级失败，清除预占状态（这种情况理论上不应该发生）
            targetGrid.ClearReservation();
        }
    }

    /// <summary>
    /// 根据位置和链接关系确定炮台状态
    /// </summary>
    /// <param name="gridZ">网格Z坐标</param>
    /// <returns>应该进入的状态</returns>
    public GridConnon_Cannon_State DetermineStateByPositionAndLinks(int gridZ)
    {
        //Debug.Log($"[状态确定] 开始为炮台 {cannon.ObjectId} 确定状态，当前位置GridZ={gridZ}");

        // 1. 如果在第一排（Z=0），直接返回FirstRow状态
        if (gridZ == 0)
        {
            //Debug.Log($"[状态确定] 炮台 {cannon.ObjectId} 在第一排，设置为FirstRow状态");
            return GridConnon_Cannon_State.FirstRow;
        }

        // 2. 如果在后排，检查是否因链接机制应该保持FirstRow状态
        if (gridZ < 0)
        {
            var linkSystem = GridConnon_LinkSystem.Instance;
            if (linkSystem != null)
            {
                //Debug.Log($"[状态确定] 炮台 {cannon.ObjectId} 在后排，检查链接机制");

                // 检查是否有同列前方的链接炮台已经在FirstRow状态
                if (ShouldStayInFirstRowDueToLinks(linkSystem))
                {
                    //Debug.Log($"[状态确定] 炮台 {cannon.ObjectId} 因链接机制保持FirstRow状态");
                    return GridConnon_Cannon_State.FirstRow;
                }
                else
                {
                    //Debug.Log($"[状态确定] 炮台 {cannon.ObjectId} 链接检查失败，将设置为BackRow状态");
                }
            }
            else
            {
                //Debug.LogWarning($"[状态确定] 链接系统实例为null，炮台 {cannon.ObjectId} 无法进行链接检查");
            }

            //Debug.Log($"[状态确定] 炮台 {cannon.ObjectId} 在后排且无链接前进条件，设置为BackRow状态");
            return GridConnon_Cannon_State.BackRow;
        }

        // 3. 默认情况（Z>0，理论上不应该出现）
        //Debug.LogWarning($"[状态确定] 炮台 {cannon.ObjectId} 位置异常(Z={gridZ})，默认设置为BackRow状态");
        return GridConnon_Cannon_State.BackRow;
    }

    /// <summary>
    /// 检查是否因链接机制应该保持FirstRow状态
    /// 修正逻辑：只有紧邻前方炮台有链接关系且在FirstRow状态时，当前炮台才能前进
    /// </summary>
    /// <param name="linkSystem">链接系统实例</param>
    /// <returns>是否应该保持FirstRow状态</returns>
    private bool ShouldStayInFirstRowDueToLinks(GridConnon_LinkSystem linkSystem)
    {
        //Debug.Log($"[链接检查] 开始检查炮台 {cannon.ObjectId} 是否应该因链接机制保持FirstRow状态");

        // 获取同列的所有炮台
        var sameColumnCannons = GetSameColumnCannons(cannon.GridX);
        //Debug.Log($"[链接检查] 同列炮台总数: {sameColumnCannons.Count}");

        // 打印同列炮台信息
        foreach (var c in sameColumnCannons)
        {
            //Debug.Log($"[链接检查] 同列炮台: {c.ObjectId}(GridZ={c.GridZ}, 状态={c.GetCurrentState()})");
        }

        // 按Z坐标排序（从前到后：Z值从大到小）
        sameColumnCannons.Sort((a, b) => b.GridZ.CompareTo(a.GridZ));

        // 找到当前炮台在列表中的位置
        int currentIndex = sameColumnCannons.IndexOf(cannon);
        if (currentIndex == -1)
        {
            //Debug.LogWarning($"[链接检查] 未找到当前炮台 {cannon.ObjectId} 在同列炮台列表中");
            return false;
        }

        //Debug.Log($"[链接检查] 当前炮台 {cannon.ObjectId} 在同列中的索引: {currentIndex}");

        // 修正逻辑：只检查紧邻前方的炮台（索引为currentIndex-1）
        if (currentIndex > 0)
        {
            var frontCannon = sameColumnCannons[currentIndex - 1];
            //Debug.Log($"[链接检查] 检查紧邻前方炮台 {frontCannon.ObjectId}(GridZ={frontCannon.GridZ})");

            // 检查与紧邻前方炮台的链接关系
            bool isLinked = linkSystem.IsLinked(cannon, frontCannon);
            //Debug.Log($"[链接检查] 炮台 {cannon.ObjectId} 与紧邻前方炮台 {frontCannon.ObjectId} 的链接关系: {isLinked}");

            if (isLinked)
            {
                var frontCannonState = frontCannon.GetCurrentState();
                //Debug.Log($"[链接检查] 紧邻前方炮台 {frontCannon.ObjectId} 当前状态: {frontCannonState}");

                // 如果紧邻前方炮台与当前炮台有链接关系，且前方炮台在FirstRow状态
                if (frontCannonState == GridConnon_Cannon_State.FirstRow)
                {
                    //Debug.Log($"[链接检查] 炮台 {cannon.ObjectId} 因紧邻前方链接炮台 {frontCannon.ObjectId} 在FirstRow状态而保持FirstRow");
                    return true;
                }
                else
                {
                    //Debug.Log($"[链接检查] 紧邻前方链接炮台 {frontCannon.ObjectId} 不在FirstRow状态，无法前进");
                }
            }
            else
            {
                //Debug.Log($"[链接检查] 炮台 {cannon.ObjectId} 与紧邻前方炮台 {frontCannon.ObjectId} 没有链接关系，无法前进");
            }
        }
        else
        {
            //Debug.Log($"[链接检查] 炮台 {cannon.ObjectId} 前方没有炮台");
        }

        //Debug.Log($"[链接检查] 炮台 {cannon.ObjectId} 不满足链接前进条件，返回false");
        return false;
    }

    /// <summary>
    /// 获取同列的所有炮台
    /// </summary>
    /// <param name="gridX">列坐标</param>
    /// <returns>同列的炮台列表</returns>
    private List<GridConnon_Cannon> GetSameColumnCannons(int gridX)
    {
        var result = new List<GridConnon_Cannon>();
        var allCannons = GridConnon_Controller.Instance.CurrentCannons;

        foreach (var cannonObj in allCannons)
        {
            if (cannonObj is GridConnon_Cannon cannon && cannon.GridX == gridX)
            {
                // 只统计仍在 GridConnon_Controller 网格中的炮台，排除已进入射击格的炮台
                if (cannon.StateManager != null && cannon.StateManager.IsInGridConnon_ControllerGrid())
                {
                    result.Add(cannon);
                }
            }
        }

        return result;
    }

    #region 效果控制方法

    /// <summary>
    /// 设置描边效果
    /// </summary>
    public void SetOutlineEffect(Color color, float width)
    {
        if (cannon.cannonRenderer == null) return;

        var propertyBlock = GetOrCreatePropertyBlock();
        // 不改变开关，只设置参数
        propertyBlock.SetColor(OutlineColorID, color);
        propertyBlock.SetFloat(OutlineWidthID, width);

        cannon.cannonRenderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 设置颜色膜层效果
    /// </summary>
    /// <param name="color">膜层颜色</param>
    /// <param name="alpha">膜层透明度</param>
    /// <param name="widthScale">膜层宽度比例（0-1）</param>
    public void SetColorOverlayEffect(Color color, float alpha, float widthScale = 1f)
    {
        if (cannon.cannonRenderer == null) return;

        var propertyBlock = GetOrCreatePropertyBlock();
        // 不改变开关，只设置参数
        propertyBlock.SetColor(OverlayColorID, color);
        propertyBlock.SetFloat(OverlayAlphaID, alpha);
        propertyBlock.SetFloat(OverlayWidthScaleID, widthScale);

        cannon.cannonRenderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 设置数字透明度
    /// </summary>
    public void SetTextAlpha(float alpha)
    {
        if (cannon.txt_Count == null) return;

        Color textColor = cannon.txt_Count.color;
        textColor.a = alpha;
        cannon.txt_Count.color = textColor;
    }

    /// <summary>
    /// 设置主颜色
    /// </summary>
    /// <param name="color">要设置的主颜色</param>
    public void SetMainColor(Color color)
    {
        if (cannon.cannonRenderer == null) return;

        var propertyBlock = GetOrCreatePropertyBlock();
        propertyBlock.SetColor("_Color", color);
        cannon.cannonRenderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 触发呼吸效果（带随机延迟）
    /// </summary>
    public void TriggerBreatheWithRandomDelay()
    {
        // 检查是否已经有呼吸效果在运行
        if (cannon.tool_JRJELLY_Simple != null && cannon.tool_JRJELLY_Simple.IsBreathing)
        {
            //Debug.Log($"[StateManager] 炮台 {cannon.ObjectId} 已经在呼吸中，跳过重复触发");
            return;
        }

        // 检查是否已经有延迟协程在运行
        if (breatheDelayCoroutine != null)
        {
            //Debug.Log($"[StateManager] 炮台 {cannon.ObjectId} 呼吸延迟协程已在运行，跳过重复触发");
            return;
        }

        // 启动新的延迟协程
        breatheDelayCoroutine = cannon.StartCoroutine(BreatheDelayCoroutine());
    }

    /// <summary>
    /// 停止呼吸效果
    /// </summary>
    /// <param name="immediateStop">是否马上停止，不进行淡出过渡</param>
    public void StopBreathe(bool immediateStop)
    {
        /*  // 停止延迟协程
         if (breatheDelayCoroutine != null)
         {
             cannon.StopCoroutine(breatheDelayCoroutine);
             breatheDelayCoroutine = null;
         } */

        // 停止呼吸
        if (cannon.tool_JRJELLY_Simple != null)
        {
            cannon.tool_JRJELLY_Simple.StopBreathe(immediateStop);
        }

        /*    // 停止白色闪烁效果
           if (whiteFlashCoroutine != null)
           {
               cannon.StopCoroutine(whiteFlashCoroutine);
               whiteFlashCoroutine = null;
           }

           // 停止其他协程
           if (rotationCoroutine != null)
           {
               cannon.StopCoroutine(rotationCoroutine);
               rotationCoroutine = null;
           }

           if (compressionCoroutine != null)
           {
               cannon.StopCoroutine(compressionCoroutine);
               compressionCoroutine = null;
           } */
    }

    /// <summary>
    /// 执行旋转动画
    /// </summary>
    public void PlayRotationAnimation()
    {
        if (rotationCoroutine != null)
        {
            cannon.StopCoroutine(rotationCoroutine);
        }
        rotationCoroutine = cannon.StartCoroutine(RotationAnimationCoroutine());
    }

    /// <summary>
    /// 执行压缩动画
    /// </summary>
    /// <param name="hasOvershoot">是否有超出阶段</param>
    public void PlayCompressionAnimation(bool hasOvershoot)
    {
        if (compressionCoroutine != null)
        {
            cannon.StopCoroutine(compressionCoroutine);
        }
        compressionCoroutine = cannon.StartCoroutine(CompressionAnimationCoroutine(hasOvershoot));
    }

    /// <summary>
    /// 执行压缩动画（自定义参数版本）
    /// </summary>
    /// <param name="hasOvershoot">是否有超出阶段</param>
    /// <param name="duration">动画持续时间</param>
    /// <param name="scaleSize">缩放比例</param>
    /// <param name="onComplete">完成回调</param>
    public void PlayCompressionAnimation(bool hasOvershoot, float duration, float scaleSize, System.Action onComplete = null)
    {
        if (compressionCoroutine != null)
        {
            cannon.StopCoroutine(compressionCoroutine);
        }
        compressionCoroutine = cannon.StartCoroutine(CustomCompressionAnimationCoroutine(hasOvershoot, duration, scaleSize, onComplete));
    }

    /// <summary>
    /// 执行白色闪烁效果
    /// </summary>
    /// <param name="duration">闪烁持续时间</param>
    /// <param name="maxAlpha">最大透明度</param>
    /// <param name="onComplete">完成回调</param>
    public void PlayWhiteFlashEffect(float duration, float maxAlpha = 0.6f, System.Action onComplete = null)
    {
        if (whiteFlashCoroutine != null)
        {
            cannon.StopCoroutine(whiteFlashCoroutine);
        }
        whiteFlashCoroutine = cannon.StartCoroutine(WhiteFlashCoroutine(duration, maxAlpha, onComplete));
    }

    /// <summary>
    /// 获取当前炮的颜色并应用淡化或加深效果
    /// </summary>
    /// <param name="fadePercent">
    /// 淡化/加深比例：
    /// - 1.0 = 不变
    /// - 0.0-1.0 = 淡化（向白色混合）
    /// - 1.0-2.0 = 加深（向黑色混合）
    /// </param>
    public Color GetFadedCannonColor(float fadePercent)
    {
        Color originalColor = cannon.HasColor() ?
            MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data?.GetColorById(cannon.ColorId) ?? Color.white :
            Color.white;

        if (fadePercent <= 1f)
        {
            // 淡化：fadePercent = 1为不淡化，0为完全淡化到白色
            return Color.Lerp(Color.white, originalColor, fadePercent);
        }
        else
        {
            // 加深：fadePercent > 1时向黑色混合，2为完全黑色
            float darkenAmount = fadePercent - 1f; // 0-1范围的加深量
            return Color.Lerp(originalColor, Color.black, darkenAmount);
        }
    }

    /// <summary>
    /// 获取当前的膜层宽度比例
    /// </summary>
    private float GetCurrentOverlayWidthScale()
    {
        if (cannon.cannonRenderer == null) return 1f;

        var propertyBlock = GetOrCreatePropertyBlock();
        if (propertyBlock == null) return 1f;

        // 尝试获取当前的OverlayWidthScale值，如果没有设置则返回默认值1f
        return propertyBlock.GetFloat(OverlayWidthScaleID);
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 获取或创建MaterialPropertyBlock
    /// </summary>
    private MaterialPropertyBlock GetOrCreatePropertyBlock()
    {
        if (cannon.cannonRenderer == null) return null;

        // 使用Cannon提供的GetCannonPropertyBlock方法，避免PropertyBlock冲突
        return cannon.GetCannonPropertyBlock();
    }

    /// <summary>
    /// 呼吸延迟协程
    /// </summary>
    private IEnumerator BreatheDelayCoroutine()
    {
        // 随机延迟0-2秒
        float delay = Random.Range(0f, 2f);
        yield return new WaitForSeconds(delay);

        // 执行呼吸
        if (cannon.tool_JRJELLY_Simple != null)
        {
            cannon.tool_JRJELLY_Simple.TriggerBreathe(Vector3.up);
        }

        breatheDelayCoroutine = null;
    }

    /// <summary>
    /// 旋转动画协程
    /// </summary>
    private IEnumerator RotationAnimationCoroutine()
    {
        Transform cannonTransform = cannon.connon_MeshObject?.transform ?? cannon.transform;
        Vector3 originalRotation = cannonTransform.eulerAngles;

        float duration = cannon.rotationAnimDuration;
        float angle = cannon.rotationAngle;

        // 第一阶段：向左旋转
        yield return RotateToAngle(cannonTransform, originalRotation.y - angle, duration * 0.33f);

        // 第二阶段：向右旋转
        yield return RotateToAngle(cannonTransform, originalRotation.y + angle, duration * 0.33f);

        // 第三阶段：回到中间
        yield return RotateToAngle(cannonTransform, originalRotation.y, duration * 0.34f);

        rotationCoroutine = null;
    }

    /// <summary>
    /// 旋转到指定角度
    /// </summary>
    private IEnumerator RotateToAngle(Transform transform, float targetY, float duration)
    {
        float startY = transform.eulerAngles.y;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;

            float currentY = Mathf.LerpAngle(startY, targetY, progress);
            transform.eulerAngles = new Vector3(transform.eulerAngles.x, currentY, transform.eulerAngles.z);

            yield return null;
        }

        // 确保最终角度准确
        transform.eulerAngles = new Vector3(transform.eulerAngles.x, targetY, transform.eulerAngles.z);
    }

    /// <summary>
    /// 压缩动画协程
    /// </summary>
    private IEnumerator CompressionAnimationCoroutine(bool hasOvershoot)
    {
        Transform cannonTransform = cannon.connon_MeshObject?.transform ?? cannon.transform;
        Vector3 originalScale = cannonTransform.localScale;

        // 从炮台配置获取参数
        float duration = cannon.compressionAnimDuration;
        float xScale = cannon.compressionXScale;
        float yScale = cannon.compressionYScale;
        float xOvershoot = cannon.compressionXOvershoot;
        float yOvershoot = cannon.compressionYOvershoot;

        if (hasOvershoot)
        {
            // 有超出的三阶段动画
            // 第一阶段：放大X轴，缩小Y轴 (33%时间)
            Vector3 firstPhaseScale = new Vector3(originalScale.x * xScale, originalScale.y * yScale, originalScale.z);
            yield return ScaleToTarget(cannonTransform, firstPhaseScale, duration * 0.33f);

            // 第二阶段：X轴回到原值减超出值，Y轴回到原值加超出值 (33%时间)
            Vector3 secondPhaseScale = new Vector3(originalScale.x - xOvershoot, originalScale.y + yOvershoot, originalScale.z);
            yield return ScaleToTarget(cannonTransform, secondPhaseScale, duration * 0.33f);

            // 第三阶段：恢复到原始缩放 (34%时间)
            yield return ScaleToTarget(cannonTransform, originalScale, duration * 0.34f);
        }
        else
        {
            // 无超出的两阶段动画
            // 第一阶段：放大X轴，缩小Y轴 (50%时间)
            Vector3 firstPhaseScale = new Vector3(originalScale.x * xScale, originalScale.y * yScale, originalScale.z);
            yield return ScaleToTarget(cannonTransform, firstPhaseScale, duration * 0.5f);

            // 第二阶段：直接恢复到原始缩放 (50%时间)
            yield return ScaleToTarget(cannonTransform, originalScale, duration * 0.5f);
        }

        compressionCoroutine = null;
    }

    /// <summary>
    /// 缩放到目标尺寸
    /// </summary>
    private IEnumerator ScaleToTarget(Transform transform, Vector3 targetScale, float duration)
    {
        Vector3 startScale = transform.localScale;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;

            Vector3 currentScale = Vector3.Lerp(startScale, targetScale, progress);
            transform.localScale = currentScale;

            yield return null;
        }

        // 确保最终缩放准确
        transform.localScale = targetScale;
    }

    /// <summary>
    /// 自定义压缩动画协程
    /// </summary>
    private IEnumerator CustomCompressionAnimationCoroutine(bool hasOvershoot, float duration, float scaleSize, System.Action onComplete)
    {
        Transform cannonTransform = cannon.connon_MeshObject?.transform ?? cannon.transform;
        Vector3 originalScale = cannonTransform.localScale;

        if (hasOvershoot)
        {
            // 有超出的三阶段动画
            // 第一阶段：放大到指定大小 (50%时间)
            Vector3 firstPhaseScale = originalScale * scaleSize;
            yield return ScaleToTarget(cannonTransform, firstPhaseScale, duration * 0.5f);

            // 第二阶段：回缩到原始大小 (50%时间)
            yield return ScaleToTarget(cannonTransform, originalScale, duration * 0.5f);
        }
        else
        {
            // 无超出的两阶段动画
            // 第一阶段：放大到指定大小 (50%时间)
            Vector3 firstPhaseScale = originalScale * scaleSize;
            yield return ScaleToTarget(cannonTransform, firstPhaseScale, duration * 0.5f);

            // 第二阶段：直接恢复到原始缩放 (50%时间)
            yield return ScaleToTarget(cannonTransform, originalScale, duration * 0.5f);
        }

        compressionCoroutine = null;

        // 调用完成回调
        onComplete?.Invoke();
    }

    /// <summary>
    /// 白色闪烁效果协程：从0过渡到maxAlpha，然后过渡回0
    /// </summary>
    /// <param name="duration">闪烁持续时间</param>
    /// <param name="maxAlpha">最大透明度</param>
    /// <param name="onComplete">完成回调</param>
    private IEnumerator WhiteFlashCoroutine(float duration, float maxAlpha, System.Action onComplete)
    {
        // 获取当前的widthScale值并保持不变
        float originalWidthScale = GetCurrentOverlayWidthScale();

        float elapsed = 0f;
        float halfDuration = duration * 0.5f;

        // 第一阶段：从0过渡到maxAlpha
        while (elapsed < halfDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / halfDuration;
            float alpha = Mathf.Lerp(0f, maxAlpha, progress);
            SetColorOverlayEffect(Color.white, alpha, originalWidthScale);
            yield return null;
        }

        // 第二阶段：从maxAlpha过渡回0
        elapsed = 0f;
        while (elapsed < halfDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / halfDuration;
            float alpha = Mathf.Lerp(maxAlpha, 0f, progress);
            SetColorOverlayEffect(Color.white, alpha, originalWidthScale);
            yield return null;
        }

        // 确保最终设置为0
        SetColorOverlayEffect(Color.white, 0f, originalWidthScale);
        onComplete?.Invoke();
        whiteFlashCoroutine = null;
    }

    #endregion

    /// <summary>
    /// 清理资源
    /// </summary>
    public void Cleanup()
    {
        StopBreathe(true);

        if (rotationCoroutine != null)
        {
            cannon.StopCoroutine(rotationCoroutine);
            rotationCoroutine = null;
        }

        if (compressionCoroutine != null)
        {
            cannon.StopCoroutine(compressionCoroutine);
            compressionCoroutine = null;
        }
    }
}