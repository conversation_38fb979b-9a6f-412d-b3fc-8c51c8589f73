using UnityEngine;

/// <summary>
/// ShootingSynthesis状态 - 射击合成状态
/// 进入：留空等待后续实现
/// 持续：留空等待后续实现
/// </summary>
public class GridConnon_CannonState_ShootingSynthesis : GridConnon_CannonStateBase
{
    public override GridConnon_Cannon_State GetStateType()
    {
        return GridConnon_Cannon_State.ShootingSynthesis;
    }

    public override void OnEnter()
    {
        //Debug.Log($"[ShootingSynthesis状态] 炮台 {cannon.ObjectId} 进入ShootingSynthesis状态");

        // TODO: 留空等待后续实现
        // 这里可以添加射击合成相关的效果
    }

    public override void OnExit()
    {
        //Debug.Log($"[ShootingSynthesis状态] 炮台 {cannon.ObjectId} 退出ShootingSynthesis状态");
    }

    public override void OnUpdate()
    {
        stateManager.cannon.UpdateRecoilSystem();
    }

    public override void OnClick()
    {
        // ShootingSynthesis状态下的点击处理（留空等待后续实现）
        //Debug.Log($"[ShootingSynthesis状态] 炮台 {cannon.ObjectId} 在射击合成状态下被点击");
    }
}