using UnityEngine;

/// <summary>
/// 问号炮ShootingSynthesis状态 - 继承基础ShootingSynthesis状态
/// 留空等待后续实现
/// </summary>
public class GridConnon_CannonState_ShootingSynthesisFinish_Question : GridConnon_CannonState_ShootingSynthesisFinish
{
    public override void OnEnter()
    {
        base.OnEnter();
        //Debug.Log($"[QuestionCannon ShootingSynthesis状态] 炮台 {cannon.ObjectId} 进入问号炮ShootingSynthesis状态");
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[QuestionCannon ShootingSynthesis状态] 炮台 {cannon.ObjectId} 退出问号炮ShootingSynthesis状态");
    }

    public override void OnUpdate()
    {
        base.OnUpdate();
        // TODO: 问号炮特殊的ShootingSynthesis更新逻辑
    }

    public override void OnClick()
    {
        base.OnClick();
        // TODO: 问号炮特殊的ShootingSynthesis点击逻辑
    }
}