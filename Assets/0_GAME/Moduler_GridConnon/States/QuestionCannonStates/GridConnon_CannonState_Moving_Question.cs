using UnityEngine;

/// <summary>
/// 问号炮Moving状态 - 继承基础Moving状态，保持贴图滚动效果
/// 从BackRow状态继承偏移状态，继续滚动贴图
/// </summary>
public class GridConnon_CannonState_Moving_Question : GridConnon_CannonState_Moving
{
    private Vector2 currentOffset = Vector2.zero;

    public override void OnEnter()
    {
        base.OnEnter();

        // 根据激活状态设置数字透明度
        if (cannon.isQuestionEffectActivated)
        {
            // 已激活：显示数字
            stateManager.SetTextAlpha(1f);
            //Debug.Log($"[问号炮台] {cannon.ObjectId} 已激活，Moving状态显示数字 (透明度=1f)");
        }
        else
        {
            // 未激活：数字半透明（保持BackRow的数字透明度）
            stateManager.SetTextAlpha(cannon.Type_Question_BackRow_textAlpha);

            // 继承现有的偏移状态，不重新初始化
            InheritOffsetFromPreviousState();
            //Debug.Log($"[问号炮台] {cannon.ObjectId} 未激活，Moving状态数字半透明 (透明度={cannon.Type_Question_BackRow_textAlpha})");
        }
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[QuestionCannon Moving状态] 炮台 {cannon.ObjectId} 退出问号炮Moving状态");
    }

    public override void OnUpdate()
    {
        base.OnUpdate();

        // 只有未激活的问号炮台才执行贴图滚动
        if (!cannon.isQuestionEffectActivated)
        {
            // 持续移动贴图偏移（与BackRow状态相同的逻辑）
            UpdateTextureOffset();
        }
    }

    public override void OnClick()
    {
        base.OnClick();
        // TODO: 问号炮特殊的Moving点击逻辑
    }



    /// <summary>
    /// 从之前状态继承偏移状态
    /// </summary>
    private void InheritOffsetFromPreviousState()
    {
        if (cannon.cannonRenderer == null || cannon.cannonRenderer.material == null) return;

        // 获取当前材质的UV偏移
        currentOffset = cannon.cannonRenderer.material.mainTextureOffset;
    }

    /// <summary>
    /// 更新贴图偏移（与BackRow状态相同的逻辑）
    /// </summary>
    private void UpdateTextureOffset()
    {
        if (cannon.cannonRenderer == null || cannon.cannonRenderer.material == null) return;

        // 根据时间和速度计算偏移
        currentOffset += cannon.Type_Question_BackRow_textureSpeed * Time.deltaTime;

        // 设置UV偏移
        cannon.cannonRenderer.material.mainTextureOffset = currentOffset;
    }
}