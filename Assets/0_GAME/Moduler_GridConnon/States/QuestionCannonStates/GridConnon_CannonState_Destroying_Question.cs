using UnityEngine;

/// <summary>
/// 问号炮Destroying状态 - 继承基础Destroying状态
/// 留空等待后续实现
/// </summary>
public class GridConnon_CannonState_Destroying_Question : GridConnon_CannonState_Destroying
{
    public override void OnEnter()
    {
        // 如果问号未清除（未激活），在销毁前强制应用激活外观并显示数字
        if (!cannon.isQuestionEffectActivated)
        {
            cannon.ApplyQuestionActivatedColor();
            stateManager.SetTextAlpha(1f);
        }

        base.OnEnter();
        //Debug.Log($"[QuestionCannon Destroying状态] 炮台 {cannon.ObjectId} 进入问号炮Destroying状态");
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[QuestionCannon Destroying状态] 炮台 {cannon.ObjectId} 退出问号炮Destroying状态");
    }

    public override void OnUpdate()
    {
        base.OnUpdate();
        // TODO: 问号炮特殊的Destroying更新逻辑
    }

    public override void OnClick()
    {
        base.OnClick();
        // TODO: 问号炮特殊的Destroying点击逻辑
    }
}