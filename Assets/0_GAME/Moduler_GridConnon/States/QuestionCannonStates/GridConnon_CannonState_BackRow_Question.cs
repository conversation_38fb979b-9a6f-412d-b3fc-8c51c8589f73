using UnityEngine;

/// <summary>
/// 问号炮BackRow状态 - 继承基础BackRow状态，添加贴图滚动效果
/// 区别：BackRow状态时，持续移动_MainTex的Offset
/// </summary>
public class GridConnon_CannonState_BackRow_Question : GridConnon_CannonState_BackRow
{
    private Vector2 currentOffset = Vector2.zero;



    public override void OnEnter()
    {
        // 如果问号效果已激活，表现得像普通炮台的BackRow状态
        if (cannon.isQuestionEffectActivated)
        {
            // 1. 设置描边效果 - 使用淡化颜色
            Color fadedOutlineColor = stateManager.GetFadedCannonColor(cannon.backRow_outlineFadePercent);
            stateManager.SetOutlineEffect(
                color: fadedOutlineColor,
                width: cannon.backRow_outlineWidth
            );

            // 2. 设置颜色膜层效果
            Color fadedOverlayColor = stateManager.GetFadedCannonColor(cannon.backRow_overlayFadePercent);
            stateManager.SetColorOverlayEffect(
                color: fadedOverlayColor,
                alpha: cannon.backRow_overlayAlpha
            );

            // 3. 显示数字（已激活问号炮台应该显示数字）
            stateManager.SetTextAlpha(1f);

            // 4. 开始呼吸效果（带随机延迟）
            stateManager.TriggerBreatheWithRandomDelay();

            // 应用已激活问号炮台的颜色
            cannon.ApplyQuestionCannonColor();

            //Debug.Log($"[问号炮台] {cannon.ObjectId} 已激活，BackRow状态表现得像普通炮台但显示数字");
            return;
        }

        // 未激活的问号炮台使用问号专用效果
        // 1. 设置问号炮专用的描边效果
        stateManager.SetOutlineEffect(
            color: cannon.Type_Question_BackRow_outlineColor,
            width: cannon.Type_Question_BackRow_outlineWidth
        );

        // 2. 设置问号炮专用的颜色膜层效果
        stateManager.SetColorOverlayEffect(
            color: cannon.Type_Question_BackRow_overlayColor,
            alpha: cannon.Type_Question_BackRow_overlayAlpha,
            widthScale: cannon.Type_Question_BackRow_overlayWidthScale
        );

        // 3. 设置问号炮专用的数字透明度
        stateManager.SetTextAlpha(cannon.Type_Question_BackRow_textAlpha);

        // 4. 设置主颜色为白色
        stateManager.SetMainColor(Color.white);

        cannon.ApplyQuestionBackRowColor();

        // 6. 开始呼吸效果（带随机延迟）
        stateManager.TriggerBreatheWithRandomDelay();

        // 7. 初始化贴图偏移
        InitializeTextureOffset();
    }

    public override void OnExit()
    {
        base.OnExit();
    }

    public override void OnUpdate()
    {
        base.OnUpdate();

        // 只有未激活的问号炮台才执行贴图滚动
        if (!cannon.isQuestionEffectActivated)
        {
            // 持续移动贴图偏移
            UpdateTextureOffset();
        }
    }

    public override void OnClick()
    {
        base.OnClick();
    }


    /// <summary>
    /// 初始化贴图偏移
    /// </summary>
    private void InitializeTextureOffset()
    {
        if (cannon.cannonRenderer == null || cannon.cannonRenderer.material == null) return;

        // 获取当前材质的UV偏移
        currentOffset = cannon.cannonRenderer.material.mainTextureOffset;
    }

    /// <summary>
    /// 更新贴图偏移
    /// </summary>
    private void UpdateTextureOffset()
    {
        if (cannon.cannonRenderer == null || cannon.cannonRenderer.material == null) return;

        // 根据时间和速度计算偏移
        currentOffset += cannon.Type_Question_BackRow_textureSpeed * Time.deltaTime;

        // 设置UV偏移
        cannon.cannonRenderer.material.mainTextureOffset = currentOffset;
    }
}