using UnityEngine;

/// <summary>
/// 问号炮Shooting状态 - 继承基础Shooting状态
/// 留空等待后续实现
/// </summary>
public class GridConnon_CannonState_Shooting_Question : GridConnon_CannonState_Shooting
{
    public override void OnEnter()
    {
        base.OnEnter();

        // 如果问号效果已激活，确保显示数字
        if (cannon.isQuestionEffectActivated)
        {
            stateManager.SetTextAlpha(1f);
            //Debug.Log($"[问号炮台] {cannon.ObjectId} 已激活，Shooting状态显示数字");
        }

        //Debug.Log($"[QuestionCannon Shooting状态] 炮台 {cannon.ObjectId} 进入问号炮Shooting状态");
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[QuestionCannon Shooting状态] 炮台 {cannon.ObjectId} 退出问号炮Shooting状态");
    }

    public override void OnUpdate()
    {
        base.OnUpdate();
        // TODO: 问号炮特殊的Shooting更新逻辑
    }

    public override void OnClick()
    {
        base.OnClick();
        // TODO: 问号炮特殊的Shooting点击逻辑
    }
}