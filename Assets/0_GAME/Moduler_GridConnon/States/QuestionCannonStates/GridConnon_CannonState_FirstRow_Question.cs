using UnityEngine;

/// <summary>
/// 问号炮FirstRow状态 - 继承基础FirstRow状态，添加特殊进入效果
/// 区别：进入FirstRow状态时立即执行特殊动画，动画期间禁用点击，置空_MainTex贴图且不恢复
/// </summary>
public class GridConnon_CannonState_FirstRow_Question : GridConnon_CannonState_FirstRow
{
    private bool isAnimationPlaying = false;

    public override void OnEnter()
    {
        // 如果问号效果已激活，表现得像普通炮台但保持数字隐藏
        if (cannon.isQuestionEffectActivated)
        {
            // 1. 设置描边效果
            stateManager.SetOutlineEffect(
                color: cannon.firstRow_outlineColor,
                width: cannon.firstRow_outlineWidth
            );

            // 2. 清除颜色膜层效果（像普通炮台一样）
            stateManager.SetColorOverlayEffect(
                color: Color.white,
                alpha: 0f,
                widthScale: 0f
            );

            // 3. 显示数字（已激活问号炮台应该显示数字）
            stateManager.SetTextAlpha(1f);

            // 4. 开始呼吸效果（带随机延迟）
            stateManager.TriggerBreatheWithRandomDelay();

            cannon.ApplyQuestionCannonColor();
            //Debug.Log($"[问号炮台] {cannon.ObjectId} 已激活，FirstRow状态显示数字");
            return;
        }

        // 未激活的问号炮台使用原有逻辑
        // 1. 设置描边效果
        stateManager.SetOutlineEffect(
            color: cannon.firstRow_outlineColor,
            width: cannon.firstRow_outlineWidth
        );

        // 2. 设置问号炮专用的颜色膜层效果
        stateManager.SetColorOverlayEffect(
            color: cannon.Type_Question_BackRow_overlayColor,
            alpha: cannon.Type_Question_BackRow_overlayAlpha,
            widthScale: cannon.Type_Question_BackRow_overlayWidthScale
        );

        // 3. 设置数字透明度为1（完全不透明）
        stateManager.SetTextAlpha(1f);

        // 4. 开始呼吸效果（带随机延迟）
        stateManager.TriggerBreatheWithRandomDelay();

        cannon.ApplyQuestionCannonColor();

        // 只在首次激活时执行问号效果
        if (!cannon.isQuestionEffectActivated)
        {
            // 标记已激活
            cannon.isQuestionEffectActivated = true;

            // 立即开始特殊进入动画
            StartFirstRowAnimation();
            //Debug.Log($"[问号炮台] {cannon.ObjectId} 首次进入FirstRow状态，执行问号效果");
        }
        else
        {
            //Debug.Log($"[问号炮台] {cannon.ObjectId} 已激活过问号效果，跳过动画");
        }
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[QuestionCannon FirstRow状态] 炮台 {cannon.ObjectId} 退出问号炮FirstRow状态");
    }

    public override void OnUpdate()
    {
        base.OnUpdate();
    }

    public override void OnClick()
    {
        if (!GridConnon_InputManager.Can_Connon_Click())
            return;

        // 如果动画正在播放且配置为禁用点击，则忽略点击
        if (isAnimationPlaying && cannon.Type_Question_FirstRow_disableClickDuringAnim)
        {
            //Debug.Log($"[QuestionCannon FirstRow状态] 炮台 {cannon.ObjectId} 动画期间点击被忽略");
            return;
        }

        base.OnClick();
    }

    /// <summary>
    /// 开始FirstRow特殊进入动画
    /// </summary>
    private void StartFirstRowAnimation()
    {
        // 置空主贴图
        ClearMainTexture();

        // 立即执行动画
        ExecuteFirstRowAnimation();
    }

    /// <summary>
    /// 置空主贴图
    /// </summary>
    private void ClearMainTexture()
    {
        if (cannon.cannonRenderer == null || cannon.cannonRenderer.material == null) return;

        // 直接设置材质的主贴图为null来置空
        cannon.cannonRenderer.material.mainTexture = null;

        //Debug.Log($"[QuestionCannon FirstRow状态] 炮台 {cannon.ObjectId} 主贴图已直接置空");
    }

    /// <summary>
    /// 执行FirstRow特殊动画（类似SynthesisFinish）
    /// </summary>
    private void ExecuteFirstRowAnimation()
    {
        isAnimationPlaying = true;

        // 使用炮台配置的问号炮FirstRow动画参数
        float animDuration = cannon.Type_Question_FirstRow_animDuration;
        float scaleSize = cannon.Type_Question_FirstRow_scaleSize;
        float whiteFlashDuration = cannon.Type_Question_FirstRow_whiteFlashDuration;

        // 同时执行白色闪烁效果和放大回缩动画
        stateManager.PlayWhiteFlashEffect(whiteFlashDuration, 0.8f);
        stateManager.PlayCompressionAnimation(false, animDuration, scaleSize, OnFirstRowAnimationComplete);

        //Debug.Log($"[QuestionCannon FirstRow状态] 炮台 {cannon.ObjectId} 开始执行FirstRow特殊动画，缩放时间:{animDuration}秒，放大比例:{scaleSize}，白色闪烁时间:{whiteFlashDuration}秒");
    }

    /// <summary>
    /// FirstRow动画完成回调
    /// </summary>
    private void OnFirstRowAnimationComplete()
    {
        isAnimationPlaying = false;

        //Debug.Log($"[QuestionCannon FirstRow状态] 炮台 {cannon.ObjectId} FirstRow特殊动画完成");
    }


}