using UnityEngine;

/// <summary>
/// 问号炮Prop_Random状态 - 继承基础Prop_Random状态
/// 留空等待后续实现
/// </summary>
public class GridConnon_CannonState_Prop_Random_Question : GridConnon_CannonState_Prop_Random
{


    public override void OnEnter()
    {
        base.OnEnter();
        //Debug.Log($"[QuestionCannon Prop_Random状态] 炮台 {cannon.ObjectId} 进入问号炮Prop_Random状态");
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[QuestionCannon Prop_Random状态] 炮台 {cannon.ObjectId} 退出问号炮Prop_Random状态");
    }

    public override void OnUpdate()
    {
        base.OnUpdate();
        // TODO: 问号炮特殊的Prop_Random更新逻辑
    }

    public override void OnClick()
    {
        base.OnClick();
        // TODO: 问号炮特殊的Prop_Random点击逻辑
    }
}