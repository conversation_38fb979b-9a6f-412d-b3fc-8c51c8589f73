using UnityEngine;

/// <summary>
/// 问号炮Prop_PickOut状态 - 继承基础Prop_PickOut状态
/// 留空等待后续实现
/// </summary>
public class GridConnon_CannonState_Prop_PickOut_Question : GridConnon_CannonState_Prop_PickOut
{
    private bool isAnimationPlaying = false;

    public override void OnEnter()
    {
        // 重置计时器
        timeInState = 0f;

        // 设置描边效果：纯白色，使用参数宽度
        stateManager.SetOutlineEffect(
            color: Color.white,
            width: cannon.propPickOut_outlineWidth
        );

        // 设置膜层透明度为0（完全透明）
        stateManager.SetColorOverlayEffect(
            color: Color.white, // 膜层颜色（透明时颜色不重要）
            alpha: 0f // 透明度设为0
        );
        // base.OnEnter();
        //Debug.Log($"[QuestionCannon Prop_PickOut状态] 炮台 {cannon.ObjectId} 进入问号炮Prop_PickOut状态");
    }

    public override void OnExit()
    {
        base.OnExit();
        //Debug.Log($"[QuestionCannon Prop_PickOut状态] 炮台 {cannon.ObjectId} 退出问号炮Prop_PickOut状态");
    }

    public override void OnUpdate()
    {
        base.OnUpdate(); // 这会调用基类的OnUpdate，包含timeInState的更新
        // TODO: 问号炮特殊的Prop_PickOut更新逻辑
    }

    public override void OnClick()
    {
        if (!GridConnon_InputManager.Can_Prop_PickOut_Click())
            return;

        // 检查是否已经过了延迟时间
        if (timeInState < CLICK_DELAY_TIME)
        {
            return; // 还未到允许点击的时间
        }
        // 1. 设置描边效果
        stateManager.SetOutlineEffect(
            color: cannon.firstRow_outlineColor,
            width: cannon.firstRow_outlineWidth
        );

        // 2. 设置问号炮专用的颜色膜层效果
        stateManager.SetColorOverlayEffect(
            color: cannon.Type_Question_BackRow_overlayColor,
            alpha: cannon.Type_Question_BackRow_overlayAlpha,
            widthScale: cannon.Type_Question_BackRow_overlayWidthScale
        );

        // 3. 设置数字透明度为1（完全不透明）
        stateManager.SetTextAlpha(1f);

        // 4. 开始呼吸效果（带随机延迟）
        stateManager.TriggerBreatheWithRandomDelay();


        cannon.ApplyQuestionCannonColor();

        // 只在首次激活时执行问号效果
        if (!cannon.isQuestionEffectActivated)
        {
            // 标记已激活
            cannon.isQuestionEffectActivated = true;

            // 立即开始特殊进入动画
            StartFirstRowAnimation();
            //Debug.Log($"[问号炮台] {cannon.ObjectId} 在拾取状态首次激活问号效果");
        }
        else
        {
            //Debug.Log($"[问号炮台] {cannon.ObjectId} 已激活过问号效果，跳过动画");
        }

        base.OnClick();
        // TODO: 问号炮特殊的Prop_PickOut点击逻辑
    }


    /// <summary>
    /// 开始FirstRow特殊进入动画
    /// </summary>
    private void StartFirstRowAnimation()
    {
        // 置空主贴图
        ClearMainTexture();

        // 立即执行动画
        ExecuteFirstRowAnimation();
    }

    /// <summary>
    /// 置空主贴图
    /// </summary>
    private void ClearMainTexture()
    {
        if (cannon.cannonRenderer == null || cannon.cannonRenderer.material == null) return;

        // 直接设置材质的主贴图为null来置空
        cannon.cannonRenderer.material.mainTexture = null;

        //Debug.Log($"[QuestionCannon FirstRow状态] 炮台 {cannon.ObjectId} 主贴图已直接置空");
    }

    /// <summary>
    /// 执行FirstRow特殊动画（类似SynthesisFinish）
    /// </summary>
    private void ExecuteFirstRowAnimation()
    {
        isAnimationPlaying = true;

        // 使用炮台配置的问号炮FirstRow动画参数
        float animDuration = cannon.Type_Question_FirstRow_animDuration;
        float scaleSize = cannon.Type_Question_FirstRow_scaleSize;
        float whiteFlashDuration = cannon.Type_Question_FirstRow_whiteFlashDuration;

        // 同时执行白色闪烁效果和放大回缩动画
        stateManager.PlayWhiteFlashEffect(whiteFlashDuration, 0.8f);
        stateManager.PlayCompressionAnimation(false, animDuration, scaleSize, OnFirstRowAnimationComplete);

        //Debug.Log($"[QuestionCannon FirstRow状态] 炮台 {cannon.ObjectId} 开始执行FirstRow特殊动画，缩放时间:{animDuration}秒，放大比例:{scaleSize}，白色闪烁时间:{whiteFlashDuration}秒");
    }

    /// <summary>
    /// FirstRow动画完成回调
    /// </summary>
    private void OnFirstRowAnimationComplete()
    {
        isAnimationPlaying = false;

        //Debug.Log($"[QuestionCannon FirstRow状态] 炮台 {cannon.ObjectId} FirstRow特殊动画完成");
    }

}