using System.Collections.Generic;

/// <summary>
/// 状态工厂接口 - 根据Cannon类型创建对应的状态集合
/// </summary>
public interface IGridConnon_StateFactory
{
    /// <summary>
    /// 创建状态字典
    /// </summary>
    /// <param name="cannon">炮台实例</param>
    /// <param name="manager">状态管理器实例</param>
    /// <returns>状态字典</returns>
    Dictionary<GridConnon_Cannon_State, GridConnon_CannonStateBase> CreateStates(
        GridConnon_Cannon cannon, GridConnon_CannonStateManager manager);
}