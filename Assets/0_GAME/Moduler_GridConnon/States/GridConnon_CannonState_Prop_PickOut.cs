using UnityEngine;

/// <summary>
/// Prop_PickOut状态 - 道具拾取状态
/// 进入：设置纯白描边和指定宽度，膜层透明度为0
/// 点击：调用状态管理器的ControllerGridToShotGrid方法
/// </summary>
public class GridConnon_CannonState_Prop_PickOut : GridConnon_CannonStateBase
{
    /// <summary>进入状态后经过的时间</summary>
    protected float timeInState = 0f;

    /// <summary>点击延迟时间（秒）</summary>
    protected const float CLICK_DELAY_TIME = 0.5f;
    public override GridConnon_Cannon_State GetStateType()
    {
        return GridConnon_Cannon_State.Prop_PickOut;
    }

    public override void OnEnter()
    {
        // 重置计时器
        timeInState = 0f;

        // 设置描边效果：纯白色，使用参数宽度
        stateManager.SetOutlineEffect(
            color: Color.white,
            width: cannon.propPickOut_outlineWidth
        );

        // 设置膜层透明度为0（完全透明）
        stateManager.SetColorOverlayEffect(
            color: Color.white, // 膜层颜色（透明时颜色不重要）
            alpha: 0f // 透明度设为0
        );

        stateManager.SetTextAlpha(1);

    }

    public override void OnExit()
    {
        //Debug.Log("[Prop_PickOut状态] 退出状态");
    }

    public override void OnUpdate()
    {
        // 更新状态计时器
        timeInState += Time.deltaTime;

        // 持续期间保持当前设置，无需额外操作
    }

    public override void OnClick()
    {
        if (!GridConnon_InputManager.Can_Prop_PickOut_Click())
            return;

        // 检查是否已经过了延迟时间
        if (timeInState < CLICK_DELAY_TIME)
        {
            return; // 还未到允许点击的时间
        }

        //Debug.Log("[Prop_PickOut状态] 检测到点击 - 调用ControllerGridToShotGrid");

        // 设置描边效果：纯白色，使用参数宽度
        stateManager.SetOutlineEffect(
            color: cannon.firstRow_outlineColor,
            width: cannon.firstRow_outlineWidth
        );

        // 直接调用状态管理器的ControllerGridToShotGrid方法
        stateManager.ControllerGridToShotGrid();

        GridConnon_Controller.Instance.Prop_PickOut_Exit();

        // 触发道具使用成功事件
        GridConnon_Controller.OnPropPickOutUsedSuccessfully?.Invoke();


        GridConnon_Controller.On_PlaySound?.Invoke("CNT_道具出列");

        GridConnon_Controller.On_PlayVib?.Invoke(MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data.Virb_Normal);
    }
}