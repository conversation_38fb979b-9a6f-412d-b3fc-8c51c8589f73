using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using System.Text;

/// <summary>
/// GridConnon 关卡转换器 - 解析压缩格式的关卡数据
/// 压缩格式说明（一行格式，使用~分隔关卡）：
/// 1|N60T120|BAD|BWATDWT|XXX|B-5@2A-5@1D~2|N80T150|ABCD|AWCTBWTD
/// 数字 = 关卡ID, | = 分隔符, ~ = 关卡分隔符
/// 第一段：关卡ID（已加密）
/// 第二段：类型HitCount (N=Normal, T=Triangle)
/// 第三段开始：每一段代表一行炮台，从Z=0开始向负方向
/// 炮台格式：字符ID+类型修饰符（W=问、T=三、WT=问三）或X=宝
/// 字符映射：A=1红, B=2黄, C=3蓝, D=4绿, E=5紫, F=6橙...
/// 链接格式：炮台-目标行@目标列
/// 注意：问号炮使用对应基础类型的HitCount（问号普通炮用Normal，问号三角炮用Triangle）
/// 加密说明：LevelText中的关卡ID已经过加密处理，真实关卡序号通过内部映射表转换
/// 支持附加关卡：Level10表示主关卡，Level10-2表示第10关的第2个附加关卡
/// </summary>
public static class GridConnon_LevelConverter
{
    // 修改缓存结构，支持(level, levelExceed)组合键 - Unity 2019兼容版本
    private static Dictionary<LevelKey, GridConnon_Data_Level> cachedLevels = new Dictionary<LevelKey, GridConnon_Data_Level>();
    private static bool isInitialized = false;
    private static Dictionary<string, int> colorNameToId = new Dictionary<string, int>();

    /// <summary>
    /// 关卡ID加密映射表 - (真实关卡序号, 附加关卡序号) -> 加密序号
    /// 支持500关×10个附加关卡的加密映射
    /// </summary>
    private static Dictionary<LevelKey, int> levelEncryptionMap = null;

    /// <summary>
    /// 关卡键结构体，用于替代元组语法以兼容Unity 2019
    /// </summary>
    [System.Serializable]
    public struct LevelKey : System.IEquatable<LevelKey>
    {
        public int level;
        public int levelExceed;

        public LevelKey(int level, int levelExceed)
        {
            this.level = level;
            this.levelExceed = levelExceed;
        }

        public bool Equals(LevelKey other)
        {
            return level == other.level && levelExceed == other.levelExceed;
        }

        public override bool Equals(object obj)
        {
            return obj is LevelKey other && Equals(other);
        }

        public override int GetHashCode()
        {
            return level.GetHashCode() ^ (levelExceed.GetHashCode() << 2);
        }

        public override string ToString()
        {
            return $"({level}, {levelExceed})";
        }
    }

    // 读取端解析期间“每行token置乱”的置乱表缓存
    private static Dictionary<int, int[]> currentRowTokenPermutationMap = null; // key: 行序号，value: 置乱

    /// <summary>
    /// 获取指定关卡数据（支持附加关卡）
    /// </summary>
    /// <param name="levelIndex">关卡索引（从1开始）</param>
    /// <param name="levelExceed">附加关卡序号（默认为1，表示主关卡，2表示Level10-2）</param>
    /// <returns>关卡数据，如果不存在返回null</returns>
    public static GridConnon_Data_Level GetLevel(int levelIndex, int levelExceed = 1)
    {
        InitializeIfNeeded();

        var cacheKey = new LevelKey(levelIndex, levelExceed);
        GridConnon_Data_Level level;
        if (cachedLevels.TryGetValue(cacheKey, out level))
        {
            return level;
        }

        //Debug.LogWarning($"关卡 {levelIndex}-{levelExceed} 不存在");
        return null;
    }

    /// <summary>
    /// 获取所有可用的关卡索引（主关卡）
    /// </summary>
    /// <returns>关卡索引列表</returns>
    public static List<int> GetAvailableLevels()
    {
        InitializeIfNeeded();
        return new List<int>(cachedLevels.Keys.Select(k => k.level).Distinct());
    }

    /// <summary>
    /// 获取所有可用的关卡信息（包含附加关卡）
    /// </summary>
    /// <returns>关卡信息列表，格式：LevelKey</returns>
    public static List<LevelKey> GetAvailableLevelDetails()
    {
        InitializeIfNeeded();
        return new List<LevelKey>(cachedLevels.Keys);
    }

    /// <summary>
    /// 获取总关卡数（只统计主关卡数量，不重复计算附加关卡）
    /// </summary>
    /// <returns>主关卡总数量</returns>
    public static int GetTotalLevelCount()
    {
        InitializeIfNeeded();

        // 只统计不同的主关卡号，不重复计算附加关卡
        var uniqueLevels = new HashSet<int>();
        foreach (var key in cachedLevels.Keys)
        {
            uniqueLevels.Add(key.level);
        }

        return uniqueLevels.Count;
    }

    /// <summary>
    /// 获取所有关卡变体的总数量（包括主关卡和所有附加关卡）
    /// </summary>
    /// <returns>所有关卡变体的总数量</returns>
    public static int GetTotalLevelVariantCount()
    {
        InitializeIfNeeded();
        return cachedLevels.Count;
    }

    /// <summary>
    /// 获取目标关卡的关卡总数（包括主关卡和所有附加关卡）
    /// </summary>
    /// <param name="targetLevel">目标关卡号</param>
    /// <returns>该关卡的总数量（主关卡+附加关卡）</returns>
    public static int GetTargetLevelCount(int targetLevel)
    {
        InitializeIfNeeded();

        // 统计指定关卡号的所有变体（主关卡和附加关卡）
        int count = 0;
        foreach (var key in cachedLevels.Keys)
        {
            if (key.level == targetLevel)
            {
                count++;
            }
        }

        return count;
    }

    /// <summary>
    /// 强制重新初始化（用于运行时更新数据）
    /// </summary>
    public static void ForceReinitialize()
    {
        isInitialized = false;
        cachedLevels.Clear();
        levelEncryptionMap = null;
        InitializeIfNeeded();
    }

    /// <summary>
    /// 初始化关卡ID加密映射表
    /// 使用伪随机算法生成500关×10个附加关卡的加密映射
    /// </summary>
    private static void InitializeLevelEncryptionMap()
    {
        if (levelEncryptionMap != null) return;

        levelEncryptionMap = new Dictionary<LevelKey, int>();
        System.Random random = new System.Random(12345);

        // 创建更大的加密序号池，支持主关卡和附加关卡
        List<int> encryptedIds = new List<int>();
        for (int i = 1; i <= 2000; i++) // 增加到2000以支持更多组合
        {
            encryptedIds.Add(i);
        }

        // 打乱加密序号池
        for (int i = encryptedIds.Count - 1; i > 0; i--)
        {
            int randomIndex = random.Next(i + 1);
            int temp = encryptedIds[i];
            encryptedIds[i] = encryptedIds[randomIndex];
            encryptedIds[randomIndex] = temp;
        }

        // 建立映射：主关卡 + 附加关卡
        int encryptedIndex = 0;

        // 为每个主关卡分配映射（1-500，每个关卡最多支持10个附加关卡）
        for (int realLevel = 1; realLevel <= 500; realLevel++)
        {
            for (int levelExceed = 1; levelExceed <= 10; levelExceed++)
            {
                if (encryptedIndex < encryptedIds.Count)
                {
                    levelEncryptionMap[new LevelKey(realLevel, levelExceed)] = encryptedIds[encryptedIndex];
                    encryptedIndex++;
                }
            }
        }

        //Debug.Log($"GridConnon关卡加密映射表初始化完成，支持500关×10个附加关卡");
    }

    /// <summary>
    /// 获取真实关卡组合对应的加密序号
    /// </summary>
    /// <param name="realLevelId">真实关卡序号</param>
    /// <param name="levelExceed">附加关卡序号（默认为1）</param>
    /// <returns>加密序号，如果不存在返回原序号</returns>
    public static int GetEncryptedLevelId(int realLevelId, int levelExceed = 1)
    {
        InitializeLevelEncryptionMap();
        var key = new LevelKey(realLevelId, levelExceed);
        int result;
        if (levelEncryptionMap.TryGetValue(key, out result))
        {
            return result;
        }
        return realLevelId;
    }

    /// <summary>
    /// 获取加密序号对应的真实关卡组合
    /// </summary>
    /// <param name="encryptedLevelId">加密序号</param>
    /// <returns>真实关卡组合，如果不存在返回LevelKey(-1, -1)</returns>
    public static LevelKey GetRealLevelId(int encryptedLevelId)
    {
        InitializeLevelEncryptionMap();

        foreach (var kvp in levelEncryptionMap)
        {
            if (kvp.Value == encryptedLevelId)
            {
                return kvp.Key;
            }
        }

        return new LevelKey(-1, -1); // 未找到对应的真实关卡序号
    }

    /// <summary>
    /// 如果需要则初始化
    /// </summary>
    private static void InitializeIfNeeded()
    {
        if (!isInitialized)
        {
            InitializeColorMap();
            ParseLevelText();
            isInitialized = true;
        }
    }

    /// <summary>
    /// 初始化颜色映射（从常量数据中获取）
    /// </summary>
    private static void InitializeColorMap()
    {
        colorNameToId.Clear();

        var constData = MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data;
        if (constData?.ColorIDMap != null)
        {
            foreach (var kvp in constData.ColorIDMap)
            {
                if (int.TryParse(kvp.Key, out int colorId))
                {
                    colorNameToId[kvp.Value] = colorId;
                    //Debug.Log($"颜色映射: {kvp.Value} -> {colorId}");
                }
            }
        }

        // 添加一些默认映射以防常量数据为空
        if (colorNameToId.Count == 0)
        {
            //Debug.LogWarning("GridConnon_Const_Data.ColorIDMap为空，使用默认颜色映射");
            colorNameToId["红"] = 1;
            colorNameToId["黄"] = 2;
            colorNameToId["蓝"] = 3;
            colorNameToId["绿"] = 4;
            colorNameToId["紫"] = 5;
            colorNameToId["橙"] = 6;
        }

        //Debug.Log($"初始化颜色映射完成，共{colorNameToId.Count}个颜色");
    }

    /// <summary>
    /// 解析LevelText字符串
    /// </summary>
    private static void ParseLevelText()
    {
        cachedLevels.Clear();

        var constData = MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data;
        if (constData == null || string.IsNullOrEmpty(constData.LevelText))
        {
            //Debug.LogWarning("GridConnon_Const_Data.LevelText为空，无法加载关卡数据");
            return;
        }

        //Debug.Log($"开始解析LevelText: {constData.LevelText}");

        string[] levels = constData.LevelText.Split(new char[] { '~' }, StringSplitOptions.RemoveEmptyEntries);

        //Debug.Log($"分割后得到 {levels.Length} 个关卡数据");

        foreach (string levelLine in levels)
        {
            if (string.IsNullOrEmpty(levelLine.Trim())) continue;

            //Debug.Log($"正在解析关卡行: {levelLine.Trim()}");

            try
            {
                GridConnon_Data_Level level = ParseSingleLevel(levelLine.Trim());
                if (level != null)
                {
                    // 从关卡数据中提取levelExceed信息
                    int levelExceed = GetLevelExceedInfo(level);
                    cachedLevels[new LevelKey(level.levelId, levelExceed)] = level;
                    //Debug.Log($"成功解析关卡 {level.levelId}-{levelExceed}，包含 {level.cannons.Count} 个炮台");
                }
                else
                {
                    //Debug.LogWarning($"解析关卡失败: {levelLine}");
                }
            }
            catch (Exception e)
            {
                //Debug.LogError($"解析关卡数据失败: {levelLine}\n错误: {e.Message}\n堆栈: {e.StackTrace}");
            }
        }

        //Debug.Log($"成功加载 {cachedLevels.Count} 个关卡");
    }

    /// <summary>
    /// 解析单个关卡数据
    /// 格式：id|E:<base64>
    /// </summary>
    private static GridConnon_Data_Level ParseSingleLevel(string levelLine)
    {
        string[] parts = levelLine.Split('|');
        if (parts.Length < 2)
        {
            return null;
        }

        // 解析关卡ID（支持Level10-2格式）
        string idPart = parts[0];
        int realLevelId;
        int levelExceed = 1;

        if (idPart.Contains("-"))
        {
            string[] idSegments = idPart.Split('-');
            if (idSegments.Length == 2 &&
                int.TryParse(idSegments[0], out int mainLevel) &&
                int.TryParse(idSegments[1], out int exceedLevel))
            {
                realLevelId = mainLevel;
                levelExceed = exceedLevel;
            }
            else
            {
                return null;
            }
        }
        else
        {
            if (!int.TryParse(idPart, out int encryptedLevelId))
            {
                return null;
            }
            var realLevelInfo = GetRealLevelId(encryptedLevelId);
            if (realLevelInfo.level == -1)
            {
                realLevelId = encryptedLevelId;
                levelExceed = 1;
            }
            else
            {
                realLevelId = realLevelInfo.level;
                levelExceed = realLevelInfo.levelExceed;
            }
        }

        // 强制要求 E: 格式
        if (!(parts.Length >= 2 && parts[1].StartsWith("E:")))
        {
            return null;
        }

        // 解码 payload：Base64 -> XOR -> 数字还原 -> 行逆置乱 -> 每行token逆置乱
        string payload;
        try
        {
            string encodedPayload = parts[1].Substring(2);
            int seedXor = ComputeDeterministicHash(idPart + "#XOR");
            payload = Base64AndXorDecode(encodedPayload, seedXor);

            int seedNum = ComputeDeterministicHash(idPart + "#NUM");
            payload = ObfuscateDigits(payload, seedNum, false);
        }
        catch
        {
            return null;
        }

        string[] payloadParts = payload.Split('|');
        if (payloadParts.Length < 1)
        {
            return null;
        }

        // 第一段为类型配置，后面为多行
        string typeHitConfigRaw = payloadParts[0];
        var typeHitCounts = ParseTypeHitCounts(typeHitConfigRaw);

        var rows = payloadParts.Skip(1).ToList();
        // 行逆置乱
        int seedRow = ComputeDeterministicHash(idPart + "#ROW");
        int[] rowPerm = GeneratePermutation(rows.Count, seedRow);
        int[] invRow = InvertPermutation(rowPerm);
        var originalRows = new List<string>(rows.Count);
        for (int i = 0; i < rows.Count; i++) originalRows.Add(rows[i]);
        for (int k = 0; k < rows.Count; k++)
        {
            originalRows[k] = rows[invRow[k]];
        }

        // 为每行准备“token置乱”的置乱表
        currentRowTokenPermutationMap = new Dictionary<int, int[]>();
        for (int r = 0; r < originalRows.Count; r++)
        {
            var tokens = SplitConvertedRowIntoTokens(originalRows[r]);
            int seedCol = ComputeDeterministicHash(idPart + "#COL#" + r);
            int[] perm = GeneratePermutation(tokens.Count, seedCol);
            currentRowTokenPermutationMap[r] = perm;
        }

        // 创建关卡对象
        GridConnon_Data_Level level = new GridConnon_Data_Level
        {
            levelId = realLevelId,
            levelName = levelExceed == 1 ? $"关卡{realLevelId}" : $"关卡{realLevelId}-{levelExceed}",
            columns = 0,
            cannons = new List<GridConnon_Data_Cannon>()
        };

        SetLevelExceedInfo(level, levelExceed);

        // 解析炮台行数据：对每行先做“token逆置乱”，再解析
        for (int rowIndex = 0; rowIndex < originalRows.Count; rowIndex++)
        {
            string rowData = originalRows[rowIndex];
            if (string.IsNullOrEmpty(rowData)) continue;

            string restoredRow = InversePerRowTokenPermutation(rowData, rowIndex, idPart);

            int zPos = -rowIndex; // 与写入端一致：第一个行是Z=0，之后-1、-2...
            ParseCannonRow(level, restoredRow, zPos, typeHitCounts);
        }

        if (level.cannons.Count > 0)
        {
            level.columns = level.cannons.Max(c => c.gridX) + 1;
        }

        ConvertLinkCoordsToCannonIds(level);

        // 清理
        currentRowTokenPermutationMap = null;

        return level;
    }

    /// <summary>
    /// 解析类型HitCount配置
    /// 格式：N60T120 表示Normal=60, Triangle=120
    /// 格式：# 表示使用默认配置 N20T40
    /// 问号炮使用对应基础类型的HitCount
    /// </summary>
    private static Dictionary<string, int> ParseTypeHitCounts(string typeHitData)
    {
        Dictionary<string, int> typeHitCounts = new Dictionary<string, int>();

        if (typeHitData == "#")
        {
            typeHitCounts["Normal"] = 20;
            typeHitCounts["Triangle"] = 40;
            return typeHitCounts;
        }

        var matches = System.Text.RegularExpressions.Regex.Matches(typeHitData, @"([A-Z])(\d+)");

        foreach (System.Text.RegularExpressions.Match match in matches)
        {
            string typeCode = match.Groups[1].Value;
            int hitCount = int.Parse(match.Groups[2].Value);

            string typeName;
            switch (typeCode)
            {
                case "N":
                    typeName = "Normal";
                    break;
                case "T":
                    typeName = "Triangle";
                    break;
                default:
                    typeName = typeCode;
                    break;
            }

            typeHitCounts[typeName] = hitCount;
        }

        if (!typeHitCounts.ContainsKey("Normal")) typeHitCounts["Normal"] = 20;
        if (!typeHitCounts.ContainsKey("Triangle")) typeHitCounts["Triangle"] = 40;

        return typeHitCounts;
    }

    /// <summary>
    /// 解析炮台行数据
    /// </summary>
    private static void ParseCannonRow(GridConnon_Data_Level level, string rowData, int zPos, Dictionary<string, int> typeHitCounts)
    {
        List<string> cannonTokens = ParseCannonTokens(rowData);

        for (int x = 0; x < cannonTokens.Count; x++)
        {
            string token = cannonTokens[x];
            if (string.IsNullOrEmpty(token)) continue;

            GridConnon_Data_Cannon cannon = ParseCannonToken(token, x, zPos, typeHitCounts);
            if (cannon != null)
            {
                level.cannons.Add(cannon);
            }
        }
    }

    /// <summary>
    /// 将行数据分割为炮台标记列表
    /// </summary>
    private static List<string> ParseCannonTokens(string rowData)
    {
        List<string> tokens = new List<string>();

        int i = 0;
        while (i < rowData.Length)
        {
            if (rowData[i] == 'X')
            {
                int start = i;
                i++;
                if (i < rowData.Length && rowData[i] == '-')
                {
                    while (i < rowData.Length && (char.IsDigit(rowData[i]) || rowData[i] == '-' || rowData[i] == '@'))
                    {
                        i++;
                    }
                }
                tokens.Add(rowData.Substring(start, i - start));
            }
            else if (char.IsLetter(rowData[i]) && rowData[i] != 'W' && rowData[i] != 'T' && rowData[i] != 'X')
            {
                int start = i;
                i++;
                if (i < rowData.Length - 1 && rowData.Substring(i, 2) == "WT")
                {
                    i += 2;
                }
                else if (i < rowData.Length && (rowData[i] == 'W' || rowData[i] == 'T'))
                {
                    i++;
                }
                if (i < rowData.Length && rowData[i] == '-')
                {
                    i++;
                    while (i < rowData.Length && (char.IsDigit(rowData[i]) || rowData[i] == '@'))
                    {
                        i++;
                    }
                }
                tokens.Add(rowData.Substring(start, i - start));
            }
            else
            {
                i++;
            }
        }

        return tokens;
    }

    /// <summary>
    /// 解析单个炮台标记
    /// </summary>
    private static GridConnon_Data_Cannon ParseCannonToken(string token, int x, int z, Dictionary<string, int> typeHitCounts)
    {
        // 处理链接信息
        string linkInfo = null;
        string cannonInfo = token;

        if (token.Contains("-"))
        {
            string[] linkParts = token.Split('-');
            cannonInfo = linkParts[0];
            if (linkParts.Length > 1)
            {
                linkInfo = linkParts[1];
            }
        }

        // 解析炮台类型和颜色
        GridConnon_ObjectType objectType;
        int colorId;
        int hitCount;

        if (cannonInfo == "X")
        {
            objectType = GridConnon_ObjectType.TreasureCannon;
            colorId = -1;
            hitCount = 1;
        }
        else
        {
            string colorChar = "";
            string typeModifier = "";

            if (cannonInfo.EndsWith("WT"))
            {
                colorChar = cannonInfo.Substring(0, cannonInfo.Length - 2);
                typeModifier = "WT";
            }
            else if (cannonInfo.EndsWith("W"))
            {
                colorChar = cannonInfo.Substring(0, cannonInfo.Length - 1);
                typeModifier = "W";
            }
            else if (cannonInfo.EndsWith("T"))
            {
                colorChar = cannonInfo.Substring(0, cannonInfo.Length - 1);
                typeModifier = "T";
            }
            else
            {
                colorChar = cannonInfo;
                typeModifier = "";
            }

            if (colorChar.Length == 1 && char.IsLetter(colorChar[0]))
            {
                colorId = colorChar[0] - 'A' + 1;
            }
            else
            {
                colorId = 1;
            }

            switch (typeModifier)
            {
                case "":
                    objectType = GridConnon_ObjectType.NormalCannon;
                    hitCount = typeHitCounts.ContainsKey("Normal") ? typeHitCounts["Normal"] : 60;
                    break;
                case "T":
                    objectType = GridConnon_ObjectType.TriangleCannon;
                    hitCount = typeHitCounts.ContainsKey("Triangle") ? typeHitCounts["Triangle"] : 120;
                    break;
                case "W":
                    objectType = GridConnon_ObjectType.QuestionNormalCannon;
                    hitCount = typeHitCounts.ContainsKey("Normal") ? typeHitCounts["Normal"] : 60;
                    break;
                case "WT":
                    objectType = GridConnon_ObjectType.QuestionTriangleCannon;
                    hitCount = typeHitCounts.ContainsKey("Triangle") ? typeHitCounts["Triangle"] : 120;
                    break;
                default:
                    objectType = GridConnon_ObjectType.NormalCannon;
                    hitCount = typeHitCounts.ContainsKey("Normal") ? typeHitCounts["Normal"] : 60;
                    break;
            }
        }

        GridConnon_Data_Cannon cannon = new GridConnon_Data_Cannon(x, z, objectType, colorId, hitCount);

        if (!string.IsNullOrEmpty(linkInfo))
        {
            var linkMatch = System.Text.RegularExpressions.Regex.Match(linkInfo, @"(\d+)@(\d+)");
            if (linkMatch.Success)
            {
                int targetRow = int.Parse(linkMatch.Groups[1].Value);
                int targetCol = int.Parse(linkMatch.Groups[2].Value);

                int targetX = targetCol - 1;
                int targetZ = -(targetRow - 2);

                string targetCoord = $"{targetX}_{targetZ}";
                cannon.linkedCannonIds.Add(targetCoord);
            }
        }

        cannon.cannonId = x * 1000 + Mathf.Abs(z);

        return cannon;
    }

    /// <summary>
    /// 将链接坐标转换为实际炮台ID
    /// </summary>
    private static void ConvertLinkCoordsToCannonIds(GridConnon_Data_Level level)
    {
        if (level?.cannons == null) return;

        Dictionary<string, int> coordToCannonId = new Dictionary<string, int>();
        foreach (var cannon in level.cannons)
        {
            string coord = $"{cannon.gridX}_{cannon.gridZ}";
            coordToCannonId[coord] = cannon.cannonId;
        }

        foreach (var cannon in level.cannons)
        {
            if (cannon.linkedCannonIds == null || cannon.linkedCannonIds.Count == 0) continue;

            List<string> newLinkedIds = new List<string>();
            foreach (string coordId in cannon.linkedCannonIds)
            {
                if (coordToCannonId.TryGetValue(coordId, out int targetCannonId))
                {
                    newLinkedIds.Add(targetCannonId.ToString());
                }
            }

            cannon.linkedCannonIds = newLinkedIds;
        }
    }

    // levelExceed 存储
    private static Dictionary<GridConnon_Data_Level, int> levelExceedInfo = new Dictionary<GridConnon_Data_Level, int>();
    private static void SetLevelExceedInfo(GridConnon_Data_Level level, int levelExceed)
    {
        levelExceedInfo[level] = levelExceed;
    }
    private static int GetLevelExceedInfo(GridConnon_Data_Level level)
    {
        int result;
        if (levelExceedInfo.TryGetValue(level, out result))
        {
            return result;
        }
        return 1;
    }

    // ===== 高级混淆读取端辅助方法 =====
    private static int ComputeDeterministicHash(string input)
    {
        unchecked
        {
            const uint fnvOffset = 2166136261;
            const uint fnvPrime = 16777619;
            uint hash = fnvOffset;
            foreach (char c in input)
            {
                hash ^= c;
                hash *= fnvPrime;
            }
            return (int)(hash & 0x7FFFFFFF);
        }
    }

    private static int[] GeneratePermutation(int length, int seed)
    {
        var perm = Enumerable.Range(0, length).ToArray();
        if (length <= 1) return perm;
        uint state = (uint)seed;
        for (int i = length - 1; i > 0; i--)
        {
            state = state * 1664525u + 1013904223u;
            int j = (int)(state % (uint)(i + 1));
            int tmp = perm[i];
            perm[i] = perm[j];
            perm[j] = tmp;
        }
        return perm;
    }

    private static int[] InvertPermutation(int[] perm)
    {
        var inv = new int[perm.Length];
        for (int i = 0; i < perm.Length; i++) inv[perm[i]] = i;
        return inv;
    }

    private static string Base64AndXorDecode(string encoded, int seed)
    {
        var bytes = Convert.FromBase64String(encoded);
        var key = GenerateXorKey(bytes.Length, seed);
        for (int i = 0; i < bytes.Length; i++) bytes[i] ^= key[i];
        return Encoding.UTF8.GetString(bytes);
    }

    private static byte[] GenerateXorKey(int length, int seed)
    {
        var key = new byte[length];
        uint state = (uint)seed;
        for (int i = 0; i < length; i++)
        {
            state = state * 1664525u + 1013904223u;
            key[i] = (byte)((state >> 16) & 0xFF);
        }
        return key;
    }

    private static string ObfuscateDigits(string text, int seed, bool encode)
    {
        var map = BuildDigitMap(seed, encode);
        var sb = new StringBuilder(text.Length);
        foreach (char c in text)
        {
            if (c >= '0' && c <= '9') sb.Append(map[c - '0']); else sb.Append(c);
        }
        return sb.ToString();
    }

    private static char[] BuildDigitMap(int seed, bool encode)
    {
        var digits = Enumerable.Range(0, 10).ToArray();
        uint state = (uint)seed;
        for (int i = digits.Length - 1; i > 0; i--)
        {
            state = state * 1664525u + 1013904223u;
            int j = (int)(state % (uint)(i + 1));
            int tmp = digits[i];
            digits[i] = digits[j];
            digits[j] = tmp;
        }
        var map = new char[10];
        if (encode)
        {
            for (int d = 0; d < 10; d++) map[d] = (char)('0' + digits[d]);
        }
        else
        {
            var inv = new int[10];
            for (int d = 0; d < 10; d++) inv[digits[d]] = d;
            for (int d = 0; d < 10; d++) map[d] = (char)('0' + inv[d]);
        }
        return map;
    }

    private static List<string> SplitConvertedRowIntoTokens(string rowData)
    {
        var tokens = new List<string>();
        int i = 0;
        while (i < rowData.Length)
        {
            if (rowData[i] == 'X')
            {
                int start = i;
                i++;
                if (i < rowData.Length && rowData[i] == '-')
                {
                    while (i < rowData.Length && (char.IsDigit(rowData[i]) || rowData[i] == '-' || rowData[i] == '@')) i++;
                }
                tokens.Add(rowData.Substring(start, i - start));
            }
            else if (char.IsLetter(rowData[i]) && rowData[i] != 'W' && rowData[i] != 'T' && rowData[i] != 'X')
            {
                int start = i;
                i++;
                if (i < rowData.Length - 1 && rowData.Substring(i, 2) == "WT") i += 2;
                else if (i < rowData.Length && (rowData[i] == 'W' || rowData[i] == 'T')) i++;
                if (i < rowData.Length && rowData[i] == '-')
                {
                    i++;
                    while (i < rowData.Length && (char.IsDigit(rowData[i]) || rowData[i] == '@')) i++;
                }
                tokens.Add(rowData.Substring(start, i - start));
            }
            else
            {
                i++;
            }
        }
        return tokens;
    }

    private static string InversePerRowTokenPermutation(string rowData, int rowIndex, string idPart)
    {
        var tokens = SplitConvertedRowIntoTokens(rowData);
        if (tokens.Count <= 1) return rowData;
        int[] perm = currentRowTokenPermutationMap != null && currentRowTokenPermutationMap.TryGetValue(rowIndex, out var p) ? p : GeneratePermutation(tokens.Count, ComputeDeterministicHash(idPart + "#COL#" + rowIndex));
        int[] inv = InvertPermutation(perm);
        var restored = new List<string>(tokens);
        for (int i = 0; i < tokens.Count; i++) restored[i] = tokens[inv[i]];
        return string.Concat(restored);
    }
}