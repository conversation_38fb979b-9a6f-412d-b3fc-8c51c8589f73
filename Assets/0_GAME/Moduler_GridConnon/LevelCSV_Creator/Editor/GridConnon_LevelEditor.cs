using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using UnityEditor;
using UnityEngine;

/// <summary>
/// 关卡数据对，用于替代元组语法以兼容Unity 2019
/// </summary>
[System.Serializable]
public class LevelDataPair
{
    public string levelData;
    public int encryptedId;
}

/// <summary>
/// 关卡信息，用于替代元组语法以兼容Unity 2019
/// </summary>
[System.Serializable]
public class LevelInfo
{
    public int mainLevel;
    public int levelExceed;
}

/// <summary>
/// GridConnon 关卡转换编辑器
/// 将CSV格式的关卡数据转换为压缩格式的一行字符串
/// </summary>
public class GridConnon_LevelEditor : EditorWindow
{
    private string colorMapFilePath = "";
    private string csvFolderPath = "";
    private string outputText = "";
    private Vector2 outputScrollPos;
    private Dictionary<string, int> colorNameToId = new Dictionary<string, int>();

    // 记住路径的Key
    private const string COLOR_MAP_PATH_KEY = "GridConnon_ColorMapPath";
    private const string CSV_FOLDER_PATH_KEY = "GridConnon_CSVFolderPath";

    [MenuItem("GridConnon/关卡转换编辑器")]
    public static void ShowWindow()
    {
        var window = GetWindow<GridConnon_LevelEditor>("GridConnon关卡转换编辑器");
        window.minSize = new Vector2(600, 500);
    }

    private void OnEnable()
    {
        // 加载上次保存的路径
        colorMapFilePath = EditorPrefs.GetString(COLOR_MAP_PATH_KEY, "");
        csvFolderPath = EditorPrefs.GetString(CSV_FOLDER_PATH_KEY, "");

        // 如果颜色映射文件存在，自动加载
        if (!string.IsNullOrEmpty(colorMapFilePath) && File.Exists(colorMapFilePath))
        {
            LoadColorMap();
        }
    }

    private void OnGUI()
    {
        GUILayout.Label("GridConnon 关卡转换编辑器", EditorStyles.boldLabel);
        GUILayout.Space(10);

        DrawColorMapSection();
        GUILayout.Space(10);

        DrawCSVFolderSection();
        GUILayout.Space(10);

        DrawConvertSection();
        GUILayout.Space(10);

        DrawOutputSection();
        GUILayout.Space(10);


    }

    /// <summary>
    /// 绘制颜色对应表部分
    /// </summary>
    private void DrawColorMapSection()
    {
        GUILayout.Label("1. 颜色对应表", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("颜色对应表文件:", GUILayout.Width(120));

        if (GUILayout.Button("选择文件", GUILayout.Width(80)))
        {
            string newPath = EditorUtility.OpenFilePanel("选择颜色对应表文件",
                string.IsNullOrEmpty(colorMapFilePath) ? "" : Path.GetDirectoryName(colorMapFilePath), "txt");
            if (!string.IsNullOrEmpty(newPath))
            {
                colorMapFilePath = newPath;
                EditorPrefs.SetString(COLOR_MAP_PATH_KEY, colorMapFilePath); // 保存路径
                LoadColorMap();
            }
        }

        EditorGUILayout.EndHorizontal();

        if (!string.IsNullOrEmpty(colorMapFilePath))
        {
            EditorGUILayout.LabelField("文件路径:", colorMapFilePath);

            /*   if (colorNameToId.Count > 0)
              {
                  EditorGUILayout.LabelField("颜色映射:");
                  foreach (var kvp in colorNameToId)
                  {
                      EditorGUILayout.LabelField($"  {kvp.Key} -> {kvp.Value}");
                  }
              } */
        }

        // 拖拽区域
        var dropRect = GUILayoutUtility.GetRect(0, 50, GUILayout.ExpandWidth(true));
        GUI.Box(dropRect, "拖拽颜色对应表文件到这里");

        if (dropRect.Contains(Event.current.mousePosition))
        {
            if (Event.current.type == EventType.DragUpdated)
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                Event.current.Use();
            }
            else if (Event.current.type == EventType.DragPerform)
            {
                DragAndDrop.AcceptDrag();
                if (DragAndDrop.paths.Length > 0 && DragAndDrop.paths[0].EndsWith(".txt"))
                {
                    colorMapFilePath = DragAndDrop.paths[0];
                    EditorPrefs.SetString(COLOR_MAP_PATH_KEY, colorMapFilePath); // 保存路径
                    LoadColorMap();
                }
                Event.current.Use();
            }
        }
    }

    /// <summary>
    /// 绘制CSV文件夹部分
    /// </summary>
    private void DrawCSVFolderSection()
    {
        GUILayout.Label("2. CSV关卡文件夹", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("CSV文件夹:", GUILayout.Width(120));

        if (GUILayout.Button("选择文件夹", GUILayout.Width(80)))
        {
            string newPath = EditorUtility.OpenFolderPanel("选择CSV关卡文件夹",
                string.IsNullOrEmpty(csvFolderPath) ? "" : csvFolderPath, "");
            if (!string.IsNullOrEmpty(newPath))
            {
                csvFolderPath = newPath;
                EditorPrefs.SetString(CSV_FOLDER_PATH_KEY, csvFolderPath); // 保存路径
            }
        }

        EditorGUILayout.EndHorizontal();

        if (!string.IsNullOrEmpty(csvFolderPath))
        {
            EditorGUILayout.LabelField("文件夹路径:", csvFolderPath);

            // 显示找到的CSV文件
            string[] csvFiles = Directory.GetFiles(csvFolderPath, "*.csv");
            if (csvFiles.Length > 0)
            {
                EditorGUILayout.LabelField($"找到 {csvFiles.Length} 个CSV文件:");
                /*      foreach (string csvFile in csvFiles)
                      {
                          EditorGUILayout.LabelField($"  {Path.GetFileName(csvFile)}");
                      } */
            }
            else
            {
                EditorGUILayout.HelpBox("文件夹中没有找到CSV文件", MessageType.Warning);
            }
        }

        // 拖拽区域
        var dropRect = GUILayoutUtility.GetRect(0, 50, GUILayout.ExpandWidth(true));
        GUI.Box(dropRect, "拖拽CSV文件夹到这里");

        if (dropRect.Contains(Event.current.mousePosition))
        {
            if (Event.current.type == EventType.DragUpdated)
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                Event.current.Use();
            }
            else if (Event.current.type == EventType.DragPerform)
            {
                DragAndDrop.AcceptDrag();
                if (DragAndDrop.paths.Length > 0 && Directory.Exists(DragAndDrop.paths[0]))
                {
                    csvFolderPath = DragAndDrop.paths[0];
                    EditorPrefs.SetString(CSV_FOLDER_PATH_KEY, csvFolderPath); // 保存路径
                }
                Event.current.Use();
            }
        }
    }

    /// <summary>
    /// 绘制转换部分
    /// </summary>
    private void DrawConvertSection()
    {
        GUILayout.Label("3. 转换操作", EditorStyles.boldLabel);

        bool canConvert = !string.IsNullOrEmpty(colorMapFilePath) &&
                         !string.IsNullOrEmpty(csvFolderPath) &&
                         colorNameToId.Count > 0;

        GUI.enabled = canConvert;
        if (GUILayout.Button("开始转换", GUILayout.Height(30)))
        {
            ConvertCSVToCompressedFormat();
        }
        GUI.enabled = true;

        if (!canConvert)
        {
            EditorGUILayout.HelpBox("请先选择颜色对应表文件和CSV文件夹", MessageType.Info);
        }
    }

    /// <summary>
    /// 绘制输出部分
    /// </summary>
    private void DrawOutputSection()
    {
        GUILayout.Label("4. 转换结果 (一行格式)", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("压缩格式关卡数据 (使用~分隔关卡):");

        GUI.enabled = !string.IsNullOrEmpty(outputText);
        if (GUILayout.Button("复制到剪贴板", GUILayout.Width(120)))
        {
            EditorGUIUtility.systemCopyBuffer = outputText;
            ShowNotification(new GUIContent("已复制到剪贴板!"));
        }
        GUI.enabled = true;

        EditorGUILayout.EndHorizontal();

        outputScrollPos = EditorGUILayout.BeginScrollView(outputScrollPos, GUILayout.Height(200));
        outputText = EditorGUILayout.TextArea(outputText, GUILayout.ExpandHeight(true));
        EditorGUILayout.EndScrollView();
    }

    /// <summary>
    /// 加载颜色对应表
    /// </summary>
    private void LoadColorMap()
    {
        colorNameToId.Clear();

        try
        {
            string[] lines = File.ReadAllLines(colorMapFilePath);
            foreach (string line in lines)
            {
                if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#")) continue;

                string[] parts = line.Split(':');
                if (parts.Length == 2)
                {
                    string colorName = parts[0].Trim();
                    string colorValue = parts[1].Trim();

                    // 尝试将colorValue解析为ID
                    if (int.TryParse(colorValue, out int colorId))
                    {
                        colorNameToId[colorName] = colorId;
                        Debug.Log($"颜色映射加载: {colorName} -> {colorId}");
                    }
                    else
                    {
                        // 如果不是数字，使用字符串哈希作为ID
                        int hashId = Mathf.Abs(colorValue.GetHashCode() % 100) + 1;
                        colorNameToId[colorName] = hashId;
                        Debug.LogWarning($"颜色值不是数字，使用哈希: {colorName} -> {hashId}");
                    }
                }
            }

            Debug.Log($"成功加载 {colorNameToId.Count} 个颜色映射");
        }
        catch (Exception e)
        {
            Debug.LogError($"加载颜色对应表失败: {e.Message}");
        }
    }

    /// <summary>
    /// 转换CSV到压缩格式 - 输出为一行（永远启用高级混淆，输出 id|E:<base64>）
    /// </summary>
    private void ConvertCSVToCompressedFormat()
    {
        try
        {
            // 存储关卡数据和对应的加密序号
            List<LevelDataPair> levelDataList = new List<LevelDataPair>();

            string[] csvFiles = Directory.GetFiles(csvFolderPath, "*.csv");
            Array.Sort(csvFiles, (a, b) =>
            {
                // 按Level1, Level2, Level10-2... 的顺序排序（支持附加关卡）
                string nameA = Path.GetFileNameWithoutExtension(a);
                string nameB = Path.GetFileNameWithoutExtension(b);

                if (nameA.StartsWith("Level") && nameB.StartsWith("Level"))
                {
                    // 解析Level格式，支持Level10-2
                    var levelInfoA = ParseLevelFileName(nameA);
                    var levelInfoB = ParseLevelFileName(nameB);

                    if (levelInfoA != null && levelInfoB != null)
                    {
                        // 先按主关卡号排序，再按附加关卡号排序
                        int mainComparison = levelInfoA.mainLevel.CompareTo(levelInfoB.mainLevel);
                        if (mainComparison == 0)
                        {
                            return levelInfoA.levelExceed.CompareTo(levelInfoB.levelExceed);
                        }
                        return mainComparison;
                    }
                }

                return string.Compare(nameA, nameB, StringComparison.OrdinalIgnoreCase);
            });



            for (int i = 0; i < csvFiles.Length; i++)
            {
                string csvFile = csvFiles[i];
                string fileName = Path.GetFileNameWithoutExtension(csvFile);

                int realLevelId = i + 1;
                int levelExceed = 1;
                string levelIdentifier = "";

                // 解析文件名支持Level10-2格式
                if (fileName.StartsWith("Level"))
                {
                    string levelPart = fileName.Substring(5); // 去掉"Level"前缀

                    if (levelPart.Contains("-"))
                    {
                        // 处理Level10-2格式
                        string[] parts = levelPart.Split('-');
                        if (parts.Length == 2 &&
                            int.TryParse(parts[0], out int mainLevel) &&
                            int.TryParse(parts[1], out int exceedLevel))
                        {
                            realLevelId = mainLevel;
                            levelExceed = exceedLevel;
                            levelIdentifier = $"{realLevelId}-{levelExceed}";
                        }
                        else
                        {
                            Debug.LogWarning($"无法解析GridConnon附加关卡文件名: {fileName}，使用默认序号");
                            levelIdentifier = realLevelId.ToString();
                        }
                    }
                    else
                    {
                        // 处理标准Level10格式
                        if (int.TryParse(levelPart, out int parsedId))
                        {
                            realLevelId = parsedId;
                        }
                        levelIdentifier = realLevelId.ToString();
                    }
                }
                else
                {
                    levelIdentifier = realLevelId.ToString();
                }

                // 对于所有关卡（包括附加关卡）都进行加密处理
                string levelId = GridConnon_LevelConverter.GetEncryptedLevelId(realLevelId, levelExceed).ToString();

                string compressedLevel = ConvertSingleCSV(csvFile, levelId);
                if (!string.IsNullOrEmpty(compressedLevel))
                {
                    // 对于排序，使用加密后的权重（包括附加关卡）
                    int sortWeight = GridConnon_LevelConverter.GetEncryptedLevelId(realLevelId, levelExceed);
                    levelDataList.Add(new LevelDataPair { levelData = compressedLevel, encryptedId = sortWeight });
                }
                else
                {
                    Debug.LogError($"转换失败: {fileName}");
                }
            }

            // 按加密序号排序，实现关卡顺序的打乱
            levelDataList.Sort((a, b) => a.encryptedId.CompareTo(b.encryptedId));

            // 提取排序后的关卡数据
            List<string> levelStrings = levelDataList.Select(x => x.levelData).ToList();

            // 使用~连接所有关卡为一行
            string joinedLevels = string.Join("~", levelStrings);
            outputText = "\"LevelText\": " + "\"" + joinedLevels + "\"";
        }
        catch (Exception e)
        {
            Debug.LogError($"转换失败: {e.Message}");
            EditorUtility.DisplayDialog("转换失败", e.Message, "确定");
        }
    }

    /// <summary>
    /// 转换单个CSV文件（写出 id|E:<base64>，payload=类型配置+行；先行内token置乱，再行置乱，然后数字混淆+XOR+Base64）
    /// </summary>
    private string ConvertSingleCSV(string csvFilePath, string levelId)
    {
        try
        {
            string[] lines = File.ReadAllLines(csvFilePath);
            if (lines.Length < 2)
            {
                Debug.LogWarning($"CSV文件格式错误: {csvFilePath}");
                return "";
            }

            // 解析第一行：类型HitCount配置（字符串形式即可）
            string typeHitConfig = ParseTypeHitConfig(lines[0]);

            // 解析关卡内容行
            List<string> cannonRows = new List<string>();

            for (int i = 1; i < lines.Length; i++)
            {
                string line = lines[i].Trim();
                if (string.IsNullOrEmpty(line)) continue;

                string convertedRow = CompressCannonRow(line); // 转换为ID串

                // 行内 token 置乱：基于 levelId 和行索引的种子
                var tokens = SplitConvertedRowIntoTokens(convertedRow);
                if (tokens.Count > 1)
                {
                    int seedCol = ComputeDeterministicHash(levelId + "#COL#" + (i - 1));
                    int[] perm = GeneratePermutation(tokens.Count, seedCol);
                    tokens = ApplyPermutationToList(tokens, perm);
                    convertedRow = string.Concat(tokens);
                }

                cannonRows.Add(convertedRow);
            }

            // 行置乱
            int seedRow = ComputeDeterministicHash(levelId + "#ROW");
            int[] rowPerm = GeneratePermutation(cannonRows.Count, seedRow);
            var permutedRows = ApplyPermutationToList(cannonRows, rowPerm);

            // 拼payload：类型配置 + 行
            string payload = typeHitConfig + "|" + string.Join("|", permutedRows);

            // 数字域混淆
            int seedNum = ComputeDeterministicHash(levelId + "#NUM");
            payload = ObfuscateDigits(payload, seedNum, true);

            // XOR + Base64
            int seedXor = ComputeDeterministicHash(levelId + "#XOR");
            string encoded = XorAndBase64Encode(payload, seedXor);

            // 写出 E: 格式
            StringBuilder result = new StringBuilder();
            result.Append($"{levelId}|E:{encoded}");
            return result.ToString();
        }
        catch (Exception e)
        {
            Debug.LogError($"转换CSV文件失败 {csvFilePath}: {e.Message}");
            return "";
        }
    }

    /// <summary>
    /// 解析类型HitCount配置
    /// 格式：Normal:60,Triangle:120 -> N60T120
    /// 格式：Normal:20,Triangle:40 -> # (默认配置压缩)
    /// </summary>
    /// <param name="configLine">配置行</param>
    /// <returns>压缩的类型配置</returns>
    private string ParseTypeHitConfig(string configLine)
    {
        StringBuilder result = new StringBuilder();
        Dictionary<string, int> typeHitCounts = new Dictionary<string, int>();

        string[] parts = configLine.Split(',');
        foreach (string part in parts)
        {
            string[] keyValue = part.Split(':');
            if (keyValue.Length == 2)
            {
                string typeName = keyValue[0].Trim();
                string hitCountStr = keyValue[1].Trim();

                if (int.TryParse(hitCountStr, out int hitCount))
                {
                    typeHitCounts[typeName] = hitCount;
                }

                string typeCode;
                switch (typeName)
                {
                    case "Normal":
                        typeCode = "N";
                        break;
                    case "Triangle":
                        typeCode = "T";
                        break;
                    default:
                        typeCode = typeName.Substring(0, 1).ToUpper();
                        break;
                }

                result.Append($"{typeCode}{hitCountStr}");
            }
        }

        // 检查是否为默认配置 N20T40
        bool isDefaultConfig = typeHitCounts.ContainsKey("Normal") &&
                              typeHitCounts.ContainsKey("Triangle") &&
                              typeHitCounts["Normal"] == 20 &&
                              typeHitCounts["Triangle"] == 40 &&
                              typeHitCounts.Count == 2;

        if (isDefaultConfig)
        {
            return "#"; // 使用#代表默认配置，节省空间
        }

        return result.ToString();
    }

    /// <summary>
    /// 压缩炮台行数据，将中文颜色转换为字符，修饰符转换为英文
    /// 格式：黄，红，绿 -> BAD (黄=B,红=A,绿=D)  
    /// 格式：黄问，红三，绿问三 -> BWATDWT (问=W,三=T,问三=WT)
    /// 格式：宝，宝，宝 -> XXX (宝=X，避免与B字母冲突)
    /// 格式：黄-5@2，红-5@1，绿 -> B-5@2A-5@1D
    /// </summary>
    /// <param name="rowLine">行数据</param>
    /// <returns>压缩的行数据</returns>
    private string CompressCannonRow(string rowLine)
    {
        string[] cannons = rowLine.Split(new char[] { '，', ',' }, StringSplitOptions.RemoveEmptyEntries);
        StringBuilder result = new StringBuilder();

        foreach (string cannon in cannons)
        {
            string trimmedCannon = cannon.Trim();
            if (!string.IsNullOrEmpty(trimmedCannon))
            {
                string convertedCannon = ConvertCannonToIdFormat(trimmedCannon);
                result.Append(convertedCannon);
            }
        }

        return result.ToString();
    }

    /// <summary>
    /// 将单个炮台的中文描述转换为ID格式
    /// </summary>
    /// <param name="cannonText">炮台文本，如：黄、黄问、黄三、黄问三、宝、黄-5@2</param>
    /// <returns>转换后的ID格式</returns>
    private string ConvertCannonToIdFormat(string cannonText)
    {
        // 处理链接信息
        string linkInfo = "";
        string cannonInfo = cannonText;

        if (cannonText.Contains("-"))
        {
            string[] linkParts = cannonText.Split('-');
            cannonInfo = linkParts[0];
            if (linkParts.Length > 1)
            {
                linkInfo = "-" + linkParts[1];
            }
        }

        // 如果是宝箱，返回X
        if (cannonInfo == "宝")
        {
            return "X" + linkInfo;
        }

        // 解析颜色和类型修饰符
        string colorName = "";
        string typeModifier = "";

        if (cannonInfo.EndsWith("问三"))
        {
            colorName = cannonInfo.Substring(0, cannonInfo.Length - 2);
            typeModifier = "WT"; // 问三 -> WT
        }
        else if (cannonInfo.EndsWith("问"))
        {
            colorName = cannonInfo.Substring(0, cannonInfo.Length - 1);
            typeModifier = "W"; // 问 -> W
        }
        else if (cannonInfo.EndsWith("三"))
        {
            colorName = cannonInfo.Substring(0, cannonInfo.Length - 1);
            typeModifier = "T"; // 三 -> T
        }
        else
        {
            colorName = cannonInfo;
            typeModifier = "";
        }

        // 获取颜色ID并转换为字符
        int colorId = 1; // 默认颜色
        if (colorNameToId.TryGetValue(colorName, out int foundColorId))
        {
            colorId = foundColorId;
        }
        else
        {
            Debug.LogWarning($"未找到颜色映射: {colorName}，使用默认颜色ID=1");
        }

        // 将颜色ID转换为字符（1->A, 2->B, 3->C...）
        char colorChar = (char)('A' + colorId - 1);

        // 构建结果
        return colorChar + typeModifier + linkInfo;
    }

    /// <summary>
    /// 解析关卡文件名，支持Level10-2格式
    /// </summary>
    /// <param name="fileName">文件名，如Level10或Level10-2</param>
    /// <returns>解析结果LevelInfo，如果解析失败返回null</returns>
    private LevelInfo ParseLevelFileName(string fileName)
    {
        if (!fileName.StartsWith("Level")) return null;

        string levelPart = fileName.Substring(5); // 去掉"Level"前缀

        if (levelPart.Contains("-"))
        {
            // Level10-2格式
            string[] parts = levelPart.Split('-');
            if (parts.Length == 2 &&
                int.TryParse(parts[0], out int mainLevel) &&
                int.TryParse(parts[1], out int levelExceed))
            {
                return new LevelInfo { mainLevel = mainLevel, levelExceed = levelExceed };
            }
        }
        else
        {
            // Level10格式
            if (int.TryParse(levelPart, out int mainLevel))
            {
                return new LevelInfo { mainLevel = mainLevel, levelExceed = 1 }; // 默认为主关卡
            }
        }

        return null;
    }

    // ===== 高级混淆辅助方法（与TileCube保持一致策略） =====

    // 将转换后的行串切分为 token 列表（用于置乱）
    private List<string> SplitConvertedRowIntoTokens(string rowData)
    {
        var tokens = new List<string>();
        int i = 0;
        while (i < rowData.Length)
        {
            if (rowData[i] == 'X')
            {
                int start = i;
                i++;
                if (i < rowData.Length && rowData[i] == '-')
                {
                    // 链接部分：-<digits>@<digits>
                    while (i < rowData.Length && (char.IsDigit(rowData[i]) || rowData[i] == '-' || rowData[i] == '@')) i++;
                }
                tokens.Add(rowData.Substring(start, i - start));
            }
            else if (char.IsLetter(rowData[i]) && rowData[i] != 'W' && rowData[i] != 'T' && rowData[i] != 'X')
            {
                int start = i;
                i++; // 颜色字母
                if (i < rowData.Length - 1 && rowData.Substring(i, 2) == "WT") i += 2;
                else if (i < rowData.Length && (rowData[i] == 'W' || rowData[i] == 'T')) i++;
                if (i < rowData.Length && rowData[i] == '-')
                {
                    i++;
                    while (i < rowData.Length && (char.IsDigit(rowData[i]) || rowData[i] == '@')) i++;
                }
                tokens.Add(rowData.Substring(start, i - start));
            }
            else
            {
                i++;
            }
        }
        return tokens;
    }

    private int ComputeDeterministicHash(string input)
    {
        unchecked
        {
            const uint fnvOffset = 2166136261;
            const uint fnvPrime = 16777619;
            uint hash = fnvOffset;
            foreach (char c in input)
            {
                hash ^= c;
                hash *= fnvPrime;
            }
            return (int)(hash & 0x7FFFFFFF);
        }
    }

    private int[] GeneratePermutation(int length, int seed)
    {
        var perm = Enumerable.Range(0, length).ToArray();
        if (length <= 1) return perm;
        uint state = (uint)seed;
        for (int i = length - 1; i > 0; i--)
        {
            state = state * 1664525u + 1013904223u;
            int j = (int)(state % (uint)(i + 1));
            int tmp = perm[i];
            perm[i] = perm[j];
            perm[j] = tmp;
        }
        return perm;
    }

    private List<T> ApplyPermutationToList<T>(List<T> source, int[] permutation)
    {
        var result = new List<T>(source.Count);
        for (int i = 0; i < source.Count; i++) result.Add(source[i]);
        for (int i = 0; i < permutation.Length && i < source.Count; i++)
        {
            result[i] = source[permutation[i]];
        }
        return result;
    }

    private string ObfuscateDigits(string text, int seed, bool encode)
    {
        var map = BuildDigitMap(seed, encode);
        var sb = new StringBuilder(text.Length);
        foreach (char c in text)
        {
            if (c >= '0' && c <= '9') sb.Append(map[c - '0']); else sb.Append(c);
        }
        return sb.ToString();
    }

    private char[] BuildDigitMap(int seed, bool encode)
    {
        var digits = Enumerable.Range(0, 10).ToArray();
        uint state = (uint)seed;
        for (int i = digits.Length - 1; i > 0; i--)
        {
            state = state * 1664525u + 1013904223u;
            int j = (int)(state % (uint)(i + 1));
            int tmp = digits[i];
            digits[i] = digits[j];
            digits[j] = tmp;
        }
        var map = new char[10];
        if (encode)
        {
            for (int d = 0; d < 10; d++) map[d] = (char)('0' + digits[d]);
        }
        else
        {
            var inv = new int[10];
            for (int d = 0; d < 10; d++) inv[digits[d]] = d;
            for (int d = 0; d < 10; d++) map[d] = (char)('0' + inv[d]);
        }
        return map;
    }

    private string XorAndBase64Encode(string plain, int seed)
    {
        var bytes = Encoding.UTF8.GetBytes(plain);
        var key = GenerateXorKey(bytes.Length, seed);
        for (int i = 0; i < bytes.Length; i++) bytes[i] ^= key[i];
        return Convert.ToBase64String(bytes);
    }

    private byte[] GenerateXorKey(int length, int seed)
    {
        var key = new byte[length];
        uint state = (uint)seed;
        for (int i = 0; i < length; i++)
        {
            state = state * 1664525u + 1013904223u;
            key[i] = (byte)((state >> 16) & 0xFF);
        }
        return key;
    }
}