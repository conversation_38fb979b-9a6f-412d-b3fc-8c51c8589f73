using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 特效管理器 - 负责特效的创建、管理和销毁
/// 支持对象池优化和自动清理
/// </summary>
public class Tool_EffectPool : MonoBehaviour
{
    [Header("对象池配置")]
    [Tooltip("对象池最大大小")]
    public int poolSize = 20;

    [Tooltip("自动清理间隔（秒）")]
    public float cleanupInterval = 5f;

    // 单例实例
    private static Tool_EffectPool instance;
    public static Tool_EffectPool Instance
    {
        get
        {
            if (instance == null)
            {
                // 查找场景中是否已有实例
                instance = FindObjectOfType<Tool_EffectPool>();
                if (instance == null)
                {
                    // 创建新的实例
                    GameObject go = new GameObject("Tool_EffectPool");
                    instance = go.AddComponent<Tool_EffectPool>();
                    DontDestroyOnLoad(go);
                }
            }
            return instance;
        }
    }

    // 对象池字典 - 按预制体路径分组
    private Dictionary<string, Queue<GameObject>> effectPools = new Dictionary<string, Queue<GameObject>>();

    // 活跃特效列表 - 用于管理销毁时间
    private List<ActiveEffect> activeEffects = new List<ActiveEffect>();

    // 清理计时器
    private float cleanupTimer = 0f;

    /// <summary>
    /// 活跃特效数据结构
    /// </summary>
    private class ActiveEffect
    {
        public GameObject effectObject;
        public float destroyTime;
        public string poolKey;

        public ActiveEffect(GameObject obj, float time, string key)
        {
            effectObject = obj;
            destroyTime = time;
            poolKey = key;
        }
    }

    void Awake()
    {
        // 确保只有一个实例
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else if (instance != this)
        {
            Destroy(gameObject);
            return;
        }

        // 初始化清理计时器
        cleanupTimer = cleanupInterval;
    }

    void Update()
    {
        // 更新清理计时器
        cleanupTimer -= Time.deltaTime;

        // 到达清理时间，执行清理
        if (cleanupTimer <= 0f)
        {
            CleanupExpiredEffects();
            cleanupTimer = cleanupInterval; // 重置计时器
        }
    }

    /// <summary>
    /// 创建特效 - 通过预制体对象
    /// </summary>
    /// <param name="prefab">特效预制体</param>
    /// <param name="position">位置</param>
    /// <param name="rotation">旋转角度</param>
    /// <param name="scale">缩放大小</param>
    /// <param name="destroyTime">销毁时间（秒）</param>
    /// <returns>创建的特效对象</returns>
    public GameObject CreateEffect(GameObject prefab, Vector3 position, Quaternion rotation, Vector3 scale, float destroyTime = 2f)
    {
        if (prefab == null)
        {
            //Debug.LogError("特效预制体为空！");
            return null;
        }

        string poolKey = prefab.name;
        GameObject effectObj = GetFromPool(poolKey, prefab);

        if (effectObj == null)
        {
            //Debug.LogError($"无法创建特效：{prefab.name}");
            return null;
        }

        // 设置特效属性
        effectObj.transform.position = position;
        effectObj.transform.rotation = rotation;
        effectObj.transform.localScale = scale;
        effectObj.SetActive(true);

        // 添加到活跃特效列表
        float targetDestroyTime = Time.time + destroyTime;
        activeEffects.Add(new ActiveEffect(effectObj, targetDestroyTime, poolKey));

        //Debug.Log($"创建特效：{prefab.name}，位置：{position}，{destroyTime}秒后销毁");
        return effectObj;
    }

    /// <summary>
    /// 创建特效 - 通过Resources路径
    /// </summary>
    /// <param name="resourcePath">Resources路径</param>
    /// <param name="position">位置</param>
    /// <param name="rotation">旋转角度</param>
    /// <param name="scale">缩放大小</param>
    /// <param name="destroyTime">销毁时间（秒）</param>
    /// <returns>创建的特效对象</returns>
    public GameObject CreateEffect(string resourcePath, Vector3 position, Quaternion rotation, Vector3 scale, float destroyTime = 2f)
    {
        if (string.IsNullOrEmpty(resourcePath))
        {
            //Debug.LogError("特效资源路径为空！");
            return null;
        }

        // 从Resources加载预制体
        GameObject prefab = Resources.Load<GameObject>(resourcePath);
        if (prefab == null)
        {
            //Debug.LogError($"无法加载特效预制体：{resourcePath}");
            return null;
        }

        return CreateEffect(prefab, position, rotation, scale, destroyTime);
    }

    /// <summary>
    /// 创建特效 - 简化版本，使用预制体原始缩放
    /// </summary>
    /// <param name="prefab">特效预制体</param>
    /// <param name="position">位置</param>
    /// <param name="rotation">旋转角度</param>
    /// <param name="destroyTime">销毁时间（秒）</param>
    /// <returns>创建的特效对象</returns>
    public GameObject CreateEffect(GameObject prefab, Vector3 position, Quaternion rotation, float destroyTime = 2f)
    {
        if (prefab == null)
        {
            //Debug.LogError("特效预制体为空！");
            return null;
        }

        // 使用预制体的原始缩放
        Vector3 originalScale = prefab.transform.localScale;
        return CreateEffect(prefab, position, rotation, originalScale, destroyTime);
    }

    /// <summary>
    /// 创建特效 - 简化版本，使用预制体原始缩放
    /// </summary>
    /// <param name="resourcePath">Resources路径</param>
    /// <param name="position">位置</param>
    /// <param name="rotation">旋转角度</param>
    /// <param name="destroyTime">销毁时间（秒）</param>
    /// <returns>创建的特效对象</returns>
    public GameObject CreateEffect(string resourcePath, Vector3 position, Quaternion rotation, float destroyTime = 2f)
    {
        if (string.IsNullOrEmpty(resourcePath))
        {
            //Debug.LogError("特效资源路径为空！");
            return null;
        }

        // 从Resources加载预制体
        GameObject prefab = Resources.Load<GameObject>(resourcePath);
        if (prefab == null)
        {
            //Debug.LogError($"无法加载特效预制体：{resourcePath}");
            return null;
        }

        // 使用预制体的原始缩放
        Vector3 originalScale = prefab.transform.localScale;
        return CreateEffect(prefab, position, rotation, originalScale, destroyTime);
    }

    /// <summary>
    /// 从对象池获取特效对象
    /// </summary>
    /// <param name="poolKey">对象池键值</param>
    /// <param name="prefab">预制体</param>
    /// <returns>特效对象</returns>
    private GameObject GetFromPool(string poolKey, GameObject prefab)
    {
        // 如果对象池不存在，创建新的
        if (!effectPools.ContainsKey(poolKey))
        {
            effectPools[poolKey] = new Queue<GameObject>();
        }

        Queue<GameObject> pool = effectPools[poolKey];

        // 从池中获取可用对象
        GameObject effectObj = null;
        while (pool.Count > 0)
        {
            effectObj = pool.Dequeue();
            if (effectObj != null && !effectObj.activeInHierarchy)
            {
                break;
            }
            effectObj = null;
        }

        // 如果池中没有可用对象，创建新的
        if (effectObj == null)
        {
            effectObj = Instantiate(prefab);
            effectObj.SetActive(false);
        }

        return effectObj;
    }

    /// <summary>
    /// 将特效对象返回到对象池
    /// </summary>
    /// <param name="effectObj">特效对象</param>
    /// <param name="poolKey">对象池键值</param>
    private void ReturnToPool(GameObject effectObj, string poolKey)
    {
        if (effectObj == null) return;

        effectObj.SetActive(false);

        // 如果对象池不存在，创建新的
        if (!effectPools.ContainsKey(poolKey))
        {
            effectPools[poolKey] = new Queue<GameObject>();
        }

        Queue<GameObject> pool = effectPools[poolKey];

        // 检查池大小限制
        if (pool.Count < poolSize)
        {
            pool.Enqueue(effectObj);
        }
        else
        {
            // 池已满，直接销毁
            Destroy(effectObj);
        }
    }

    /// <summary>
    /// 清理过期的特效
    /// </summary>
    private void CleanupExpiredEffects()
    {
        float currentTime = Time.time;

        for (int i = activeEffects.Count - 1; i >= 0; i--)
        {
            ActiveEffect effect = activeEffects[i];

            // 检查是否过期或对象已被销毁
            if (effect.effectObject == null || currentTime >= effect.destroyTime)
            {
                if (effect.effectObject != null)
                {
                    ReturnToPool(effect.effectObject, effect.poolKey);
                }
                activeEffects.RemoveAt(i);
            }
        }
    }

    /// <summary>
    /// 立即销毁特效
    /// </summary>
    /// <param name="effectObj">特效对象</param>
    public void DestroyEffect(GameObject effectObj)
    {
        if (effectObj == null) return;

        // 从活跃列表中移除
        for (int i = activeEffects.Count - 1; i >= 0; i--)
        {
            if (activeEffects[i].effectObject == effectObj)
            {
                ReturnToPool(effectObj, activeEffects[i].poolKey);
                activeEffects.RemoveAt(i);
                break;
            }
        }
    }

    /// <summary>
    /// 清理所有特效
    /// </summary>
    public void ClearAllEffects()
    {
        // 清理活跃特效
        for (int i = activeEffects.Count - 1; i >= 0; i--)
        {
            if (activeEffects[i].effectObject != null)
            {
                ReturnToPool(activeEffects[i].effectObject, activeEffects[i].poolKey);
            }
        }
        activeEffects.Clear();

        //Debug.Log("已清理所有特效");
    }

    /// <summary>
    /// 获取活跃特效数量
    /// </summary>
    /// <returns>活跃特效数量</returns>
    public int GetActiveEffectCount()
    {
        return activeEffects.Count;
    }

    /// <summary>
    /// 获取对象池状态信息
    /// </summary>
    /// <returns>对象池状态字符串</returns>
    public string GetPoolStatus()
    {
        string status = "对象池状态：\n";
        foreach (var pool in effectPools)
        {
            status += $"{pool.Key}: {pool.Value.Count} 个对象\n";
        }
        status += $"活跃特效: {activeEffects.Count} 个";
        return status;
    }

    void OnDestroy()
    {
        // 清理所有特效
        ClearAllEffects();
    }
}