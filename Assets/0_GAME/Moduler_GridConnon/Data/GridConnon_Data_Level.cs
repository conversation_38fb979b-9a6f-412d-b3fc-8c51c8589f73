using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// GridConnon 关卡数据
/// 包含网格尺寸和所有炮的配置信息
/// </summary>
[System.Serializable]
public class GridConnon_Data_Level
{
    [Header("网格配置")]
    [Tooltip("网格列数（X轴方向）")]
    public int columns = 8;

    [Header("炮配置")]
    [Tooltip("关卡中所有炮的数据")]
    public List<GridConnon_Data_Cannon> cannons = new List<GridConnon_Data_Cannon>();

    [Header("关卡信息")]
    [Tooltip("关卡ID")]
    public int levelId = 1;

    [Tooltip("关卡名称")]
    public string levelName = "关卡1";

    /// <summary>
    /// 根据炮的位置计算所需的行数
    /// </summary>
    /// <returns>所需的行数</returns>
    public int CalculateRequiredRows()
    {
        if (cannons == null || cannons.Count == 0) return 1;

        int minZ = 0;
        foreach (var cannon in cannons)
        {
            if (cannon != null && cannon.gridZ < minZ)
            {
                minZ = cannon.gridZ;
            }
        }

        // 行数 = 从Z=0到最小Z值的距离 + 1
        return Mathf.Abs(minZ) + 1;
    }

    /// <summary>
    /// 验证关卡数据的有效性
    /// </summary>
    /// <returns>数据是否有效</returns>
    public bool ValidateData()
    {
        bool isValid = true;

        // 检查网格尺寸
        if (columns <= 0)
        {
            //Debug.LogError($"关卡 {levelId}: columns 必须大于0，当前值: {columns}");
            isValid = false;
        }

        // 检查炮数据
        if (cannons == null)
        {
            //Debug.LogError($"关卡 {levelId}: cannons 列表不能为null");
            isValid = false;
        }
        else
        {
            for (int i = 0; i < cannons.Count; i++)
            {
                var cannon = cannons[i];
                if (cannon == null)
                {
                    //Debug.LogError($"关卡 {levelId}: cannons[{i}] 不能为null");
                    isValid = false;
                    continue;
                }

                // 检查炮的位置是否在网格范围内
                if (cannon.gridX < 0 || cannon.gridX >= columns)
                {
                    //Debug.LogError($"关卡 {levelId}: cannons[{i}] gridX={cannon.gridX} 超出范围 [0, {columns - 1}]");
                    isValid = false;
                }

                // 检查Z坐标是否合理（Z=0为第一行，向负值延伸）
                if (cannon.gridZ > 0)
                {
                    //Debug.LogError($"关卡 {levelId}: cannons[{i}] gridZ={cannon.gridZ} 不能大于0");
                    isValid = false;
                }

                // 验证单个炮数据
                if (!cannon.ValidateData())
                {
                    //Debug.LogError($"关卡 {levelId}: cannons[{i}] 数据无效");
                    isValid = false;
                }
            }

            // 检查是否有重复位置的炮
            HashSet<Vector2Int> usedPositions = new HashSet<Vector2Int>();
            for (int i = 0; i < cannons.Count; i++)
            {
                var cannon = cannons[i];
                if (cannon == null) continue;

                Vector2Int pos = new Vector2Int(cannon.gridX, cannon.gridZ);
                if (usedPositions.Contains(pos))
                {
                    //Debug.LogError($"关卡 {levelId}: 位置 ({cannon.gridX}, {cannon.gridZ}) 有重复的炮");
                    isValid = false;
                }
                else
                {
                    usedPositions.Add(pos);
                }
            }
        }

        return isValid;
    }

    /// <summary>
    /// 获取指定位置的炮数据
    /// </summary>
    /// <param name="gridX">网格X坐标</param>
    /// <param name="gridZ">网格Z坐标</param>
    /// <returns>炮数据，如果没有则返回null</returns>
    public GridConnon_Data_Cannon GetCannonAt(int gridX, int gridZ)
    {
        if (cannons == null) return null;

        foreach (var cannon in cannons)
        {
            if (cannon != null && cannon.gridX == gridX && cannon.gridZ == gridZ)
            {
                return cannon;
            }
        }
        return null;
    }

    /// <summary>
    /// 添加炮数据
    /// </summary>
    /// <param name="cannon">要添加的炮数据</param>
    /// <returns>是否添加成功</returns>
    public bool AddCannon(GridConnon_Data_Cannon cannon)
    {
        if (cannon == null) return false;

        // 检查位置是否已被占用
        if (GetCannonAt(cannon.gridX, cannon.gridZ) != null)
        {
            //Debug.LogWarning($"位置 ({cannon.gridX}, {cannon.gridZ}) 已有炮存在");
            return false;
        }

        if (cannons == null)
            cannons = new List<GridConnon_Data_Cannon>();

        cannons.Add(cannon);
        return true;
    }

    /// <summary>
    /// 移除指定位置的炮
    /// </summary>
    /// <param name="gridX">网格X坐标</param>
    /// <param name="gridZ">网格Z坐标</param>
    /// <returns>是否移除成功</returns>
    public bool RemoveCannonAt(int gridX, int gridZ)
    {
        if (cannons == null) return false;

        for (int i = 0; i < cannons.Count; i++)
        {
            if (cannons[i] != null && cannons[i].gridX == gridX && cannons[i].gridZ == gridZ)
            {
                cannons.RemoveAt(i);
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 清空所有炮数据
    /// </summary>
    public void ClearAllCannons()
    {
        if (cannons != null)
        {
            cannons.Clear();
        }
    }

    /// <summary>
    /// 获取指定类型的所有炮
    /// </summary>
    /// <param name="objectType">炮的类型</param>
    /// <returns>指定类型的炮列表</returns>
    public List<GridConnon_Data_Cannon> GetCannonsByType(GridConnon_ObjectType objectType)
    {
        List<GridConnon_Data_Cannon> result = new List<GridConnon_Data_Cannon>();

        if (cannons == null) return result;

        foreach (var cannon in cannons)
        {
            if (cannon != null && cannon.objectType == objectType)
            {
                result.Add(cannon);
            }
        }

        return result;
    }

    /// <summary>
    /// 获取指定颜色的所有普通炮
    /// </summary>
    /// <param name="colorId">颜色ID</param>
    /// <returns>指定颜色的普通炮列表</returns>
    public List<GridConnon_Data_Cannon> GetCannonsByColor(int colorId)
    {
        List<GridConnon_Data_Cannon> result = new List<GridConnon_Data_Cannon>();

        if (cannons == null) return result;

        foreach (var cannon in cannons)
        {
            if (cannon != null && cannon.colorId == colorId)
            {
                result.Add(cannon);
            }
        }

        return result;
    }
}