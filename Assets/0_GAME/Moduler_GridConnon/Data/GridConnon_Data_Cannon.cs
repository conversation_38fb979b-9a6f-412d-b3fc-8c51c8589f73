using UnityEngine;
using LitJson;

/// <summary>
/// GridConnon 炮数据
/// 存储单个炮的配置信息
/// </summary>
[System.Serializable]
public class GridConnon_Data_Cannon
{
    [Header("位置信息")]
    [Tooltip("网格X坐标（列）")]
    public int gridX = 0;

    [Tooltip("网格Z坐标（行，Z=0为第一行，Z=-1为第二行）")]
    public int gridZ = 0;

    [Header("炮属性")]
    [Tooltip("炮的类型")]
    public GridConnon_ObjectType objectType = GridConnon_ObjectType.NormalCannon;

    [Tooltip("颜色ID（1开始，-1表示无颜色）")]
    public int colorId = 1;

    [Tooltip("炮的唯一ID")]
    public int cannonId = 0;

    [Tooltip("击打值（发射子弹次数）")]
    public int hitCount = 3;

    [Header("特殊属性")]
    [Tooltip("是否已被激活（用于问号炮等特殊逻辑）")]
    public bool isActivated = false;

    [Tooltip("自定义数据（JSON格式，用于扩展）")]
    public string customData = "";

    [Header("链接配置")]
    [Tooltip("链接的炮台ID列表")]
    public System.Collections.Generic.List<string> linkedCannonIds = new System.Collections.Generic.List<string>();

    [Tooltip("是否启用链接功能")]
    public bool enableLinking = true;

    /// <summary>
    /// 默认构造函数
    /// </summary>
    public GridConnon_Data_Cannon()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="gridX">网格X坐标</param>
    /// <param name="gridZ">网格Z坐标</param>
    /// <param name="objectType">炮类型</param>
    /// <param name="colorId">颜色ID</param>
    /// <param name="hitCount">击打值</param>
    public GridConnon_Data_Cannon(int gridX, int gridZ, GridConnon_ObjectType objectType, int colorId = 1, int hitCount = 3)
    {
        this.gridX = gridX;
        this.gridZ = gridZ;
        this.objectType = objectType;
        this.colorId = colorId;
        this.hitCount = hitCount;
    }

    /// <summary>
    /// 验证炮数据的有效性
    /// </summary>
    /// <returns>数据是否有效</returns>
    public bool ValidateData()
    {
        bool isValid = true;

        // 验证炮类型相关的颜色ID
        switch (objectType)
        {
            case GridConnon_ObjectType.NormalCannon:
            case GridConnon_ObjectType.TriangleCannon:
            case GridConnon_ObjectType.QuestionNormalCannon:
            case GridConnon_ObjectType.QuestionTriangleCannon:
                // 这些类型需要有效的颜色ID
                if (colorId <= 0)
                {
                    //Debug.LogError($"炮 ({gridX}, {gridZ}): {objectType} 需要有效的颜色ID，当前值: {colorId}");
                    isValid = false;
                }
                break;

            case GridConnon_ObjectType.TreasureCannon:
                // 宝箱炮不需要颜色，设置为-1
                if (colorId != -1)
                {
                    //Debug.LogWarning($"炮 ({gridX}, {gridZ}): TreasureCannon 不需要颜色，自动设置为-1");
                    colorId = -1;
                }
                break;
        }

        // 验证cannonId
        if (cannonId < 0)
        {
            //Debug.LogWarning($"炮 ({gridX}, {gridZ}): cannonId 应该大于等于0，当前值: {cannonId}");
        }

        return isValid;
    }

    /// <summary>
    /// 是否有颜色
    /// </summary>
    /// <returns>是否有颜色</returns>
    public bool HasColor()
    {
        return colorId > 0;
    }

    /// <summary>
    /// 是否为特殊炮（非普通炮）
    /// </summary>
    /// <returns>是否为特殊炮</returns>
    public bool IsSpecialCannon()
    {
        return objectType != GridConnon_ObjectType.NormalCannon;
    }

    /// <summary>
    /// 获取网格位置
    /// </summary>
    /// <returns>网格位置</returns>
    public Vector2Int GetGridPosition()
    {
        return new Vector2Int(gridX, gridZ);
    }

    /// <summary>
    /// 设置网格位置
    /// </summary>
    /// <param name="position">新的网格位置</param>
    public void SetGridPosition(Vector2Int position)
    {
        gridX = position.x;
        gridZ = position.y;
    }

    /// <summary>
    /// 克隆炮数据
    /// </summary>
    /// <returns>克隆的炮数据</returns>
    public GridConnon_Data_Cannon Clone()
    {
        GridConnon_Data_Cannon clone = new GridConnon_Data_Cannon();
        clone.gridX = this.gridX;
        clone.gridZ = this.gridZ;
        clone.objectType = this.objectType;
        clone.colorId = this.colorId;
        clone.cannonId = this.cannonId;
        clone.isActivated = this.isActivated;
        clone.customData = this.customData;
        clone.hitCount = this.hitCount;
        clone.linkedCannonIds = new System.Collections.Generic.List<string>(this.linkedCannonIds);
        clone.enableLinking = this.enableLinking;
        return clone;
    }

    /// <summary>
    /// 转换为字符串（用于调试）
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        string colorStr = HasColor() ? $"Color{colorId}" : "NoColor";
        return $"Cannon[{cannonId}] ({gridX}, {gridZ}) {objectType} {colorStr} Hits:{hitCount}";
    }

    /// <summary>
    /// 比较两个炮数据是否相等
    /// </summary>
    /// <param name="obj">要比较的对象</param>
    /// <returns>是否相等</returns>
    public override bool Equals(object obj)
    {
        if (obj == null || GetType() != obj.GetType())
            return false;

        GridConnon_Data_Cannon other = (GridConnon_Data_Cannon)obj;
        return gridX == other.gridX &&
               gridZ == other.gridZ &&
               objectType == other.objectType &&
               colorId == other.colorId &&
               cannonId == other.cannonId &&
               hitCount == other.hitCount;
    }

    /// <summary>
    /// 获取哈希码
    /// </summary>
    /// <returns>哈希码</returns>
    public override int GetHashCode()
    {
        // Unity 2019兼容的哈希码计算方式
        unchecked
        {
            int hash = 17;
            hash = hash * 23 + gridX.GetHashCode();
            hash = hash * 23 + gridZ.GetHashCode();
            hash = hash * 23 + objectType.GetHashCode();
            hash = hash * 23 + colorId.GetHashCode();
            hash = hash * 23 + cannonId.GetHashCode();
            hash = hash * 23 + hitCount.GetHashCode();
            return hash;
        }
    }

    /// <summary>
    /// 设置自定义数据
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    public void SetCustomData(string key, object value)
    {
        try
        {
            var data = string.IsNullOrEmpty(customData) ?
                new System.Collections.Generic.Dictionary<string, object>() :
                JsonMapper.ToObject<System.Collections.Generic.Dictionary<string, object>>(customData);

            data[key] = value;
            customData = JsonMapper.ToJson(data);
        }
        catch (System.Exception e)
        {
            //Debug.LogError($"设置自定义数据失败: {e.Message}");
        }
    }

    /// <summary>
    /// 获取自定义数据
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>值</returns>
    public T GetCustomData<T>(string key, T defaultValue = default(T))
    {
        try
        {
            if (string.IsNullOrEmpty(customData))
                return defaultValue;

            var data = JsonMapper.ToObject<System.Collections.Generic.Dictionary<string, object>>(customData);

            if (data.TryGetValue(key, out object value))
            {
                return JsonMapper.ToObject<T>(value.ToString());
            }
        }
        catch (System.Exception e)
        {
            //Debug.LogError($"获取自定义数据失败: {e.Message}");
        }

        return defaultValue;
    }
}