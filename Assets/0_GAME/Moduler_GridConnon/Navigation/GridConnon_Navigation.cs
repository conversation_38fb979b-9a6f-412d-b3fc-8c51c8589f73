using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 炮台导航组件 - 负责炮台的目标点移动和平滑移动
/// 挂载到需要移动的炮台上，提供A*导航、避障功能和平滑移动
/// </summary>
[RequireComponent(typeof(GridConnon_Object))]
public class GridConnon_Navigation : MonoBehaviour
{
    [Header("导航配置")]
    [Tooltip("导航数据配置")]
    public GridConnon_NavigationData navigationData;

    [Tooltip("是否检测移动中的炮台作为障碍物（关闭时，导航计算不会将正在移动的炮台视为障碍物）")]
    public bool detectMovingCannonsAsObstacles = true;

    [Header("平滑移动配置")]
    [Tooltip("位置插值速度倍数")]
    [Range(1f, 20f)]
    public float positionLerpMultiplier = 5f;

    [Tooltip("旋转插值速度倍数")]
    [Range(1f, 20f)]
    public float rotationLerpMultiplier = 8f;

    [Tooltip("是否启用朝向插值")]
    public bool enableRotation = true;

    [Tooltip("是否启用预测性旋转（提前朝向下一个路径点）")]
    public bool enablePredictiveRotation = true;

    [Tooltip("是否使用平滑移动（false则使用原来的直接移动）")]
    public bool useSmoothMovement = true;

    [Header("障碍检测精度")]
    [Tooltip("障碍矩形膨胀系数（占单元格宽/高的比例），越大越不容易穿插")]
    [Range(0.5f, 1.2f)]
    public float obstacleAABBInflation = 0.95f;

    [Tooltip("障碍圆半径系数（相对单元格最小半边），用于距离近似判断")]
    [Range(0.1f, 1.0f)]
    public float obstacleCircleInflation = 0.5f;

    [Header("调试选项")]
    [Tooltip("是否显示路径调试信息")]
    public bool showDebugPath = true;

    [Tooltip("路径调试颜色")]
    public Color debugPathColor = Color.green;

    // 组件引用
    private GridConnon_Object cannonObject;
    private Collider cannonCollider;
    private Transform cachedTransform;

    // 导航相关
    private Vector3 lastKnownPosition;

    // 平滑移动相关
    private Vector3 currentVelocity;
    private Vector3 lastTargetPoint;
    private bool isMovingSmooth = false;

    // 事件
    public System.Action OnMoveStarted;
    public System.Action OnMoveCompleted;
    public System.Action<Vector3> OnMoveProgress;

    // 临时完成回调（针对单次移动）
    private System.Action temporaryMoveCompletedCallback;

    // 新增：复用的障碍缓存，避免每次分配 List（性能优化）
    private readonly System.Collections.Generic.List<Vector2> obstaclesBuffer = new System.Collections.Generic.List<Vector2>(64);

    #region Unity 生命周期

    private void Awake()
    {
        // 获取组件引用
        cannonObject = GetComponent<GridConnon_Object>();
        cannonCollider = GetComponent<Collider>();
        cachedTransform = transform;

        // 初始化导航数据
        if (navigationData == null)
        {
            navigationData = new GridConnon_NavigationData();
        }

        // 验证导航参数的合理性
        navigationData.ValidateParameters();

        // 根据碰撞器大小自动调整导航缩放
        //AutoAdjustNavigationScale();
    }

    private void Start()
    {
        lastKnownPosition = transform.position;
    }

    private void Update()
    {
        // 更新移动逻辑
        if (navigationData.isMoving)
        {
            if (useSmoothMovement)
            {
                UpdateSmoothMovement();
            }

            // 检查是否需要重新计算路径
            if (navigationData.ShouldUpdate())
            {
                CheckForPathRecalculation();
                navigationData.UpdateTimestamp();
            }
        }
    }

    #endregion

    #region 公共接口

    /// <summary>
    /// 开始导航到指定目标点
    /// </summary>
    /// <param name="targetPosition">目标位置（世界坐标）</param>
    /// <param name="onCompleted">移动完成回调（可选）</param>
    /// <returns>是否成功开始导航</returns>
    public bool StartNavigation(Vector3 targetPosition, System.Action onCompleted = null)
    {
        // 停止当前导航
        StopNavigation();

        // 设置目标位置（忽略Y轴差异）
        navigationData.targetPosition = new Vector3(targetPosition.x, transform.position.y, targetPosition.z);


        // 计算路径
        List<Vector3> path = GridConnon_PathFinder.FindPath(
            transform.position,
            navigationData.targetPosition,
            cannonObject,
            navigationData.avoidanceDistance,
            navigationData
        );

        if (path == null || path.Count == 0)
        {
            //Debug.LogWarning($"炮台 {cannonObject.ObjectId} 无法找到到目标位置的路径");
            return false;
        }

        // 简化路径
        List<Vector2> obstacles = GetCurrentObstacles();
        path = GridConnon_PathFinder.SimplifyPath(path, obstacles, navigationData.avoidanceDistance, navigationData);

        // 开始导航
        navigationData.currentPath = path;
        navigationData.currentPathIndex = 0;
        navigationData.isMoving = true;
        lastKnownPosition = transform.position;

        // 设置临时完成回调
        temporaryMoveCompletedCallback = onCompleted;

        // 初始化平滑移动状态
        if (useSmoothMovement)
        {
            isMovingSmooth = true;
            currentVelocity = Vector3.zero;
            if (path.Count > 0)
            {
                lastTargetPoint = path[0];
            }
        }

        OnMoveStarted?.Invoke();

        //Debug.Log($"炮台 {cannonObject.ObjectId} 开始导航到目标位置，路径点数: {path.Count}");
        return true;
    }

    /// <summary>
    /// 停止导航
    /// </summary>
    public void StopNavigation()
    {
        navigationData.ResetRuntimeData();

        // 重置平滑移动状态
        isMovingSmooth = false;
        currentVelocity = Vector3.zero;

        // 清除临时回调（停止时不调用完成回调）
        temporaryMoveCompletedCallback = null;

        OnMoveCompleted?.Invoke();
    }

    /// <summary>
    /// 获取当前应该朝向的目标点
    /// 调用者负责具体的移动插值实现
    /// </summary>
    /// <returns>当前目标点，如果没有目标则返回null</returns>
    public Vector3? GetCurrentTargetPoint()
    {
        if (!navigationData.isMoving || navigationData.currentPath == null || navigationData.currentPath.Count == 0)
            return null;

        if (navigationData.currentPathIndex >= navigationData.currentPath.Count)
            return null;

        return navigationData.currentPath[navigationData.currentPathIndex];
    }

    /// <summary>
    /// 通知导航系统已到达当前目标点
    /// 这会让导航系统切换到下一个路径点
    /// </summary>
    /// <param name="currentPosition">当前实际位置</param>
    /// <returns>是否还有更多路径点</returns>
    public bool NotifyReachedCurrentPoint(Vector3 currentPosition)
    {
        if (useSmoothMovement)
        {
            //Debug.LogWarning("平滑移动模式下不应该手动调用NotifyReachedCurrentPoint");
            return false;
        }

        if (!navigationData.isMoving || navigationData.currentPath == null)
            return false;

        // 自动检查是否到达当前目标点
        Vector3? targetPoint = GetCurrentTargetPoint();
        if (targetPoint.HasValue)
        {
            float distance = Vector3.Distance(currentPosition, targetPoint.Value);
            if (distance <= navigationData.arrivalThreshold)
            {
                // 到达当前路径点，移动到下一个
                navigationData.currentPathIndex++;
                OnMoveProgress?.Invoke(currentPosition);

                // 检查是否完成所有路径
                if (navigationData.currentPathIndex >= navigationData.currentPath.Count)
                {
                    // 导航完成
                    var tempCallback = temporaryMoveCompletedCallback;
                    navigationData.ResetRuntimeData();
                    temporaryMoveCompletedCallback = null;

                    OnMoveCompleted?.Invoke();
                    tempCallback?.Invoke(); // 执行临时完成回调
                    //Debug.Log($"炮台 {cannonObject.ObjectId} 到达目标位置");
                    return false;
                }

                return true; // 还有更多路径点
            }
        }

        return navigationData.currentPathIndex < navigationData.currentPath.Count;
    }

    /// <summary>
    /// 检查是否正在导航
    /// </summary>
    /// <returns>是否正在导航</returns>
    public bool IsNavigating()
    {
        return navigationData.isMoving;
    }

    /// <summary>
    /// 获取最终目标位置
    /// </summary>
    /// <returns>最终目标位置</returns>
    public Vector3 GetFinalTargetPosition()
    {
        return navigationData.targetPosition;
    }

    /// <summary>
    /// 获取导航进度（0-1）
    /// </summary>
    /// <returns>导航进度</returns>
    public float GetNavigationProgress()
    {
        if (!navigationData.isMoving || navigationData.currentPath == null || navigationData.currentPath.Count == 0)
            return 0f;

        return (float)navigationData.currentPathIndex / (navigationData.currentPath.Count - 1);
    }

    /// <summary>
    /// 获取移动速度（供外部移动系统使用）
    /// </summary>
    /// <returns>移动速度</returns>
    public float GetMoveSpeed()
    {
        return navigationData.moveSpeed;
    }

    /// <summary>
    /// 获取当前移动速度向量（仅在平滑移动模式下有效）
    /// </summary>
    /// <returns>当前速度向量</returns>
    public Vector3 GetCurrentVelocity()
    {
        return useSmoothMovement ? currentVelocity : Vector3.zero;
    }

    /// <summary>
    /// 兼容性方法 - 保持向后兼容
    /// </summary>
    [System.Obsolete("使用StartNavigation替代")]
    public bool MoveTo(Vector3 targetPosition)
    {
        return StartNavigation(targetPosition);
    }

    /// <summary>
    /// 兼容性方法 - 保持向后兼容
    /// </summary>
    [System.Obsolete("使用StopNavigation替代")]
    public void StopMovement()
    {
        StopNavigation();
    }

    /// <summary>
    /// 兼容性方法 - 保持向后兼容
    /// </summary>
    [System.Obsolete("使用IsNavigating替代")]
    public bool IsMoving()
    {
        return IsNavigating();
    }

    /// <summary>
    /// 兼容性方法 - 保持向后兼容
    /// </summary>
    [System.Obsolete("使用GetFinalTargetPosition替代")]
    public Vector3 GetTargetPosition()
    {
        return GetFinalTargetPosition();
    }

    /// <summary>
    /// 兼容性方法 - 保持向后兼容
    /// </summary>
    [System.Obsolete("使用GetNavigationProgress替代")]
    public float GetMoveProgress()
    {
        return GetNavigationProgress();
    }

    #endregion

    #region 私有方法

    /*     /// <summary>
        /// 根据碰撞器大小自动调整导航缩放
        /// </summary>
        private void AutoAdjustNavigationScale()
        {
            if (cannonCollider != null)
            {
                // 获取碰撞器的边界大小
                Bounds bounds = cannonCollider.bounds;
                float maxSize = Mathf.Max(bounds.size.x, bounds.size.z); // 只考虑XZ平面

                // 根据大小调整导航缩放，确保合理的避障距离
                navigationData.avoidanceDistance = maxSize * navigationData.navigationScale;

                //Debug.Log($"炮台 {cannonObject.ObjectId} 自动调整避障距离: {navigationData.avoidanceDistance}");
            }
        } */

    /// <summary>
    /// 检查是否需要重新计算路径
    /// </summary>
    private void CheckForPathRecalculation()
    {
        if (!navigationData.isMoving) return;

        // 检查是否偏离路径太远（使用平方距离，避免开根号）
        if (navigationData.currentPath != null && navigationData.currentPathIndex < navigationData.currentPath.Count)
        {
            Vector3 expectedPosition = navigationData.currentPath[navigationData.currentPathIndex];
            float sqrDistanceFromPath = (transform.position - expectedPosition).sqrMagnitude; // 使用平方距离
            float recalcSqr = navigationData.recalculateDistance * navigationData.recalculateDistance;

            if (sqrDistanceFromPath > recalcSqr)
            {
                //   //Debug.Log($"炮台 {cannonObject.ObjectId} 偏离路径，重新计算路径");
                RecalculatePath();
            }
        }

        // 检查其他炮台是否移动了，可能需要重新规划路径
        if (HasObstaclesMoved())
        {
            //            //Debug.Log($"炮台 {cannonObject.ObjectId} 检测到障碍物移动，重新计算路径");
            RecalculatePath();
        }

        // 额外检测：当前移动段是否被障碍侵入（动态障碍切入路径时尽快重算）
        if (IsCurrentPathSegmentBlocked())
        {
            RecalculatePath();
        }
    }

    /// <summary>
    /// 重新计算路径
    /// </summary>
    private void RecalculatePath()
    {
        List<Vector3> newPath = GridConnon_PathFinder.FindPath(
            transform.position,
            navigationData.targetPosition,
            cannonObject,
            navigationData.avoidanceDistance,
            navigationData
        );

        if (newPath != null && newPath.Count > 0)
        {
            // 简化新路径
            List<Vector2> obstacles = GetCurrentObstacles();
            newPath = GridConnon_PathFinder.SimplifyPath(newPath, obstacles, navigationData.avoidanceDistance, navigationData);

            navigationData.currentPath = newPath;
            navigationData.currentPathIndex = 0;
        }
    }

    /// <summary>
    /// 检查障碍物是否移动
    /// </summary>
    private bool HasObstaclesMoved()
    {
        // 简单的检查：如果自身位置变化超过阈值，认为环境可能发生了变化（使用平方距离）
        float sqrPositionChange = (transform.position - lastKnownPosition).sqrMagnitude;
        lastKnownPosition = transform.position;

        float recalcSqr = navigationData.recalculateDistance * navigationData.recalculateDistance;
        return sqrPositionChange > recalcSqr;
    }

    /// <summary>
    /// 获取当前所有障碍物位置
    /// </summary>
    private System.Collections.Generic.List<Vector2> GetCurrentObstacles()
    {
        // 复用缓存列表，避免 GC
        obstaclesBuffer.Clear();

        if (GridConnon_Controller.Instance != null)
        {
            foreach (GridConnon_Object cannon in GridConnon_Controller.Instance.CurrentCannons)
            {
                if (cannon == cannonObject) continue;

                // 当不把“移动中的炮台”视为障碍物时，避免循环内 GetComponent：
                // 尝试转为 GridConnon_Cannon 并调用其 IsMoving()（已缓存导航组件）
                if (!detectMovingCannonsAsObstacles)
                {
                    var otherCannon = cannon as GridConnon_Cannon;
                    if (otherCannon != null && otherCannon.IsMoving())
                    {
                        continue; // 跳过正在移动的炮台
                    }
                }

                Vector3 pos = cannon.GetWorldPosition();
                obstaclesBuffer.Add(new Vector2(pos.x, pos.z));
            }
        }

        return obstaclesBuffer;
    }

    /// <summary>
    /// 更新平滑移动逻辑
    /// </summary>
    private void UpdateSmoothMovement()
    {
        if (!isMovingSmooth || navigationData.currentPath == null || navigationData.currentPath.Count == 0)
        {
            return;
        }

        if (navigationData.currentPathIndex >= navigationData.currentPath.Count)
        {
            // 导航完成
            CompleteNavigation();
            return;
        }

        // 在平滑移动前再次快速检查当前段是否被障碍侵入
        if (IsCurrentPathSegmentBlocked())
        {
            RecalculatePath();
            // 重新计算后若无路径，则等待下次更新
            if (navigationData.currentPath == null || navigationData.currentPath.Count == 0 || navigationData.currentPathIndex >= navigationData.currentPath.Count)
            {
                return;
            }
        }

        Vector3 targetPoint = navigationData.currentPath[navigationData.currentPathIndex];
        Vector3 currentPosition = cachedTransform.position;

        // 检查是否切换到新的目标点
        if ((targetPoint - lastTargetPoint).sqrMagnitude > 0.0001f)
        {
            lastTargetPoint = targetPoint;
        }

        // 计算到目标点的平方距离（避免开根号）
        Vector3 toTarget = targetPoint - currentPosition;
        float sqrDistanceToTarget = toTarget.sqrMagnitude;
        float arrivalSqr = navigationData.arrivalThreshold * navigationData.arrivalThreshold;

        // 检查是否到达当前目标点
        if (sqrDistanceToTarget <= arrivalSqr)
        {
            // 移动到下一个路径点
            navigationData.currentPathIndex++;
            OnMoveProgress?.Invoke(currentPosition);

            // 检查是否完成所有路径
            if (navigationData.currentPathIndex >= navigationData.currentPath.Count)
            {
                CompleteNavigation();
                return;
            }

            // 获取下一个目标点
            targetPoint = navigationData.currentPath[navigationData.currentPathIndex];
            lastTargetPoint = targetPoint;
            toTarget = targetPoint - currentPosition;
            sqrDistanceToTarget = toTarget.sqrMagnitude;

            // 切点优化：立即将速度对齐到新方向的满速，避免过渡到下一个点时出现减速/顿一下
            if (toTarget.sqrMagnitude > 0.000001f)
            {
                Vector3 newDir = toTarget.normalized;
                currentVelocity = newDir * navigationData.moveSpeed;
            }
        }

        // 平滑移动到目标点
        MoveTowardsTargetSmooth(targetPoint);

        // 平滑旋转朝向目标方向（改进版本）
        if (enableRotation)
        {
            RotateTowardsTarget(targetPoint);
        }
    }

    /// <summary>
    /// 平滑移动到目标位置
    /// </summary>
    /// <param name="target">目标位置</param>
    private void MoveTowardsTargetSmooth(Vector3 target)
    {
        Vector3 currentPosition = cachedTransform.position;
        float moveSpeed = navigationData.moveSpeed;

        // 改进的移动方案：结合线性插值和速度控制，保持丝滑感觉的同时正确响应速度设置
        Vector3 direction = (target - currentPosition).normalized;
        float maxMoveDistance = moveSpeed * Time.deltaTime;
        float maxMoveSqr = maxMoveDistance * maxMoveDistance;

        // 使用改进的插值：在保持平滑的同时确保速度响应
        Vector3 targetVelocity = direction * moveSpeed;

        // 平滑过渡到目标速度，但保持较快的响应速度以维持丝滑感觉
        float accelerationTime = 1f / positionLerpMultiplier; // 降低加速时间，保持丝滑
        currentVelocity = Vector3.Lerp(currentVelocity, targetVelocity, Time.deltaTime / accelerationTime);

        // 限制最大移动距离，避免超调
        Vector3 movement = currentVelocity * Time.deltaTime;
        if (movement.sqrMagnitude > maxMoveSqr)
        {
            movement = movement.normalized * maxMoveDistance;
        }

        // 如果非常接近目标，直接移动到目标避免抖动（用平方距离）
        if ((target - currentPosition).sqrMagnitude <= maxMoveSqr)
        {
            cachedTransform.position = target;
            currentVelocity = Vector3.zero;
        }
        else
        {
            cachedTransform.position = currentPosition + movement;
        }
    }

    /// <summary>
    /// 平滑旋转朝向目标位置（支持提前朝向下一个路径点）
    /// </summary>
    private void RotateTowardsTarget(Vector3 targetPosition)
    {
        Vector3 currentPosition = cachedTransform.position;
        Vector3 rotationTarget = targetPosition;

        // 预测性旋转：当接近当前目标点时，提前朝向下一个路径点
        if (enablePredictiveRotation)
        {
            float sqrDistanceToCurrentTarget = (currentPosition - targetPosition).sqrMagnitude;
            float lookAhead = navigationData.arrivalThreshold * 2f;
            float lookAheadSqr = lookAhead * lookAhead;

            if (sqrDistanceToCurrentTarget <= lookAheadSqr)
            {
                // 检查是否有下一个路径点
                int nextPathIndex = navigationData.currentPathIndex + 1;
                if (nextPathIndex < navigationData.currentPath.Count)
                {
                    Vector3 nextTarget = navigationData.currentPath[nextPathIndex];

                    // 计算插值比例：越接近当前目标点，越多地朝向下一个目标点
                    float lerpRatio = 1f - Mathf.Clamp01(Mathf.Sqrt(sqrDistanceToCurrentTarget) / lookAhead);
                    rotationTarget = Vector3.Lerp(targetPosition, nextTarget, lerpRatio);
                }
            }
        }

        // 计算朝向目标的方向向量（忽略Y轴）
        Vector3 directionToTarget = new Vector3(
            rotationTarget.x - currentPosition.x,
            0,
            rotationTarget.z - currentPosition.z
        ).normalized;

        // 如果目标方向有效，则进行旋转
        if (directionToTarget.magnitude > 0.01f)
        {
            Quaternion targetRotation = Quaternion.LookRotation(directionToTarget);
            cachedTransform.rotation = Quaternion.Slerp(
                cachedTransform.rotation,
                targetRotation,
                rotationLerpMultiplier * Time.deltaTime
            );
        }
    }

    /// <summary>
    /// 平滑旋转朝向移动方向（保留旧方法用于兼容）
    /// </summary>
    private void RotateTowardsMovementDirection()
    {
        if (currentVelocity.magnitude < 0.01f)
            return;

        // 计算朝向（忽略Y轴）
        Vector3 lookDirection = new Vector3(currentVelocity.x, 0, currentVelocity.z).normalized;

        if (lookDirection.magnitude > 0.01f)
        {
            Quaternion targetRotation = Quaternion.LookRotation(lookDirection);
            cachedTransform.rotation = Quaternion.Slerp(
                cachedTransform.rotation,
                targetRotation,
                rotationLerpMultiplier * Time.deltaTime
            );
        }
    }

    /// <summary>
    /// 完成导航
    /// </summary>
    private void CompleteNavigation()
    {
        var tempCallback = temporaryMoveCompletedCallback;
        navigationData.ResetRuntimeData();
        isMovingSmooth = false;
        currentVelocity = Vector3.zero;
        temporaryMoveCompletedCallback = null;

        OnMoveCompleted?.Invoke();
        tempCallback?.Invoke(); // 执行临时完成回调
        //Debug.Log($"炮台 {cannonObject.ObjectId} 到达目标位置");
    }

    #endregion

    #region 调试

    /// <summary>
    /// 绘制调试信息
    /// </summary>
    // private void OnDrawGizmos()
    // {
    //     if (!showDebugPath || navigationData == null) return;
    //
    //     // 绘制当前路径
    //     if (navigationData.currentPath != null && navigationData.currentPath.Count > 1)
    //     {
    //         Gizmos.color = debugPathColor;
    //         for (int i = 0; i < navigationData.currentPath.Count - 1; i++)
    //         {
    //             Gizmos.DrawLine(navigationData.currentPath[i], navigationData.currentPath[i + 1]);
    //         }
    //
    //         // 绘制路径点
    //         foreach (Vector3 point in navigationData.currentPath)
    //         {
    //             Gizmos.DrawWireSphere(point, 0.1f);
    //         }
    //
    //         // 绘制当前目标点（高亮显示）
    //         if (navigationData.currentPathIndex < navigationData.currentPath.Count)
    //         {
    //             Vector3 currentTarget = navigationData.currentPath[navigationData.currentPathIndex];
    //             Gizmos.color = Color.cyan;
    //             Gizmos.DrawWireSphere(currentTarget, 0.15f);
    //
    //             // 绘制到当前目标点的连线
    //             Gizmos.color = Color.yellow;
    //             Gizmos.DrawLine(transform.position, currentTarget);
    //         }
    //     }
    //
    //     // 绘制最终目标位置
    //     if (navigationData.isMoving)
    //     {
    //         Gizmos.color = Color.red;
    //         Gizmos.DrawWireSphere(navigationData.targetPosition, 0.2f);
    //     }
    //
    //     // 绘制避障范围
    //     Gizmos.color = Color.yellow;
    //     Gizmos.DrawWireSphere(transform.position, navigationData.avoidanceDistance);
    //
    //     // 平滑移动调试信息
    //     if (useSmoothMovement && isMovingSmooth)
    //     {
    //         // 绘制移动速度向量
    //         if (currentVelocity.magnitude > 0.01f)
    //         {
    //             Gizmos.color = Color.blue;
    //             Gizmos.DrawRay(transform.position, currentVelocity.normalized);
    //         }
    //     }
    // }
    //
    // /// <summary>
    // /// 在Scene视图中显示调试信息
    // /// </summary>
    // private void OnDrawGizmosSelected()
    // {
    //     if (navigationData == null) return;
    //
    //     // 绘制导航参数信息
    //     Gizmos.color = Color.cyan;
    //     Gizmos.DrawWireSphere(transform.position, navigationData.arrivalThreshold);
    // }

    #endregion

    // 距离工具：计算点到线段的平方距离（XZ平面）
    private static float DistancePointToSegmentSqr_XZ(Vector3 point, Vector3 segA, Vector3 segB)
    {
        Vector2 p = new Vector2(point.x, point.z);
        Vector2 a = new Vector2(segA.x, segA.z);
        Vector2 b = new Vector2(segB.x, segB.z);
        Vector2 ab = b - a;
        float abSqr = ab.sqrMagnitude;
        if (abSqr < 1e-8f)
        {
            return (p - a).sqrMagnitude;
        }
        float t = Vector2.Dot(p - a, ab) / abSqr;
        t = Mathf.Clamp01(t);
        Vector2 proj = a + t * ab;
        return (p - proj).sqrMagnitude;
    }

    // 检测：当前移动段是否被障碍侵入（任一障碍点到当前线段的距离 < 避障距离）
    private bool IsCurrentPathSegmentBlocked()
    {
        if (!navigationData.isMoving || navigationData.currentPath == null || navigationData.currentPathIndex >= navigationData.currentPath.Count)
            return false;

        Vector3 currentPos = cachedTransform != null ? cachedTransform.position : transform.position;
        Vector3 currentTarget = navigationData.currentPath[navigationData.currentPathIndex];

        // 极近时无需检测
        if ((currentTarget - currentPos).sqrMagnitude < 0.0001f)
            return false;

        var obstacles = GetCurrentObstacles();
        if (obstacles == null || obstacles.Count == 0)
            return false;

        // 从控制器读取网格尺寸，作为障碍大小的依据
        float cellHalfX = 0.5f;
        float cellHalfZ = 0.5f;
        if (GridConnon_Controller.Instance != null)
        {
            cellHalfX = Mathf.Max(0.05f, GridConnon_Controller.Instance.cellSizeX * 0.5f * obstacleAABBInflation);
            cellHalfZ = Mathf.Max(0.05f, GridConnon_Controller.Instance.cellSizeZ * 0.5f * obstacleAABBInflation);
        }

        // 圆形近似半径，取较小半轴并可调膨胀
        float circleR = Mathf.Min(cellHalfX, cellHalfZ) * obstacleCircleInflation;
        circleR = Mathf.Max(circleR, navigationData.avoidanceDistance); // 至少不小于避障距离
        float circleR2 = circleR * circleR;

        for (int i = 0; i < obstacles.Count; i++)
        {
            Vector2 ob2 = obstacles[i];
            Vector3 ob = new Vector3(ob2.x, currentPos.y, ob2.y);
            // 1) AABB 相交（更严格）：线段与以障碍为中心，半径为 cellHalfX/cellHalfZ 的矩形是否相交
            if (SegmentIntersectsAABB_XZ(currentPos, currentTarget, ob, cellHalfX, cellHalfZ))
                return true;

            // 2) 圆形近似（更宽松）：线段到中心距离是否小于近似半径
            float dSqr = DistancePointToSegmentSqr_XZ(ob, currentPos, currentTarget);
            if (dSqr < circleR2)
                return true;
        }
        return false;
    }

    // 线段-矩形(AABB, XZ平面)相交测试：中心为center，半径halfX/halfZ
    private static bool SegmentIntersectsAABB_XZ(Vector3 a, Vector3 b, Vector3 center, float halfX, float halfZ)
    {
        Vector2 s = new Vector2(a.x, a.z);
        Vector2 e = new Vector2(b.x, b.z);
        Vector2 d = e - s;
        float t0 = 0f, t1 = 1f;

        float minX = center.x - halfX;
        float maxX = center.x + halfX;
        float minZ = center.z - halfZ;
        float maxZ = center.z + halfZ;

        // X slabs
        if (Mathf.Abs(d.x) < 1e-6f)
        {
            if (s.x < minX || s.x > maxX) return false;
        }
        else
        {
            float inv = 1f / d.x;
            float tA = (minX - s.x) * inv;
            float tB = (maxX - s.x) * inv;
            if (tA > tB) { float tmp = tA; tA = tB; tB = tmp; }
            t0 = Mathf.Max(t0, tA);
            t1 = Mathf.Min(t1, tB);
            if (t0 > t1) return false;
        }

        // Z slabs
        if (Mathf.Abs(d.y) < 1e-6f)
        {
            if (s.y < minZ || s.y > maxZ) return false;
        }
        else
        {
            float inv = 1f / d.y;
            float tA = (minZ - s.y) * inv;
            float tB = (maxZ - s.y) * inv;
            if (tA > tB) { float tmp = tA; tA = tB; tB = tmp; }
            t0 = Mathf.Max(t0, tA);
            t1 = Mathf.Min(t1, tB);
            if (t0 > t1) return false;
        }

        return true;
    }
}