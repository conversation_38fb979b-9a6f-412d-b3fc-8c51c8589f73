using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 改进的炮台路径查找器 - 高效的绕行导航
/// 修复了avoidanceDistance过大时的穿透问题
/// </summary>
public static class GridConnon_PathFinder
{
    /// <summary>
    /// 查找绕行路径
    /// </summary>
    /// <param name="startPos">起始位置</param>
    /// <param name="targetPos">目标位置</param>
    /// <param name="movingCannon">移动的炮台（忽略自身）</param>
    /// <param name="avoidanceDistance">避障距离</param>
    /// <param name="navigationData">导航配置数据（可选）</param>
    /// <returns>路径点列表</returns>
    public static List<Vector3> FindPath(Vector3 startPos, Vector3 targetPos, GridConnon_Object movingCannon, float avoidanceDistance = 1.5f, GridConnon_NavigationData navigationData = null)
    {
        List<Vector3> path = new List<Vector3>();

        // 获取所有其他炮台位置
        List<Vector3> obstacles = GetObstaclePositions(movingCannon);

        // 检查直线路径是否可行
        if (IsPathClear(startPos, targetPos, obstacles, avoidanceDistance, navigationData))
        {
            // 直线路径可行，直接返回
            path.Add(startPos);
            path.Add(targetPos);

            if (navigationData?.showDetailedPathfindingLogs == true)
            {
                //Debug.Log($"炮台 {movingCannon?.ObjectId} 找到直线路径");
            }

            return path;
        }

        // 需要绕行，计算最佳绕行路径
        List<Vector3> detourPath = CalculateOptimalDetourPath(startPos, targetPos, obstacles, avoidanceDistance, navigationData);

        if (detourPath != null && detourPath.Count > 0)
        {
            if (navigationData?.showDetailedPathfindingLogs == true)
            {
                //Debug.Log($"炮台 {movingCannon?.ObjectId} 找到绕行路径，路径点数: {detourPath.Count}");
            }
            return detourPath;
        }

        // 如果找不到绕行路径，返回直线路径（最后的备选方案）
        if (navigationData?.showDetailedPathfindingLogs == true)
        {
            //Debug.LogWarning($"炮台 {movingCannon?.ObjectId} 无法找到安全绕行路径，使用直线路径");
        }

        path.Add(startPos);
        path.Add(targetPos);
        return path;
    }

    /// <summary>
    /// 获取所有其他炮台的位置作为障碍物
    /// </summary>
    /// <param name="movingCannon">正在移动的炮台（忽略自身）</param>
    /// <returns>障碍物位置列表</returns>
    private static List<Vector3> GetObstaclePositions(GridConnon_Object movingCannon)
    {
        List<Vector3> obstacles = new List<Vector3>();

        if (GridConnon_Controller.Instance == null)
            return obstacles;

        foreach (GridConnon_Object cannon in GridConnon_Controller.Instance.CurrentCannons)
        {
            // 跳过正在移动的炮台自身
            if (cannon == movingCannon || cannon == null)
                continue;

            obstacles.Add(cannon.GetWorldPosition());
        }

        return obstacles;
    }

    /// <summary>
    /// 检查两点间路径是否通畅（改进版本 - 动态调整检测精度）
    /// </summary>
    /// <param name="from">起点</param>
    /// <param name="to">终点</param>
    /// <param name="obstacles">障碍物列表</param>
    /// <param name="avoidanceDistance">避障距离</param>
    /// <param name="navigationData">导航配置数据（可选）</param>
    /// <returns>路径是否通畅</returns>
    private static bool IsPathClear(Vector3 from, Vector3 to, List<Vector3> obstacles, float avoidanceDistance, GridConnon_NavigationData navigationData = null)
    {
        Vector3 direction = (to - from).normalized;
        float distance = Vector3.Distance(from, to);

        // 使用导航数据的检测精度，如果没有则使用默认值
        float checkStep;
        if (navigationData != null)
        {
            checkStep = navigationData.GetPathCheckPrecision();
        }
        else
        {
            checkStep = Mathf.Max(0.1f, Mathf.Min(0.3f, avoidanceDistance * 0.15f));
        }

        // 沿路径检查障碍物
        for (float t = 0; t <= distance; t += checkStep)
        {
            Vector3 checkPoint = from + direction * t;

            foreach (Vector3 obstacle in obstacles)
            {
                // 只检查XZ平面距离，忽略Y轴
                Vector2 checkPoint2D = new Vector2(checkPoint.x, checkPoint.z);
                Vector2 obstacle2D = new Vector2(obstacle.x, obstacle.z);

                if (Vector2.Distance(checkPoint2D, obstacle2D) < avoidanceDistance)
                {
                    return false;
                }
            }
        }

        // 额外检查终点
        Vector2 endPoint2D = new Vector2(to.x, to.z);
        foreach (Vector3 obstacle in obstacles)
        {
            Vector2 obstacle2D = new Vector2(obstacle.x, obstacle.z);
            if (Vector2.Distance(endPoint2D, obstacle2D) < avoidanceDistance)
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 计算最佳绕行路径（改进版本）
    /// </summary>
    /// <param name="start">起点</param>
    /// <param name="target">终点</param>
    /// <param name="obstacles">障碍物列表</param>
    /// <param name="avoidanceDistance">避障距离</param>
    /// <param name="navigationData">导航配置数据（可选）</param>
    /// <returns>绕行路径</returns>
    private static List<Vector3> CalculateOptimalDetourPath(Vector3 start, Vector3 target, List<Vector3> obstacles, float avoidanceDistance, GridConnon_NavigationData navigationData = null)
    {
        List<Vector3> bestPath = null;
        float shortestDistance = float.MaxValue;

        // 找到所有阻挡路径的障碍物
        List<Vector3> blockingObstacles = FindAllBlockingObstacles(start, target, obstacles, avoidanceDistance);

        if (blockingObstacles.Count == 0)
        {
            // 没有阻挡障碍物，直接返回直线路径
            return new List<Vector3> { start, target };
        }

        // 对每个阻挡的障碍物，尝试计算绕行路径
        foreach (Vector3 obstacle in blockingObstacles)
        {
            List<Vector3> detourPath = TryDetourAroundObstacle(start, target, obstacle, obstacles, avoidanceDistance, navigationData);

            if (detourPath != null)
            {
                float totalDistance = CalculatePathDistance(detourPath);
                if (totalDistance < shortestDistance)
                {
                    shortestDistance = totalDistance;
                    bestPath = detourPath;
                }
            }
        }

        // 如果单个障碍物绕行失败，尝试多点绕行
        if (bestPath == null && (navigationData?.enableMultiPointDetour ?? true))
        {
            bestPath = CalculateMultiPointDetour(start, target, blockingObstacles, obstacles, avoidanceDistance, navigationData);
        }

        return bestPath;
    }

    /// <summary>
    /// 找到所有阻挡路径的障碍物
    /// </summary>
    private static List<Vector3> FindAllBlockingObstacles(Vector3 start, Vector3 target, List<Vector3> obstacles, float avoidanceDistance)
    {
        List<Vector3> blockingObstacles = new List<Vector3>();
        Vector3 direction = (target - start).normalized;
        float distance = Vector3.Distance(start, target);

        foreach (Vector3 obstacle in obstacles)
        {
            // 计算障碍物到路径线的距离
            Vector3 toObstacle = obstacle - start;
            float projectionLength = Vector3.Dot(toObstacle, direction);

            // 确保障碍物在路径范围内
            if (projectionLength >= -avoidanceDistance && projectionLength <= distance + avoidanceDistance)
            {
                Vector3 closestPointOnPath = start + direction * Mathf.Clamp(projectionLength, 0, distance);
                float distanceToPath = Vector3.Distance(obstacle, closestPointOnPath);

                if (distanceToPath < avoidanceDistance)
                {
                    blockingObstacles.Add(obstacle);
                }
            }
        }

        return blockingObstacles;
    }

    /// <summary>
    /// 尝试绕过单个障碍物
    /// </summary>
    private static List<Vector3> TryDetourAroundObstacle(Vector3 start, Vector3 target, Vector3 obstacle, List<Vector3> obstacles, float avoidanceDistance, GridConnon_NavigationData navigationData = null)
    {
        // 计算改进的绕行方向（8个方向）
        Vector3[] detourDirections = GetImprovedDetourDirections(start, target, obstacle, avoidanceDistance, navigationData);

        foreach (Vector3 detourDir in detourDirections)
        {
            Vector3 detourPoint = obstacle + detourDir;

            // 检查绕行路径是否可行
            if (IsPathClear(start, detourPoint, obstacles, avoidanceDistance, navigationData) &&
                IsPathClear(detourPoint, target, obstacles, avoidanceDistance, navigationData))
            {
                return new List<Vector3> { start, detourPoint, target };
            }
        }

        return null;
    }

    /// <summary>
    /// 获取改进的绕行方向（8个方向，动态距离）
    /// </summary>
    private static Vector3[] GetImprovedDetourDirections(Vector3 start, Vector3 target, Vector3 obstacle, float avoidanceDistance, GridConnon_NavigationData navigationData = null)
    {
        // 计算基本方向
        Vector3 pathDirection = (target - start).normalized;
        Vector3 perpendicular = Vector3.Cross(pathDirection, Vector3.up).normalized;

        // 使用导航数据的最大绕行距离，如果没有则使用默认值
        float baseDetourDistance = avoidanceDistance + 0.5f;
        float maxDetourDistance;

        if (navigationData != null)
        {
            maxDetourDistance = navigationData.GetMaxDetourDistance();
        }
        else
        {
            maxDetourDistance = avoidanceDistance * 1.8f;
        }

        float actualDetourDistance = Mathf.Min(baseDetourDistance, maxDetourDistance);

        // 计算8个绕行方向
        List<Vector3> directions = new List<Vector3>();

        // 主要4个方向
        directions.Add(perpendicular * actualDetourDistance);                    // 右侧
        directions.Add(-perpendicular * actualDetourDistance);                   // 左侧
        directions.Add(pathDirection * actualDetourDistance);                    // 前方
        directions.Add(-pathDirection * actualDetourDistance);                   // 后方

        // 对角线4个方向
        Vector3 rightForward = (perpendicular + pathDirection).normalized * actualDetourDistance;
        Vector3 leftForward = (-perpendicular + pathDirection).normalized * actualDetourDistance;
        Vector3 rightBackward = (perpendicular - pathDirection).normalized * actualDetourDistance;
        Vector3 leftBackward = (-perpendicular - pathDirection).normalized * actualDetourDistance;

        directions.Add(rightForward);
        directions.Add(leftForward);
        directions.Add(rightBackward);
        directions.Add(leftBackward);

        return directions.ToArray();
    }

    /// <summary>
    /// 计算多点绕行路径（处理复杂障碍物布局）
    /// </summary>
    private static List<Vector3> CalculateMultiPointDetour(Vector3 start, Vector3 target, List<Vector3> blockingObstacles, List<Vector3> allObstacles, float avoidanceDistance, GridConnon_NavigationData navigationData = null)
    {
        // 简化的多点绕行：尝试先绕到障碍物群的边缘，再绕到目标
        if (blockingObstacles.Count == 0) return null;

        // 计算障碍物群的中心和边界
        Vector3 obstacleCenter = Vector3.zero;
        foreach (Vector3 obs in blockingObstacles)
        {
            obstacleCenter += obs;
        }
        obstacleCenter /= blockingObstacles.Count;

        // 计算绕行到障碍物群侧面的路径
        Vector3 pathDirection = (target - start).normalized;
        Vector3 perpendicular = Vector3.Cross(pathDirection, Vector3.up).normalized;

        // 尝试左右两侧绕行
        float sideDistance = avoidanceDistance * 2f;
        Vector3[] sidePoints = {
            obstacleCenter + perpendicular * sideDistance,      // 右侧
            obstacleCenter - perpendicular * sideDistance       // 左侧
        };

        foreach (Vector3 sidePoint in sidePoints)
        {
            if (IsPathClear(start, sidePoint, allObstacles, avoidanceDistance, navigationData) &&
                IsPathClear(sidePoint, target, allObstacles, avoidanceDistance, navigationData))
            {
                return new List<Vector3> { start, sidePoint, target };
            }
        }

        return null;
    }

    /// <summary>
    /// 计算路径总距离
    /// </summary>
    private static float CalculatePathDistance(List<Vector3> path)
    {
        if (path == null || path.Count < 2) return float.MaxValue;

        float totalDistance = 0f;
        for (int i = 0; i < path.Count - 1; i++)
        {
            totalDistance += Vector3.Distance(path[i], path[i + 1]);
        }
        return totalDistance;
    }

    /// <summary>
    /// 简化路径，移除不必要的中间点（改进版本）
    /// </summary>
    /// <param name="originalPath">原始路径</param>
    /// <param name="obstacles">障碍物列表（2D）</param>
    /// <param name="avoidanceDistance">避障距离</param>
    /// <param name="navigationData">导航配置数据（可选）</param>
    /// <returns>简化后的路径</returns>
    public static List<Vector3> SimplifyPath(List<Vector3> originalPath, List<Vector2> obstacles, float avoidanceDistance, GridConnon_NavigationData navigationData = null)
    {
        // 转换2D障碍物为3D
        List<Vector3> obstacles3D = new List<Vector3>();
        foreach (Vector2 obs2D in obstacles)
        {
            obstacles3D.Add(new Vector3(obs2D.x, 0, obs2D.y));
        }

        return SimplifyPath3D(originalPath, obstacles3D, avoidanceDistance, navigationData);
    }

    /// <summary>
    /// 简化3D路径（改进版本 - 添加路径验证）
    /// </summary>
    /// <param name="originalPath">原始路径</param>
    /// <param name="obstacles">障碍物列表（3D）</param>
    /// <param name="avoidanceDistance">避障距离</param>
    /// <param name="navigationData">导航配置数据（可选）</param>
    /// <returns>简化后的路径</returns>
    public static List<Vector3> SimplifyPath3D(List<Vector3> originalPath, List<Vector3> obstacles, float avoidanceDistance, GridConnon_NavigationData navigationData = null)
    {
        if (originalPath == null || originalPath.Count <= 2)
            return originalPath;

        List<Vector3> simplifiedPath = new List<Vector3>();
        simplifiedPath.Add(originalPath[0]); // 添加起点

        int currentIndex = 0;
        while (currentIndex < originalPath.Count - 1)
        {
            int farthestIndex = currentIndex + 1;

            // 找到从当前点能直接到达的最远点
            for (int i = currentIndex + 2; i < originalPath.Count; i++)
            {
                if (IsPathClear(originalPath[currentIndex], originalPath[i], obstacles, avoidanceDistance, navigationData))
                {
                    farthestIndex = i;
                }
                else
                {
                    break;
                }
            }

            currentIndex = farthestIndex;
            if (currentIndex < originalPath.Count)
            {
                simplifiedPath.Add(originalPath[currentIndex]);
            }
        }

        // 验证简化后的路径是否真的可行（如果启用了路径验证）
        if (navigationData?.enablePathValidation ?? true)
        {
            if (!ValidateSimplifiedPath(simplifiedPath, obstacles, avoidanceDistance, navigationData))
            {
                // 如果简化后的路径不可行，返回原始路径
                if (navigationData?.showDetailedPathfindingLogs == true)
                {
                    //Debug.LogWarning("路径简化失败，返回原始路径");
                }
                return originalPath;
            }
        }

        return simplifiedPath;
    }

    /// <summary>
    /// 验证简化后的路径是否可行
    /// </summary>
    private static bool ValidateSimplifiedPath(List<Vector3> path, List<Vector3> obstacles, float avoidanceDistance, GridConnon_NavigationData navigationData = null)
    {
        if (path == null || path.Count < 2) return false;

        for (int i = 0; i < path.Count - 1; i++)
        {
            if (!IsPathClear(path[i], path[i + 1], obstacles, avoidanceDistance, navigationData))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 兼容性方法 - 保持向后兼容
    /// </summary>
    public static List<Vector3> SimplifyPath(List<Vector3> originalPath, List<Vector2> obstacles, float avoidanceDistance)
    {
        return SimplifyPath(originalPath, obstacles, avoidanceDistance, null);
    }

    /// <summary>
    /// 兼容性方法 - 保持向后兼容
    /// </summary>
    public static List<Vector3> SimplifyPath3D(List<Vector3> originalPath, List<Vector3> obstacles, float avoidanceDistance)
    {
        return SimplifyPath3D(originalPath, obstacles, avoidanceDistance, null);
    }
}