using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 改进的导航数据结构 - 存储导航相关配置和运行时数据
/// 支持更精确的避障和路径寻找配置
/// </summary>
[System.Serializable]
public class GridConnon_NavigationData
{
    [Header("基础导航参数")]
    [Tooltip("移动速度（单位/秒）")]
    [Range(0.1f, 50.0f)]
    public float moveSpeed = 5.0f;

    [Tooltip("避障检测距离")]
    [Range(0.1f, 5.0f)]
    public float avoidanceDistance = 1.5f;

    [Tooltip("到达目标的距离阈值")]
    [Range(0.01f, 0.5f)]
    public float arrivalThreshold = 0.1f;

    [Header("路径优化参数")]
    [Tooltip("路径重计算距离阈值")]
    [Range(0.1f, 2.0f)]
    public float recalculateDistance = 0.5f;

    [Tooltip("导航更新频率（每秒更新次数）")]
    [Range(1, 20)]
    public int updateFrequency = 10;

    [Header("改进的避障参数")]
    [Tooltip("最大绕行距离倍数（相对于避障距离）")]
    [Range(1.2f, 3.0f)]
    public float maxDetourDistanceMultiplier = 1.8f;

    [Tooltip("路径检测精度倍数（相对于避障距离）")]
    [Range(0.05f, 0.3f)]
    public float pathCheckPrecisionMultiplier = 0.15f;

    [Tooltip("是否启用多点绕行（处理复杂障碍物布局）")]
    public bool enableMultiPointDetour = true;

    [Tooltip("是否启用路径验证（确保简化后的路径可行）")]
    public bool enablePathValidation = true;

    [Header("调试参数")]
    [Tooltip("是否显示详细的路径寻找日志")]
    public bool showDetailedPathfindingLogs = false;

    // 运行时数据
    [System.NonSerialized]
    public List<Vector3> currentPath;
    [System.NonSerialized]
    public int currentPathIndex;
    [System.NonSerialized]
    public Vector3 targetPosition;
    [System.NonSerialized]
    public bool isMoving;
    [System.NonSerialized]
    public float lastUpdateTime;

    public GridConnon_NavigationData()
    {
        currentPath = new List<Vector3>();
        currentPathIndex = 0;
        isMoving = false;
        lastUpdateTime = 0f;
    }

    /// <summary>
    /// 重置运行时数据
    /// </summary>
    public void ResetRuntimeData()
    {
        currentPath.Clear();
        currentPathIndex = 0;
        isMoving = false;
        lastUpdateTime = 0f;
    }

    /// <summary>
    /// 检查是否需要更新导航
    /// </summary>
    /// <returns>是否需要更新</returns>
    public bool ShouldUpdate()
    {
        return Time.time - lastUpdateTime >= (1f / updateFrequency);
    }

    /// <summary>
    /// 更新时间戳
    /// </summary>
    public void UpdateTimestamp()
    {
        lastUpdateTime = Time.time;
    }

    /// <summary>
    /// 获取默认避障距离（供外部使用）
    /// </summary>
    /// <returns>避障距离</returns>
    public float GetAvoidanceDistance()
    {
        return avoidanceDistance;
    }

    /// <summary>
    /// 获取默认移动速度（供外部使用）
    /// </summary>
    /// <returns>移动速度</returns>
    public float GetMoveSpeed()
    {
        return moveSpeed;
    }

    /// <summary>
    /// 获取最大绕行距离
    /// </summary>
    /// <returns>最大绕行距离</returns>
    public float GetMaxDetourDistance()
    {
        return avoidanceDistance * maxDetourDistanceMultiplier;
    }

    /// <summary>
    /// 获取路径检测精度
    /// </summary>
    /// <returns>路径检测步长</returns>
    public float GetPathCheckPrecision()
    {
        float checkStep = avoidanceDistance * pathCheckPrecisionMultiplier;
        return Mathf.Max(0.1f, Mathf.Min(0.3f, checkStep));
    }

    /// <summary>
    /// 验证配置参数的合理性
    /// </summary>
    public void ValidateParameters()
    {
        // 确保参数在合理范围内
        moveSpeed = Mathf.Max(0.1f, moveSpeed);
        avoidanceDistance = Mathf.Max(0.5f, avoidanceDistance);
        arrivalThreshold = Mathf.Max(0.01f, arrivalThreshold);
        recalculateDistance = Mathf.Max(0.1f, recalculateDistance);
        updateFrequency = Mathf.Max(5, updateFrequency);
        maxDetourDistanceMultiplier = Mathf.Max(1.2f, maxDetourDistanceMultiplier);
        pathCheckPrecisionMultiplier = Mathf.Max(0.05f, pathCheckPrecisionMultiplier);

        // 确保到达阈值不大于避障距离的一半
        if (arrivalThreshold > avoidanceDistance * 0.5f)
        {
            arrivalThreshold = avoidanceDistance * 0.5f;
            //Debug.LogWarning($"到达阈值过大，已调整为 {arrivalThreshold:F2}");
        }
    }
}