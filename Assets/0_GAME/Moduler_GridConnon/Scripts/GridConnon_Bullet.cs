using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GridConnon_Bullet : MonoBehaviour
{
    public float Check_Frequency​;

    public GameObject ShotTarget;

    public float Speed;                // 飞行速度

    public bool HasHitTarget;          // 是否已击中目标

    public Vector3 TargetPositionOffset = Vector3.zero;  // 目标位置偏移

    private GameObject lastValidTarget;  // 记录最后有效的目标，用于目标被销毁时的清理

    // 调试相关
    [Header("调试设置")]
    public bool ShowDebugGizmos = true;          // 是否显示调试线框
    public Color DebugBoundsColor = Color.yellow; // 包围盒检测范围颜色

    [Header("子弹包围盒设置")]
    public Vector3 BulletBoundsSize = Vector3.one * 0.2f;  // 子弹自己的包围盒大小
    public Vector3 BulletBoundsOffset = Vector3.zero;      // 子弹包围盒的偏移量

    [Header("碰撞检测设置")]
    [Range(0.1f, 2.0f)]
    [Tooltip("击中目标的碰撞百分比。0.5=碰撞框缩小50%，1.0=正常大小，1.2=碰撞框扩大20%")]
    public float HitTargetCollisionPercent = 1.0f;         // 击中目标的碰撞百分比
    [Range(0.1f, 2.0f)]
    [Tooltip("穿过物体的碰撞百分比。0.5=碰撞框缩小50%，1.0=正常大小，1.2=碰撞框扩大20%")]
    public float PenetrateCollisionPercent = 1.0f;         // 穿过物体的碰撞百分比

    [Header("爆炸特效配置")]

    [Tooltip("爆炸特效销毁时间")]
    public float explosionEffectDuration = 2f;

    Vector3 startPosition;      // 发射起始位置
    Vector3 direction;          // 飞行方向
    float startTime;            // 发射时间
    float lifetime;             // 生存时间
    HashSet<GameObject> penetratedObjects = new HashSet<GameObject>(); // 已穿过的物体
    public bool IsExpired => Time.time - startTime > lifetime;

    // 碰撞检测计时器
    private float lastCheckTime = 0f;


    void Awake()
    {


    }

    void Update()
    {
        if (HasHitTarget)
            return;
        // 子弹移动逻辑 - 飞向目标
        MoveBulletToTarget();

        // 根据Check_Frequency监测碰撞（仅在正常移动时检测，插值移动时已经检测过了）
        if (Time.time - lastCheckTime >= Check_Frequency​)
        {
            CheckBulletCollision();
            lastCheckTime = Time.time;
        }
    }


    public void SetTarget(GameObject shotTarget)
    {
        ShotTarget = shotTarget;
        lastValidTarget = shotTarget;  // 记录最后有效目标
        GridConnon_Controller.Act_On_Bullet_Targeted?.Invoke(this, ShotTarget);
    }

    /// <summary>
    /// 设置目标和位置偏移
    /// </summary>
    public void SetTargetWithOffset(GameObject shotTarget, Vector3 positionOffset)
    {
        ShotTarget = shotTarget;
        lastValidTarget = shotTarget;  // 记录最后有效目标
        TargetPositionOffset = positionOffset;

        //        Debug.Log($"[GridConnon_Bullet] 子弹 {gameObject.name} 设置目标: {shotTarget?.name} (是否为空: {shotTarget == null})");

        GridConnon_Controller.Act_On_Bullet_Targeted?.Invoke(this, ShotTarget);
    }

    /// <summary>
    /// 子弹移动逻辑 - 使用插值方式飞向目标，防止穿透
    /// </summary>
    void MoveBulletToTarget()
    {
        // 如果没有目标或已击中目标，则不移动
        if (ShotTarget == null)
        {
            Debug.LogError($"[GridConnon_Bullet] 子弹 {gameObject.name} 目标为空！lastValidTarget: {lastValidTarget?.name} (是否为空: {lastValidTarget == null})");
            return;
        }

        if (HasHitTarget)
            return;

        Vector3 targetPosition = ShotTarget.transform.position + TargetPositionOffset;
        Vector3 currentPosition = transform.position;

        // 计算到目标的距离
        float distanceToTarget = Vector3.Distance(currentPosition, targetPosition);

        // 如果已经非常接近目标，直接移动到目标位置
        if (distanceToTarget <= 0.01f)
        {
            transform.position = targetPosition;
            return;
        }

        // 计算本帧移动的距离
        float moveDistance = Speed * Time.deltaTime;

        // 使用插值移动，防止穿透
        if (moveDistance >= distanceToTarget)
        {
            // 如果本帧移动距离会超过目标，分步移动并检测碰撞
            MoveWithInterpolation(currentPosition, targetPosition, distanceToTarget);
        }
        else
        {
            // 正常移动
            Vector3 directionToTarget = (targetPosition - currentPosition).normalized;
            transform.position = currentPosition + directionToTarget * moveDistance;

            // 更新飞行方向（用于调试绘制）
            direction = directionToTarget;
        }
    }

    /// <summary>
    /// 使用插值方式移动，分步检测碰撞防止穿透
    /// </summary>
    void MoveWithInterpolation(Vector3 startPos, Vector3 endPos, float totalDistance)
    {
        // 计算插值步数，确保每步移动距离不超过子弹包围盒大小的一半
        float stepSize = Mathf.Min(BulletBoundsSize.magnitude * 0.5f, totalDistance * 0.1f);
        int steps = Mathf.CeilToInt(totalDistance / stepSize);
        steps = Mathf.Max(steps, 5); // 最少5步

        Vector3 currentPos = startPos;

        for (int i = 1; i <= steps; i++)
        {
            // 计算插值位置
            float t = (float)i / steps;
            Vector3 nextPos = Vector3.Lerp(startPos, endPos, t);

            // 更新位置
            transform.position = nextPos;

            // 每步都检查碰撞
            CheckBulletCollision();

            // 如果已经击中目标，停止移动
            if (HasHitTarget)
            {
                break;
            }

            currentPos = nextPos;
        }

        // 更新飞行方向（用于调试绘制）
        direction = (endPos - startPos).normalized;
    }

    /// <summary>
    /// 检查碰撞 - 使用百分比控制碰撞框大小（带缓存优化）
    /// </summary>
    /// <param name="bulletBounds">子弹包围盒</param>
    /// <param name="collisionBody">目标碰撞体</param>
    /// <param name="collisionPercent">碰撞百分比</param>
    /// <returns>是否发生碰撞</returns>
    bool CheckCollisionWithPercent(Bounds bulletBounds, GridConnon_Bullet_CollisionBody collisionBody, float collisionPercent)
    {
        // 子弹的调整后包围盒（子弹移动频繁，不缓存）
        Vector3 adjustedBulletSize = bulletBounds.size * collisionPercent;
        Bounds adjustedBulletBounds = new Bounds(bulletBounds.center, adjustedBulletSize);

        // 目标的调整后包围盒（使用缓存）
        Bounds adjustedTargetBounds = collisionBody.GetAdjustedBounds(collisionPercent);

        // 检测调整后的包围盒是否相交
        return adjustedBulletBounds.Intersects(adjustedTargetBounds);
    }

    /// <summary>
    /// 检查子弹碰撞 - 统一使用包围盒检测
    /// </summary>
    void CheckBulletCollision()
    {
        Vector3 bulletPos = transform.position + BulletBoundsOffset;
        Bounds bulletBounds = new Bounds(bulletPos, BulletBoundsSize);

        // 重置所有碰撞体的碰撞状态
        foreach (var collisionBody in GridConnon_Bullet_CollisionBody.GetAllCollisionBodies())
        {
            if (collisionBody != null)
            {
                collisionBody.SetHitState(false);
            }
        }

        // 遍历所有碰撞体
        foreach (var collisionBody in GridConnon_Bullet_CollisionBody.GetAllCollisionBodies())
        {
            if (collisionBody == null) continue;
            GameObject obj = collisionBody.gameObject;
            if (obj == gameObject) continue; // 跳过子弹本身

            // 检查是否是目标物体
            if (ShotTarget == obj && !HasHitTarget)
            {
                // 使用击中目标的碰撞百分比检测
                if (CheckCollisionWithPercent(bulletBounds, collisionBody, HitTargetCollisionPercent))
                {
                    // 设置碰撞状态用于调试显示
                    collisionBody.SetHitState(true);

                    // 击中目标
                    HasHitTarget = true;
                    GridConnon_Controller.Act_On_Bullet_HitTarget?.Invoke(this, obj);

                    DestroyBullet();
                }
            }
            else if (!penetratedObjects.Contains(obj))
            {
                // 使用穿过物体的碰撞百分比检测
                if (CheckCollisionWithPercent(bulletBounds, collisionBody, PenetrateCollisionPercent))
                {
                    // 设置碰撞状态用于调试显示
                    collisionBody.SetHitState(true);

                    // 穿过其他物体
                    penetratedObjects.Add(obj);
                    GridConnon_Controller.Act_On_Bullet_Thorw?.Invoke(this, obj);
                }
            }
        }
    }

    /// <summary>
    /// 销毁子弹
    /// </summary>
    public void DestroyBullet()
    {
        // 创建爆炸特效
        CreateExplosionEffect();

        // 清理穿透物体记录
        penetratedObjects.Clear();

        // 重置所有碰撞体的碰撞状态
        foreach (var collisionBody in GridConnon_Bullet_CollisionBody.GetAllCollisionBodies())
        {
            if (collisionBody != null)
            {
                collisionBody.SetHitState(false);
            }
        }

        // 销毁游戏对象
        Destroy(gameObject);
    }

    /// <summary>
    /// 创建爆炸特效
    /// </summary>
    private void CreateExplosionEffect()
    {


        // 爆炸特效位置为子弹当前位置
        Vector3 explosionPosition = transform.position;

        // 爆炸特效角度与子弹角度一致
        Quaternion explosionRotation = transform.rotation;

        // 创建爆炸特效
        Tool_EffectPool.Instance.CreateEffect("CNT/Effect/Bullet_Explosion", explosionPosition, explosionRotation, explosionEffectDuration);

        //Debug.Log($"子弹 {gameObject.name} 创建爆炸特效，位置: {explosionPosition}");
    }

    #region 调试绘制

    /// <summary>
    /// 在Scene视图中绘制调试信息
    /// </summary>
    void OnDrawGizmos()
    {
        if (!ShowDebugGizmos) return;

        Vector3 bulletPos = transform.position + BulletBoundsOffset;

        // 绘制子弹的包围盒
        Gizmos.color = DebugBoundsColor;
        Gizmos.DrawWireCube(bulletPos, BulletBoundsSize);

        // 绘制半透明的子弹包围盒
        Gizmos.color = new Color(DebugBoundsColor.r, DebugBoundsColor.g, DebugBoundsColor.b, 0.1f);
        Gizmos.DrawCube(bulletPos, BulletBoundsSize);

        // 绘制子弹中心点
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(bulletPos, 0.05f);

        // 绘制物体原点（用于对比偏移）
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(transform.position, 0.03f);

        // 绘制到目标的连线
        if (ShotTarget != null)
        {
            Gizmos.color = HasHitTarget ? Color.green : Color.red;
            Gizmos.DrawLine(bulletPos, ShotTarget.transform.position);
        }
    }

    /// <summary>
    /// 仅在选中时绘制调试信息
    /// </summary>
    void OnDrawGizmosSelected()
    {
        if (!ShowDebugGizmos) return;

        // 绘制更详细的信息
        Gizmos.color = Color.white;

        // 绘制子弹的前进方向
        if (direction != Vector3.zero)
        {
            Gizmos.DrawRay(transform.position, direction.normalized * 2f);
        }

        // 显示穿透过的物体
        Gizmos.color = Color.magenta;
        foreach (GameObject penetrated in penetratedObjects)
        {
            if (penetrated != null)
            {
                Gizmos.DrawWireCube(penetrated.transform.position, Vector3.one * 0.2f);
            }
        }
    }

    #endregion
}

