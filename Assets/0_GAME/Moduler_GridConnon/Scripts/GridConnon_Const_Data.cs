using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// GridConnon 模块常量数据配置
/// 存放所有相关的参数、颜色、预制体路径等配置
/// </summary>
[System.Serializable]
public class GridConnon_Const_Data
{


    public Dictionary<string, string> ColorIDMap = new Dictionary<string, string>();
    public Dictionary<string, float[]> ColorDic = new Dictionary<string, float[]>();

    public float ShootingSpeed_CheckInterval;
    public float MovingSpeed;
    public bool EnableShooting3Synthesis;

    public int Virb_Shoot;
    public int Virb_Normal;


    public string normalCannonPrefabPath = "GridConnon/NormalCannon";

    public string triangleCannonPrefabPath = "GridConnon/TriangleCannon";


    public string questionNormalCannonPrefabPath = "GridConnon/QuestionNormalCannon";

    public string questionTriangleCannonPrefabPath = "GridConnon/QuestionTriangleCannon";
    public string treasureCannonPrefabPath = "GridConnon/TreasureCannon";

    public string bulletPrefabPath = "GridConnon/Bullet";


    public string linkstickPrefabPath = "GridConnon/LinkStick";


    public string LevelText = "";

    /// <summary>
    /// 根据物体类型获取预制体路径
    /// </summary>
    /// <param name="objectType">物体类型</param>
    /// <returns>预制体资源路径</returns>
    public string GetPrefabPath(GridConnon_ObjectType objectType)
    {
        switch (objectType)
        {
            case GridConnon_ObjectType.NormalCannon:
                return normalCannonPrefabPath;
            case GridConnon_ObjectType.TriangleCannon:
                return triangleCannonPrefabPath;
            case GridConnon_ObjectType.QuestionNormalCannon:
                return questionNormalCannonPrefabPath;
            case GridConnon_ObjectType.QuestionTriangleCannon:
                return questionTriangleCannonPrefabPath;
            case GridConnon_ObjectType.TreasureCannon:
                return treasureCannonPrefabPath;
            default:
                //Debug.LogError($"未知的物体类型: {objectType}");
                return normalCannonPrefabPath;
        }
    }



    // 通过ID获取颜色的辅助方法
    public Color GetColorById(int id)
    {
        string idStr = id.ToString();
        if (ColorIDMap.TryGetValue(idStr, out string colorName) && ColorDic.TryGetValue(colorName, out float[] colorValues))
        {
            if (colorValues.Length >= 4)
            {
                return new Color(colorValues[0], colorValues[1], colorValues[2], colorValues[3]);
            }
        }
        return Color.white; // 默认返回白色
    }


    public float[] Parent_Scale = new float[3];

    // 辅助方法：获取Vector3格式的Parent_Scale
    public Vector3 GetParentScale()
    {
        return new Vector3(Parent_Scale[0], Parent_Scale[1], Parent_Scale[2]);
    }

}

/// <summary>
/// GridConnon 物体类型枚举
/// </summary>
public enum GridConnon_ObjectType
{
    NormalCannon,    // 普通炮（有颜色）
    TriangleCannon,  // 三角炮（衍生自普通炮）
    QuestionNormalCannon,  // 问号普通炮（衍生自普通炮）,
    QuestionTriangleCannon,  // 问号三角炮（衍生自普通炮）
    TreasureCannon   // 宝箱炮（衍生自普通炮，无颜色）
}