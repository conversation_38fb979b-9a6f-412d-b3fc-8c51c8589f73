using System.Collections.Generic;
using UnityEngine;
using System.Linq;

/// <summary>
/// GridConnon链接系统管理器 - 管理炮台间的链接关系
/// 基于关卡数据初始化，支持链接组状态检查和协调行为
/// </summary>
public class GridConnon_LinkSystem : MonoBehaviour
{
    private static GridConnon_LinkSystem _instance;
    private static bool _applicationIsQuitting = false;
    private static bool _sceneChanging = false; // 添加场景切换标志

    /// <summary>
    /// 单例实例
    /// </summary>
    public static GridConnon_LinkSystem Instance
    {
        get
        {
            // 如果应用程序正在退出或场景正在切换，不创建新实例
            if (_applicationIsQuitting || _sceneChanging)
            {
                //Debug.LogWarning("[链接系统] 应用程序正在退出或场景正在切换，返回null实例");
                return null;
            }

            if (_instance == null)
            {
                _instance = FindObjectOfType<GridConnon_LinkSystem>();
                if (_instance == null)
                {
                    // 只有在非退出状态下才创建新实例
                    GameObject go = new GameObject("GridConnon_LinkSystem");
                    _instance = go.AddComponent<GridConnon_LinkSystem>();
                    //Debug.Log("[链接系统] 创建新的单例实例");
                }
            }
            return _instance;
        }
    }

    [Header("链接系统配置")]
    [Tooltip("圆棍预制体路径")]
    public string linkStickPrefabPath = "GridConnon/LinkStick";

    [Header("调试信息")]
    [Tooltip("当前所有链接关系")]
    [SerializeField] private List<string> debugLinkRelations = new List<string>();

    [Tooltip("当前所有链接组")]
    [SerializeField] private List<string> debugLinkGroups = new List<string>();

    // 链接关系存储：字典，键为炮台实例，值为直接链接的炮台列表
    private Dictionary<GridConnon_Cannon, List<GridConnon_Cannon>> linkRelations = new Dictionary<GridConnon_Cannon, List<GridConnon_Cannon>>();

    // 链接组存储：每个链接组是一个相互连接的炮台集合
    private List<HashSet<GridConnon_Cannon>> linkGroups = new List<HashSet<GridConnon_Cannon>>();

    // 炮台ID到实例的映射（用于关卡数据解析）
    private Dictionary<string, GridConnon_Cannon> cannonIdMap = new Dictionary<string, GridConnon_Cannon>();

    // 新增：组级销毁方向缓存（true=向左，false=向右）
    // 说明：当组中心与网格中心重合时，按组内确定性规则决定一次并缓存，保证同组一致
    private readonly Dictionary<string, bool> groupDestroyDirectionCache = new Dictionary<string, bool>();

    #region Unity生命周期

    private void Awake()
    {
        // 重置所有标志
        _applicationIsQuitting = false;
        _sceneChanging = false;

        if (_instance == null)
        {
            _instance = this;
            // 监听场景变化，确保在场景切换时能被正确清理
            UnityEngine.SceneManagement.SceneManager.sceneUnloaded += OnSceneUnloaded;
            UnityEngine.SceneManagement.SceneManager.sceneLoaded += OnSceneLoaded;
            //Debug.Log("[链接系统] 单例实例已设置");
        }
        else if (_instance != this)
        {
            //Debug.LogWarning("[链接系统] 检测到重复实例，销毁当前对象");
            Destroy(gameObject);
            return;
        }
    }

    private void OnDestroy()
    {
        //Debug.Log("[链接系统] OnDestroy 被调用");

        // 取消场景监听
        UnityEngine.SceneManagement.SceneManager.sceneUnloaded -= OnSceneUnloaded;
        UnityEngine.SceneManagement.SceneManager.sceneLoaded -= OnSceneLoaded;

        // 清理所有链接关系，但只在非退出状态下进行
        if (!_applicationIsQuitting)
        {
            ClearAllLinks();
        }

        // 清理单例引用
        if (_instance == this)
        {
            _instance = null;
            //Debug.Log("[链接系统] 单例实例已清理");
        }
    }

    /// <summary>
    /// 场景卸载时的清理方法
    /// </summary>
    private void OnSceneUnloaded(UnityEngine.SceneManagement.Scene scene)
    {
        //Debug.Log("[链接系统] 场景卸载，执行清理");

        // 设置场景切换标志，防止在销毁过程中重新创建
        _sceneChanging = true;

        // 场景切换时强制清理
        if (_instance != null && _instance.gameObject != null)
        {
            // 立即清理所有链接
            _instance.ClearAllLinks();

            // 销毁GameObject
            if (Application.isPlaying)
            {
                Destroy(_instance.gameObject);
            }
            else
            {
                DestroyImmediate(_instance.gameObject);
            }

            _instance = null;
        }
    }

    /// <summary>
    /// 场景加载时的重置方法
    /// </summary>
    private void OnSceneLoaded(UnityEngine.SceneManagement.Scene scene, UnityEngine.SceneManagement.LoadSceneMode mode)
    {
        //Debug.Log("[链接系统] 场景加载，重置场景切换标志");

        // 重置场景切换标志，允许在新场景中创建新实例
        ResetSceneFlags();
    }

    /// <summary>
    /// 重置场景相关标志（静态方法，可以从外部调用）
    /// </summary>
    public static void ResetSceneFlags()
    {
        _sceneChanging = false;
        _applicationIsQuitting = false;
        //Debug.Log("[链接系统] 重置场景标志");
    }

    private void OnApplicationQuit()
    {
        //Debug.Log("[链接系统] 应用程序正在退出");
        _applicationIsQuitting = true;
        _sceneChanging = true; // 同时设置场景切换标志

        // 立即清理所有资源
        ClearAllLinks();
    }

    #endregion

    #region 初始化和清理

    /// <summary>
    /// 根据关卡数据初始化链接关系
    /// </summary>
    /// <param name="cannons">关卡中的所有炮台</param>
    public void InitializeLinkSystem(List<GridConnon_Object> cannons)
    {
        //Debug.Log("[链接系统] 开始初始化链接关系");

        // 清空现有数据
        ClearAllLinks();

        // 建立炮台ID映射
        BuildCannonIdMap(cannons);

        // 解析链接关系
        ParseLinkRelationsFromData(cannons);

        // 生成链接组
        GenerateLinkGroups();

        // 创建圆棍
        CreateLinkSticks();

        // 更新调试信息
        UpdateDebugInfo();

        //Debug.Log($"[链接系统] 初始化完成，共{linkRelations.Count}个炮台有链接关系，{linkGroups.Count}个链接组");
    }

    /// <summary>
    /// 建立炮台ID到实例的映射
    /// </summary>
    /// <param name="cannons">炮台列表</param>
    private void BuildCannonIdMap(List<GridConnon_Object> cannons)
    {
        cannonIdMap.Clear();

        foreach (var cannonObj in cannons)
        {
            if (cannonObj is GridConnon_Cannon cannon)
            {
                cannonIdMap[cannon.ObjectId.ToString()] = cannon;
            }
        }

        //Debug.Log($"[链接系统] 建立炮台ID映射完成，共{cannonIdMap.Count}个炮台");
    }

    /// <summary>
    /// 从关卡数据解析链接关系
    /// </summary>
    /// <param name="cannons">炮台列表</param>
    private void ParseLinkRelationsFromData(List<GridConnon_Object> cannons)
    {
        foreach (var cannonObj in cannons)
        {
            if (!(cannonObj is GridConnon_Cannon cannon)) continue;

            // 获取炮台的链接数据
            var cannonData = cannon.GetCannonData();
            if (cannonData?.linkedCannonIds == null || cannonData.linkedCannonIds.Count == 0) continue;

            // 检查是否启用链接
            if (!cannonData.enableLinking) continue;

            // 检查类型限制（宝箱炮不能链接）
            if (cannon.ObjectType == GridConnon_ObjectType.TreasureCannon)
            {
                //Debug.LogWarning($"[链接系统] 炮台{cannon.ObjectId}是宝箱类型，跳过链接配置");
                continue;
            }

            // 处理每个链接目标
            foreach (string targetId in cannonData.linkedCannonIds)
            {
                if (cannonIdMap.TryGetValue(targetId, out GridConnon_Cannon targetCannon))
                {
                    // 检查目标炮台类型限制
                    if (targetCannon.ObjectType == GridConnon_ObjectType.TreasureCannon)
                    {
                        //Debug.LogWarning($"[链接系统] 目标炮台{targetId}是宝箱类型，跳过链接");
                        continue;
                    }

                    // 建立双向链接
                    AddLinkRelation(cannon, targetCannon);
                    AddLinkRelation(targetCannon, cannon);

                    //Debug.Log($"[链接系统] 建立链接：{cannon.ObjectId} ↔ {targetCannon.ObjectId}");
                }
                else
                {
                    //Debug.LogWarning($"[链接系统] 找不到目标炮台：{targetId}");
                }
            }
        }
    }

    /// <summary>
    /// 添加链接关系
    /// </summary>
    /// <param name="cannon">源炮台</param>
    /// <param name="targetCannon">目标炮台</param>
    private void AddLinkRelation(GridConnon_Cannon cannon, GridConnon_Cannon targetCannon)
    {
        if (!linkRelations.ContainsKey(cannon))
        {
            linkRelations[cannon] = new List<GridConnon_Cannon>();
        }

        if (!linkRelations[cannon].Contains(targetCannon))
        {
            linkRelations[cannon].Add(targetCannon);
        }
    }

    /// <summary>
    /// 生成链接组（连通图）
    /// </summary>
    private void GenerateLinkGroups()
    {
        linkGroups.Clear();
        HashSet<GridConnon_Cannon> visited = new HashSet<GridConnon_Cannon>();

        foreach (var cannon in linkRelations.Keys)
        {
            if (!visited.Contains(cannon))
            {
                HashSet<GridConnon_Cannon> group = new HashSet<GridConnon_Cannon>();
                DepthFirstSearch(cannon, visited, group);

                if (group.Count > 1) // 只有多于1个炮台的才算链接组
                {
                    linkGroups.Add(group);
                }
            }
        }
    }

    /// <summary>
    /// 深度优先搜索构建连通图
    /// </summary>
    /// <param name="cannon">当前炮台</param>
    /// <param name="visited">已访问集合</param>
    /// <param name="group">当前组</param>
    private void DepthFirstSearch(GridConnon_Cannon cannon, HashSet<GridConnon_Cannon> visited, HashSet<GridConnon_Cannon> group)
    {
        visited.Add(cannon);
        group.Add(cannon);

        if (linkRelations.ContainsKey(cannon))
        {
            foreach (var linkedCannon in linkRelations[cannon])
            {
                if (!visited.Contains(linkedCannon))
                {
                    DepthFirstSearch(linkedCannon, visited, group);
                }
            }
        }
    }

    /// <summary>
    /// 为链接关系创建圆棍
    /// </summary>
    private void CreateLinkSticks()
    {
        // 为每个炮台的每个链接目标创建圆棍
        foreach (var kvp in linkRelations)
        {
            var cannon = kvp.Key;
            var linkedCannons = kvp.Value;

            // 为每个直接链接的炮台创建圆棍
            foreach (var targetCannon in linkedCannons)
            {
                // 为当前炮台创建指向目标炮台的圆棍
                cannon.SetLinkStickTarget(targetCannon);
                //Debug.Log($"[链接系统] 炮台 {cannon.ObjectId} 设置圆棍指向炮台 {targetCannon.ObjectId}");
            }
        }

        //Debug.Log($"[链接系统] 圆棍创建完成，共处理 {linkRelations.Count} 个炮台的链接关系");
    }

    /// <summary>
    /// 清空所有链接关系
    /// </summary>
    public void ClearAllLinks()
    {
        try
        {
            //Debug.Log("[链接系统] 开始清理所有链接关系");

            // 安全清理圆棍 - 创建副本避免在遍历时修改集合
            var cannonsToClean = new List<GridConnon_Cannon>();
            foreach (var cannon in linkRelations.Keys)
            {
                if (cannon != null && cannon.gameObject != null)
                {
                    cannonsToClean.Add(cannon);
                }
            }

            // 清理圆棍
            foreach (var cannon in cannonsToClean)
            {
                try
                {
                    if (cannon != null && cannon.gameObject != null)
                    {
                        cannon.ClearAllLinkSticks();
                    }
                }
                catch (System.Exception e)
                {
                    //Debug.LogWarning($"[链接系统] 清理炮台 {cannon?.ObjectId} 的圆棍时出错: {e.Message}");
                }
            }

            // 清理数据结构
            linkRelations.Clear();
            linkGroups.Clear();
            cannonIdMap.Clear();
            groupDestroyDirectionCache.Clear(); // 新增：清理组级方向缓存

            // 安全清理调试信息
            if (debugLinkRelations != null)
                debugLinkRelations.Clear();
            if (debugLinkGroups != null)
                debugLinkGroups.Clear();

            //Debug.Log("[链接系统] 已清空所有链接关系");
        }
        catch (System.Exception e)
        {
            //Debug.LogError($"[链接系统] 清理所有链接时发生错误: {e.Message}");
        }
    }

    /// <summary>
    /// 清理指定炮台的链接（炮台销毁时调用）
    /// </summary>
    /// <param name="cannon">要清理的炮台</param>
    public void ClearLinksForCannon(GridConnon_Cannon cannon)
    {
        if (cannon == null) return;

        try
        {
            // 安全清理该炮台的圆棍
            if (cannon.gameObject != null)
            {
                cannon.ClearAllLinkSticks();
            }

            // 从其他炮台的链接关系中移除该炮台
            var cannonsToUpdate = new List<GridConnon_Cannon>();
            var keysToCheck = new List<GridConnon_Cannon>(linkRelations.Keys);

            foreach (var key in keysToCheck)
            {
                if (key != null && linkRelations.ContainsKey(key))
                {
                    if (linkRelations[key].Contains(cannon))
                    {
                        linkRelations[key].Remove(cannon);
                        cannonsToUpdate.Add(key);
                    }
                }
            }

            // 移除该炮台的链接关系
            linkRelations.Remove(cannon);

            // 重新生成链接组（只在非退出状态下执行）
            if (!_applicationIsQuitting)
            {
                GenerateLinkGroups();

                // 更新调试信息
                UpdateDebugInfo();
            }

            //Debug.Log($"[链接系统] 已清理炮台{cannon.ObjectId}的所有链接关系");
        }
        catch (System.Exception e)
        {
            //Debug.LogWarning($"[链接系统] 清理炮台{cannon?.ObjectId}链接时出错: {e.Message}");
        }
    }

    #endregion

    #region 查询接口

    /// <summary>
    /// 检查两个炮台是否直接链接
    /// </summary>
    /// <param name="cannonA">炮台A</param>
    /// <param name="cannonB">炮台B</param>
    /// <returns>是否直接链接</returns>
    public bool IsLinked(GridConnon_Cannon cannonA, GridConnon_Cannon cannonB)
    {
        if (_applicationIsQuitting || cannonA == null || cannonB == null)
        {
            //Debug.Log($"[链接检查] 参数检查失败: _applicationIsQuitting={_applicationIsQuitting}, cannonA={cannonA?.ObjectId}, cannonB={cannonB?.ObjectId}");
            return false;
        }

        bool hasRelation = linkRelations.ContainsKey(cannonA);
        //Debug.Log($"[链接检查] 炮台{cannonA.ObjectId}是否存在链接关系: {hasRelation}");

        if (!hasRelation)
        {
            //Debug.Log($"[链接检查] 炮台{cannonA.ObjectId}没有任何链接关系");
            return false;
        }

        bool isLinked = linkRelations[cannonA].Contains(cannonB);
        //Debug.Log($"[链接检查] 炮台{cannonA.ObjectId}与{cannonB.ObjectId}是否直接链接: {isLinked}");

        if (hasRelation)
        {
            var linkedIds = linkRelations[cannonA].Select(c => c.ObjectId.ToString()).ToList();
            //Debug.Log($"[链接检查] 炮台{cannonA.ObjectId}的所有链接目标: [{string.Join(", ", linkedIds)}]");
        }

        return isLinked;
    }

    /// <summary>
    /// 检查炮台是否在任何链接组中
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <returns>是否在链接组中</returns>
    public bool IsInLinkGroup(GridConnon_Cannon cannon)
    {
        if (_applicationIsQuitting || cannon == null) return false;

        return linkRelations.ContainsKey(cannon) && linkRelations[cannon].Count > 0;
    }

    /// <summary>
    /// 获取指定炮台所属的链接组
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <returns>链接组，如果不在任何组中返回null</returns>
    public HashSet<GridConnon_Cannon> GetLinkGroup(GridConnon_Cannon cannon)
    {
        if (_applicationIsQuitting || cannon == null) return null;

        foreach (var group in linkGroups)
        {
            if (group.Contains(cannon))
            {
                return group;
            }
        }

        return null;
    }

    /// <summary>
    /// 获取直接链接的炮台列表
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <returns>直接链接的炮台列表</returns>
    public List<GridConnon_Cannon> GetDirectlyLinkedCannons(GridConnon_Cannon cannon)
    {
        if (cannon == null || !linkRelations.ContainsKey(cannon))
        {
            return new List<GridConnon_Cannon>();
        }

        return new List<GridConnon_Cannon>(linkRelations[cannon]);
    }

    /// <summary>
    /// 获取链接组大小
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <returns>链接组大小，如果不在组中返回1</returns>
    public int GetLinkGroupSize(GridConnon_Cannon cannon)
    {
        var group = GetLinkGroup(cannon);
        return group?.Count ?? 1;
    }

    #endregion

    #region 状态检查

    /// <summary>
    /// 检查链接组中所有炮台是否都处于指定状态
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <param name="state">要检查的状态</param>
    /// <returns>是否全部处于指定状态</returns>
    public bool IsAllInGroupInState(GridConnon_Cannon cannon, GridConnon_Cannon_State state)
    {
        var group = GetLinkGroup(cannon);

        // 没有链接组，只检查自己
        if (group == null)
        {
            return cannon.GetCurrentState() == state;
        }

        // 检查组内所有炮台
        foreach (var linkedCannon in group)
        {
            if (linkedCannon == null || linkedCannon.GetCurrentState() != state)
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 检查链接组中所有炮台是否都准备好移动到射击格
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <returns>是否全部准备好</returns>
    public bool IsAllInGroupReadyForShotGrid(GridConnon_Cannon cannon)
    {
        var group = GetLinkGroup(cannon);

        // 没有链接组，只检查自己
        if (group == null)
        {
            return IsCannonReadyForShotGrid(cannon);
        }

        // 检查组内所有炮台
        foreach (var linkedCannon in group)
        {
            if (!IsCannonReadyForShotGrid(linkedCannon))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 检查单个炮台是否准备好移动到射击格
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <returns>是否准备好</returns>
    private bool IsCannonReadyForShotGrid(GridConnon_Cannon cannon)
    {
        if (cannon == null) return false;

        // 必须是FirstRow状态
        if (cannon.GetCurrentState() != GridConnon_Cannon_State.FirstRow)
            return false;

        // 如果是问号类型，检查动画是否完成
        if (IsQuestionType(cannon) && !IsQuestionAnimationComplete(cannon))
            return false;

        return true;
    }

    /// <summary>
    /// 检查是否为问号类型
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <returns>是否为问号类型</returns>
    private bool IsQuestionType(GridConnon_Cannon cannon)
    {
        return cannon.ObjectType == GridConnon_ObjectType.QuestionNormalCannon ||
               cannon.ObjectType == GridConnon_ObjectType.QuestionTriangleCannon;
    }

    /// <summary>
    /// 检查问号炮的动画是否完成
    /// </summary>
    /// <param name="cannon">问号炮</param>
    /// <returns>动画是否完成</returns>
    private bool IsQuestionAnimationComplete(GridConnon_Cannon cannon)
    {
        // 获取FirstRow状态实例
        var firstRowState = cannon.StateManager.GetStateByType(GridConnon_Cannon_State.FirstRow);

        // 这里需要问号炮状态类提供动画完成检查方法
        // 暂时返回true，具体实现需要根据问号炮的动画系统
        return true;
    }

    /// <summary>
    /// 检查链接组中所有炮台是否都处于销毁状态
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <returns>是否全部处于销毁状态</returns>
    public bool IsAllInGroupDestroying(GridConnon_Cannon cannon)
    {
        return IsAllInGroupInState(cannon, GridConnon_Cannon_State.Destroying);
    }

    /// <summary>
    /// 检查链接组中所有炮台是否都完成了销毁动画并在等待GoToDes执行
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <returns>是否全部完成动画且在等待状态</returns>
    public bool IsAllInGroupAnimationCompleteAndWaiting(GridConnon_Cannon cannon)
    {
        var group = GetLinkGroup(cannon);

        // 没有链接组，只检查自己
        if (group == null)
        {
            if (cannon.GetCurrentState() != GridConnon_Cannon_State.Destroying)
                return false;

            var destroyingState = cannon.StateManager.GetStateByType(GridConnon_Cannon_State.Destroying) as GridConnon_CannonState_Destroying;
            return destroyingState?.IsAnimationCompleteAndWaiting() ?? false;
        }

        // 检查组内所有炮台
        foreach (var linkedCannon in group)
        {
            if (linkedCannon == null || linkedCannon.GetCurrentState() != GridConnon_Cannon_State.Destroying)
            {
                return false;
            }

            var destroyingState = linkedCannon.StateManager.GetStateByType(GridConnon_Cannon_State.Destroying) as GridConnon_CannonState_Destroying;
            if (destroyingState == null || !destroyingState.IsAnimationCompleteAndWaiting())
            {
                return false;
            }
        }

        return true;
    }

    #endregion

    #region 同列前进检查

    /// <summary>
    /// 检查并触发同列前进机制
    /// 当炮台进入FirstRow状态时调用
    /// </summary>
    /// <param name="cannon">进入FirstRow状态的炮台</param>
    public void CheckSameColumnAdvancement(GridConnon_Cannon cannon)
    {
        if (cannon == null) return;

        //Debug.Log($"[同列前进] 开始检查炮台{cannon.ObjectId}(GridX={cannon.GridX}, GridZ={cannon.GridZ})的同列前进机制");

        // 获取同列的所有炮台并按Z坐标排序（从前到后：Z值从大到小）
        var sameColumnCannons = GetSameColumnCannons(cannon.GridX);
        //Debug.Log($"[同列前进] 同列炮台总数: {sameColumnCannons.Count}");

        // 打印同列炮台信息
        foreach (var c in sameColumnCannons)
        {
            //Debug.Log($"[同列前进] 同列炮台: {c.ObjectId}(GridZ={c.GridZ}, 状态={c.GetCurrentState()})");
        }

        sameColumnCannons.Sort((a, b) => b.GridZ.CompareTo(a.GridZ));

        // 找到当前炮台在列表中的位置
        int currentIndex = sameColumnCannons.IndexOf(cannon);
        if (currentIndex == -1)
        {
            //Debug.LogWarning($"[同列前进] 未找到当前炮台{cannon.ObjectId}在同列炮台列表中");
            return;
        }

        //Debug.Log($"[同列前进] 当前炮台{cannon.ObjectId}在同列中的索引: {currentIndex}");

        // 只检查紧邻的下一个炮台（Z坐标更小的第一个炮台）
        if (currentIndex + 1 < sameColumnCannons.Count)
        {
            var nextCannon = sameColumnCannons[currentIndex + 1];
            //Debug.Log($"[同列前进] 检查紧邻后方炮台{nextCannon.ObjectId}(GridZ={nextCannon.GridZ})");

            // 检查是否与当前炮台有链接关系
            bool isLinked = IsLinked(cannon, nextCannon);
            //Debug.Log($"[同列前进] 炮台{cannon.ObjectId}与{nextCannon.ObjectId}的链接关系: {isLinked}");

            if (isLinked)
            {
                var nextCannonState = nextCannon.GetCurrentState();
                //Debug.Log($"[同列前进] 紧邻后方炮台{nextCannon.ObjectId}当前状态: {nextCannonState}");

                // 如果紧邻后方炮台还在BackRow状态，让它进入FirstRow状态
                if (nextCannonState == GridConnon_Cannon_State.BackRow)
                {
                    //Debug.Log($"[同列前进] 触发紧邻后方炮台{nextCannon.ObjectId}从BackRow进入FirstRow状态");
                    nextCannon.ChangeState(GridConnon_Cannon_State.FirstRow);
                    //Debug.Log($"[同列前进] 炮台{nextCannon.ObjectId}因前方链接炮台{cannon.ObjectId}到达FirstRow而自动前进");

                    // 递归检查该炮台是否也能触发后方炮台前进
                    CheckSameColumnAdvancement(nextCannon);
                }
                else
                {
                    //Debug.Log($"[同列前进] 紧邻后方炮台{nextCannon.ObjectId}不在BackRow状态，无需前进");
                }
            }
            else
            {
                //Debug.Log($"[同列前进] 炮台{cannon.ObjectId}与紧邻后方炮台{nextCannon.ObjectId}没有链接关系，无法前进");
            }
        }
        else
        {
            //Debug.Log($"[同列前进] 炮台{cannon.ObjectId}后面没有更多炮台");
        }

        //Debug.Log($"[同列前进] 完成炮台{cannon.ObjectId}的同列前进检查");
    }

    /// <summary>
    /// 获取同列的所有炮台
    /// </summary>
    /// <param name="gridX">列坐标</param>
    /// <returns>同列的炮台列表</returns>
    private List<GridConnon_Cannon> GetSameColumnCannons(int gridX)
    {
        var result = new List<GridConnon_Cannon>();
        var allCannons = GridConnon_Controller.Instance.CurrentCannons;

        foreach (var cannonObj in allCannons)
        {
            if (cannonObj is GridConnon_Cannon cannon && cannon.GridX == gridX)
            {
                // 只统计仍在 GridConnon_Controller 网格中的炮台，排除已进入射击格的炮台
                if (cannon.StateManager != null && cannon.StateManager.IsInGridConnon_ControllerGrid())
                {
                    result.Add(cannon);
                }
            }
        }

        return result;
    }



    #endregion

    #region 射击格连续性检查

    /// <summary>
    /// 检查是否有足够的连续DirectShot格子
    /// </summary>
    /// <param name="requiredCount">需要的格子数量</param>
    /// <returns>是否有足够的连续格子</returns>
    public bool HasEnoughConsecutiveDirectShotGrids(int requiredCount)
    {
        // 检查控制器设置是否启用连续格子检测
        var controller = GridConnon_Controller.Instance;
        if (controller != null && !controller.enableLinkConsecutiveGridCheck)
        {
            // 如果关闭连续检测，则检查是否有足够数量的空闲格子（不需要连续）
            return HasEnoughDirectShotGrids(requiredCount);
        }

        var shotController = GridConnon_ShotGrid_Controller.Instance;
        if (shotController == null || shotController.DirectShotGrids == null)
        {
            return false;
        }

        int consecutiveCount = 0;
        int maxConsecutive = 0;

        // 遍历DirectShot格子数组，查找最长连续空闲段
        for (int i = 0; i < shotController.DirectShotGrids.Length; i++)
        {
            var grid = shotController.DirectShotGrids[i];
            if (grid != null && grid.IsFree && !grid.IsLocked)
            {
                consecutiveCount++;
                maxConsecutive = Mathf.Max(maxConsecutive, consecutiveCount);
            }
            else
            {
                consecutiveCount = 0;
            }
        }

        bool hasEnough = maxConsecutive >= requiredCount;
        //Debug.Log($"[链接系统] 连续格子检查：需要{requiredCount}个，最大连续{maxConsecutive}个，结果：{hasEnough}");

        return hasEnough;
    }

    /// <summary>
    /// 检查是否有足够数量的DirectShot格子（不需要连续）
    /// </summary>
    /// <param name="requiredCount">需要的格子数量</param>
    /// <returns>是否有足够的格子</returns>
    public bool HasEnoughDirectShotGrids(int requiredCount)
    {
        var shotController = GridConnon_ShotGrid_Controller.Instance;
        if (shotController == null || shotController.DirectShotGrids == null)
        {
            return false;
        }

        int freeCount = 0;

        // 遍历DirectShot格子数组，统计空闲格子数量
        for (int i = 0; i < shotController.DirectShotGrids.Length; i++)
        {
            var grid = shotController.DirectShotGrids[i];
            if (grid != null && grid.IsFree && !grid.IsLocked)
            {
                freeCount++;
            }
        }

        bool hasEnough = freeCount >= requiredCount;
        //Debug.Log($"[链接系统] 非连续格子检查：需要{requiredCount}个，可用{freeCount}个，结果：{hasEnough}");

        return hasEnough;
    }

    /// <summary>
    /// 获取连续的DirectShot格子用于链接组移动
    /// </summary>
    /// <param name="requiredCount">需要的格子数量</param>
    /// <returns>连续的格子列表，如果没有足够连续格子返回null</returns>
    public List<GridConnon_ShotGrid_Grid> GetConsecutiveDirectShotGrids(int requiredCount)
    {
        // 检查控制器设置是否启用连续格子检测
        var controller = GridConnon_Controller.Instance;
        if (controller != null && !controller.enableLinkConsecutiveGridCheck)
        {
            //Debug.Log($"[链接系统] 连续检测已关闭，使用非连续模式");
            // 如果关闭连续检测，则获取任意可用的格子
            return GetAvailableDirectShotGrids(requiredCount);
        }

        //Debug.Log($"[链接系统] 连续检测已开启，查找{requiredCount}个连续格子");

        var shotController = GridConnon_ShotGrid_Controller.Instance;
        if (shotController == null || shotController.DirectShotGrids == null)
        {
            //Debug.LogError($"[链接系统] ShotController或DirectShotGrids为null");
            return null;
        }

        //Debug.Log($"[链接系统] 当前格子状态：");
        for (int idx = 0; idx < shotController.DirectShotGrids.Length; idx++)
        {
            var grid = shotController.DirectShotGrids[idx];
            if (grid != null)
            {
                string status = grid.IsFree ? (grid.IsLocked ? "锁定" : "空闲") : "占用";
                //Debug.Log($"[链接系统]   格子{idx}: {status}");
            }
            else
            {
                //Debug.Log($"[链接系统]   格子{idx}: null");
            }
        }

        List<GridConnon_ShotGrid_Grid> consecutiveGrids = new List<GridConnon_ShotGrid_Grid>();

        // 查找第一个满足条件的连续段
        for (int i = 0; i <= shotController.DirectShotGrids.Length - requiredCount; i++)
        {
            consecutiveGrids.Clear();
            bool isValid = true;

            //Debug.Log($"[链接系统] 检查从索引{i}开始的{requiredCount}个格子:");

            // 检查从i开始的requiredCount个格子是否都空闲且连续
            for (int j = 0; j < requiredCount; j++)
            {
                var grid = shotController.DirectShotGrids[i + j];
                //Debug.Log($"[链接系统]   检查格子索引{i + j}: {(grid != null ? (grid.IsFree ? (grid.IsLocked ? "锁定" : "空闲") : "占用") : "null")}");

                if (grid == null || !grid.IsFree || grid.IsLocked)
                {
                    //Debug.Log($"[链接系统]   格子索引{i + j}不可用，跳过此连续段");
                    isValid = false;
                    break;
                }
                consecutiveGrids.Add(grid);
            }

            if (isValid)
            {
                //Debug.Log($"[链接系统] ✅ 找到连续格子段：从索引{i}开始的{requiredCount}个格子");
                for (int k = 0; k < consecutiveGrids.Count; k++)
                {
                    //Debug.Log($"[链接系统]   选择格子 {k}: 数组索引{i + k}");
                }
                return consecutiveGrids;
            }
        }

        //Debug.Log($"[链接系统] ❌ 未找到{requiredCount}个连续的空闲格子");
        return null;
    }

    /// <summary>
    /// 获取可用的DirectShot格子用于链接组移动（不需要连续）
    /// </summary>
    /// <param name="requiredCount">需要的格子数量</param>
    /// <returns>可用的格子列表，如果没有足够格子返回null</returns>
    public List<GridConnon_ShotGrid_Grid> GetAvailableDirectShotGrids(int requiredCount)
    {
        var shotController = GridConnon_ShotGrid_Controller.Instance;
        if (shotController == null || shotController.DirectShotGrids == null)
        {
            return null;
        }

        // 首先尝试找到连续的格子段
        List<GridConnon_ShotGrid_Grid> consecutiveGrids = new List<GridConnon_ShotGrid_Grid>();

        // 查找最长的连续段
        for (int i = 0; i <= shotController.DirectShotGrids.Length - requiredCount; i++)
        {
            consecutiveGrids.Clear();
            bool isValid = true;

            // 检查从i开始的requiredCount个格子是否都空闲
            for (int j = 0; j < requiredCount; j++)
            {
                var grid = shotController.DirectShotGrids[i + j];
                if (grid == null || !grid.IsFree || grid.IsLocked)
                {
                    isValid = false;
                    break;
                }
                consecutiveGrids.Add(grid);
            }

            if (isValid)
            {
                //Debug.Log($"[链接系统] 找到连续格子段：从索引{i}开始的{requiredCount}个格子（非连续模式下优先选择连续）");
                return consecutiveGrids;
            }
        }

        // 如果没有找到足够的连续格子，则查找所有可用格子
        List<GridConnon_ShotGrid_Grid> availableGrids = new List<GridConnon_ShotGrid_Grid>();

        // 收集所有可用的格子
        for (int i = 0; i < shotController.DirectShotGrids.Length; i++)
        {
            var grid = shotController.DirectShotGrids[i];
            if (grid != null && grid.IsFree && !grid.IsLocked)
            {
                availableGrids.Add(grid);
            }
        }

        // 检查是否有足够的格子
        if (availableGrids.Count >= requiredCount)
        {
            // 优先选择较大的连续段
            var result = SelectBestAvailableGrids(availableGrids, requiredCount);
            //Debug.Log($"[链接系统] 找到{requiredCount}个可用格子（非连续模式，优先选择连续段）");
            return result;
        }

        //Debug.Log($"[链接系统] 未找到足够的可用格子：需要{requiredCount}个，可用{availableGrids.Count}个");
        return null;
    }

    /// <summary>
    /// 从可用格子中选择最佳的格子组合（优先连续段）
    /// </summary>
    /// <param name="availableGrids">所有可用格子</param>
    /// <param name="requiredCount">需要的格子数量</param>
    /// <returns>最佳的格子组合</returns>
    private List<GridConnon_ShotGrid_Grid> SelectBestAvailableGrids(List<GridConnon_ShotGrid_Grid> availableGrids, int requiredCount)
    {
        var shotController = GridConnon_ShotGrid_Controller.Instance;
        if (shotController == null || shotController.DirectShotGrids == null)
        {
            return availableGrids.GetRange(0, requiredCount);
        }

        // 创建格子索引映射
        Dictionary<GridConnon_ShotGrid_Grid, int> gridIndexMap = new Dictionary<GridConnon_ShotGrid_Grid, int>();
        for (int i = 0; i < shotController.DirectShotGrids.Length; i++)
        {
            if (shotController.DirectShotGrids[i] != null)
            {
                gridIndexMap[shotController.DirectShotGrids[i]] = i;
            }
        }

        // 按索引排序可用格子
        availableGrids.Sort((a, b) =>
        {
            int indexA = gridIndexMap.ContainsKey(a) ? gridIndexMap[a] : int.MaxValue;
            int indexB = gridIndexMap.ContainsKey(b) ? gridIndexMap[b] : int.MaxValue;
            return indexA.CompareTo(indexB);
        });

        // 找到所有连续段
        List<List<GridConnon_ShotGrid_Grid>> consecutiveSegments = new List<List<GridConnon_ShotGrid_Grid>>();
        List<GridConnon_ShotGrid_Grid> currentSegment = new List<GridConnon_ShotGrid_Grid>();
        int lastIndex = -1;

        foreach (var grid in availableGrids)
        {
            int currentIndex = gridIndexMap.ContainsKey(grid) ? gridIndexMap[grid] : -1;

            if (currentIndex != -1)
            {
                // 如果是连续的，添加到当前段
                if (lastIndex == -1 || currentIndex == lastIndex + 1)
                {
                    currentSegment.Add(grid);
                }
                else
                {
                    // 开始新的连续段
                    if (currentSegment.Count > 0)
                    {
                        consecutiveSegments.Add(new List<GridConnon_ShotGrid_Grid>(currentSegment));
                    }
                    currentSegment.Clear();
                    currentSegment.Add(grid);
                }
                lastIndex = currentIndex;
            }
        }

        // 添加最后一个段
        if (currentSegment.Count > 0)
        {
            consecutiveSegments.Add(currentSegment);
        }

        // 按段长度排序（从长到短）
        consecutiveSegments.Sort((a, b) => b.Count.CompareTo(a.Count));

        // 尝试从最长的连续段开始选择
        List<GridConnon_ShotGrid_Grid> result = new List<GridConnon_ShotGrid_Grid>();
        HashSet<GridConnon_ShotGrid_Grid> used = new HashSet<GridConnon_ShotGrid_Grid>();

        foreach (var segment in consecutiveSegments)
        {
            foreach (var grid in segment)
            {
                if (!used.Contains(grid) && result.Count < requiredCount)
                {
                    result.Add(grid);
                    used.Add(grid);
                }

                if (result.Count >= requiredCount)
                    break;
            }

            if (result.Count >= requiredCount)
                break;
        }

        // 如果还不够，从剩余的格子中补充
        if (result.Count < requiredCount)
        {
            foreach (var grid in availableGrids)
            {
                if (!used.Contains(grid) && result.Count < requiredCount)
                {
                    result.Add(grid);
                    used.Add(grid);
                }
            }
        }

        // 按索引重新排序结果，确保返回的格子是有序的
        result.Sort((a, b) =>
        {
            int indexA = gridIndexMap.ContainsKey(a) ? gridIndexMap[a] : int.MaxValue;
            int indexB = gridIndexMap.ContainsKey(b) ? gridIndexMap[b] : int.MaxValue;
            return indexA.CompareTo(indexB);
        });

        if (consecutiveSegments.Count > 0)
        {
            //Debug.Log($"[链接系统] 找到{consecutiveSegments.Count}个连续段，最长段有{consecutiveSegments[0].Count}个格子");
            for (int i = 0; i < result.Count; i++)
            {
                int gridIndex = gridIndexMap.ContainsKey(result[i]) ? gridIndexMap[result[i]] : -1;
                //Debug.Log($"[链接系统] 选择格子 {i}: 索引{gridIndex}");
            }
        }

        return result;
    }

    #endregion

    #region 链接组统一移动方向

    /// <summary>
    /// 计算链接组的统一移动方向
    /// </summary>
    /// <param name="cannon">链接组中的任意炮台</param>
    /// <returns>true表示向左移动，false表示向右移动</returns>
    public bool CalculateUnifiedMoveDirection(GridConnon_Cannon cannon)
    {
        if (cannon == null) return false;

        var linkGroup = GetLinkGroup(cannon);

        // 如果不在链接组中，使用原来的逻辑
        if (linkGroup == null || linkGroup.Count <= 1)
        {
            return CalculateIndividualMoveDirection(cannon);
        }

        // 计算链接组的中心位置
        float groupCenterX = 0f;
        int validCannonCount = 0;

        foreach (var linkedCannon in linkGroup)
        {
            if (linkedCannon != null)
            {
                groupCenterX += linkedCannon.transform.position.x;
                validCannonCount++;
            }
        }

        if (validCannonCount == 0) return false;

        groupCenterX /= validCannonCount;

        // 计算网格中心位置
        var controller = GridConnon_Controller.Instance;
        if (controller == null || controller.CurrentLevel == null)
        {
            //Debug.LogError("[链接系统] 无法获取GridConnon_Controller或当前关卡数据");
            return false;
        }

        int gridColumns = controller.CurrentLevel.columns;
        float gridMiddleX = (gridColumns - 1) * 0.5f;
        Vector3 middleWorldPos = controller.GridToWorldPosition(Mathf.FloorToInt(gridMiddleX), cannon.GridZ);

        // 判断链接组中心位置相对于网格中心的位置
        if (groupCenterX < middleWorldPos.x)
        {
            return true; // 左
        }
        else if (groupCenterX > middleWorldPos.x)
        {
            return false; // 右
        }
        else
        {
            // 组中心与网格中心重合：组级确定性方向（不再逐炮台随机）
            return GetOrCacheDeterministicGroupDirection(linkGroup);
        }
    }

    /// <summary>
    /// 获取或缓存组级确定性销毁方向（true=左，false=右）
    /// 规则：取组内最小ObjectId，使用其奇偶决定；使用稳定的组键缓存，保证同组一致
    /// </summary>
    private bool GetOrCacheDeterministicGroupDirection(HashSet<GridConnon_Cannon> group)
    {
        if (group == null || group.Count == 0) return false;

        string key = BuildStableGroupKey(group);
        if (groupDestroyDirectionCache.TryGetValue(key, out bool cached))
        {
            return cached;
        }

        int minId = int.MaxValue;
        foreach (var c in group)
        {
            if (c == null) continue;
            if (c.ObjectId < minId) minId = c.ObjectId;
        }

        bool moveLeft = (minId % 2) == 0;
        groupDestroyDirectionCache[key] = moveLeft;
        return moveLeft;
    }

    /// <summary>
    /// 构造稳定的组键：将组内所有有效ObjectId排序后用逗号连接
    /// </summary>
    private string BuildStableGroupKey(HashSet<GridConnon_Cannon> group)
    {
        // 收集并排序ID
        var ids = new System.Collections.Generic.List<int>();
        foreach (var c in group)
        {
            if (c != null) ids.Add(c.ObjectId);
        }
        ids.Sort();
        return string.Join(",", ids);
    }

    /// <summary>
    /// 计算单个炮台的移动方向（原逻辑）
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <returns>true表示向左移动，false表示向右移动</returns>
    private bool CalculateIndividualMoveDirection(GridConnon_Cannon cannon)
    {
        if (cannon == null) return false;

        var controller = GridConnon_Controller.Instance;
        if (controller == null || controller.CurrentLevel == null) return false;

        Vector3 currentWorldPos = cannon.transform.position;
        int gridColumns = controller.CurrentLevel.columns;
        float gridMiddleX = (gridColumns - 1) * 0.5f;
        Vector3 middleWorldPos = controller.GridToWorldPosition(Mathf.FloorToInt(gridMiddleX), cannon.GridZ);

        bool isLeft = currentWorldPos.x < middleWorldPos.x;
        bool isRight = currentWorldPos.x > middleWorldPos.x;

        if (isLeft)
        {
            return true; // 向左移动
        }
        else if (isRight)
        {
            return false; // 向右移动
        }
        else
        {
            // 中间位置随机选择
            return Random.value < 0.5f;
        }
    }

    #endregion

    #region 调试信息

    /// <summary>
    /// 更新调试信息
    /// </summary>
    private void UpdateDebugInfo()
    {
        debugLinkRelations.Clear();
        debugLinkGroups.Clear();

        // 更新链接关系信息
        foreach (var kvp in linkRelations)
        {
            string cannonId = kvp.Key?.ObjectId.ToString() ?? "null";
            var linkedIds = kvp.Value.Select(c => c?.ObjectId.ToString() ?? "null");
            debugLinkRelations.Add($"{cannonId} → [{string.Join(", ", linkedIds)}]");
        }

        // 更新链接组信息
        for (int i = 0; i < linkGroups.Count; i++)
        {
            var group = linkGroups[i];
            var groupIds = group.Select(c => c?.ObjectId.ToString() ?? "null");
            debugLinkGroups.Add($"组{i + 1}: [{string.Join(", ", groupIds)}]");
        }
    }

    /// <summary>
    /// 获取所有链接组（用于调试）
    /// </summary>
    /// <returns>所有链接组</returns>
    public List<HashSet<GridConnon_Cannon>> GetAllLinkGroups()
    {
        return new List<HashSet<GridConnon_Cannon>>(linkGroups);
    }

    /// <summary>
    /// 获取被链接炮台总数
    /// </summary>
    /// <returns>被链接炮台总数</returns>
    public int GetTotalLinkedCannons()
    {
        int total = 0;
        foreach (var group in linkGroups)
        {
            total += group.Count;
        }
        return total;
    }

    /// <summary>
    /// 获取链接组数量
    /// </summary>
    /// <returns>链接组数量</returns>
    public int GetLinkGroupsCount()
    {
        return linkGroups.Count;
    }

    /// <summary>
    /// 强制更新所有链接棍的状态（调试用）
    /// </summary>
    public void ForceUpdateAllLinkSticks()
    {
        int updatedCount = 0;

        foreach (var kvp in linkRelations)
        {
            var cannon = kvp.Key;
            var linkedCannons = kvp.Value;

            if (cannon == null || linkedCannons.Count == 0) continue;

            // 为每个链接目标更新圆棍
            foreach (var targetCannon in linkedCannons)
            {
                // 确保圆棍存在
                if (!cannon.linkStickObjects.ContainsKey(targetCannon.ObjectId))
                {
                    cannon.SetLinkStickTarget(targetCannon);
                }

                // 强制立即更新
                if (cannon.linkStickComponents.ContainsKey(targetCannon.ObjectId))
                {
                    var stickComponent = cannon.linkStickComponents[targetCannon.ObjectId];
                    if (stickComponent != null)
                    {
                        stickComponent.ForceUpdate();
                    }
                }

                updatedCount++;
                //Debug.Log($"[调试] 强制更新炮台 {cannon.ObjectId} 到炮台 {targetCannon.ObjectId} 的链接棍");
            }
        }

        //Debug.Log($"[调试] 强制更新完成，共更新 {updatedCount} 个链接棍");
    }

    /// <summary>
    /// 强制清理单例实例（用于场景切换或应用退出时的彻底清理）
    /// </summary>
    public static void ForceCleanupInstance()
    {
        //Debug.Log("[链接系统] 强制清理单例实例");

        _applicationIsQuitting = true;
        _sceneChanging = true; // 同时设置场景切换标志

        if (_instance != null)
        {
            try
            {
                // 先清理所有链接
                _instance.ClearAllLinks();

                // 如果实例仍然存在且GameObject有效，销毁它
                if (_instance.gameObject != null)
                {
                    if (Application.isPlaying)
                    {
                        Destroy(_instance.gameObject);
                    }
                    else
                    {
                        DestroyImmediate(_instance.gameObject);
                    }
                }
            }
            catch (System.Exception e)
            {
                //Debug.LogError($"[链接系统] 强制清理时发生错误: {e.Message}");
            }
            finally
            {
                _instance = null;
            }
        }
    }

    /// <summary>
    /// 打印所有链接关系的详细信息（调试用）
    /// </summary>
    public void PrintAllLinkRelations()
    {
        //Debug.Log($"[链接系统调试] ===== 所有链接关系详细信息 =====");
        //Debug.Log($"[链接系统调试] 总共有 {linkRelations.Count} 个炮台有链接关系");
        //Debug.Log($"[链接系统调试] 总共有 {linkGroups.Count} 个链接组");

        // 打印每个炮台的链接关系
        foreach (var kvp in linkRelations)
        {
            var cannon = kvp.Key;
            var linkedCannons = kvp.Value;

            //Debug.Log($"[链接系统调试] 炮台 {cannon.ObjectId} (GridX={cannon.GridX}, GridZ={cannon.GridZ}):");
            foreach (var linkedCannon in linkedCannons)
            {
                //Debug.Log($"[链接系统调试]   → 链接到炮台 {linkedCannon.ObjectId} (GridX={linkedCannon.GridX}, GridZ={linkedCannon.GridZ})");
            }
        }

        // 打印每个链接组
        for (int i = 0; i < linkGroups.Count; i++)
        {
            var group = linkGroups[i];
            //Debug.Log($"[链接系统调试] 链接组 {i + 1} (共{group.Count}个炮台):");
            foreach (var cannon in group)
            {
                //Debug.Log($"[链接系统调试]   - 炮台 {cannon.ObjectId} (GridX={cannon.GridX}, GridZ={cannon.GridZ}, 状态={cannon.GetCurrentState()})");
            }
        }

        //Debug.Log($"[链接系统调试] ===== 链接关系详细信息结束 =====");
    }

    /// <summary>
    /// 测试连续格子检测开关功能（调试用）
    /// </summary>
    /// <param name="requiredCount">测试所需格子数量</param>
    public void TestConsecutiveGridCheck(int requiredCount = 3)
    {
        var controller = GridConnon_Controller.Instance;
        if (controller == null)
        {
            //Debug.LogError("[测试] 控制器未初始化");
            return;
        }

        var shotController = GridConnon_ShotGrid_Controller.Instance;
        if (shotController == null || shotController.DirectShotGrids == null)
        {
            //Debug.LogError("[测试] 射击格控制器未初始化");
            return;
        }

        //Debug.Log($"[测试] ===== 连续格子检测开关测试 =====");
        //Debug.Log($"[测试] 当前开关状态: {(controller.enableLinkConsecutiveGridCheck ? "✅ 启用连续检测" : "❌ 禁用连续检测")}");
        //Debug.Log($"[测试] 测试所需格子数量: {requiredCount}");

        // 显示当前格子状态
        //Debug.Log($"[测试] 当前格子状态（总共{shotController.DirectShotGrids.Length}个）:");
        for (int i = 0; i < shotController.DirectShotGrids.Length; i++)
        {
            var grid = shotController.DirectShotGrids[i];
            if (grid != null)
            {
                string status = grid.IsFree ? (grid.IsLocked ? "🔒锁定" : "✅空闲") : "❌占用";
                //Debug.Log($"[测试]   格子{i}: {status}");
            }
            else
            {
                //Debug.Log($"[测试]   格子{i}: null");
            }
        }

        // 根据开关状态选择测试方法
        if (controller.enableLinkConsecutiveGridCheck)
        {
            //Debug.Log($"[测试] 🔍 测试连续格子检测模式...");

            // 测试连续格子检测
            bool hasConsecutive = HasEnoughConsecutiveDirectShotGrids(requiredCount);
            //Debug.Log($"[测试] HasEnoughConsecutiveDirectShotGrids({requiredCount}) = {hasConsecutive}");

            // 测试获取格子
            var grids = GetConsecutiveDirectShotGrids(requiredCount);
            if (grids != null)
            {
                //Debug.Log($"[测试] ✅ GetConsecutiveDirectShotGrids({requiredCount}) 成功获取 {grids.Count} 个格子:");
                for (int i = 0; i < grids.Count; i++)
                {
                    // 找到格子在数组中的索引
                    int gridIndex = -1;
                    for (int j = 0; j < shotController.DirectShotGrids.Length; j++)
                    {
                        if (shotController.DirectShotGrids[j] == grids[i])
                        {
                            gridIndex = j;
                            break;
                        }
                    }
                    //Debug.Log($"[测试]   选择的格子 {i}: 索引{gridIndex} (IsFree={grids[i].IsFree}, IsLocked={grids[i].IsLocked})");
                }
            }
            else
            {
                //Debug.Log($"[测试] ❌ GetConsecutiveDirectShotGrids({requiredCount}) 返回null");
            }
        }
        else
        {
            //Debug.Log($"[测试] 🔍 测试非连续格子检测模式...");

            bool hasEnough = HasEnoughDirectShotGrids(requiredCount);
            //Debug.Log($"[测试] HasEnoughDirectShotGrids({requiredCount}) = {hasEnough}");

            var availableGrids = GetAvailableDirectShotGrids(requiredCount);
            if (availableGrids != null)
            {
                //Debug.Log($"[测试] ✅ GetAvailableDirectShotGrids({requiredCount}) 成功获取 {availableGrids.Count} 个格子:");
                for (int i = 0; i < availableGrids.Count; i++)
                {
                    // 找到格子在数组中的索引
                    int gridIndex = -1;
                    for (int j = 0; j < shotController.DirectShotGrids.Length; j++)
                    {
                        if (shotController.DirectShotGrids[j] == availableGrids[i])
                        {
                            gridIndex = j;
                            break;
                        }
                    }
                    //Debug.Log($"[测试]   选择的格子 {i}: 索引{gridIndex}");
                }
            }
            else
            {
                //Debug.Log($"[测试] ❌ GetAvailableDirectShotGrids({requiredCount}) 返回null");
            }
        }

        //Debug.Log($"[测试] ===== 连续格子检测开关测试结束 =====");
    }

    #endregion
}