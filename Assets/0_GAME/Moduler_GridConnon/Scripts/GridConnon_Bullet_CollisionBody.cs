using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 网格炮碰撞体组件
/// 挂载此组件的物体会自动注册到子弹的碰撞检测系统中
/// </summary>
public class GridConnon_Bullet_CollisionBody : MonoBehaviour
{
    [Header("碰撞框设置")]
    [Tooltip("碰撞框大小")]
    public Vector3 BoundsSize = Vector3.one * 0.2f;
    [Tooltip("碰撞框相对于物体的偏移量")]
    public Vector3 BoundsOffset = Vector3.zero;

    [Header("调试设置")]
    [Tooltip("是否显示调试碰撞框")]
    public bool ShowDebugBounds = true;
    [Tooltip("调试碰撞框颜色")]
    public Color DebugBoundsColor = Color.green;
    [Tooltip("被碰撞时的高亮颜色")]
    public Color HitHighlightColor = Color.red;

    // 静态管理所有碰撞体
    private static List<GridConnon_Bullet_CollisionBody> allCollisionBodies = new List<GridConnon_Bullet_CollisionBody>();

    // 当前是否被检测到碰撞（用于调试显示）
    private bool isBeingHit = false;

    // 缓存调整后的尺寸，避免重复计算
    private Dictionary<float, Vector3> cachedAdjustedSizes = new Dictionary<float, Vector3>();
    private Dictionary<float, Bounds> cachedAdjustedBounds = new Dictionary<float, Bounds>();

    /// <summary>
    /// 获取所有碰撞体列表
    /// </summary>
    public static List<GridConnon_Bullet_CollisionBody> GetAllCollisionBodies()
    {
        return allCollisionBodies;
    }

    /// <summary>
    /// 获取物体的世界包围盒
    /// </summary>
    public Bounds GetWorldBounds()
    {
        return new Bounds(transform.position + BoundsOffset, BoundsSize);
    }

    /// <summary>
    /// 获取调整后的包围盒（带缓存）
    /// </summary>
    /// <param name="collisionPercent">碰撞百分比</param>
    /// <returns>调整后的包围盒</returns>
    public Bounds GetAdjustedBounds(float collisionPercent)
    {
        // 如果百分比为1.0，直接返回原始包围盒
        if (Mathf.Approximately(collisionPercent, 1.0f))
        {
            return GetWorldBounds();
        }

        // 检查缓存
        if (cachedAdjustedBounds.ContainsKey(collisionPercent))
        {
            // 更新位置（因为物体可能移动了），但尺寸使用缓存
            var cachedBounds = cachedAdjustedBounds[collisionPercent];
            cachedBounds.center = transform.position + BoundsOffset;
            return cachedBounds;
        }

        // 计算并缓存调整后的尺寸
        Vector3 adjustedSize = BoundsSize * collisionPercent;
        cachedAdjustedSizes[collisionPercent] = adjustedSize;

        // 创建并缓存调整后的包围盒
        Bounds adjustedBounds = new Bounds(transform.position + BoundsOffset, adjustedSize);
        cachedAdjustedBounds[collisionPercent] = adjustedBounds;

        return adjustedBounds;
    }

    /// <summary>
    /// 设置碰撞状态（用于调试显示）
    /// </summary>
    public void SetHitState(bool isHit)
    {
        isBeingHit = isHit;
    }

    void Awake()
    {
        // 自动注册到全局列表
        if (!allCollisionBodies.Contains(this))
        {
            allCollisionBodies.Add(this);
        }
    }

    void OnDestroy()
    {
        // 销毁时自动移除
        if (allCollisionBodies.Contains(this))
        {
            allCollisionBodies.Remove(this);
        }
    }

    #region 调试绘制

    void OnDrawGizmos()
    {
        if (!ShowDebugBounds) return;

        Vector3 boundsCenter = transform.position + BoundsOffset;

        // 选择颜色
        Color drawColor = isBeingHit ? HitHighlightColor : DebugBoundsColor;
        Gizmos.color = drawColor;

        // 绘制线框
        Gizmos.DrawWireCube(boundsCenter, BoundsSize);

        // 绘制半透明填充
        Gizmos.color = new Color(drawColor.r, drawColor.g, drawColor.b, 0.1f);
        Gizmos.DrawCube(boundsCenter, BoundsSize);

        // 绘制物体原点（用于对比偏移）
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(transform.position, 0.03f);
    }

    void OnDrawGizmosSelected()
    {
        if (!ShowDebugBounds) return;

        Vector3 boundsCenter = transform.position + BoundsOffset;

        // 选中时绘制更明显的边框
        Gizmos.color = Color.white;
        Gizmos.DrawWireCube(boundsCenter, BoundsSize);

        // 绘制碰撞框中心点
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(boundsCenter, 0.05f);

        // 绘制物体原点
        Gizmos.color = Color.cyan;
        Gizmos.DrawWireSphere(transform.position, 0.03f);
    }

    #endregion
}