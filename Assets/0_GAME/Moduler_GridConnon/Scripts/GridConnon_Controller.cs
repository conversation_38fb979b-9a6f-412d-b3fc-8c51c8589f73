using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

using UnityEngine;

/// <summary>
/// GridConnon 主控制器 - 管理整个网格炮系统
/// 参考 TileCube_Controller 架构模式，集成 Common_ReserveGrid 网格系统
/// </summary>
public class GridConnon_Controller : MonoBehaviour
{
    private static GridConnon_Controller _instance;
    private static bool _isQuitting = false; // 标记是否正在退出，防止在销毁时重新创建实例

    public static GridConnon_Controller Instance
    {
        get
        {
            // 如果正在退出或销毁，不创建新实例
            if (_isQuitting || _instance == null)
            {
                if (_isQuitting) return null;

                _instance = FindObjectOfType<GridConnon_Controller>();
                if (_instance == null && !_isQuitting)
                {
                    GameObject go = new GameObject(typeof(GridConnon_Controller).Name);
                    _instance = go.AddComponent<GridConnon_Controller>();
                }
            }
            return _instance;
        }
    }





    public GameObject GridConnon_Parent
    {
        get
        {
            if (gridConnon_Parent == null)
            {
                gridConnon_Parent = GameObject.Find("GridConnon_Parent");
                if (gridConnon_Parent == null)
                {
                    gridConnon_Parent = new GameObject("GridConnon_Parent");
                }
                gridConnon_Parent.transform.localScale = MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data.GetParentScale();
            }
            return gridConnon_Parent;
        }
    }



    private GameObject gridConnon_Parent;

    [Header("控制器状态管理")]
    [Tooltip("当前控制器状态")]
    public GridConnon_Controller_State currentControllerState = GridConnon_Controller_State.Normal;

    [Header("当前数据")]
    [Tooltip("当前关卡数据")]
    public GridConnon_Data_Level CurrentLevel;

    [Tooltip("当前所有炮对象")]
    public List<GridConnon_Object> CurrentCannons = new List<GridConnon_Object>();

    [Header("性能优化 - 射击状态炮台缓存")]
    [Tooltip("当前处于Shooting状态的炮台列表（性能优化）")]
    [SerializeField]
    private List<GridConnon_Cannon> shootingCannons = new List<GridConnon_Cannon>();

    /// <summary>
    /// 获取当前所有处于Shooting状态的炮台（只读）
    /// 性能优化：避免每次都遍历所有炮台检查状态
    /// </summary>
    public IReadOnlyList<GridConnon_Cannon> ShootingCannons => shootingCannons.AsReadOnly();

    // 网格系统相关
    private List<Common_ReserveGrid> currentGrids = new List<Common_ReserveGrid>();
    private Dictionary<Vector2Int, Common_ReserveGrid> gridPositionMap = new Dictionary<Vector2Int, Common_ReserveGrid>();

    [Header("网格尺寸配置")]
    [Tooltip("网格X方向的大小")]
    public float cellSizeX = 1f;

    [Tooltip("网格Z方向的大小")]
    public float cellSizeZ = 1f;

    [Header("第一行特殊间距配置")]
    [Tooltip("第一行（Z=0）与第二行（Z=-1）之间的额外距离")]
    public float firstRowExtraDistance = 0f;

    [Tooltip("道具随机重排影响的行数。值为4表示影响从第5行到第1行（Z坐标：-4到0）的所有炮台")]
    public int Prop_Random_Z = 4;

    public string gridGroupName = "GridConnon";

    // 道具随机重排相关
    private List<GridConnon_Cannon> propRandomCannons = new List<GridConnon_Cannon>();

    // 射击合成系统
    private GridConnon_ShootingSynthesisSystem synthesisSystem;

    [Header("射击合成配置")]
    [Tooltip("升高阶段时长")]
    public float synthesisRiseDuration = 0.8f;

    [Tooltip("合成移动阶段时长")]
    public float synthesisMoveDuration = 1.2f;

    [Tooltip("下落阶段时长")]
    public float synthesisFallDuration = 0.8f;

    [Tooltip("上升高度")]
    public float synthesisRiseHeight = 2f;

    [Tooltip("左右偏移距离")]
    public float synthesisSideOffset = 1f;

    public bool EnableShootingSynthesis
    {
        get
        {
            return MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data.EnableShooting3Synthesis;
        }
    }

    [Header("链接系统配置")]
    [Tooltip("是否启用链接系统连续格子检测。开启时：链接组需要连续的射击格子；关闭时：链接组只需要足够数量的射击格子（不需要连续）")]
    public bool enableLinkConsecutiveGridCheck = true;


    [Header("Z行隐藏优化配置")]
    public int optimizeZ = 5;



    [Header("格子底部装饰")]
    public GameObject GridSlot_prefab;
    public Vector3 GridSlot_OffPos;

    // Z行隐藏优化相关
    private Dictionary<GridConnon_Cannon, bool> cannonVisibilityStates = new Dictionary<GridConnon_Cannon, bool>();
    private Dictionary<GridConnon_Cannon, Dictionary<int, bool>> cannonLinkStickVisibilityStates = new Dictionary<GridConnon_Cannon, Dictionary<int, bool>>();

    // public static Action<GridConnon_Cannon> Act_On_Cannon_Shooting_Update;

    public static Action<GridConnon_Bullet, GameObject> Act_On_Bullet_Targeted;
    public static Action<GridConnon_Bullet, GameObject> Act_On_Bullet_Thorw;
    public static Action<GridConnon_Bullet, GameObject> Act_On_Bullet_HitTarget;

    public static Action<GridConnon_Cannon> Act_On_TreasureCannonFindKey;


    /// <summary>播放音效</summary>
    public static Action<string> On_PlaySound;

    /// <summary>播放振动</summary>
    public static Action<int> On_PlayVib;

    /// <summary>道具拾取使用成功事件</summary>
    public static Action OnPropPickOutUsedSuccessfully;


    #region Unity 生命周期

    private void Awake()
    {
        // 重置退出标志，确保场景重载时可以正常创建实例
        _isQuitting = false;

        // 确保单例实例正确设置
        if (_instance == null)
        {
            _instance = this;
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
            return;
        }

        // 初始化射击合成系统
        synthesisSystem = new GridConnon_ShootingSynthesisSystem(this);

        // 应用配置参数到合成系统
        ApplySynthesisConfig();
    }







    void Update()
    {
        // 检测射击合成
        if (EnableShootingSynthesis && synthesisSystem != null)
        {
            synthesisSystem.CheckForSynthesis();
        }

        // 定时检测清除要求
        CheckClearRequirements();
    }

    private void Start()
    {
        // 可以在这里进行其他初始化
    }

    private void OnApplicationQuit()
    {
        _isQuitting = true;
    }

    private void OnDestroy()
    {
        // 清理合成系统
        if (synthesisSystem != null)
        {
            synthesisSystem.StopAllSynthesis();
            synthesisSystem = null;
        }

        if (_instance == this)
        {
            _instance = null;
        }
    }




    #endregion



    #region 关卡管理

    /// <summary>
    /// 创建关卡
    /// </summary>
    /// <param name="levelData">关卡数据</param>
    public void CreateLevel(GridConnon_Data_Level levelData)
    {
        if (levelData == null)
        {
            return;
        }

        // 验证关卡数据
        if (!levelData.ValidateData())
        {
            return;
        }

        // 清空现有关卡
        ClearCurrentLevel();

        // 设置当前关卡
        CurrentLevel = levelData;

        // 初始化网格系统
        InitializeGridSystem(levelData.CalculateRequiredRows(), levelData.columns);

        // 创建炮
        CreateCannonsFromLevelData(levelData);

        // 初始化链接系统
        InitializeLinkSystemForLevel();

        // 性能优化：刷新射击炮台列表
        RefreshShootingCannonsList();

        // 应用Z行隐藏优化到所有炮台
        ApplyZRowOptimizationToAllCannons();
    }

    /// <summary>
    /// 清空当前关卡
    /// </summary>
    public void ClearCurrentLevel()
    {
        // 清空链接系统
        if (GridConnon_LinkSystem.Instance != null)
        {
            GridConnon_LinkSystem.Instance.ClearAllLinks();
        }

        // 清空所有炮
        ClearAllCannons();

        // 清空网格系统
        ClearGridSystem();

        // 性能优化：清空射击炮台列表
        shootingCannons.Clear();

        // 重置当前关卡数据
        CurrentLevel = null;
    }

    #endregion

    #region 网格系统管理

    /// <summary>
    /// 初始化网格系统
    /// </summary>
    /// <param name="rows">行数</param>
    /// <param name="columns">列数</param>
    private void InitializeGridSystem(int rows, int columns)
    {
        // 清空现有网格
        ClearGridSystem();

        // 创建网格
        for (int z = 0; z >= -(rows - 1); z--) // Z从0到-(rows-1)
        {
            for (int x = 0; x < columns; x++)
            {
                Vector2Int gridPos = new Vector2Int(x, z);
                Common_ReserveGrid grid = CreateGridAtPosition(gridPos);

                if (grid != null)
                {
                    currentGrids.Add(grid);
                    gridPositionMap[gridPos] = grid;
                }
            }
        }

        // 初始化网格管理器
        if (currentGrids.Count > 0)
        {
            Common_ReserveGridManager.Instance.InitGrids(currentGrids, gridGroupName);
        }
    }

    /// <summary>
    /// 在指定位置创建网格格子
    /// </summary>
    /// <param name="gridPos">网格位置</param>
    /// <returns>创建的网格格子</returns>
    private Common_ReserveGrid CreateGridAtPosition(Vector2Int gridPos)
    {
        // 创建格子游戏对象
        GameObject gridObj = new GameObject($"Grid_{gridPos.x}_{gridPos.y}");
        gridObj.transform.SetParent(GridConnon_Parent.transform);

        // 计算世界坐标
        Vector3 worldPos = GridToWorldPosition(gridPos.x, gridPos.y);
        gridObj.transform.position = worldPos;

        // 添加网格组件
        Common_ReserveGrid grid = gridObj.AddComponent<Common_ReserveGrid>();
        grid.GroupId = gridGroupName;

        // 创建格子底部装饰
        CreateGridSlotDecoration(worldPos, gridPos);

        return grid;
    }

    /// <summary>
    /// 创建格子底部装饰
    /// </summary>
    /// <param name="gridWorldPos">格子的世界坐标</param>
    /// <param name="gridPos">网格位置</param>
    private void CreateGridSlotDecoration(Vector3 gridWorldPos, Vector2Int gridPos)
    {
        // 检查是否有预制体
        if (GridSlot_prefab == null)
        {
            return;
        }

        // 计算装饰物体的位置：格子位置 + 偏移
        Vector3 decorationPos = gridWorldPos + GridSlot_OffPos;

        // 实例化装饰物体
        GameObject decorationObj = Instantiate(GridSlot_prefab, decorationPos, Quaternion.identity, GridConnon_Parent.transform);
        decorationObj.name = $"GridSlot_{gridPos.x}_{gridPos.y}";
    }

    /// <summary>
    /// 清空网格系统
    /// </summary>
    private void ClearGridSystem()
    {
        // 清空网格管理器中的数据
        if (Common_ReserveGridManager.Instance != null)
        {
            Common_ReserveGridManager.Instance.RemoveGroup(gridGroupName);
        }

        // 销毁网格游戏对象
        foreach (var grid in currentGrids)
        {
            if (grid != null && grid.gameObject != null)
            {
                DestroyImmediate(grid.gameObject);
            }
        }

        // 清理格子装饰物体
        ClearGridSlotDecorations();

        // 清空列表和字典
        currentGrids.Clear();
        gridPositionMap.Clear();
    }

    /// <summary>
    /// 清理所有格子底部装饰物体
    /// </summary>
    private void ClearGridSlotDecorations()
    {
        if (GridConnon_Parent == null) return;

        // 查找并销毁所有以"GridSlot_"开头的子对象
        Transform[] children = GridConnon_Parent.GetComponentsInChildren<Transform>();
        foreach (Transform child in children)
        {
            if (child != GridConnon_Parent.transform && child.name.StartsWith("GridSlot_"))
            {
                DestroyImmediate(child.gameObject);
            }
        }
    }

    /// <summary>
    /// 网格坐标转世界坐标
    /// </summary>
    /// <param name="gridX">网格X坐标</param>
    /// <param name="gridZ">网格Z坐标</param>
    /// <returns>世界坐标</returns>
    public Vector3 GridToWorldPosition(int gridX, int gridZ)
    {
        Vector3 origin = GridConnon_Parent.transform.position;

        // 计算X坐标，以GridConnon_Parent为中心
        float x = origin.x + CalculateXPosition(gridX);

        // 计算Z坐标，考虑第一行的额外距离
        float z = origin.z + CalculateZPosition(gridZ);

        return new Vector3(x, origin.y, z);
    }

    /// <summary>
    /// 计算网格X坐标对应的世界X位置
    /// 以GridConnon_Parent为中心，让炮台居中对称分布
    /// </summary>
    /// <param name="gridX">网格X坐标</param>
    /// <returns>相对于原点的X位置偏移</returns>
    private float CalculateXPosition(int gridX)
    {
        if (CurrentLevel == null) return gridX * cellSizeX; // 如果没有关卡数据，使用原来的计算方式

        int totalColumns = CurrentLevel.columns;

        // 计算中心偏移：让整个网格以GridConnon_Parent为中心
        // 如果有3列(0,1,2)，中心应该在1的位置，偏移为 -(totalColumns-1)/2.0f * cellSizeX
        // 如果有2列(0,1)，中心应该在0.5的位置，偏移为 -(totalColumns-1)/2.0f * cellSizeX
        float centerOffset = -(totalColumns - 1) / 2.0f * cellSizeX;

        return gridX * cellSizeX + centerOffset;
    }

    /// <summary>
    /// 计算网格Z坐标对应的世界Z位置
    /// 第一行（Z=0）和第二行（Z=-1）之间有额外距离
    /// </summary>
    /// <param name="gridZ">网格Z坐标</param>
    /// <returns>相对于原点的Z位置偏移</returns>
    private float CalculateZPosition(int gridZ)
    {
        if (gridZ >= 0)
        {
            // Z=0及以上的行，使用标准间距
            return gridZ * cellSizeZ;
        }
        else
        {
            // Z=-1及以下的行，需要减去额外的距离
            // gridZ = -1时，位置应该是 -cellSizeZ - firstRowExtraDistance
            // gridZ = -2时，位置应该是 -cellSizeZ - firstRowExtraDistance - cellSizeZ = -2*cellSizeZ - firstRowExtraDistance
            return gridZ * cellSizeZ - firstRowExtraDistance;
        }
    }

    /// <summary>
    /// 世界坐标转网格坐标
    /// </summary>
    /// <param name="worldPos">世界坐标</param>
    /// <returns>网格坐标</returns>
    public Vector2Int WorldToGridPosition(Vector3 worldPos)
    {
        Vector3 origin = GridConnon_Parent.transform.position;

        // 计算网格X坐标，考虑居中布局
        float xOffset = worldPos.x - origin.x;
        int gridX = CalculateGridXFromWorldOffset(xOffset);

        // 计算网格Z坐标，考虑第一行的额外距离
        float zOffset = worldPos.z - origin.z;
        int gridZ = CalculateGridZFromWorldOffset(zOffset);

        return new Vector2Int(gridX, gridZ);
    }

    /// <summary>
    /// 根据世界X坐标偏移计算网格X坐标
    /// 考虑居中布局
    /// </summary>
    /// <param name="xOffset">相对于原点的X偏移</param>
    /// <returns>网格X坐标</returns>
    private int CalculateGridXFromWorldOffset(float xOffset)
    {
        if (CurrentLevel == null) return Mathf.RoundToInt(xOffset / cellSizeX); // 如果没有关卡数据，使用原来的计算方式

        int totalColumns = CurrentLevel.columns;

        // 计算中心偏移
        float centerOffset = -(totalColumns - 1) / 2.0f * cellSizeX;

        // 减去中心偏移后再计算网格坐标
        float adjustedOffset = xOffset - centerOffset;

        return Mathf.RoundToInt(adjustedOffset / cellSizeX);
    }

    /// <summary>
    /// 根据世界Z坐标偏移计算网格Z坐标
    /// 考虑第一行的额外距离
    /// </summary>
    /// <param name="zOffset">相对于原点的Z偏移</param>
    /// <returns>网格Z坐标</returns>
    private int CalculateGridZFromWorldOffset(float zOffset)
    {
        // 如果偏移为正或0，直接计算
        if (zOffset >= 0)
        {
            return Mathf.RoundToInt(zOffset / cellSizeZ);
        }

        // 如果偏移为负，需要考虑额外距离
        // 首先检查是否在第一行到第二行的额外距离范围内
        float firstRowToSecondRowDistance = cellSizeZ + firstRowExtraDistance;

        if (zOffset >= -firstRowToSecondRowDistance)
        {
            // 在Z=0到Z=-1之间的范围
            if (zOffset >= -cellSizeZ)
            {
                return 0; // 属于第一行
            }
            else
            {
                return -1; // 属于第二行
            }
        }
        else
        {
            // 超出第二行，计算后续行
            float remainingOffset = zOffset + firstRowToSecondRowDistance;
            int additionalRows = Mathf.RoundToInt(remainingOffset / cellSizeZ);
            return -1 + additionalRows; // -1是第二行，additionalRows是额外的行数
        }
    }

    /// <summary>
    /// 获取指定位置的网格格子
    /// </summary>
    /// <param name="gridX">网格X坐标</param>
    /// <param name="gridZ">网格Z坐标</param>
    /// <returns>网格格子，如果不存在返回null</returns>
    public Common_ReserveGrid GetGridAt(int gridX, int gridZ)
    {
        Vector2Int gridPos = new Vector2Int(gridX, gridZ);
        return gridPositionMap.TryGetValue(gridPos, out Common_ReserveGrid grid) ? grid : null;
    }

    /// <summary>
    /// 检查网格位置是否被占用
    /// </summary>
    /// <param name="gridX">网格X坐标</param>
    /// <param name="gridZ">网格Z坐标</param>
    /// <returns>是否被占用</returns>
    public bool IsGridReserveOrOccupied(int gridX, int gridZ)
    {
        Common_ReserveGrid grid = GetGridAt(gridX, gridZ);
        return grid != null && grid.IsOccOrRese;
    }

    #endregion

    #region 链接系统管理

    /// <summary>
    /// 为当前关卡初始化链接系统
    /// </summary>
    private void InitializeLinkSystemForLevel()
    {
        if (GridConnon_LinkSystem.Instance != null)
        {
            GridConnon_LinkSystem.Instance.InitializeLinkSystem(CurrentCannons);
        }
    }

    #endregion

    #region 炮管理

    /// <summary>
    /// 根据关卡数据创建炮
    /// </summary>
    /// <param name="levelData">关卡数据</param>
    private void CreateCannonsFromLevelData(GridConnon_Data_Level levelData)
    {
        if (levelData.cannons == null || levelData.cannons.Count == 0)
        {
            return;
        }

        foreach (var cannonData in levelData.cannons)
        {
            if (cannonData != null)
            {
                CreateCannon(cannonData);
            }
        }
    }

    /// <summary>
    /// 创建单个炮
    /// </summary>
    /// <param name="cannonData">炮数据</param>
    /// <returns>创建的炮对象</returns>
    public GridConnon_Object CreateCannon(GridConnon_Data_Cannon cannonData)
    {
        if (cannonData == null)
        {
            return null;
        }

        // 获取对应的网格格子
        Common_ReserveGrid grid = GetGridAt(cannonData.gridX, cannonData.gridZ);
        if (grid == null)
        {
            return null;
        }

        // 检查格子是否已被占用
        if (grid.IsOccOrRese)
        {
            return null;
        }

        // 加载预制体
        string prefabPath = MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data.GetPrefabPath(cannonData.objectType);
        GameObject prefab = Resources.Load<GameObject>(prefabPath);
        if (prefab == null)
        {
            return null;
        }

        // 实例化炮对象
        Vector3 worldPos = GridToWorldPosition(cannonData.gridX, cannonData.gridZ);
        GameObject cannonObj = Instantiate(prefab, worldPos, Quaternion.identity, GridConnon_Parent.transform);
        cannonObj.name = $"{cannonData.objectType}_{cannonData.gridX}_{cannonData.gridZ}";

        // 获取炮组件
        GridConnon_Object cannon = cannonObj.GetComponent<GridConnon_Object>();
        if (cannon == null)
        {
            DestroyImmediate(cannonObj);
            return null;
        }

        // 初始化炮
        cannon.InitializeCannon(cannonData, grid);

        // 添加到列表
        CurrentCannons.Add(cannon);

        return cannon;
    }



    /// <summary>
    /// 获取指定位置的炮
    /// </summary>
    /// <param name="gridX">网格X坐标</param>
    /// <param name="gridZ">网格Z坐标</param>
    /// <returns>炮对象，如果没有则返回null</returns>
    public GridConnon_Object GetCannonAt(int gridX, int gridZ)
    {
        foreach (var cannon in CurrentCannons)
        {
            if (cannon != null && cannon.IsAtPosition(gridX, gridZ))
            {
                return cannon;
            }
        }
        return null;
    }

    /// <summary>
    /// 获取指定颜色的所有炮
    /// </summary>
    /// <param name="colorId">颜色ID</param>
    /// <returns>指定颜色的炮列表</returns>
    public List<GridConnon_Object> GetCannonsByColor(int colorId)
    {
        List<GridConnon_Object> result = new List<GridConnon_Object>();

        foreach (var cannon in CurrentCannons)
        {
            if (cannon != null && cannon.ColorId == colorId)
            {
                result.Add(cannon);
            }
        }

        return result;
    }

    /// <summary>
    /// 获取指定类型的所有炮
    /// </summary>
    /// <param name="objectType">炮类型</param>
    /// <returns>指定类型的炮列表</returns>
    public List<GridConnon_Object> GetCannonsByType(GridConnon_ObjectType objectType)
    {
        List<GridConnon_Object> result = new List<GridConnon_Object>();

        foreach (var cannon in CurrentCannons)
        {
            if (cannon != null && cannon.ObjectType == objectType)
            {
                result.Add(cannon);
            }
        }

        return result;
    }

    /// <summary>
    /// 移除炮从列表中（由炮对象调用）
    /// </summary>
    /// <param name="cannon">要移除的炮</param>
    public void DestroyCannonNoClearGrid(GridConnon_Object cannon)
    {
        if (cannon != null)
        {
            // 清理可见性状态记录
            var gridCannonInstance = cannon as GridConnon_Cannon;
            if (gridCannonInstance != null)
            {
                cannonVisibilityStates.Remove(gridCannonInstance);
                cannonLinkStickVisibilityStates.Remove(gridCannonInstance);
            }

            CurrentCannons.Remove(cannon);
            GameObject.Destroy(cannon.gameObject);
        }
    }

    /// <summary>
    /// 移除炮从列表中（由炮对象调用）
    /// </summary>
    /// <param name="cannon">要移除的炮</param>
    public void DestroyCannonAndClearGrid(GridConnon_Object cannon)
    {
        if (cannon != null)
        {
            var gridCannonInstance = cannon as GridConnon_Cannon;
            if (gridCannonInstance != null)
            {
                var cannonstate = gridCannonInstance.StateManager;
                if (cannonstate.GetCurrentGridType() == GridConnon_CannonStateManager.GridType.ShotGrid)
                {
                    cannonstate.ClearShotGrid();
                }
                else
                {
                    cannonstate.ClearGridConnon_ControllerGrid();
                }

                // 清理可见性状态记录
                cannonVisibilityStates.Remove(gridCannonInstance);
                cannonLinkStickVisibilityStates.Remove(gridCannonInstance);
            }

            CurrentCannons.Remove(cannon);
            GameObject.Destroy(cannon.gameObject);
        }
    }

    /// <summary>
    /// 清空所有炮
    /// </summary>
    public void ClearAllCannons()
    {
        // 创建副本以避免在遍历时修改列表
        var cannonsToDestroy = new List<GridConnon_Object>(CurrentCannons);

        foreach (var cannon in cannonsToDestroy)
        {
            if (cannon != null)
            {
                DestroyCannonAndClearGrid(cannon);
            }
        }

        CurrentCannons.Clear();

        // 清空所有可见性状态记录
        cannonVisibilityStates.Clear();
        cannonLinkStickVisibilityStates.Clear();
    }

    #endregion

    #region 射击格管理

    /// <summary>
    /// 获取第一个空闲的DirectShot格子
    /// </summary>
    /// <returns>空闲的DirectShot格子，没有则返回null</returns>
    public GridConnon_ShotGrid_Grid GetFirstFreeDirectShotGrid()
    {
        var shotGridController = GridConnon_ShotGrid_Controller.Instance;
        if (shotGridController == null)
        {
            return null;
        }

        // 遍历DirectShot格子数组，找到第一个空闲的
        for (int i = 0; i < shotGridController.DirectShotGrids.Length; i++)
        {
            var shotGrid = shotGridController.DirectShotGrids[i];
            if (shotGrid != null)
            {
                if (shotGrid.IsFree && !shotGrid.IsLocked)
                {
                    return shotGrid;
                }
            }
        }

        return null;
    }

    #endregion

    #region 工具方法

    /// <summary>
    /// 获取当前关卡信息
    /// </summary>
    /// <returns>当前关卡数据</returns>
    public GridConnon_Data_Level GetCurrentLevel()
    {
        return CurrentLevel;
    }

    /// <summary>
    /// 获取当前炮的数量
    /// </summary>
    /// <returns>炮的数量</returns>
    public int GetCannonCount()
    {
        return CurrentCannons.Count;
    }


    /// <summary>
    /// 检查关卡是否已创建
    /// </summary>
    /// <returns>是否已创建关卡</returns>
    public bool IsLevelCreated()
    {
        return CurrentLevel != null && currentGrids.Count > 0;
    }

    #endregion

    #region 道具管理

    /// <summary>
    /// 道具拾取功能：所有StateManager的GetCurrentGridType为GridConnon的炮进入PickOut状态
    /// </summary>
    public bool Prop_PickOut_EnterState()
    {
        // 检查是否可以执行拾取操作
        if (!CanPerformOperation(ControllerOperation.PickUp))
        {
            return false;
        }

        // 先收集可进入拾取状态的炮台（避免先切状态导致 UI 瞬时显示）
        List<GridConnon_Cannon> eligibleCannons = new List<GridConnon_Cannon>();
        foreach (var cannonObject in CurrentCannons)
        {
            if (cannonObject == null) continue;

            var cannon = cannonObject as GridConnon_Cannon;
            if (cannon == null || cannon.StateManager == null) continue;
            if (cannon.ObjectType == GridConnon_ObjectType.TreasureCannon) continue;

            // 链接的炮台不能进入 PickOut 状态
            if (GridConnon_LinkSystem.Instance != null && GridConnon_LinkSystem.Instance.IsInLinkGroup(cannon))
            {
                continue;
            }

            // 仅当处于 GridConnon 且状态为 BackRow 或 FirstRow 才能进入拾取
            if (cannon.StateManager.GetCurrentGridType() == GridConnon_CannonStateManager.GridType.GridConnon &&
                (cannon.StateManager.GetCurrentState() == GridConnon_Cannon_State.BackRow ||
                 cannon.StateManager.GetCurrentState() == GridConnon_Cannon_State.FirstRow))
            {
                eligibleCannons.Add(cannon);
            }
        }

        // 无可选目标，不进入拾取状态，不触发 UI/镜头
        if (eligibleCannons.Count <= 0)
        {
            return false;
        }

        // 存在可选目标，才切换到拾取状态（这将触发 UI 与镜头下移）
        ChangeControllerState(GridConnon_Controller_State.Prop_PickUp);

        // 让可选炮台进入拾取状态
        foreach (var cannon in eligibleCannons)
        {
            cannon.StateManager.ChangeState(GridConnon_Cannon_State.Prop_PickOut);
        }

        return true;
    }



    public bool Prop_Random()
    {
        // 检查是否可以执行随机重排操作
        if (!CanPerformOperation(ControllerOperation.RandomShuffle))
        {
            return false;
        }

        // 切换到随机重排状态
        ChangeControllerState(GridConnon_Controller_State.Prop_Random);

        // 计算目标Z行范围：从 -Prop_Random_Z 到 0（即从后往前Prop_Random_Z+1行）
        int targetRowStart = -Prop_Random_Z; // 最后一行
        int targetRowEnd = 0; // 第一行

        List<GridConnon_Cannon> targetCannons = new List<GridConnon_Cannon>();
        List<Vector2Int> originalPositions = new List<Vector2Int>();

        // 清空之前的随机重排炮台列表
        propRandomCannons.Clear();

        // 第一步：检索指定Z行范围内所有符合条件的炮
        foreach (var cannonObject in CurrentCannons)
        {
            if (cannonObject == null) continue;

            // 检查是否为GridConnon_Cannon（只有这个类型才有StateManager）
            var cannon = cannonObject as GridConnon_Cannon;
            if (cannon == null || cannon.StateManager == null) continue;

            if (cannon.ObjectType == GridConnon_ObjectType.TreasureCannon)
                continue;

            // 检查是否在指定Z行范围内、StateManager的GetCurrentGridType为GridConnon，且当前状态为BackRow或FirstRow
            if (cannon.GridZ >= targetRowStart && cannon.GridZ <= targetRowEnd &&
                cannon.StateManager.GetCurrentGridType() == GridConnon_CannonStateManager.GridType.GridConnon &&
                (cannon.StateManager.GetCurrentState() == GridConnon_Cannon_State.BackRow ||
                 cannon.StateManager.GetCurrentState() == GridConnon_Cannon_State.FirstRow))
            {
                // 检查是否被链接，链接的炮台不能进入Random状态
                if (GridConnon_LinkSystem.Instance != null && GridConnon_LinkSystem.Instance.IsInLinkGroup(cannon))
                {
                    continue;
                }

                targetCannons.Add(cannon);
                originalPositions.Add(new Vector2Int(cannon.GridX, cannon.GridZ));

                // 添加到随机重排列表
                propRandomCannons.Add(cannon);
            }
        }

        if (propRandomCannons.Count <= 0)
        {
            // 没有符合条件的炮台，切换回正常状态
            ChangeControllerState(GridConnon_Controller_State.Normal);
            return false;
        }

        // 第二步：对所有找到的炮调用ClearGridConnon_ControllerGrid
        foreach (var cannon in targetCannons)
        {


            cannon.StateManager.ClearGridConnon_ControllerGrid();
        }

        // 第三步：基于已有的炮网格位打乱
        List<Vector2Int> shuffledPositions = new List<Vector2Int>(originalPositions);

        // 使用Fisher-Yates洗牌算法打乱位置
        for (int i = shuffledPositions.Count - 1; i > 0; i--)
        {
            int randomIndex = UnityEngine.Random.Range(0, i + 1);
            Vector2Int temp = shuffledPositions[i];
            shuffledPositions[i] = shuffledPositions[randomIndex];
            shuffledPositions[randomIndex] = temp;
        }

        // 检查是否有重复的目标位置
        HashSet<Vector2Int> uniquePositions = new HashSet<Vector2Int>();
        foreach (var pos in shuffledPositions)
        {
            uniquePositions.Add(pos);
        }

        // 计数器，用于跟踪已到达目标位置的炮台数量
        int reachedCount = 0;
        int totalCount = targetCannons.Count;

        // 第四步：设置每个炮的prop_random状态的目标并进入Prop_Random状态
        for (int i = 0; i < targetCannons.Count; i++)
        {
            var cannon = targetCannons[i];
            var targetPos = shuffledPositions[i];


            // 获取炮的Prop_Random状态并设置目标
            var propRandomState = cannon.StateManager.GetStateByType(GridConnon_Cannon_State.Prop_Random) as GridConnon_CannonState_Prop_Random;
            if (propRandomState != null)
            {

                var beginEang = cannon.transform.eulerAngles;

                // 设置目标位置和到达回调
                propRandomState.SetTargetAndReachAct(targetPos.x, targetPos.y, () =>
                {
                    cannon.transform.eulerAngles = beginEang;

                    // 炮台到达目标位置的回调
                    reachedCount++;

                    // 检查是否所有炮台都已到达
                    if (reachedCount >= totalCount)
                    {

                        Prop_Random_Reached();
                    }
                });


                cannon.StateManager.ChangeState(GridConnon_Cannon_State.Prop_Random);


            }
        }


        GridConnon_Controller.On_PlaySound?.Invoke("CNT_道具打乱占位");
        GridConnon_Controller.On_PlayVib?.Invoke(MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data.Virb_Normal);
        return true;
    }


    private void Prop_Random_Reached()
    {
        if (propRandomCannons.Count == 0)
        {
            return;
        }



        // 遍历所有炮台，调用OnReachedControllerGridToControllerGrid
        foreach (var cannon in propRandomCannons)
        {
            if (cannon == null || cannon.StateManager == null) continue;

            // 获取炮台当前所在的网格格子
            Common_ReserveGrid currentGrid = GetGridAt(cannon.GridX, cannon.GridZ);

            cannon.StateManager.OnReachedControllerGridToControllerGrid(currentGrid, cannon.GridX, cannon.GridZ);
        }

        // 清空列表
        propRandomCannons.Clear();

        // 对所有炮台应用Z行隐藏优化（移动完成后）
        ApplyZRowOptimizationToAllCannons();

        // 随机重排完成，切换回正常状态
        ChangeControllerState(GridConnon_Controller_State.Normal);
    }

    /// <summary>
    /// 道具拾取退出功能：根据炮所处的行位置，分别进入BackRow和FirstRow状态
    /// </summary>
    /// <param name="isSuccess">道具是否成功使用</param>
    public void Prop_PickOut_Exit()
    {
        int affectedCount = 0;
        int firstRowCount = 0;
        int backRowCount = 0;

        // 再次检索所有StateManager的GetCurrentGridType为GridConnon的炮
        foreach (var cannonObject in CurrentCannons)
        {
            if (cannonObject == null) continue;

            // 检查是否为GridConnon_Cannon（只有这个类型才有StateManager）
            var cannon = cannonObject as GridConnon_Cannon;
            if (cannon == null || cannon.StateManager == null) continue;

            // 检查StateManager的GetCurrentGridType是否为GridConnon
            if (cannon.StateManager.GetCurrentGridType() == GridConnon_CannonStateManager.GridType.GridConnon)
            {
                // 根据所处位置与链接规则判断目标状态
                if (cannon.GridZ == 0) // 第一行（Z=0）
                {
                    cannon.StateManager.ChangeState(GridConnon_Cannon_State.FirstRow);
                    firstRowCount++;
                }
                else // 非第一行：使用状态管理器的判定，考虑链接保持规则
                {
                    var targetState = cannon.StateManager.DetermineStateByPositionAndLinks(cannon.GridZ);
                    cannon.StateManager.ChangeState(targetState);
                    if (targetState == GridConnon_Cannon_State.FirstRow) firstRowCount++; else backRowCount++;
                }

                affectedCount++;
            }
        }

        // 拾取操作完成，切换回正常状态
        ChangeControllerState(GridConnon_Controller_State.Normal);
    }

    #endregion

    #region 射击合成管理

    /// <summary>
    /// 应用合成配置参数到合成系统
    /// </summary>
    private void ApplySynthesisConfig()
    {
        if (synthesisSystem != null)
        {
            synthesisSystem.riseAnimationDuration = synthesisRiseDuration;
            synthesisSystem.synthesisAnimationDuration = synthesisMoveDuration;
            synthesisSystem.fallAnimationDuration = synthesisFallDuration;
            synthesisSystem.riseHeight = synthesisRiseHeight;
            synthesisSystem.sideOffsetDistance = synthesisSideOffset;
        }
    }


    #endregion

    #region 控制器状态管理

    /// <summary>
    /// 状态变化事件
    /// </summary>
    public static System.Action<GridConnon_Controller_State, GridConnon_Controller_State> OnControllerStateChanged;

    /// <summary>
    /// 切换控制器状态
    /// </summary>
    /// <param name="newState">新状态</param>
    public void ChangeControllerState(GridConnon_Controller_State newState)
    {
        if (currentControllerState == newState)
        {
            return;
        }

        GridConnon_Controller_State oldState = currentControllerState;

        // 退出当前状态
        OnExitControllerState(oldState);


        // 切换到新状态
        currentControllerState = newState;

        // 进入新状态
        OnEnterControllerState(newState);


        // 触发状态变化事件
        OnControllerStateChanged?.Invoke(oldState, newState);
    }

    /// <summary>
    /// 进入控制器状态时的处理
    /// </summary>
    /// <param name="state">进入的状态</param>
    private void OnEnterControllerState(GridConnon_Controller_State state)
    {
        switch (state)
        {
            case GridConnon_Controller_State.Normal:
                OnEnterNormalState();
                break;
            case GridConnon_Controller_State.Prop_Random:
                OnEnterPropRandomState();
                break;
            case GridConnon_Controller_State.Prop_PickUp:
                OnEnterPropPickUpState();
                break;
        }
    }

    /// <summary>
    /// 退出控制器状态时的处理
    /// </summary>
    /// <param name="state">退出的状态</param>
    private void OnExitControllerState(GridConnon_Controller_State state)
    {
        switch (state)
        {
            case GridConnon_Controller_State.Normal:
                OnExitNormalState();
                break;
            case GridConnon_Controller_State.Prop_Random:
                OnExitPropRandomState();
                break;
            case GridConnon_Controller_State.Prop_PickUp:
                OnExitPropPickUpState();
                break;
        }
    }

    /// <summary>
    /// 进入正常状态
    /// </summary>
    private void OnEnterNormalState()
    {
        // 在正常状态下可以执行的操作
        // 例如：启用用户输入、恢复游戏逻辑等
    }

    /// <summary>
    /// 退出正常状态
    /// </summary>
    private void OnExitNormalState()
    {
        // 退出正常状态时的清理工作
    }

    /// <summary>
    /// 进入道具随机重排状态
    /// </summary>
    private void OnEnterPropRandomState()
    {
        // 禁用某些用户输入或操作
        // 显示随机重排的UI提示等
    }

    /// <summary>
    /// 退出道具随机重排状态
    /// </summary>
    private void OnExitPropRandomState()
    {
        // 恢复用户操作
        // 隐藏随机重排的UI提示等
    }

    /// <summary>
    /// 进入道具拾取状态
    /// </summary>
    private void OnEnterPropPickUpState()
    {
        // 启用拾取相关的UI和交互
        // 显示拾取提示等
    }

    /// <summary>
    /// 退出道具拾取状态
    /// </summary>
    private void OnExitPropPickUpState()
    {
        // 禁用拾取相关的UI和交互
        // 隐藏拾取提示等
    }

    /// <summary>
    /// 检查是否可以执行某个操作（基于当前状态）
    /// </summary>
    /// <param name="operation">操作类型</param>
    /// <returns>是否可以执行</returns>
    public bool CanPerformOperation(ControllerOperation operation)
    {
        switch (currentControllerState)
        {
            case GridConnon_Controller_State.Normal:
                // 正常状态下允许所有操作
                return true;

            case GridConnon_Controller_State.Prop_Random:
                // 随机重排状态下只允许取消操作
                return operation == ControllerOperation.Cancel;

            case GridConnon_Controller_State.Prop_PickUp:
                // 拾取状态下允许拾取相关操作和取消操作
                return operation == ControllerOperation.PickUp ||
                       operation == ControllerOperation.Cancel;

            default:
                return false;
        }
    }


    #endregion

    #region 清除功能

    /// <summary>
    /// 清除炮台的颜色hitcount要求数据结构
    /// </summary>
    [System.Serializable]
    public class CannonClearRequirement
    {
        [Tooltip("颜色ID")]
        public int colorId;

        [Tooltip("需要清除的hitcount总数")]
        public int clearValue;

        public CannonClearRequirement(int colorId, int clearValue)
        {
            this.colorId = colorId;
            this.clearValue = clearValue;
        }
    }

    private Dictionary<int, int> activeClearRequirements = new Dictionary<int, int>();
    private float clearCheckInterval = 0.1f; // 清除检测间隔（秒）
    private float lastClearCheckTime = 0f;

    /// <summary>
    /// 根据颜色和hitcount数值要求清除炮台
    /// 例如：传入颜色1需要清除20点hitcount，系统会按优先级清除颜色1的炮台，直到总共清除了20点hitcount为止
    /// </summary>
    /// <param name="clearRequirements">清除要求列表，clearValue表示需要清除的hitcount总数</param>
    public void ClearCannonsByColorValue(List<CannonClearRequirement> clearRequirements)
    {
        if (clearRequirements == null || clearRequirements.Count == 0)
        {
            return;
        }

        // 如果已有清除要求在处理，叠加新的清除要求
        if (activeClearRequirements.Count > 0)
        {
            ////Debug.Log("已有清除要求在处理，叠加新的清除要求");

            // 叠加新的清除要求到现有的要求中
            foreach (var requirement in clearRequirements)
            {
                if (activeClearRequirements.ContainsKey(requirement.colorId))
                {
                    activeClearRequirements[requirement.colorId] += requirement.clearValue;
                }
                else
                {
                    activeClearRequirements[requirement.colorId] = requirement.clearValue;
                }

                ////Debug.Log($"叠加清除要求：颜色{requirement.colorId} +{requirement.clearValue}，总计需要清除 {activeClearRequirements[requirement.colorId]} 个");
            }

            return;
        }

        // 初始化清除要求
        activeClearRequirements.Clear();
        foreach (var requirement in clearRequirements)
        {
            activeClearRequirements[requirement.colorId] = requirement.clearValue;
        }

        // 重置检测时间，立即开始检测
        lastClearCheckTime = 0f;
        ////Debug.Log("开始清除炮台，定时检测已启动");
    }

    /// <summary>
    /// 定时检测清除要求（在Update中调用）
    /// </summary>
    private void CheckClearRequirements()
    {
        // 如果没有清除要求，直接返回
        if (activeClearRequirements.Count == 0)
        {
            return;
        }

        // 检查时间间隔
        if (Time.time - lastClearCheckTime < clearCheckInterval)
        {
            return;
        }

        // 更新检测时间
        lastClearCheckTime = Time.time;

        // 检查是否所有要求都已满足
        if (IsAllRequirementsMet(activeClearRequirements))
        {
            ////Debug.Log("所有清除要求已完成");
            activeClearRequirements.Clear();
            return;
        }

        // 获取符合条件的炮台列表
        List<GridConnon_Cannon> targetCannons = GetClearTargetCannons(activeClearRequirements);

        if (targetCannons.Count == 0)
        {
            // 没有符合条件的炮台，等待下次检测
            ////Debug.Log("当前没有符合条件的炮台（BackRow、FirstRow、Shooting状态），等待中...");
            return;
        }

        // 按优先级排序炮台
        targetCannons = SortCannonsByPriority(targetCannons);

        // 按颜色分组处理炮台，按hitcount总和进行清除
        var colorGroups = new Dictionary<int, List<GridConnon_Cannon>>();
        foreach (var cannon in targetCannons)
        {
            if (cannon == null) continue;

            if (!colorGroups.ContainsKey(cannon.ColorId))
            {
                colorGroups[cannon.ColorId] = new List<GridConnon_Cannon>();
            }
            colorGroups[cannon.ColorId].Add(cannon);
        }

        // 处理每种颜色的炮台
        foreach (var colorGroup in colorGroups)
        {
            int colorId = colorGroup.Key;
            var cannonsOfColor = colorGroup.Value;

            // 检查是否还需要清除这个颜色
            if (!activeClearRequirements.ContainsKey(colorId) || activeClearRequirements[colorId] <= 0)
            {
                continue;
            }

            int remainingHitcountToClear = activeClearRequirements[colorId];
            int clearedHitcount = 0;

            // 按hitcount总和清除炮台
            foreach (var cannon in cannonsOfColor)
            {
                if (cannon == null) continue;

                int cannonCurrentHitcount = cannon.GetCurrentHitCount();

                // 如果该炮当前hitcount>0，才会消耗清除值并减少；否则（=0）只进行必要的链接处理，随后统一在末尾触发销毁
                if (cannonCurrentHitcount > 0 && remainingHitcountToClear > 0)
                {
                    // 处理链接关系（可能影响该炮与其他炮的状态显示）
                    HandleCannonLinks(cannon);

                    int hitcountToReduce = Mathf.Min(cannonCurrentHitcount, remainingHitcountToClear);

                    for (int i = 0; i < hitcountToReduce; i++)
                    {
                        cannon.ReduceHitCount();
                    }

                    clearedHitcount += hitcountToReduce;
                    remainingHitcountToClear -= hitcountToReduce;
                }

                // 无论是被减到0，还是原本就是0，都在此处统一判断并触发销毁
                if (cannon.GetCurrentHitCount() <= 0)
                {
                    cannon.ToDestroy(cannon.GetCurrentState());
                }
            }

            // 更新剩余要求
            if (clearedHitcount > 0)
            {
                activeClearRequirements[colorId] -= clearedHitcount;
                ////Debug.Log($"颜色 {colorId} 清除了 {clearedHitcount} 点hitcount，剩余需要清除 {activeClearRequirements[colorId]} 点");
            }

            // 检查是否已满足所有要求
            if (IsAllRequirementsMet(activeClearRequirements))
            {
                ////Debug.Log("所有清除要求已完成");
                activeClearRequirements.Clear();
                return;
            }
        }
    }

    /// <summary>
    /// 检查是否满足所有清除要求
    /// </summary>
    /// <param name="remainingRequirements">剩余要求字典</param>
    /// <returns>是否全部满足</returns>
    private bool IsAllRequirementsMet(Dictionary<int, int> remainingRequirements)
    {
        foreach (var requirement in remainingRequirements)
        {
            if (requirement.Value > 0)
            {
                return false;
            }
        }
        return true;
    }

    /// <summary>
    /// 获取符合清除条件的炮台列表
    /// </summary>
    /// <param name="remainingRequirements">剩余要求字典</param>
    /// <returns>符合条件的炮台列表</returns>
    private List<GridConnon_Cannon> GetClearTargetCannons(Dictionary<int, int> remainingRequirements)
    {
        List<GridConnon_Cannon> targetCannons = new List<GridConnon_Cannon>();

        // 遍历所有炮台
        foreach (var cannonObject in CurrentCannons)
        {
            if (cannonObject == null) continue;

            // 检查是否为GridConnon_Cannon类型
            var cannon = cannonObject as GridConnon_Cannon;
            if (cannon == null) continue;

            // 检查颜色是否在要求列表中
            if (!remainingRequirements.ContainsKey(cannon.ColorId) || remainingRequirements[cannon.ColorId] <= 0)
            {
                continue;
            }

            // 检查状态是否符合要求（只清除BackRow、FirstRow、Shooting状态）
            var currentState = cannon.GetCurrentState();
            if (currentState != GridConnon_Cannon_State.BackRow &&
                currentState != GridConnon_Cannon_State.FirstRow &&
                currentState != GridConnon_Cannon_State.Shooting)
            {
                continue;
            }

            // 允许击打值为0进入列表，后续流程会触发销毁

            targetCannons.Add(cannon);
        }

        return targetCannons;
    }

    /// <summary>
    /// 按优先级排序炮台（先清除GridConnon网格上的，按Z坐标从大到小；再清除ShotGrid上的，按索引从左到右）
    /// </summary>
    /// <param name="cannons">炮台列表</param>
    /// <returns>排序后的炮台列表</returns>
    private List<GridConnon_Cannon> SortCannonsByPriority(List<GridConnon_Cannon> cannons)
    {
        List<GridConnon_Cannon> gridConnons = new List<GridConnon_Cannon>();
        List<GridConnon_Cannon> shotGridCannons = new List<GridConnon_Cannon>();

        // 分类炮台
        foreach (var cannon in cannons)
        {
            if (cannon.StateManager.GetCurrentGridType() == GridConnon_CannonStateManager.GridType.GridConnon)
            {
                gridConnons.Add(cannon);
            }
            else if (cannon.StateManager.GetCurrentGridType() == GridConnon_CannonStateManager.GridType.ShotGrid)
            {
                shotGridCannons.Add(cannon);
            }
        }

        // 排序GridConnon炮台：按Z坐标从大到小（从后往前）
        gridConnons.Sort((a, b) => b.GridZ.CompareTo(a.GridZ));

        // 排序ShotGrid炮台：按在射击格中的索引从左到右
        shotGridCannons.Sort((a, b) => GetShotGridIndex(a).CompareTo(GetShotGridIndex(b)));

        // 合并列表：先GridConnon，再ShotGrid
        List<GridConnon_Cannon> result = new List<GridConnon_Cannon>();
        result.AddRange(gridConnons);
        result.AddRange(shotGridCannons);

        return result;
    }

    /// <summary>
    /// 获取炮台在射击格中的索引
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <returns>射击格索引</returns>
    private int GetShotGridIndex(GridConnon_Cannon cannon)
    {
        var shotController = GridConnon_ShotGrid_Controller.Instance;
        if (shotController == null) return int.MaxValue;

        // 检查DirectShotGrids
        for (int i = 0; i < shotController.DirectShotGrids.Length; i++)
        {
            var grid = shotController.DirectShotGrids[i];
            if (grid != null && grid.IsOccupied)
            {
                var cannonObj = grid.GetItem();
                if (cannonObj != null && cannonObj.GetComponent<GridConnon_Cannon>() == cannon)
                {
                    return i;
                }
            }
        }

        // 检查RewardUnlockGrids
        for (int i = 0; i < shotController.RewardUnlockGrids.Length; i++)
        {
            var grid = shotController.RewardUnlockGrids[i];
            if (grid != null && grid.IsOccupied)
            {
                var cannonObj = grid.GetItem();
                if (cannonObj != null && cannonObj.GetComponent<GridConnon_Cannon>() == cannon)
                {
                    return shotController.DirectShotGrids.Length + i;
                }
            }
        }

        // 检查ReviveUnlockGrids
        for (int i = 0; i < shotController.ReviveUnlockGrids.Length; i++)
        {
            var grid = shotController.ReviveUnlockGrids[i];
            if (grid != null && grid.IsOccupied)
            {
                var cannonObj = grid.GetItem();
                if (cannonObj != null && cannonObj.GetComponent<GridConnon_Cannon>() == cannon)
                {
                    return shotController.DirectShotGrids.Length + shotController.RewardUnlockGrids.Length + i;
                }
            }
        }

        return int.MaxValue;
    }

    /// <summary>
    /// 处理炮台的链接关系
    /// </summary>
    /// <param name="cannonToRemove">要移除的炮台</param>
    private void HandleCannonLinks(GridConnon_Cannon cannonToRemove)
    {
        if (GridConnon_LinkSystem.Instance == null || !GridConnon_LinkSystem.Instance.IsInLinkGroup(cannonToRemove))
        {
            return;
        }

        // 获取与该炮台直接链接的炮台列表
        var linkedCannons = GridConnon_LinkSystem.Instance.GetDirectlyLinkedCannons(cannonToRemove);

        if (linkedCannons.Count > 0)
        {
            ////Debug.Log($"炮台 {cannonToRemove.ObjectId} 有 {linkedCannons.Count} 个链接炮台，开始解除链接");
        }

        // 先清除双方的链接圆棍
        foreach (var linkedCannon in linkedCannons)
        {
            if (linkedCannon == null) continue;

            // 清除被移除炮台上指向链接炮台的圆棍
            cannonToRemove.DestroyLinkStick(linkedCannon.ObjectId);

            // 清除链接炮台上指向被移除炮台的圆棍
            linkedCannon.DestroyLinkStick(cannonToRemove.ObjectId);

            ////Debug.Log($"已清除炮台 {cannonToRemove.ObjectId} 与炮台 {linkedCannon.ObjectId} 之间的双向链接圆棍");
        }

        // 清除该炮台的所有链接关系
        GridConnon_LinkSystem.Instance.ClearLinksForCannon(cannonToRemove);

        // 重新评估被影响炮台的状态
        foreach (var linkedCannon in linkedCannons)
        {
            if (linkedCannon == null) continue;

            // 获取当前状态
            var currentState = linkedCannon.GetCurrentState();

            // 如果链接炮台已经在Shooting状态，应该保持Shooting状态
            if (currentState == GridConnon_Cannon_State.Shooting)
            {
                ////Debug.Log($"链接炮台 {linkedCannon.ObjectId} 保持Shooting状态");
                continue; // 保持当前状态，不需要改变
            }

            // 只有在BackRow或FirstRow状态时才需要重新评估
            if (currentState == GridConnon_Cannon_State.BackRow || currentState == GridConnon_Cannon_State.FirstRow)
            {
                // 根据Z坐标重新确定状态
                GridConnon_Cannon_State newState = linkedCannon.GridZ == 0
                    ? GridConnon_Cannon_State.FirstRow
                    : GridConnon_Cannon_State.BackRow;

                linkedCannon.StateManager.ChangeState(newState);
                ////Debug.Log($"链接炮台 {linkedCannon.ObjectId} 从 {currentState} 重新评估为 {newState}");
            }
            else
            {
                ////Debug.Log($"链接炮台 {linkedCannon.ObjectId} 保持当前状态 {currentState}");
            }
        }
    }

    /// <summary>
    /// 处理炮台清除
    /// </summary>
    /// <param name="cannon">要清除的炮台</param>
    /// <param name="clearValue">清除数值</param>
    /// <returns>炮台是否进入销毁状态</returns>
    private bool ProcessCannonClearance(GridConnon_Cannon cannon, int clearValue)
    {
        // 减少指定数值的hitcount
        for (int i = 0; i < clearValue && cannon.GetCurrentHitCount() > 0; i++)
        {
            cannon.ReduceHitCount();
        }

        // 检查是否需要进入销毁状态
        if (cannon.GetCurrentHitCount() <= 0)
        {
            // 进入销毁状态，系统会自动处理占位清除
            cannon.ToDestroy(cannon.GetCurrentState());
            return true; // 炮台进入销毁状态
        }

        return false; // 炮台仍有hitcount，保持当前状态和占位
    }




    #endregion

    #region Z行隐藏优化

    /// <summary>
    /// 对所有炮台应用Z行隐藏优化
    /// </summary>
    private void ApplyZRowOptimizationToAllCannons()
    {
        // 遍历所有炮台，检查其Z坐标是否需要优化
        foreach (var cannonObject in CurrentCannons)
        {
            if (cannonObject == null) continue;

            // 检查是否为GridConnon_Cannon类型
            var cannon = cannonObject as GridConnon_Cannon;
            if (cannon == null || cannon.StateManager == null) continue;

            // 应用Z行隐藏优化到单个炮台
            ApplyZRowOptimizationToCannon(cannon);
        }
    }



    /// <summary>
    /// 设置炮台网格对象的可见性
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <param name="isVisible">是否可见</param>
    private void SetCannonMeshVisibility(GridConnon_Cannon cannon, bool isVisible)
    {
        if (cannon != null && cannon.connon_MeshObject != null)
        {
            // 调试日志
            //Debug.Log($"设置炮台 {cannon.ObjectId} 网格对象可见性: {isVisible}, connon_MeshObject: {cannon.connon_MeshObject.name}");

            // 设置炮台本身的可见性
            cannon.connon_MeshObject.SetActive(isVisible);
            //   cannon.foot_MeshObject.SetActive(isVisible);

            // 设置链接圆棍的可见性
            SetCannonLinkSticksVisibility(cannon, isVisible);
        }
        else
        {
            // 调试日志
            //Debug.LogWarning($"无法设置炮台可见性 - cannon: {cannon?.ObjectId}, connon_MeshObject: {cannon?.connon_MeshObject}");
        }
    }

    /// <summary>
    /// 设置炮台链接圆棍的可见性
    /// </summary>
    /// <param name="cannon">炮台</param>
    /// <param name="isVisible">是否可见</param>
    private void SetCannonLinkSticksVisibility(GridConnon_Cannon cannon, bool isVisible)
    {
        if (cannon == null || cannon.linkStickObjects == null) return;

        // 初始化炮台的链接圆棍可见性状态字典
        if (!cannonLinkStickVisibilityStates.ContainsKey(cannon))
        {
            cannonLinkStickVisibilityStates[cannon] = new Dictionary<int, bool>();
        }

        int linkStickCount = 0;
        foreach (var kvp in cannon.linkStickObjects)
        {
            if (kvp.Value != null)
            {
                kvp.Value.SetActive(isVisible);
                cannonLinkStickVisibilityStates[cannon][kvp.Key] = isVisible;
                linkStickCount++;
            }
        }

        if (linkStickCount > 0)
        {
            //Debug.Log($"设置炮台 {cannon.ObjectId} 的 {linkStickCount} 个链接圆棍可见性: {isVisible}");
        }
    }

    /// <summary>
    /// 对单个炮台应用Z行隐藏优化
    /// </summary>
    /// <param name="cannon">炮台</param>
    public void ApplyZRowOptimizationToCannon(GridConnon_Cannon cannon)
    {
        if (cannon == null) return;

        // 检查炮台的Z坐标是否需要优化（Z轴为负数，越小越远）
        bool shouldBeVisible = cannon.GridZ > -optimizeZ;

        // 调试日志
        //Debug.Log($"单独应用优化 - 炮台 {cannon.ObjectId} 位置 ({cannon.GridX}, {cannon.GridZ}), optimizeZ: {optimizeZ}, 应该可见: {shouldBeVisible}");

        SetCannonMeshVisibility(cannon, shouldBeVisible);
        cannonVisibilityStates[cannon] = shouldBeVisible;
    }

    #endregion

    #region 性能优化 - Shooting状态炮台管理

    /// <summary>
    /// 炮台进入Shooting状态时调用（由炮台状态管理器调用）
    /// 性能优化：维护专门的Shooting状态炮台列表
    /// </summary>
    /// <param name="cannon">进入Shooting状态的炮台</param>
    public void OnCannonEnterShooting(GridConnon_Cannon cannon)
    {
        if (cannon == null) return;

        // 避免重复添加
        if (!shootingCannons.Contains(cannon))
        {
            shootingCannons.Add(cannon);
            //Debug.Log($"[GridConnon_Controller] 炮台 {cannon.ObjectId} 进入Shooting状态，当前Shooting炮台数量: {shootingCannons.Count}");
        }
    }

    /// <summary>
    /// 炮台退出Shooting状态时调用（由炮台状态管理器调用）
    /// 性能优化：从专门的Shooting状态炮台列表中移除
    /// </summary>
    /// <param name="cannon">退出Shooting状态的炮台</param>
    public void OnCannonExitShooting(GridConnon_Cannon cannon)
    {
        if (cannon == null) return;

        // 从列表中移除
        if (shootingCannons.Remove(cannon))
        {
            //Debug.Log($"[GridConnon_Controller] 炮台 {cannon.ObjectId} 退出Shooting状态，当前Shooting炮台数量: {shootingCannons.Count}");
        }
    }

    /// <summary>
    /// 清理已被销毁的Shooting状态炮台
    /// 定期清理避免空引用累积
    /// </summary>
    public void CleanupDestroyedShootingCannons()
    {
        int originalCount = shootingCannons.Count;
        shootingCannons.RemoveAll(cannon => cannon == null);

        if (originalCount != shootingCannons.Count)
        {
            //Debug.Log($"[GridConnon_Controller] 清理了 {originalCount - shootingCannons.Count} 个已销毁的Shooting炮台，剩余: {shootingCannons.Count}");
        }
    }

    /// <summary>
    /// 重新扫描所有炮台的Shooting状态（用于修复不一致的情况）
    /// 一般在关卡创建完成后或出现异常时调用
    /// </summary>
    public void RefreshShootingCannonsList()
    {
        shootingCannons.Clear();

        foreach (var cannonObject in CurrentCannons)
        {
            if (cannonObject is GridConnon_Cannon cannon &&
                cannon.StateManager != null &&
                cannon.StateManager.GetCurrentState() == GridConnon_Cannon_State.Shooting)
            {
                shootingCannons.Add(cannon);
            }
        }

        //Debug.Log($"[GridConnon_Controller] 刷新Shooting炮台列表完成，共找到 {shootingCannons.Count} 个Shooting状态炮台");
    }

    #endregion


    /* 
        public static bool CanClick()
        {
            if (Tool_InputManager.CanProcessInput() && !Tool_IsPointerOverUI.Check())
                return true;
            return false;
        } */
}

/// <summary>
/// GridConnon控制器状态枚举
/// </summary>
public enum GridConnon_Controller_State
{
    /// <summary>
    /// 正常状态 - 默认状态，可以进行正常游戏操作
    /// </summary>
    Normal,

    /// <summary>
    /// 道具随机重排状态 - 正在执行随机重排操作
    /// </summary>
    Prop_Random,

    /// <summary>
    /// 道具拾取状态 - 正在执行拾取操作
    /// </summary>
    Prop_PickUp
}

/// <summary>
/// 控制器操作类型枚举
/// </summary>
public enum ControllerOperation
{
    /// <summary>
    /// 正常射击操作
    /// </summary>
    Shoot,

    /// <summary>
    /// 移动炮台操作
    /// </summary>
    Move,

    /// <summary>
    /// 道具拾取操作
    /// </summary>
    PickUp,

    /// <summary>
    /// 道具随机重排操作
    /// </summary>
    RandomShuffle,

    /// <summary>
    /// 取消当前操作
    /// </summary>
    Cancel,

    /// <summary>
    /// 创建关卡操作
    /// </summary>
    CreateLevel,

    /// <summary>
    /// 清空关卡操作
    /// </summary>
    ClearLevel
}
