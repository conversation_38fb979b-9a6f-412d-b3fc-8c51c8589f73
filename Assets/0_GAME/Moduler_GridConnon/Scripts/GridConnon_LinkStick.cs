using UnityEngine;

/// <summary>
/// 链接圆棍组件 - 负责显示两个炮台间的连接
/// 实时更新位置、旋转、长度和颜色
/// </summary>
public class GridConnon_LinkStick : MonoBehaviour
{
    [Header("圆棍配置")]
    [Tooltip("圆棍渲染器")]
    public MeshRenderer stickRenderer;

    [Toolt<PERSON>("拥有者炮台")]
    public GridConnon_Cannon ownerCannon;

    [<PERSON>lt<PERSON>("目标炮台")]
    public GridConnon_Cannon targetCannon;

    [Header("视觉参数")]
    [Tooltip("圆棍局部位置偏移")]
    public Vector3 localPositionOffset = Vector3.zero;

    [Tooltip("长度缩放因子（建议值：1.0-2.0，可根据预制体调整）")]
    [Range(0.1f, 5.0f)]
    public float lengthScaleFactor = 1.0f;

    [Tooltip("圆棍材质")]
    public Material stickMaterial;

    [Header("调试信息")]
    [Tooltip("当前距离")]
    [SerializeField] private float currentDistance = 0f;

    [Toolt<PERSON>("当前长度")]
    [SerializeField] private float currentLength = 0f;

    [Toolt<PERSON>("当前颜色")]
    [SerializeField] private Color currentColor = Color.clear;

    // 私有变量
    private MaterialPropertyBlock propertyBlock;
    private bool isInitialized = false;
    private Vector3 originalScale; // 保存预制体的原始缩放

    #region Unity生命周期

    private void Awake()
    {
        // 保存预制体的原始缩放
        originalScale = transform.localScale;

        // 自动获取组件
        if (stickRenderer == null)
        {
            stickRenderer = GetComponent<MeshRenderer>();
        }

        // 初始状态隐藏
        HideStick();
    }

    void Update()
    {
        if (isInitialized && ownerCannon != null && targetCannon != null)
        {
            UpdateStickTransform();
        }
    }

    #endregion

    #region 链接管理

    /// <summary>
    /// 设置链接的炮台
    /// </summary>
    /// <param name="owner">拥有者炮台</param>
    /// <param name="target">目标炮台</param>
    public void SetLinkedCannons(GridConnon_Cannon owner, GridConnon_Cannon target)
    {
        ownerCannon = owner;
        targetCannon = target;

        // 初始化MaterialPropertyBlock
        if (propertyBlock == null && stickRenderer != null)
        {
            propertyBlock = new MaterialPropertyBlock();
            stickRenderer.GetPropertyBlock(propertyBlock);
        }

        isInitialized = true;

        // 立即更新变换
        UpdateStickTransform();

        //Debug.Log($"[圆棍] 设置链接：{owner?.ObjectId} -> {target?.ObjectId}");
    }

    /// <summary>
    /// 清理链接
    /// </summary>
    public void ClearLink()
    {
        ownerCannon = null;
        targetCannon = null;
        isInitialized = false;

        // 隐藏圆棍
        if (stickRenderer != null)
        {
            stickRenderer.enabled = false;
        }

        //Debug.Log("[圆棍] 链接已清理");
    }

    #endregion

    #region 变换更新

    /// <summary>
    /// 更新圆棍的变换（位置、旋转、缩放）
    /// </summary>
    private void UpdateStickTransform()
    {
        if (ownerCannon == null || targetCannon == null)
        {
            HideStick();
            return;
        }

        // 显示圆棍
        ShowStick();

        // 计算两炮台间的距离和方向
        Vector3 ownerPos = ownerCannon.transform.position;
        Vector3 targetPos = targetCannon.transform.position;
        Vector3 direction = targetPos - ownerPos;
        float distance = direction.magnitude;

        // 更新调试信息
        currentDistance = distance;

        // 如果距离太小，隐藏圆棍
        if (distance < 0.01f)
        {
            HideStick();
            return;
        }

        // 圆棍位置：固定在拥有者炮台位置加上局部偏移
        transform.position = ownerPos + localPositionOffset;

        // 圆棍旋转：朝向目标炮台
        transform.rotation = Quaternion.LookRotation(direction);

        // 圆棍长度：只修改Z轴缩放，距离乘以缩放因子
        float stickLength = distance * lengthScaleFactor;
        currentLength = distance; // 调试信息显示实际距离

        // 获取原始缩放，只修改Z轴（长度），保持X和Y轴由预制体决定
        Vector3 currentScale = originalScale;
        transform.localScale = new Vector3(currentScale.x, currentScale.y, stickLength);

        // 更新颜色
        UpdateStickColor();
    }

    /// <summary>
    /// 显示圆棍
    /// </summary>
    private void ShowStick()
    {
        if (stickRenderer != null && !stickRenderer.enabled)
        {
            stickRenderer.enabled = true;
        }
    }

    /// <summary>
    /// 隐藏圆棍
    /// </summary>
    private void HideStick()
    {
        if (stickRenderer != null && stickRenderer.enabled)
        {
            stickRenderer.enabled = false;
        }
    }

    #endregion

    #region 颜色更新

    /// <summary>
    /// 更新圆棍颜色为拥有者炮台的颜色
    /// </summary>
    private void UpdateStickColor()
    {
        if (stickRenderer == null || ownerCannon == null) return;

        Color cannonColor = GetOwnerCannonColor();

        // 只有颜色变化时才更新
        if (currentColor != cannonColor)
        {
            currentColor = cannonColor;
            ApplyColor(cannonColor);
        }
    }

    /// <summary>
    /// 获取拥有者炮台的颜色
    /// </summary>
    /// <returns>炮台颜色</returns>
    private Color GetOwnerCannonColor()
    {
        if (ownerCannon == null)
        {
            return Color.white;
        }

        // 检查是否为问号炮台类型
        bool isQuestionCannon = ownerCannon.ObjectType == GridConnon_ObjectType.QuestionNormalCannon ||
                               ownerCannon.ObjectType == GridConnon_ObjectType.QuestionTriangleCannon;

        if (isQuestionCannon)
        {
            var currentState = ownerCannon.GetCurrentState();

            // 问号炮台在BackRow状态时使用专用颜色
            if (currentState == GridConnon_Cannon_State.BackRow)
            {
                return ownerCannon.Type_Question_LinkStick_Color;
            }
            // 问号炮台在FirstRow状态时使用正常颜色（如果有颜色的话）
            else if (currentState == GridConnon_Cannon_State.FirstRow)
            {
                if (ownerCannon.HasColor())
                {
                    var questionColorData = MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data;
                    if (questionColorData != null)
                    {
                        return questionColorData.GetColorById(ownerCannon.ColorId);
                    }
                }
                return Color.white;
            }
            // 其他状态（包括Moving）不改变颜色，返回当前颜色
            else
            {
                return currentColor;
            }
        }

        // 非问号炮台使用正常颜色逻辑
        if (!ownerCannon.HasColor())
        {
            return Color.white;
        }

        // 从游戏设置中获取颜色
        var colorData = MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data;
        if (colorData != null)
        {
            return colorData.GetColorById(ownerCannon.ColorId);
        }

        return Color.white;
    }

    /// <summary>
    /// 应用颜色到圆棍
    /// </summary>
    /// <param name="color">要应用的颜色</param>
    private void ApplyColor(Color color)
    {
        if (stickRenderer == null) return;

        // 优先使用MaterialPropertyBlock避免材质实例化
        if (propertyBlock != null)
        {
            propertyBlock.SetColor("_Color", color);
            stickRenderer.SetPropertyBlock(propertyBlock);
        }
        else if (stickMaterial != null)
        {
            // 备用方案：直接设置材质颜色
            stickMaterial.color = color;
        }
    }

    #endregion

    #region 工具方法

    /// <summary>
    /// 检查链接是否有效
    /// </summary>
    /// <returns>链接是否有效</returns>
    public bool IsLinkValid()
    {
        return ownerCannon != null && targetCannon != null && isInitialized;
    }

    /// <summary>
    /// 获取当前链接距离
    /// </summary>
    /// <returns>链接距离</returns>
    public float GetLinkDistance()
    {
        if (!IsLinkValid()) return 0f;

        return Vector3.Distance(ownerCannon.transform.position, targetCannon.transform.position);
    }

    /// <summary>
    /// 获取链接方向（从拥有者指向目标）
    /// </summary>
    /// <returns>标准化的方向向量</returns>
    public Vector3 GetLinkDirection()
    {
        if (!IsLinkValid()) return Vector3.zero;

        Vector3 direction = targetCannon.transform.position - ownerCannon.transform.position;
        return direction.normalized;
    }

    /// <summary>
    /// 强制更新圆棍状态
    /// </summary>
    public void ForceUpdate()
    {
        if (isInitialized)
        {
            UpdateStickTransform();
        }
    }

    /// <summary>
    /// 强制更新链接棍颜色
    /// </summary>
    public void ForceUpdateColor()
    {
        if (stickRenderer == null || ownerCannon == null) return;

        Color cannonColor = GetOwnerCannonColor();
        currentColor = cannonColor;
        ApplyColor(cannonColor);

        //Debug.Log($"[圆棍颜色] 强制更新炮台 {ownerCannon.ObjectId} 的链接棍颜色为 {cannonColor}");
    }

    /// <summary>
    /// 设置局部位置偏移
    /// </summary>
    /// <param name="offset">偏移量</param>
    public void SetLocalPositionOffset(Vector3 offset)
    {
        localPositionOffset = offset;
        if (isInitialized)
        {
            UpdateStickTransform();
        }
    }

    /// <summary>
    /// 获取局部位置偏移
    /// </summary>
    /// <returns>当前偏移量</returns>
    public Vector3 GetLocalPositionOffset()
    {
        return localPositionOffset;
    }

    /// <summary>
    /// 设置长度缩放因子
    /// </summary>
    /// <param name="scaleFactor">缩放因子</param>
    public void SetLengthScaleFactor(float scaleFactor)
    {
        lengthScaleFactor = Mathf.Clamp(scaleFactor, 0.1f, 5.0f);
        if (isInitialized)
        {
            UpdateStickTransform();
        }
    }

    /// <summary>
    /// 获取长度缩放因子
    /// </summary>
    /// <returns>当前缩放因子</returns>
    public float GetLengthScaleFactor()
    {
        return lengthScaleFactor;
    }

    #endregion

    #region 调试

    /// <summary>
    /// 在Scene视图中绘制调试信息
    /// </summary>
    // private void OnDrawGizmosSelected()
    // {
    //     if (!IsLinkValid()) return;
    //
    //     // 绘制连接线
    //     Gizmos.color = currentColor;
    //     Gizmos.DrawLine(ownerCannon.transform.position, targetCannon.transform.position);
    //
    //     // 绘制中点
    //     Vector3 midPoint = (ownerCannon.transform.position + targetCannon.transform.position) * 0.5f;
    //     Gizmos.color = Color.yellow;
    //     Gizmos.DrawSphere(midPoint, 0.1f);
    //
    //     // 绘制拥有者和目标标识
    //     Gizmos.color = Color.green;
    //     Gizmos.DrawSphere(ownerCannon.transform.position, 0.05f);
    //
    //     Gizmos.color = Color.red;
    //     Gizmos.DrawSphere(targetCannon.transform.position, 0.05f);
    // }

    #endregion
}