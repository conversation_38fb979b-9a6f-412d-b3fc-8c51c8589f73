using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 射击格控制器，负责管理射击格子系统
/// 参考ParkingGrid_Controller架构，集成在GridConnon模块内
/// 使用数组方式管理射击格，无需动态创建
/// </summary>
public class GridConnon_ShotGrid_Controller : MonoBehaviour
{
    private static GridConnon_ShotGrid_Controller _instance;

    public static GridConnon_ShotGrid_Controller Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<GridConnon_ShotGrid_Controller>();
                if (_instance == null)
                {
                    GameObject go = new GameObject(typeof(GridConnon_ShotGrid_Controller).Name);
                    _instance = go.AddComponent<GridConnon_ShotGrid_Controller>();
                }
            }

            return _instance;
        }
    }

    [Header("射击格配置")]
    [Tooltip("直接射击格数组（无锁）")]
    public GridConnon_ShotGrid_Grid[] DirectShotGrids = new GridConnon_ShotGrid_Grid[6];

    [Tooltip("奖励解锁格数组（平时锁定，可解锁，使用一次就锁上）")]
    public GridConnon_ShotGrid_Grid[] RewardUnlockGrids = new GridConnon_ShotGrid_Grid[1];

    [Tooltip("复活解锁格数组（平时锁定，可解锁，使用一次就锁上）")]
    public GridConnon_ShotGrid_Grid[] ReviveUnlockGrids = new GridConnon_ShotGrid_Grid[2];

    [Header("射击格状态列表")]
    [Tooltip("所有射击格列表")]
    public List<GridConnon_ShotGrid_Grid> AllShotGrids = new List<GridConnon_ShotGrid_Grid>();

    [Tooltip("已锁定的格子列表")]
    public List<GridConnon_ShotGrid_Grid> LockedGrids = new List<GridConnon_ShotGrid_Grid>();

    [Tooltip("已解锁的格子列表")]
    public List<GridConnon_ShotGrid_Grid> UnlockedGrids = new List<GridConnon_ShotGrid_Grid>();

    // 事件定义

    public static Action<GridConnon_ShotGrid_Grid> Action_ShotGrid_Unlock;
    public static Action<Action> Action_RewardShotGrid_AskClick; // 新增事件

    [Header("测试配置")]
    [Tooltip("是否跳过AskForClick直接触发回调（测试用）")]
    public bool skipAskForClick = false;


    private void Awake()
    {
        // 确保单例实例正确设置
        if (_instance == null)
        {
            _instance = this;
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
            return;
        }

        InitShotGrids();
    }

    /// <summary>
    /// 初始化射击格系统
    /// </summary>
    public void InitShotGrids()
    {
        // 清空现有格子列表
        ClearAllGridLists();

        // 初始化直接射击格
        for (int i = 0; i < DirectShotGrids.Length; i++)
        {
            if (DirectShotGrids[i] != null)
            {
                DirectShotGrids[i].Init(GridConnon_ShotGrid_Type.DirectShot);
                DirectShotGrids[i].name = $"DirectShotGrid_{i}";
                AddGridToLists(DirectShotGrids[i]);
            }
        }


        // 初始化奖励解锁格
        for (int i = 0; i < RewardUnlockGrids.Length; i++)
        {
            if (RewardUnlockGrids[i] != null)
            {
                RewardUnlockGrids[i].Init(GridConnon_ShotGrid_Type.RewardUnlock);
                RewardUnlockGrids[i].name = $"RewardUnlockGrid_{i}";
                AddGridToLists(RewardUnlockGrids[i]);
            }
        }

        // 初始化复活解锁格
        for (int i = 0; i < ReviveUnlockGrids.Length; i++)
        {
            if (ReviveUnlockGrids[i] != null)
            {
                ReviveUnlockGrids[i].Init(GridConnon_ShotGrid_Type.ReviveUnlock);
                ReviveUnlockGrids[i].name = $"ReviveUnlockGrid_{i}";
                AddGridToLists(ReviveUnlockGrids[i]);
            }
        }



        // 注册到Common_ReserveGrid系统
        Common_ReserveGridManager.Instance.InitGrids(AllShotGrids.Cast<Common_ReserveGrid>().ToList(), "GridConnon_ShotGrid");

        //////Debug.Log($"射击格系统初始化完成 - 总数: {AllShotGrids.Count}, 直接射击: {DirectShotGrids.Length}, 奖励解锁: {RewardUnlockGrids.Length}, 复活解锁: {ReviveUnlockGrids.Length}");
    }

    /// <summary>
    /// 清空所有格子分类列表
    /// </summary>
    private void ClearAllGridLists()
    {
        AllShotGrids.Clear();
        LockedGrids.Clear();
        UnlockedGrids.Clear();
    }

    /// <summary>
    /// 将格子添加到对应列表
    /// </summary>
    /// <param name="grid">射击格</param>
    private void AddGridToLists(GridConnon_ShotGrid_Grid grid)
    {
        // 添加到总列表
        AllShotGrids.Add(grid);

        // 根据类型添加到状态列表
        switch (grid.gridType)
        {
            case GridConnon_ShotGrid_Type.DirectShot:
                UnlockedGrids.Add(grid);
                break;
            case GridConnon_ShotGrid_Type.RewardUnlock:
                // RewardUnlock类型始终解锁，添加到解锁列表
                UnlockedGrids.Add(grid);
                break;
            case GridConnon_ShotGrid_Type.ReviveUnlock:
                // ReviveUnlock类型始终解锁，添加到解锁列表
                UnlockedGrids.Add(grid);
                break;
        }
    }




    /// <summary>
    /// 处理射击格解锁
    /// </summary>
    /// <param name="grid">解锁的格子</param>
    public void OnGridUnlocked(GridConnon_ShotGrid_Grid grid)
    {
        if (grid == null) return;

        // 从锁定列表移除，添加到解锁列表
        if (LockedGrids.Contains(grid))
        {
            LockedGrids.Remove(grid);
        }

        if (!UnlockedGrids.Contains(grid))
        {
            UnlockedGrids.Add(grid);
        }

        // 触发全局解锁事件
        Action_ShotGrid_Unlock?.Invoke(grid);

        //////Debug.Log($"射击格控制器：格子 {grid.name} 已解锁");
    }



    /// <summary>
    /// 处理点击请求（新增方法）
    /// </summary>
    public void AskForClick(GridConnon_ShotGrid_Grid grid)
    {
        if (grid == null || grid.gridType != GridConnon_ShotGrid_Type.RewardUnlock) return;

        // 如果测试模式，直接触发回调
        if (skipAskForClick)
        {
            HandleRewardShotGridClick(grid);
            return;
        }

        // 正常流程，触发询问事件
        Action_RewardShotGrid_AskClick?.Invoke(() => HandleRewardShotGridClick(grid));
    }

    /// <summary>
    /// 为复活解锁格分配炮台
    /// 检索所有ReviveUnlock格子，从左到右查找符合条件的炮台（处于shooting状态且hitcount>0）
    /// 优先分配链接组，若数量不足则分配无链接的炮台
    /// 注意：链接组作为整体，选择其中一个炮台时，整个链接组都会占用格子
    /// </summary>
    public void AssignCannonsToReviveUnlockGrids()
    {
        // 获取所有未被占用的ReviveUnlock格子
        List<GridConnon_ShotGrid_Grid> availableReviveGrids = new List<GridConnon_ShotGrid_Grid>();
        foreach (var reviveGrid in ReviveUnlockGrids)
        {
            if (reviveGrid != null && !reviveGrid.IsOccOrRese)
            {
                availableReviveGrids.Add(reviveGrid);
            }
        }

        if (availableReviveGrids.Count == 0)
        {
            //Debug.Log("[ReviveUnlock] 没有可用的复活解锁格子");
            return;
        }

        // 收集所有符合条件的链接组和无链接炮台
        List<HashSet<GridConnon_Cannon>> availableLinkGroups = new List<HashSet<GridConnon_Cannon>>();  // 符合条件的链接组
        List<GridConnon_Cannon> unlinkedCannons = new List<GridConnon_Cannon>();  // 无链接的炮台
        HashSet<GridConnon_Cannon> processedCannons = new HashSet<GridConnon_Cannon>(); // 已处理的炮台，避免重复

        var linkSystem = GridConnon_LinkSystem.Instance;

        for (int i = 0; i < DirectShotGrids.Length; i++)
        {
            var directGrid = DirectShotGrids[i];
            if (directGrid == null || !directGrid.IsOccOrRese) continue;

            GameObject cannonObj = directGrid.GetItem();
            if (cannonObj == null) continue;

            GridConnon_Cannon cannon = cannonObj.GetComponent<GridConnon_Cannon>();
            if (cannon == null || processedCannons.Contains(cannon)) continue;

            // 检查条件：1处于shooting状态 2hitcount大于0
            if (cannon.StateManager.GetCurrentState() == GridConnon_Cannon_State.Shooting &&
                cannon.GetCurrentHitCount() > 0)
            {
                if (cannon.IsLinked() && linkSystem != null)
                {
                    // 获取整个链接组
                    var linkGroup = linkSystem.GetLinkGroup(cannon);
                    if (linkGroup != null)
                    {
                        // 检查链接组中所有炮台是否都符合条件
                        bool allValidInGroup = true;
                        foreach (var groupCannon in linkGroup)
                        {
                            if (groupCannon.StateManager.GetCurrentState() != GridConnon_Cannon_State.Shooting ||
                                groupCannon.GetCurrentHitCount() <= 0)
                            {
                                allValidInGroup = false;
                                break;
                            }
                        }

                        if (allValidInGroup)
                        {
                            availableLinkGroups.Add(linkGroup);
                            // 标记组内所有炮台为已处理
                            foreach (var groupCannon in linkGroup)
                            {
                                processedCannons.Add(groupCannon);
                            }
                        }
                    }
                }
                else
                {
                    // 无链接的炮台
                    unlinkedCannons.Add(cannon);
                    processedCannons.Add(cannon);
                }
            }
        }

        // 输出调试信息
        int totalLinkedCannons = availableLinkGroups.Sum(group => group.Count);
        //Debug.Log($"[ReviveUnlock] 找到符合条件的链接组: {availableLinkGroups.Count}个 (总炮台{totalLinkedCannons}个)");
        //Debug.Log($"[ReviveUnlock] 找到符合条件的无链接炮台: {unlinkedCannons.Count}个");
        //Debug.Log($"[ReviveUnlock] 可用复活格子数量: {availableReviveGrids.Count}个");

        // 分配策略：优先分配链接组，再分配无链接炮台
        List<GridConnon_Cannon> cannonsToAssign = new List<GridConnon_Cannon>();

        // 计算链接组占用的格子数
        int linkGroupGridsNeeded = totalLinkedCannons;

        // 输出分配策略分析
        DebugReviveUnlockLinkGroupStrategy(availableReviveGrids.Count, availableLinkGroups, unlinkedCannons.Count);

        // 优先选择链接组
        int remainingGrids = availableReviveGrids.Count;
        foreach (var linkGroup in availableLinkGroups)
        {
            if (remainingGrids >= linkGroup.Count)
            {
                // 从链接组中选择一个代表炮台（实际上整个组都会移动）
                cannonsToAssign.Add(linkGroup.First());
                remainingGrids -= linkGroup.Count;
            }
            else
            {
                break; // 格子不够，跳出
            }
        }

        // 如果还有剩余格子，分配无链接炮台
        for (int i = 0; i < unlinkedCannons.Count && remainingGrids > 0; i++)
        {
            cannonsToAssign.Add(unlinkedCannons[i]);
            remainingGrids--;
        }

        // 执行分配
        int gridIndex = 0;
        foreach (var cannon in cannonsToAssign)
        {
            if (gridIndex >= availableReviveGrids.Count) break;

            if (cannon.IsLinked() && linkSystem != null)
            {
                // 处理链接组：移动整个链接组的所有炮台
                var linkGroup = linkSystem.GetLinkGroup(cannon);
                if (linkGroup != null)
                {
                    // 按照炮台在DirectShotGrids中的索引顺序排序（从左到右）
                    var sortedLinkGroup = linkGroup.OrderBy(c => GetCannonDirectGridIndex(c)).ToList();

                    bool allMoveSuccess = true;
                    int movedCount = 0;

                    foreach (var linkedCannon in sortedLinkGroup)
                    {
                        if (gridIndex + movedCount >= availableReviveGrids.Count) break;

                        var targetGrid = availableReviveGrids[gridIndex + movedCount];
                        bool moveSuccess = linkedCannon.ShotGridToReviveShotGrid(targetGrid);

                        if (!moveSuccess)
                        {
                            allMoveSuccess = false;
                            //Debug.LogError($"[ReviveUnlock] 链接组中的炮台 {linkedCannon.name} 移动到 {targetGrid.name} 失败");
                        }
                        else
                        {
                            //Debug.Log($"[ReviveUnlock] 链接组炮台 {linkedCannon.name} (索引{GetCannonDirectGridIndex(linkedCannon)}) 移动到 {targetGrid.name} 成功");
                        }

                        movedCount++;
                    }

                    //Debug.Log($"[ReviveUnlock] 分配链接组(共{linkGroup.Count}个炮台): {(allMoveSuccess ? "全部成功" : "部分失败")}");
                    gridIndex += linkGroup.Count; // 链接组占用多个格子
                }
            }
            else
            {
                // 处理无链接炮台：只移动单个炮台
                var targetGrid = availableReviveGrids[gridIndex];
                bool moveSuccess = cannon.ShotGridToReviveShotGrid(targetGrid);

                //Debug.Log($"[ReviveUnlock] 分配无链接炮台 {cannon.name} 到 {targetGrid.name}: {(moveSuccess ? "成功" : "失败")}");
                gridIndex++; // 无链接炮台占用1个格子
            }
        }

        // 最终结果报告
        if (gridIndex < availableReviveGrids.Count)
        {
            //Debug.Log($"[ReviveUnlock] 警告：只使用了{gridIndex}个格子，但有{availableReviveGrids.Count}个复活格子可用");
        }
    }

    /// <summary>
    /// 获取炮台在DirectShotGrids中的索引位置
    /// </summary>
    /// <param name="cannon">要查找的炮台</param>
    /// <returns>索引位置，如果找不到返回-1</returns>
    private int GetCannonDirectGridIndex(GridConnon_Cannon cannon)
    {
        if (cannon == null) return -1;

        for (int i = 0; i < DirectShotGrids.Length; i++)
        {
            var grid = DirectShotGrids[i];
            if (grid != null && grid.IsOccOrRese)
            {
                var gridCannon = grid.GetItem()?.GetComponent<GridConnon_Cannon>();
                if (gridCannon == cannon)
                {
                    return i;
                }
            }
        }

        return -1; // 找不到时返回-1
    }

    /// <summary>
    /// 输出复活解锁链接组分配策略的调试信息
    /// </summary>
    private void DebugReviveUnlockLinkGroupStrategy(int gridCount, List<HashSet<GridConnon_Cannon>> linkGroups, int unlinkedCount)
    {
        //Debug.Log($"[ReviveUnlock策略分析]");
        //Debug.Log($"  可用格子: {gridCount}个");
        //Debug.Log($"  符合条件的链接组: {linkGroups.Count}个");

        int totalLinkedCannons = linkGroups.Sum(group => group.Count);
        //Debug.Log($"  链接组总炮台数: {totalLinkedCannons}个");

        // 详细列出每个链接组
        for (int i = 0; i < linkGroups.Count; i++)
        {
            var group = linkGroups[i];
            var groupIndices = new List<int>();

            // 找出组内炮台在DirectGrid中的索引
            foreach (var cannon in group)
            {
                for (int j = 0; j < DirectShotGrids.Length; j++)
                {
                    var grid = DirectShotGrids[j];
                    if (grid != null && grid.IsOccOrRese)
                    {
                        var gridCannon = grid.GetItem()?.GetComponent<GridConnon_Cannon>();
                        if (gridCannon == cannon)
                        {
                            groupIndices.Add(j);
                            break;
                        }
                    }
                }
            }

            //Debug.Log($"    链接组{i + 1}: {group.Count}个炮台 [索引: {string.Join(",", groupIndices.OrderBy(x => x))}]");
        }

        //Debug.Log($"  无链接炮台: {unlinkedCount}个");

        // 分析分配策略
        if (totalLinkedCannons <= gridCount)
        {
            //Debug.Log($"  策略：可以分配所有链接组({totalLinkedCannons}个格子)，剩余{gridCount - totalLinkedCannons}个格子给无链接炮台");
        }
        else
        {
            // 计算能分配多少个完整的链接组
            int assignableLinkGroups = 0;
            int usedGrids = 0;

            foreach (var group in linkGroups)
            {
                if (usedGrids + group.Count <= gridCount)
                {
                    assignableLinkGroups++;
                    usedGrids += group.Count;
                }
                else
                {
                    break;
                }
            }

            //Debug.Log($"  策略：只能分配前{assignableLinkGroups}个链接组({usedGrids}个格子)，剩余{gridCount - usedGrids}个格子给无链接炮台");
        }
    }

    /// <summary>
    /// 处理奖励射击格点击回调
    /// </summary>
    private void HandleRewardShotGridClick(GridConnon_ShotGrid_Grid targetGrid)
    {
        // 找到所有无链接炮台中最左边的炮台
        GridConnon_Cannon leftmostCannon = GetLeftmostUnlinkedCannon();
        if (leftmostCannon != null && targetGrid != null)
        {
            // 炮台已经在GetLeftmostUnlinkedCannon中验证过条件，直接执行移动
            leftmostCannon.ShotGridToRewardShotGrid(targetGrid);

            // 移动开始后立即隐藏可点击状态
            HideRewardUnlockClickable(targetGrid);
            ////Debug.Log($"[奖励射击格] 最左边无链接炮台开始移动到: {targetGrid.name}");
        }
        else
        {
            // 没有找到符合条件的炮台，重置RewardUnlock格子状态
            if (targetGrid != null)
            {
                ResetRewardUnlockGrid(targetGrid);
                ////Debug.Log($"[奖励射击格] 未找到符合条件的无链接炮台，重置格子: {targetGrid.name}");
            }
        }
    }

    /// <summary>
    /// 重置RewardUnlock格子状态
    /// </summary>
    /// <param name="rewardGrid">要重置的RewardUnlock格子</param>
    private void ResetRewardUnlockGrid(GridConnon_ShotGrid_Grid rewardGrid)
    {
        if (rewardGrid == null || rewardGrid.gridType != GridConnon_ShotGrid_Type.RewardUnlock) return;

        // 使用格子自己的缩放动画设置为不可点击状态，显示金币文本
        rewardGrid.SetToUnClickableState_WithAnimation();
    }

    /// <summary>
    /// 隐藏RewardUnlock格子的可点击状态（移动开始时调用）
    /// </summary>
    /// <param name="rewardGrid">要隐藏的RewardUnlock格子</param>
    private void HideRewardUnlockClickable(GridConnon_ShotGrid_Grid rewardGrid)
    {
        if (rewardGrid == null || rewardGrid.gridType != GridConnon_ShotGrid_Type.RewardUnlock) return;

        // 使用格子自己的缩放动画隐藏所有UI元素（因为即将被占用）
        rewardGrid.HideAllRewardUnlockUI_WithAnimation();
    }

    /// <summary>
    /// 获取所有无链接炮台中最右边的炮台（索引最大）
    /// </summary>
    private GridConnon_Cannon GetLeftmostUnlinkedCannon()
    {
        GridConnon_Cannon targetCannon = null;
        int targetIndex = -1;

        // 遍历所有DirectGrid，找到所有符合条件的无链接炮台中索引最大的，且实际到达
        for (int i = 0; i < DirectShotGrids.Length; i++)
        {
            var directGrid = DirectShotGrids[i];
            if (directGrid != null && directGrid.IsOccupied)
            {
                GameObject cannonObj = directGrid.GetItem();
                if (cannonObj != null)
                {
                    GridConnon_Cannon cannon = cannonObj.GetComponent<GridConnon_Cannon>();
                    if (cannon != null)
                    {
                        // 检查炮台是否符合条件：无链接 + Shooting状态 + HitCount>0
                        if (!cannon.IsLinked() &&
                            cannon.StateManager.GetCurrentState() == GridConnon_Cannon_State.Shooting &&
                            cannon.GetCurrentHitCount() > 0)
                        {
                            // 如果是第一个找到的，或者索引更大，则更新目标
                            if (targetCannon == null || i > targetIndex)
                            {
                                targetCannon = cannon;
                                targetIndex = i;
                                ////Debug.Log($"[RewardUnlock] 更新目标炮台: DirectGrid[{i}]");
                            }
                        }
                    }
                }
            }
        }



        return targetCannon;
    }

    /// <summary>
    /// 设置奖励解锁格的金币显示文本
    /// </summary>
    /// <param name="coinAmount">金币数量</param>
    public void SetRewardUnlockCoinText(int coinAmount)
    {
        // 找到下标0的RewardUnlock grid
        if (RewardUnlockGrids != null && RewardUnlockGrids.Length > 0)
        {
            var firstRewardGrid = RewardUnlockGrids[0];
            if (firstRewardGrid != null && firstRewardGrid.RewardUnlock_txtCoin != null)
            {
                firstRewardGrid.RewardUnlock_txtCoin.text = coinAmount.ToString();
            }
        }
    }

    /// <summary>
    /// 解锁指定类型的格子
    /// </summary>
    /// <param name="gridType">格子类型</param>
    /// <param name="count">解锁数量（-1表示全部）</param>
    public void UnlockGridsByType(GridConnon_ShotGrid_Type gridType, int count = -1)
    {
        GridConnon_ShotGrid_Grid[] targetGrids = null;

        switch (gridType)
        {
            case GridConnon_ShotGrid_Type.RewardUnlock:
                targetGrids = RewardUnlockGrids;
                break;
            case GridConnon_ShotGrid_Type.ReviveUnlock:
                targetGrids = ReviveUnlockGrids;
                break;
            default:
                //////Debug.LogWarning($"无法解锁类型为 {gridType} 的格子");
                return;
        }

        if (targetGrids == null) return;

        int unlockCount = 0;
        int maxUnlock = (count == -1) ? targetGrids.Length : Mathf.Min(count, targetGrids.Length);

        foreach (var grid in targetGrids)
        {
            if (unlockCount >= maxUnlock) break;
            if (grid != null && grid.IsLocked && !grid.IsUsed)
            {
                grid.Unlock();
                unlockCount++;
            }
        }

        //////Debug.Log($"解锁了 {unlockCount} 个 {gridType} 类型的格子");
    }

    /// <summary>
    /// 重置所有格子状态
    /// </summary>
    public void ResetAllGrids()
    {
        foreach (var grid in AllShotGrids)
        {
            if (grid != null)
            {
                grid.ResetGrid();
            }
        }

        // 重新整理列表
        ClearAllGridLists();
        foreach (var grid in AllShotGrids)
        {
            if (grid != null)
            {
                AddGridToLists(grid);
            }
        }

        //////Debug.Log("所有射击格状态已重置");
    }

    /// <summary>
    /// 获取第一个可用的指定类型格子
    /// </summary>
    /// <param name="gridType">格子类型</param>
    /// <returns>可用格子</returns>
    public GridConnon_ShotGrid_Grid GetFirstAvailableGrid(GridConnon_ShotGrid_Type gridType)
    {
        GridConnon_ShotGrid_Grid[] targetGrids = null;

        switch (gridType)
        {
            case GridConnon_ShotGrid_Type.DirectShot:
                targetGrids = DirectShotGrids;
                break;
            case GridConnon_ShotGrid_Type.RewardUnlock:
                targetGrids = RewardUnlockGrids;
                break;
            case GridConnon_ShotGrid_Type.ReviveUnlock:
                targetGrids = ReviveUnlockGrids;
                break;
        }

        if (targetGrids != null)
        {
            foreach (var grid in targetGrids)
            {
                if (grid != null)
                {
                    return grid;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// 清理射击格系统
    /// </summary>
    public void ClearShotGridSystem()
    {
        // 清空网格管理器中的数据
        if (Common_ReserveGridManager.Instance != null)
        {
            Common_ReserveGridManager.Instance.RemoveGroup("GridConnon_ShotGrid");
        }

        // 清空列表
        ClearAllGridLists();

        //////Debug.Log("射击格系统已清理");
    }

    /// <summary>
    /// 获取总格子数量
    /// </summary>
    public int GetTotalGridCount()
    {
        return DirectShotGrids.Length + RewardUnlockGrids.Length + ReviveUnlockGrids.Length;
    }

    /// <summary>
    /// 获取锁定格子数量
    /// </summary>
    public int GetLockedGridCount()
    {
        return RewardUnlockGrids.Length + ReviveUnlockGrids.Length;
    }

    /// <summary>
    /// 获取指定类型的可用格子数量
    /// </summary>
    public int GetAvailableGridCount(GridConnon_ShotGrid_Type gridType)
    {
        switch (gridType)
        {
            case GridConnon_ShotGrid_Type.DirectShot:
                return DirectShotGrids.Length;
            case GridConnon_ShotGrid_Type.RewardUnlock:
                return RewardUnlockGrids.Length;
            case GridConnon_ShotGrid_Type.ReviveUnlock:
                return ReviveUnlockGrids.Length;
            default:
                return 0;
        }
    }


    public bool AreAllShotGridsOccupied(GridConnon_ShotGrid_Type t)
    {
        switch (t)
        {
            case GridConnon_ShotGrid_Type.DirectShot:
                foreach (var grid in DirectShotGrids)
                {
                    if (grid == null) continue; // 跳过空格子
                    if (!grid.IsOccupied) return false;
                }
                break;
            case GridConnon_ShotGrid_Type.ReviveUnlock:
                foreach (var grid in ReviveUnlockGrids)
                {
                    if (grid == null) continue; // 跳过空格子
                    if (!grid.IsOccupied) return false;
                }
                break;
            case GridConnon_ShotGrid_Type.RewardUnlock:
                foreach (var grid in RewardUnlockGrids)
                {
                    if (grid == null) continue; // 跳过空格子
                    if (!grid.IsOccupied) return false;
                }
                break;
        }

        return true; // 所有格子都被占用
    }


    public bool AreAllShotGridsReservedOrOcc(GridConnon_ShotGrid_Type t)
    {
        switch (t)
        {
            case GridConnon_ShotGrid_Type.DirectShot:
                foreach (var grid in DirectShotGrids)
                {
                    if (grid == null) continue; // 跳过空格子
                    if (!grid.IsReserved && !grid.IsOccupied) return false;
                }
                break;
            case GridConnon_ShotGrid_Type.ReviveUnlock:
                foreach (var grid in ReviveUnlockGrids)
                {
                    if (grid == null) continue; // 跳过空格子
                    if (!grid.IsReserved && !grid.IsOccupied) return false;
                }
                break;
            case GridConnon_ShotGrid_Type.RewardUnlock:
                foreach (var grid in RewardUnlockGrids)
                {
                    if (grid == null) continue; // 跳过空格子
                    if (!grid.IsReserved && !grid.IsOccupied) return false;
                }
                break;
        }

        return true; // 所有格子都被占用
    }

    public bool AreAllShotGridsReserved(GridConnon_ShotGrid_Type t)
    {
        switch (t)
        {
            case GridConnon_ShotGrid_Type.DirectShot:
                foreach (var grid in DirectShotGrids)
                {
                    if (grid == null) continue; // 跳过空格子
                    if (!grid.IsReserved) return false;
                }
                break;
            case GridConnon_ShotGrid_Type.ReviveUnlock:
                foreach (var grid in ReviveUnlockGrids)
                {
                    if (grid == null) continue; // 跳过空格子
                    if (!grid.IsReserved) return false;
                }
                break;
            case GridConnon_ShotGrid_Type.RewardUnlock:
                foreach (var grid in RewardUnlockGrids)
                {
                    if (grid == null) continue; // 跳过空格子
                    if (!grid.IsReserved) return false;
                }
                break;
        }

        return true; // 所有格子都被占用
    }


    private void OnDestroy()
    {
        if (_instance == this)
        {
            _instance = null;
        }
    }
}