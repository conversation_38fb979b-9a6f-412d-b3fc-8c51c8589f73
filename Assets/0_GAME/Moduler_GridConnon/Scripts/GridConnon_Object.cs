using UnityEngine;

/// <summary>
/// GridConnon 物体基类 - 所有炮类型的基类
/// 提供基础的网格信息管理和通用功能
/// 子类可根据需要自行实现开火、特效、颜色等功能
/// </summary>
public abstract class GridConnon_Object : MonoBehaviour
{
    [Header("基础信息")]
    [Tooltip("物体唯一ID")]
    public int ObjectId;

    [Tooltip("物体类型")]
    public GridConnon_ObjectType ObjectType;

    [Tooltip("颜色ID（1开始，-1表示无颜色）")]
    public int ColorId = 1;

    [Header("网格信息")]
    [Tooltip("关联的网格格子")]
    public Common_ReserveGrid reserveGrid;

    [Tooltip("网格X坐标")]
    public int GridX;

    [Tooltip("网格Z坐标")]
    public int GridZ;





    /// <summary>
    /// 初始化炮数据
    /// </summary>
    /// <param name="data">炮数据</param>
    /// <param name="grid">关联的网格格子</param>
    public virtual void InitializeCannon(GridConnon_Data_Cannon data, Common_ReserveGrid grid)
    {
        if (data == null)
        {
            //Debug.LogError("炮数据不能为null");
            return;
        }

        if (grid == null)
        {
            //Debug.LogError("网格格子不能为null");
            return;
        }

        // 设置基础数据
        ObjectId = data.cannonId;
        ObjectType = data.objectType;
        ColorId = data.colorId;
        GridX = data.gridX;
        GridZ = data.gridZ;


        // 关联网格格子
        reserveGrid = grid;

        // 将炮放置到格子中
        if (!reserveGrid.TryPlaceItem(gameObject))
        {
            //Debug.LogError($"无法将炮放置到网格位置 ({GridX}, {GridZ})");
        }

        OnInitialize(data);

    }
    protected virtual void OnInitialize(GridConnon_Data_Cannon data)
    { }

    /// <summary>
    /// 获取网格位置
    /// </summary>
    /// <returns>网格位置</returns>
    public virtual Vector2Int GetGridPosition()
    {
        return new Vector2Int(GridX, GridZ);
    }

    /// <summary>
    /// 检查是否在指定位置
    /// </summary>
    /// <param name="gridX">网格X坐标</param>
    /// <param name="gridZ">网格Z坐标</param>
    /// <returns>是否在指定位置</returns>
    public virtual bool IsAtPosition(int gridX, int gridZ)
    {
        return GridX == gridX && GridZ == gridZ;
    }

    /// <summary>
    /// 是否有颜色
    /// </summary>
    /// <returns>是否有颜色</returns>
    public virtual bool HasColor()
    {
        return ColorId > 0;
    }





    /// <summary>
    /// 设置颜色
    /// </summary>
    /// <param name="colorId">颜色ID</param>
    public virtual void SetColor(int colorId)
    {
        ColorId = colorId;
        OnColorChanged(colorId);
    }

    /// <summary>
    /// 子类重写此方法实现颜色变化逻辑
    /// </summary>
    /// <param name="colorId">新的颜色ID</param>
    protected virtual void OnColorChanged(int colorId)
    {
        // 子类实现颜色变化逻辑
    }






    /// <summary>
    /// 获取世界坐标
    /// </summary>
    /// <returns>世界坐标</returns>
    public virtual Vector3 GetWorldPosition()
    {
        return transform.position;
    }

    /// <summary>
    /// 设置世界坐标
    /// </summary>
    /// <param name="worldPosition">世界坐标</param>
    public virtual void SetWorldPosition(Vector3 worldPosition)
    {
        transform.position = worldPosition;
    }

    /// <summary>
    /// 当物体被销毁时清理资源
    /// </summary>
    protected virtual void OnDestroy()
    {
        // 清理网格引用
        reserveGrid = null;
    }

    protected virtual void Awake()
    {

    }

    protected virtual void Start()
    {

    }

}