using System;
using System.Collections;
using UnityEngine;
using TMPro;

/// <summary>
/// 射击格类型枚举
/// </summary>
public enum GridConnon_ShotGrid_Type
{
    DirectShot = 0,      // 直接射击格（无锁）
    RewardUnlock = 1,    // 奖励解锁格（平时锁定，可解锁，使用一次就锁上）
    ReviveUnlock = 2     // 复活解锁格（平时锁定，可解锁，使用一次就锁上）
}

/// <summary>
/// 射击格格子，继承自Common_ReserveGrid，添加射击和解锁功能
/// 参考ParkingGrid_Grid架构，集成在GridConnon模块内
/// </summary>
public class GridConnon_ShotGrid_Grid : Common_ReserveGrid
{
    [Header("射击格配置")]
    [Tooltip("射击格类型")]
    public GridConnon_ShotGrid_Type gridType = GridConnon_ShotGrid_Type.DirectShot;

    [Header("状态信息")]
    [Tooltip("是否已被使用（对于解锁格子）")]
    public bool IsUsed = false;

    [Tooltip("使用次数计数")]
    public int UseCount = 0;

    [Header("奖励解锁格")]
    public GameObject RewardUnlock_CanClickObj;
    public GameObject RewardUnlock_UnClickObj;
    public float RewardUnlock_CheckTime;
    public TextMeshProUGUI RewardUnlock_txtCoin;

    [Tooltip("格子从占用变为空时的延迟激活时间（秒）")]
    public float RewardUnlock_DelayActivateTime = 1.0f;

    [Header("缩放动画配置")]
    [Tooltip("UI切换时的缩放动画速度")]
    public float ScaleAnimationSpeed = 5.0f;

    // 新增字段 - 用于RewardUnlock检测
    private float lastCheckTime = 0f;
    private bool canClick = false;
    private float activationAllowedTime = 0f; // 允许激活的时间点（0表示可以立即激活）

    // 缩放动画相关字段
    private Vector3 canClickOriginalScale = Vector3.one;    // CanClickObj的原始大小
    private Vector3 unClickOriginalScale = Vector3.one;    // UnClickObj的原始大小
    private Vector3 coinOriginalScale = Vector3.one;       // 金币文本的原始大小
    private bool isScalingCanClick = false;                // CanClickObj是否正在缩放
    private bool isScalingUnClick = false;                 // UnClickObj是否正在缩放
    private bool isScalingCoin = false;                    // 金币文本是否正在缩放
    private bool targetCanClickActive = false;             // CanClickObj的目标激活状态
    private bool targetUnClickActive = false;              // UnClickObj的目标激活状态
    private bool targetCoinActive = false;                 // 金币文本的目标激活状态

    /// <summary>
    /// Unity Update - 处理RewardUnlock检测
    /// </summary>
    private void Update()
    {
        // 只在鼠标左键按下的那一帧处理
        if (Input.GetMouseButtonDown(0))
        {
            // 检查是否点击在UI元素上，如果是则不处理点击事件
            if (Tool_IsPointerOverUI.Check())
                return;

            // 发射射线检测是否点击到了当前物体
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            RaycastHit hit;

            if (Physics.Raycast(ray, out hit))
            {
                // 检查是否点击到了当前物体
                if (hit.collider.gameObject == gameObject)
                {
                    // 调用点击响应方法
                    OnClick();
                }
            }
        }

        // 只有RewardUnlock类型才需要检测
        if (gridType == GridConnon_ShotGrid_Type.RewardUnlock)
        {
            CheckRewardUnlockConditions();
            // 处理缩放动画
            HandleScaleAnimations();
        }
    }

    /// <summary>
    /// 检测奖励解锁格条件（在Update中调用）
    /// </summary>
    private void CheckRewardUnlockConditions()
    {


        // 如果格子被占用，隐藏所有UI并设置延迟激活时间
        if (IsOccOrRese)
        {
            HideAllRewardUnlockUI();
            // 重置点击状态
            canClick = false;
            // 设置格子变为空时的延迟激活时间
            activationAllowedTime = Time.time + RewardUnlock_DelayActivateTime;
            //Debug.Log($"[RewardUnlock] 格子 {name} 被占用，设置延迟激活时间到: {activationAllowedTime}");
            return;
        }

        // 格子为空，检查是否在延迟期内
        if (Time.time < activationAllowedTime)
        {
            // 还在延迟期内，隐藏所有UI
            HideAllRewardUnlockUI();
            // 确保点击状态为false
            canClick = false;
            float remainingTime = activationAllowedTime - Time.time;
            //Debug.Log($"[RewardUnlock] 格子 {name} 在延迟期内，剩余时间: {remainingTime:F2}秒");
            return;
        }

        // 延迟期已过，可以正常检测和激活
        // 按照设定的时间间隔检测
        if (Time.time - lastCheckTime >= RewardUnlock_CheckTime)
        {
            lastCheckTime = Time.time;

            // 格子为空且延迟期已过，检测是否有符合条件的炮台
            bool hasValidCannon = HasValidCannonInDirectGrids();
            //            Debug.Log($"[RewardUnlock] 格子 {name} 检测炮台条件，结果: {hasValidCannon}");
            UpdateRewardUnlockClickState(hasValidCannon);
        }
    }

    /// <summary>
    /// 处理缩放动画
    /// </summary>
    private void HandleScaleAnimations()
    {
        // 处理CanClickObj的缩放动画
        if (isScalingCanClick && RewardUnlock_CanClickObj != null)
        {
            Vector3 targetScale = targetCanClickActive ? canClickOriginalScale : Vector3.zero;
            RewardUnlock_CanClickObj.transform.localScale = Vector3.Lerp(
                RewardUnlock_CanClickObj.transform.localScale,
                targetScale,
                ScaleAnimationSpeed * Time.deltaTime
            );

            // 检查是否接近目标大小
            if (Vector3.Distance(RewardUnlock_CanClickObj.transform.localScale, targetScale) < 0.01f)
            {
                RewardUnlock_CanClickObj.transform.localScale = targetScale;
                isScalingCanClick = false;

                // 如果缩放到0，则隐藏对象
                if (!targetCanClickActive)
                {
                    RewardUnlock_CanClickObj.SetActive(false);
                }
            }
        }

        // 处理UnClickObj的缩放动画
        if (isScalingUnClick && RewardUnlock_UnClickObj != null)
        {
            Vector3 targetScale = targetUnClickActive ? unClickOriginalScale : Vector3.zero;
            RewardUnlock_UnClickObj.transform.localScale = Vector3.Lerp(
                RewardUnlock_UnClickObj.transform.localScale,
                targetScale,
                ScaleAnimationSpeed * Time.deltaTime
            );

            // 检查是否接近目标大小
            if (Vector3.Distance(RewardUnlock_UnClickObj.transform.localScale, targetScale) < 0.01f)
            {
                RewardUnlock_UnClickObj.transform.localScale = targetScale;
                isScalingUnClick = false;

                // 如果缩放到0，则隐藏对象
                if (!targetUnClickActive)
                {
                    RewardUnlock_UnClickObj.SetActive(false);
                }
            }
        }

        // 处理金币文本的缩放动画
        if (isScalingCoin && RewardUnlock_txtCoin != null)
        {
            Vector3 targetScale = targetCoinActive ? coinOriginalScale : Vector3.zero;
            RewardUnlock_txtCoin.transform.localScale = Vector3.Lerp(
                RewardUnlock_txtCoin.transform.localScale,
                targetScale,
                ScaleAnimationSpeed * Time.deltaTime
            );

            // 检查是否接近目标大小
            if (Vector3.Distance(RewardUnlock_txtCoin.transform.localScale, targetScale) < 0.01f)
            {
                RewardUnlock_txtCoin.transform.localScale = targetScale;
                isScalingCoin = false;

                // 如果缩放到0，则隐藏对象
                if (!targetCoinActive)
                {
                    RewardUnlock_txtCoin.gameObject.SetActive(false);
                }
            }
        }
    }

    /// <summary>
    /// 开始缩放动画
    /// </summary>
    /// <param name="obj">要缩放的对象</param>
    /// <param name="targetActive">目标激活状态</param>
    /// <param name="objType">对象类型（0=CanClick, 1=UnClick, 2=Coin）</param>
    private void StartScaleAnimation(GameObject obj, bool targetActive, int objType)
    {
        if (obj == null) return;

        // 如果要显示对象，先激活它并设置为零大小
        if (targetActive && !obj.activeInHierarchy)
        {
            obj.SetActive(true);
            obj.transform.localScale = Vector3.zero;
        }

        // 设置缩放状态
        switch (objType)
        {
            case 0: // CanClick
                targetCanClickActive = targetActive;
                isScalingCanClick = true;
                break;
            case 1: // UnClick
                targetUnClickActive = targetActive;
                isScalingUnClick = true;
                break;
            case 2: // Coin
                targetCoinActive = targetActive;
                isScalingCoin = true;
                break;
        }
    }

    /// <summary>
    /// 记录UI对象的原始大小
    /// </summary>
    private void RecordOriginalScales()
    {
        if (RewardUnlock_CanClickObj != null)
        {
            canClickOriginalScale = RewardUnlock_CanClickObj.transform.localScale;
        }

        if (RewardUnlock_UnClickObj != null)
        {
            unClickOriginalScale = RewardUnlock_UnClickObj.transform.localScale;
        }

        if (RewardUnlock_txtCoin != null)
        {
            coinOriginalScale = RewardUnlock_txtCoin.transform.localScale;
        }
    }

    /// <summary>
    /// 检测DirectGrids中是否有符合条件的炮台，实际到达
    /// </summary>
    private bool HasValidCannonInDirectGrids()
    {
        var shotGridController = GridConnon_ShotGrid_Controller.Instance;
        if (shotGridController == null) return false;

        foreach (var directGrid in shotGridController.DirectShotGrids)
        {
            if (directGrid == null || !directGrid.IsOccupied) continue;

            GameObject cannonObj = directGrid.GetItem();
            if (cannonObj == null) continue;

            GridConnon_Cannon cannon = cannonObj.GetComponent<GridConnon_Cannon>();
            if (cannon == null) continue;

            // 检查条件：1无链接 2处于shooting状态 3hitcount大于0
            if (!cannon.IsLinked() &&
                cannon.StateManager.GetCurrentState() == GridConnon_Cannon_State.Shooting &&
                cannon.GetCurrentHitCount() > 0)
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 更新奖励解锁格点击状态
    /// </summary>
    private void UpdateRewardUnlockClickState(bool newCanClick)
    {
        // 强制更新UI状态（即使canClick值相同，也要确保UI正确显示）
        canClick = newCanClick;

        if (canClick)
        {
            // 可点击状态 - 使用缩放动画显示可点击UI和金币文本
            StartScaleAnimation(RewardUnlock_CanClickObj, true, 0);  // 显示CanClick
            StartScaleAnimation(RewardUnlock_UnClickObj, false, 1);  // 隐藏UnClick
            if (RewardUnlock_txtCoin != null)
                StartScaleAnimation(RewardUnlock_txtCoin.gameObject, true, 2);  // 显示金币文本
            //  Debug.Log($"[RewardUnlock] 格子 {name} 设置为可点击状态");
        }
        else
        {
            // 不可点击状态 - 使用缩放动画显示不可点击UI和金币文本
            StartScaleAnimation(RewardUnlock_CanClickObj, false, 0);  // 隐藏CanClick
            StartScaleAnimation(RewardUnlock_UnClickObj, true, 1);   // 显示UnClick
            if (RewardUnlock_txtCoin != null)
                StartScaleAnimation(RewardUnlock_txtCoin.gameObject, true, 2);  // 显示金币文本
            // Debug.Log($"[RewardUnlock] 格子 {name} 设置为不可点击状态");
        }
    }

    /// <summary>
    /// 隐藏所有RewardUnlock UI元素
    /// </summary>
    private void HideAllRewardUnlockUI()
    {
        // 使用缩放动画隐藏所有UI元素
        StartScaleAnimation(RewardUnlock_CanClickObj, false, 0);
        StartScaleAnimation(RewardUnlock_UnClickObj, false, 1);
        if (RewardUnlock_txtCoin != null)
            StartScaleAnimation(RewardUnlock_txtCoin.gameObject, false, 2);
    }

    /// <summary>
    /// 初始化射击格
    /// </summary>
    /// <param name="type">射击格类型</param>
    public void Init(GridConnon_ShotGrid_Type type)
    {
        gridType = type;

        // RewardUnlock和ReviveUnlock类型始终保持解锁状态
        bool isLocked = (type != GridConnon_ShotGrid_Type.DirectShot &&
                         type != GridConnon_ShotGrid_Type.RewardUnlock &&
                         type != GridConnon_ShotGrid_Type.ReviveUnlock);

        IsLocked = isLocked;
        IsUsed = false;
        UseCount = 0;

        // 设置组ID
        GroupId = "GridConnon_ShotGrid";

        // 初始化RewardUnlock的点击状态
        if (type == GridConnon_ShotGrid_Type.RewardUnlock)
        {
            // 记录UI对象的原始大小
            RecordOriginalScales();

            canClick = false;
            lastCheckTime = 0f;
            activationAllowedTime = 0f; // 初始可以立即激活

            // 重置缩放状态
            isScalingCanClick = false;
            isScalingUnClick = false;
            isScalingCoin = false;

            // 初始设置为不可点击状态（使用缩放动画）
            UpdateRewardUnlockClickState(false);
        }

        // 设置物体显示状态
        UpdateVisualState();
    }

    /// <summary>
    /// 更新视觉状态
    /// </summary>
    private void UpdateVisualState()
    {

    }

    /// <summary>
    /// 解锁格子
    /// </summary>
    public void Unlock()
    {
        if (!IsLocked || gridType == GridConnon_ShotGrid_Type.DirectShot) return;

        // 更改锁定状态
        IsLocked = false;
        IsUsed = false; // 重置使用状态

        // 通知控制器处理解锁
        GridConnon_ShotGrid_Controller.Instance?.OnGridUnlocked(this);

        //Debug.Log($"射击格 {name} 已解锁，类型: {gridType}");
    }



    /// <summary>
    /// 请求点击（用于奖励解锁格）
    /// </summary>
    public void RewardUnlock_AskForClick()
    {
        if (gridType != GridConnon_ShotGrid_Type.RewardUnlock) return;
        if (!canClick) return; // 只有可点击状态才能触发

        // 通知控制器处理点击请求
        GridConnon_ShotGrid_Controller.Instance?.AskForClick(this);
    }

    /// <summary>
    /// 公共方法：使用缩放动画隐藏所有RewardUnlock UI（供外部调用）
    /// </summary>
    public void HideAllRewardUnlockUI_WithAnimation()
    {
        if (gridType != GridConnon_ShotGrid_Type.RewardUnlock) return;
        HideAllRewardUnlockUI();
    }

    /// <summary>
    /// 公共方法：使用缩放动画设置为不可点击状态（供外部调用）
    /// </summary>
    public void SetToUnClickableState_WithAnimation()
    {
        if (gridType != GridConnon_ShotGrid_Type.RewardUnlock) return;
        UpdateRewardUnlockClickState(false);
    }

    /// <summary>
    /// 点击格子时的响应
    /// </summary>
    public void OnClick()
    {

        if (!GridConnon_InputManager.Can_UnlockGrid_Click())
            return;

        if (gridType == GridConnon_ShotGrid_Type.RewardUnlock)
            RewardUnlock_AskForClick();
    }


    /// <summary>
    /// 重新锁定格子（用于解锁格子使用后）
    /// </summary>
    public void LockAgain()
    {
        if (gridType == GridConnon_ShotGrid_Type.DirectShot) return;

        IsLocked = true;
        IsUsed = true;
        UpdateVisualState();

        //Debug.Log($"射击格 {name} 重新锁定，类型: {gridType}");
    }

    /// <summary>
    /// 重置格子状态
    /// </summary>
    public void ResetGrid()
    {
        IsUsed = false;
        UseCount = 0;

        // 根据类型重置锁定状态
        IsLocked = (gridType != GridConnon_ShotGrid_Type.DirectShot &&
                   gridType != GridConnon_ShotGrid_Type.RewardUnlock &&
                   gridType != GridConnon_ShotGrid_Type.ReviveUnlock);

        UpdateVisualState();

        //Debug.Log($"射击格 {name} 状态已重置，类型: {gridType}");
    }
}