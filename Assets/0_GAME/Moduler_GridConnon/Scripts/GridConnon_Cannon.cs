using JRJelly;
using TMPro;
using UnityEngine;
using System.Collections;
using System.Collections.Generic; // Added for Dictionary


public class GridConnon_Cannon : GridConnon_Object
{
    [Header("状态显示")]
    [Tooltip("当前炮台状态（只读显示）")]
    [SerializeField] private GridConnon_Cannon_State currentStateDisplay = GridConnon_Cannon_State.BackRow;




    [HideInInspector]
    public SkinnedMeshRenderer footRenderer;
    [HideInInspector]
    public GameObject foot_MeshObject;
    [Header("腿部属性")]
    public Material footMaterial;

    [Header("宝箱炮专用配置 - TreasureCannon")]
    [Tooltip("宝箱炮腿部颜色")]
    public Color TreasureCannon_FootColor = Color.gray;

    [Tooltip("宝箱炮炮身颜色")]
    public Color TreasureCannon_CannonColor = Color.yellow;


    [HideInInspector]
    public MeshRenderer cannonRenderer;
    [Header("普通炮属性")]
    [Tooltip("炮身材质")]
    public Material cannonMaterial;




    // 私有变量
    private MaterialPropertyBlock propertyBlock;
    private MaterialPropertyBlock footPropertyBlock;

    [HideInInspector]
    public GridConnon_Navigation navigationComponent;
    [HideInInspector]
    public Tool_JRJELLY_Simple tool_JRJELLY_Simple;

    [HideInInspector]
    public GameObject connon_MeshObject;
    [HideInInspector]
    public TextMeshProUGUI txt_Count;

    [Header("状态系统配置")]
    [Header("BackRow状态配置")]
    [Tooltip("膜层颜色淡化/加深比例（1为不变，<1淡化，>1加深）")]
    [Range(0f, 2f)]
    public float backRow_overlayFadePercent = 0.5f;

    [Tooltip("膜层透明度")]
    [Range(0f, 1f)]
    public float backRow_overlayAlpha = 0.8f;

    [Tooltip("数字透明度")]
    [Range(0f, 1f)]
    public float backRow_textAlpha = 0.3f;

    [Tooltip("描边宽度")]
    [Range(0f, 0.3f)]
    public float backRow_outlineWidth = 0.002f;

    [Tooltip("描边颜色淡化/加深比例（1为不变，<1淡化，>1加深）")]
    [Range(0f, 2f)]
    public float backRow_outlineFadePercent = 0.8f;

    [Header("FirstRow状态配置")]
    [Tooltip("描边宽度")]
    [Range(0f, 0.3f)]
    public float firstRow_outlineWidth = 0.003f;

    [Tooltip("描边颜色")]
    public Color firstRow_outlineColor = Color.white;

    [Header("Shooting状态配置")]
    [Tooltip("描边宽度")]
    [Range(0f, 0.3f)]
    public float shooting_outlineWidth = 0.001f;

    [Tooltip("描边颜色淡化/加深比例（1为不变，<1淡化，>1加深）")]
    [Range(0f, 2f)]
    public float shooting_outlineFadePercent = 0.5f;

    [Header("旋转动画配置")]
    [Tooltip("旋转动画持续时间")]
    public float rotationAnimDuration = 0.5f;

    [Tooltip("旋转角度")]
    public float rotationAngle = 30f;

    [Header("压缩动画配置")]
    [Tooltip("压缩动画持续时间")]
    public float compressionAnimDuration = 0.6f;

    [Tooltip("X轴缩放值")]
    public float compressionXScale = 1.2f;

    [Tooltip("Y轴缩放值")]
    public float compressionYScale = 0.8f;

    [Tooltip("X轴超出值")]
    public float compressionXOvershoot = 0.1f;

    [Tooltip("Y轴超出值")]
    public float compressionYOvershoot = 0.1f;

    [Header("Destroying状态配置")]
    [Tooltip("销毁动画持续时间")]
    public float destroying_animDuration = 1.0f;

    [Tooltip("销毁动画放大比例")]
    public float destroying_scaleSize = 1.5f;

    [Tooltip("白色闪烁效果持续时间")]
    public float destroying_whiteFlashDuration = 0.8f;

    [Header("SynthesisFinish状态配置")]
    [Tooltip("合成完成延时执行时间")]
    public float SynthesisFinish_delayTime = 0.5f;

    [Tooltip("合成完成动画持续时间")]
    public float SynthesisFinish_animDuration = 1.2f;

    [Tooltip("合成完成动画放大比例")]
    public float SynthesisFinish_scaleSize = 1.8f;

    [Tooltip("合成完成白色闪烁效果持续时间")]
    public float SynthesisFinish_whiteFlashDuration = 1.0f;

    [Header("销毁Z偏移距离")]

    [Tooltip("销毁目标X偏移距离")]
    public float destroying_targetXOffset = 5f;

    [Tooltip("BackRow状态销毁Z偏差数组（空数组表示不偏差）")]
    public float[] destroying_BackRow_ZOffsets = new float[] { };

    [Tooltip("FirstRow状态销毁Z偏差数组（空数组表示不偏差）")]
    public float[] destroying_FirstRow_ZOffsets = new float[] { };

    [Tooltip("Shooting状态销毁Z偏差数组（空数组表示不偏差）")]
    public float[] destroying_Shooting_ZOffsets = new float[] { };

    [Tooltip("Shooting额外销毁Z偏差数组（空数组表示不偏差）")]
    public float[] destroying_ShootingDeep_ZOffsets = new float[] { };

    [Header("Prop_PickOut状态配置")]
    [Tooltip("道具拾取状态描边宽度")]
    [Range(0f, 0.3f)]
    public float propPickOut_outlineWidth = 0.004f;

    [Header("问号炮专用配置 - Type_Question_XXX")]
    [Tooltip("问号炮BackRow状态贴图移动速度")]
    public Vector2 Type_Question_BackRow_textureSpeed = new Vector2(0.1f, 0.0f);

    [Tooltip("问号炮BackRow状态炮身颜色")]
    public Color Type_Question_BackRow_CannonColor = Color.white;

    [Tooltip("问号炮BackRow状态腿部颜色")]
    public Color Type_Question_BackRow_FootColor = Color.white;

    [Tooltip("问号炮BackRow状态描边颜色")]
    public Color Type_Question_BackRow_outlineColor = Color.white;

    [Tooltip("问号炮BackRow状态描边宽度")]
    [Range(0f, 0.3f)]
    public float Type_Question_BackRow_outlineWidth = 0.002f;

    [Tooltip("问号炮BackRow状态膜层颜色")]
    public Color Type_Question_BackRow_overlayColor = Color.red;

    [Tooltip("问号炮BackRow状态膜层透明度")]
    [Range(0f, 1f)]
    public float Type_Question_BackRow_overlayAlpha = 0.8f;

    [Tooltip("问号炮BackRow状态膜层宽度比例")]
    [Range(0f, 1f)]
    public float Type_Question_BackRow_overlayWidthScale = 0.5f;

    [Tooltip("问号炮BackRow状态数字透明度")]
    [Range(0f, 1f)]
    public float Type_Question_BackRow_textAlpha = 0.3f;

    [Tooltip("问号炮FirstRow状态动画持续时间")]
    public float Type_Question_FirstRow_animDuration = 1.2f;

    [Tooltip("问号炮FirstRow状态动画放大比例")]
    public float Type_Question_FirstRow_scaleSize = 1.8f;

    [Tooltip("问号炮FirstRow状态白色闪烁效果持续时间")]
    public float Type_Question_FirstRow_whiteFlashDuration = 1.0f;


    [Tooltip("问号炮BackRow状态膜层颜色")]
    public Color Type_Question_LinkStick_Color = Color.red;

    [Tooltip("问号炮FirstRow状态动画期间是否禁用点击")]
    public bool Type_Question_FirstRow_disableClickDuringAnim = true;

    [Header("问号炮专用状态")]
    [Tooltip("问号炮台是否已经激活过问号效果")]
    public bool isQuestionEffectActivated = false;

    [Header("宝箱炮专用配置 - TreasureCannon")]

    [Tooltip("宝箱炮解锁透明底图")]
    public SpriteRenderer TreasureCannon_UnlockQuad;

    [Tooltip("宝箱炮解锁件")]
    public GameObject TreasureCannon_UnlockAssemble;

    [Tooltip("宝箱炮解锁透明底图放大比例")]
    public float TreasureCannon_UnlockQuad_ScaleTo;

    [Tooltip("宝箱炮解锁件插值坐标")]
    public Vector3 TreasureCannon_UnlockAssemble_ToLocalPos;

    [Tooltip("宝箱炮解锁件动画时间")]
    public float TreasureCannon_UnlockAssemble_Duration = 0.25f;

    [Tooltip("宝箱炮解锁透明底图动画时间")]
    public float TreasureCannon_UnlockQuad_Duration = 0.25f;



    [HideInInspector]
    public bool TreasureCannon_IsUnlocked = false;

    [Header("子弹发射配置")]
    [Tooltip("子弹创建位置偏移（相对于炮身）")]
    public Vector3 bulletSpawnOffset = new Vector3(0, 0.5f, 0);

    [Header("发射特效配置")]

    [Tooltip("发射特效位置偏移（相对于炮身）")]
    public Vector3 fireEffectOffset = new Vector3(0, 0.3f, 0.5f);

    [Tooltip("发射特效销毁时间")]
    public float fireEffectDuration = 1.5f;

    [Tooltip("炮身后退距离")]
    public float recoilDistance = 0.3f;

    [Tooltip("炮身后退动画时间")]
    public float recoilDuration = 0.1f;

    [Tooltip("炮身回弹动画时间")]
    public float recoilRecoveryDuration = 0.2f;

    [Tooltip("后退动画曲线")]
    public AnimationCurve recoilCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    [Tooltip("回弹动画曲线")]
    public AnimationCurve recoveryCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    [Header("旋转瞄准配置")]
    [Tooltip("旋转到目标速度（度/秒）")]
    [Range(30f, 360f)]
    public float rotationSpeed = 90f;

    [Tooltip("旋转回原角度速度（度/秒）")]
    [Range(30f, 360f)]
    public float returnRotationSpeed = 120f;

    [Tooltip("空闲检测间隔（秒）：在没有旋转与目标时，隔多少秒检查一次是否需要回正")]
    [Range(0.02f, 0.5f)]
    public float rotationIdleCheckInterval = 0.15f;

    [Tooltip("回正角度阈值（度）：小于该角度不触发回正")]
    [Range(0.1f, 10f)]
    public float rotationReturnAngleThreshold = 1.5f;

    [Tooltip("目标旋转最小角度阈值（度）：与目标角度差小于该值时不进行旋转")]
    [Range(0.1f, 10f)]
    public float rotationTargetAngleThreshold = 1.0f;

    [Header("链接系统配置")]
    [Tooltip("链接圆棍字典 - 键为目标炮台ID，值为圆棍对象")]
    public Dictionary<int, GameObject> linkStickObjects = new Dictionary<int, GameObject>();

    [Tooltip("链接圆棍组件字典 - 键为目标炮台ID，值为圆棍组件")]
    public Dictionary<int, GridConnon_LinkStick> linkStickComponents = new Dictionary<int, GridConnon_LinkStick>();

    [Tooltip("是否启用链接功能")]
    public bool enableLinking = true;

    // 状态管理器
    private GridConnon_CannonStateManager stateManager;

    // 子弹发射相关私有变量
    private Vector3 originalCannonPosition; // 炮身原始位置
    private bool isRecoiling = false; // 是否正在后退动画中

    // 后退动画状态机相关变量
    private enum RecoilState { None, Recoiling, Recovering }
    private RecoilState recoilState = RecoilState.None;
    private float recoilTimer = 0f;
    private Vector3 recoilStartPosition;
    private Vector3 recoilTargetPosition;

    // 旋转瞄准相关私有变量
    private System.Collections.Generic.List<Vector3> targetQueue = new System.Collections.Generic.List<Vector3>(); // 目标点队列
    private Quaternion originalRotation; // 原始旋转角度
    private bool isRotating = false; // 是否正在旋转中

    // Update方式旋转相关变量
    private Quaternion rotationStartAngle; // 旋转开始角度
    private Quaternion rotationTargetAngle; // 旋转目标角度
    private float rotationStartTime; // 旋转开始时间
    private float rotationDuration; // 旋转持续时间
    private float currentRotationSpeed; // 当前旋转速度
    private System.Action rotationCompleteCallback; // 旋转完成回调
    private float nextRotationIdleCheckTime = 0f; // 空闲时下一次检查时间戳

    // 击打值相关私有变量
    private int currentHitCount = 0; // 当前击打值

    // 链接相关私有变量
    private GridConnon_Data_Cannon cannonData; // 保存炮台数据引用

    /// <summary>
    /// 获取状态管理器（公共访问接口）
    /// </summary>
    public GridConnon_CannonStateManager StateManager => stateManager;

    /// <summary>
    /// 获取当前击打值
    /// </summary>
    /// <returns>当前击打值</returns>
    public int GetCurrentHitCount()
    {
        return currentHitCount;
    }

    /// <summary>
    /// 检查击打值是否为0
    /// </summary>
    /// <returns>击打值是否为0</returns>
    public bool IsHitCountZero()
    {
        return currentHitCount <= 0;
    }

    /// <summary>
    /// 减少击打值
    /// </summary>
    /// <returns>减少后的击打值</returns>
    public int ReduceHitCount()
    {
        if (currentHitCount > 0)
        {
            currentHitCount--;
            UpdateHitCountDisplay();
        }
        return currentHitCount;
    }

    /// <summary>
    /// 增加击打值
    /// </summary>
    /// <param name="amount">要增加的数量</param>
    /// <returns>增加后的击打值</returns>
    public int AddHitCount(int amount)
    {
        if (amount > 0)
        {
            currentHitCount += amount;
            UpdateHitCountDisplay();
        }
        return currentHitCount;
    }

    /// <summary>
    /// 更新击打值显示
    /// </summary>
    private void UpdateHitCountDisplay()
    {
        if (txt_Count != null)
        {
            txt_Count.text = currentHitCount.ToString();
        }
    }

    #region Unity 生命周期

    protected override void Awake()
    {
        base.Awake();

        // 自动获取导航组件
        if (navigationComponent == null)
            navigationComponent = GetComponent<GridConnon_Navigation>();

        cannonRenderer = transform.GetChild(0).GetComponent<MeshRenderer>();
        footRenderer = transform.GetChild(1).GetComponentInChildren<SkinnedMeshRenderer>();
        tool_JRJELLY_Simple = transform.GetChild(0).GetComponent<Tool_JRJELLY_Simple>();
        connon_MeshObject = transform.GetChild(0).gameObject;
        foot_MeshObject = transform.GetChild(1).gameObject;
        txt_Count = transform.GetChild(0).Find("CountObj/Count/txtCount").GetComponent<TextMeshProUGUI>();

        // 初始化材质属性块
        InitializeMaterialPropertyBlock();

        // 初始化状态管理器
        stateManager = new GridConnon_CannonStateManager(this);

        // 初始化炮身原始位置
        if (connon_MeshObject != null)
        {
            originalCannonPosition = connon_MeshObject.transform.localPosition;
        }

        // 初始化原始旋转角度
        originalRotation = transform.rotation;


        navigationComponent.navigationData.moveSpeed = MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data.MovingSpeed;


    }

    void Update()
    {
        // 更新状态管理器
        stateManager?.Update();

        // if (Input.GetKeyDown(KeyCode.N))
        //   TreasureCannon_Unlock();

    }

    protected override void Start()
    {
        base.Start();

        // 应用颜色
        if (HasColor())
        {
            ApplyColor();
        }

        // 根据Z坐标自动设置初始状态
        SetInitialStateByPosition();
    }

    #endregion

    #region 初始化

    protected override void OnInitialize(GridConnon_Data_Cannon data)
    {
        base.OnInitialize(data);

        // 保存炮台数据引用
        cannonData = data;

        // 初始化击打值
        currentHitCount = data.hitCount;
        UpdateHitCountDisplay();

        // 普通炮特定的初始化
        if (HasColor())
        {
            ApplyColor();
        }

        // 问号炮台特定的初始化
        if (ObjectType == GridConnon_ObjectType.QuestionNormalCannon ||
            ObjectType == GridConnon_ObjectType.QuestionTriangleCannon)
        {
            // 初始化时重置问号效果激活状态
            isQuestionEffectActivated = false;
        }

        // 初始化完成后，根据位置设置状态
        SetInitialStateByPosition();
    }

    /// <summary>
    /// 初始化材质属性块
    /// </summary>
    private void InitializeMaterialPropertyBlock()
    {
        // 初始化炮身材质属性块
        if (cannonRenderer != null && propertyBlock == null)
        {
            propertyBlock = new MaterialPropertyBlock();
            cannonRenderer.GetPropertyBlock(propertyBlock);
        }

        // 初始化腿部材质属性块
        if (footRenderer != null && footPropertyBlock == null)
        {
            footPropertyBlock = new MaterialPropertyBlock();
            footRenderer.GetPropertyBlock(footPropertyBlock);
        }
    }

    /// <summary>
    /// 根据网格位置设置初始状态
    /// Z=0为FirstRow（前排），Z<0为BackRow（后排）
    /// </summary>
    private void SetInitialStateByPosition()
    {
        if (stateManager == null) return;

        // 使用状态管理器的状态确定逻辑，该逻辑会考虑链接机制
        GridConnon_Cannon_State initialState = stateManager.DetermineStateByPositionAndLinks(GridZ);

        stateManager.ChangeState(initialState);
    }

    /// <summary>
    /// 重写InitializeCannon，在设置网格信息后自动设置状态
    /// </summary>
    public override void InitializeCannon(GridConnon_Data_Cannon data, Common_ReserveGrid grid)
    {
        base.InitializeCannon(data, grid);

        // 网格信息设置完成后，根据位置设置状态
        SetInitialStateByPosition();
    }

    #endregion

    #region MaterialPropertyBlock管理

    /// <summary>
    /// 获取炮身的MaterialPropertyBlock（供StateManager使用）
    /// </summary>
    /// <returns>炮身的MaterialPropertyBlock</returns>
    public MaterialPropertyBlock GetCannonPropertyBlock()
    {
        if (cannonRenderer == null) return null;

        // 确保propertyBlock已初始化
        if (propertyBlock == null)
        {
            InitializeMaterialPropertyBlock();
        }

        // 获取当前属性状态
        if (propertyBlock != null)
        {
            cannonRenderer.GetPropertyBlock(propertyBlock);
        }

        return propertyBlock;
    }

    #endregion

    #region 颜色管理

    protected override void OnColorChanged(int colorId)
    {
        base.OnColorChanged(colorId);
        ApplyColor();
    }

    /// <summary>
    /// 应用颜色到炮身和腿部
    /// </summary>
    public void ApplyColor()
    {
        // 处理宝箱炮类型（无颜色）
        if (ObjectType == GridConnon_ObjectType.TreasureCannon)
        {
            ApplyTreasureCannonColor();
            return;
        }

        // 处理问号炮类型（根据状态决定颜色）
        if (ObjectType == GridConnon_ObjectType.QuestionNormalCannon ||
            ObjectType == GridConnon_ObjectType.QuestionTriangleCannon)
        {
            ApplyQuestionCannonColor();
            return;
        }

        // 处理普通炮（有颜色）
        if (HasColor())
        {
            ApplyNormalCannonColor();
        }
    }

    /// <summary>
    /// 应用宝箱炮颜色
    /// </summary>
    public void ApplyTreasureCannonColor()
    {
        // 炮身颜色
        if (cannonRenderer != null)
        {
            if (propertyBlock != null)
            {
                propertyBlock.SetColor("_Color", TreasureCannon_CannonColor);
                cannonRenderer.SetPropertyBlock(propertyBlock);
            }
            else if (cannonMaterial != null)
            {
                cannonMaterial.color = TreasureCannon_CannonColor;
            }
        }

        // 腿部颜色
        if (footRenderer != null)
        {
            if (footPropertyBlock != null)
            {
                footPropertyBlock.SetColor("_Color", TreasureCannon_FootColor);
                footRenderer.SetPropertyBlock(footPropertyBlock);
            }
            else if (footMaterial != null)
            {
                footMaterial.color = TreasureCannon_FootColor;
            }
        }
    }

    /// <summary>
    /// 应用问号炮颜色（根据状态）
    /// </summary>
    public void ApplyQuestionCannonColor()
    {
        if (!HasColor()) return;

        // 如果问号效果已激活，表现得像普通炮台（但保持主贴图为空）
        if (isQuestionEffectActivated)
        {
            ApplyQuestionActivatedColor();
            return;
        }

        // 检查当前状态
        GridConnon_Cannon_State currentState = GetCurrentState();

        if (currentState == GridConnon_Cannon_State.BackRow || currentState == GridConnon_Cannon_State.Moving)
        {
            // BackRow和Moving状态：使用问号炮专用的白色
            ApplyQuestionBackRowColor();
        }
        else if (currentState == GridConnon_Cannon_State.FirstRow)
        {
            // FirstRow状态：使用原始颜色（ColorId对应的颜色）
            ApplyNormalCannonColor();
        }
        else
        {
            // 其他状态：使用原始颜色
            ApplyNormalCannonColor();
        }
    }

    /// <summary>
    /// 应用问号炮BackRow状态颜色
    /// </summary>
    public void ApplyQuestionBackRowColor()
    {
        // 炮身颜色
        if (cannonRenderer != null)
        {
            if (propertyBlock != null)
            {
                propertyBlock.SetColor("_Color", Type_Question_BackRow_CannonColor);
                cannonRenderer.SetPropertyBlock(propertyBlock);
            }
            else if (cannonMaterial != null)
            {
                cannonMaterial.color = Type_Question_BackRow_CannonColor;
            }
        }

        // 腿部颜色
        if (footRenderer != null)
        {
            if (footPropertyBlock != null)
            {
                footPropertyBlock.SetColor("_Color", Type_Question_BackRow_FootColor);
                footRenderer.SetPropertyBlock(footPropertyBlock);
            }
            else if (footMaterial != null)
            {
                footMaterial.color = Type_Question_BackRow_FootColor;
            }
        }
    }

    /// <summary>
    /// 应用已激活问号炮颜色（表现得像普通炮台但保持主贴图为空）
    /// </summary>
    public void ApplyQuestionActivatedColor()
    {
        if (!HasColor()) return;

        // 获取ColorId对应的颜色
        Color color = MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data?.GetColorById(ColorId) ?? Color.white;

        // 炮身颜色
        if (cannonRenderer != null)
        {
            if (propertyBlock != null)
            {
                propertyBlock.SetColor("_Color", color);
                cannonRenderer.SetPropertyBlock(propertyBlock);
            }
            else if (cannonMaterial != null)
            {
                cannonMaterial.color = color;
            }
        }

        // 腿部颜色（与炮身相同）
        if (footRenderer != null)
        {
            if (footPropertyBlock != null)
            {
                footPropertyBlock.SetColor("_Color", color);
                footRenderer.SetPropertyBlock(footPropertyBlock);
            }
            else if (footMaterial != null)
            {
                footMaterial.color = color;
            }
        }

        // 确保主贴图保持为空（已激活的问号炮台特征）
        if (cannonRenderer != null && cannonRenderer.material != null)
        {
            cannonRenderer.material.mainTexture = null;
        }
    }

    public void ClearTexture()
    {
        // 确保主贴图保持为空（已激活的问号炮台特征）
        if (cannonRenderer != null && cannonRenderer.material != null)
        {
            cannonRenderer.material.mainTexture = null;
        }
    }

    /// <summary>
    /// 应用普通炮颜色（根据ColorId）
    /// </summary>
    public void ApplyNormalCannonColor()
    {
        if (!HasColor()) return;

        Color color = MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data?.GetColorById(ColorId) ?? Color.white;

        // 炮身颜色
        if (cannonRenderer != null)
        {
            if (propertyBlock != null)
            {
                propertyBlock.SetColor("_Color", color);
                cannonRenderer.SetPropertyBlock(propertyBlock);
            }
            else if (cannonMaterial != null)
            {
                cannonMaterial.color = color;
            }
        }

        // 腿部颜色（与炮身相同）
        if (footRenderer != null)
        {
            if (footPropertyBlock != null)
            {
                footPropertyBlock.SetColor("_Color", color);
                footRenderer.SetPropertyBlock(footPropertyBlock);
            }
            else if (footMaterial != null)
            {
                footMaterial.color = color;
            }
        }
    }

    #endregion

    #region 调试和信息

    /// <summary>
    /// 在Scene视图中绘制调试信息
    /// </summary>
    // protected virtual void OnDrawGizmosSelected()
    // {
    //     // 绘制网格位置
    //     if (GridConnon_Controller.Instance != null)
    //     {
    //         Vector3 gridCenter = GridConnon_Controller.Instance.GridToWorldPosition(GridX, GridZ);
    //         Gizmos.color = HasColor() ?
    //             MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data.GetColorById(ColorId) :
    //             Color.white;
    //         // 使用cellSizeX和cellSizeZ创建适当的立方体大小
    //         Vector3 cubeSize = new Vector3(GridConnon_Controller.Instance.cellSizeX, 1f, GridConnon_Controller.Instance.cellSizeZ);
    //         Gizmos.DrawWireCube(gridCenter, cubeSize);
    //     }
    // }

    #endregion

    #region 导航移动功能

    /// <summary>
    /// 移动到指定目标点
    /// </summary>
    /// <param name="targetPosition">目标位置（世界坐标）</param>
    /// <param name="onCompleted">移动完成回调（可选）</param>
    /// <returns>是否成功开始移动</returns>
    public bool MoveToPosition(Vector3 targetPosition, System.Action onCompleted = null)
    {
        if (navigationComponent == null)
        {
            return false;
        }

        return navigationComponent.StartNavigation(targetPosition, onCompleted);
    }

    /// <summary>
    /// 停止移动
    /// </summary>
    public void StopMoving()
    {
        if (navigationComponent != null)
        {
            navigationComponent.StopNavigation();
        }
    }

    /// <summary>
    /// 检查是否正在移动
    /// </summary>
    /// <returns>是否正在移动</returns>
    public bool IsMoving()
    {
        return navigationComponent != null && navigationComponent.IsNavigating();
    }

    /// <summary>
    /// 获取移动进度
    /// </summary>
    /// <returns>移动进度（0-1）</returns>
    public float GetMoveProgress()
    {
        return navigationComponent != null ? navigationComponent.GetNavigationProgress() : 0f;
    }

    #endregion

    #region 状态管理

    /// <summary>
    /// 切换状态
    /// </summary>
    public void ChangeState(GridConnon_Cannon_State newState)
    {
        stateManager?.ChangeState(newState);
        // 更新状态显示字段
        currentStateDisplay = newState;

        // 状态切换后更新颜色（特别是问号炮需要根据状态显示不同颜色）
        if (ObjectType == GridConnon_ObjectType.QuestionNormalCannon ||
            ObjectType == GridConnon_ObjectType.QuestionTriangleCannon)
        {
            ApplyColor();
        }
    }

    /// <summary>
    /// 智能切换到Destroying状态（自动设置Zoff）
    /// 根据当前状态自动选择合适的Z偏移数组
    /// </summary>
    /// <param name="fromState">来源状态（可选，如果不指定则使用当前状态）</param>
    public void ToDestroy(GridConnon_Cannon_State fromState)
    {

        if (GetCurrentState() == GridConnon_Cannon_State.Destroying)
            return;


        // 获取对应的Z偏移数组
        float[] zOffsetArray = GetDestroyingZOffsetArray(fromState);


        // 获取Destroying状态实例并设置Zoff
        var desState = StateManager.GetStateByType(GridConnon_Cannon_State.Destroying) as GridConnon_CannonState_Destroying;
        if (desState != null)
        {
            desState.Zoff = zOffsetArray;
            ChangeState(GridConnon_Cannon_State.Destroying);
        }
        else
        {
            ////Debug.LogError($"[智能销毁切换] 炮台 {ObjectId} 无法获取Destroying状态实例");
        }
    }


    /// <summary>
    /// 内部方法：更新状态显示字段（供状态管理器调用）
    /// </summary>
    /// <param name="newState">新状态</param>
    internal void UpdateStateDisplay(GridConnon_Cannon_State newState)
    {
        currentStateDisplay = newState;
    }

    /// <summary>
    /// 更新网格位置并自动调整状态
    /// 用于炮台移动后更新状态
    /// </summary>
    public void UpdateGridPosition(int newGridX, int newGridZ)
    {
        GridX = newGridX;
        GridZ = newGridZ;

        // 通知控制器应用Z行隐藏优化
        if (GridConnon_Controller.Instance != null)
        {
            GridConnon_Controller.Instance.ApplyZRowOptimizationToCannon(this);
        }
    }

    /// <summary>
    /// 获取当前状态
    /// </summary>
    public GridConnon_Cannon_State GetCurrentState()
    {
        GridConnon_Cannon_State actualState = stateManager?.GetCurrentState() ?? GridConnon_Cannon_State.BackRow;
        // 确保显示字段与实际状态同步
        currentStateDisplay = actualState;
        return actualState;
    }

    /// <summary>
    /// 处理点击事件
    /// </summary>
    public void HandleClick()
    {
        stateManager?.HandleClick();
    }

    /// <summary>
    /// Unity的鼠标点击检测
    /// </summary>
    private void OnMouseDown()
    {
        // 处理鼠标点击
        HandleClick();
    }



    #endregion

    #region 旋转瞄准系统

    /// <summary>
    /// 更新旋转系统
    /// </summary>
    public void UpdateRotationSystem()
    {
        // 如果有目标队列，优先处理目标（中断回转）
        if (targetQueue.Count > 0)
        {
            // 如果正在回转到原始角度，中断回转并开始瞄准目标
            if (isRotating && IsRotatingToOriginal())
            {
                ////Debug.Log($"炮台 {ObjectId} 中断回转，开始瞄准新目标");
                StartRotateToNextTarget();
                return;
            }

            // 如果没有在旋转，开始旋转到第一个目标
            if (!isRotating)
            {
                StartRotateToNextTarget();
                return;
            }
        }

        // 如果正在旋转，更新旋转进度
        if (isRotating)
        {
            UpdateRotationProgress();
            return;
        }

        // 如果没有目标队列且不在旋转中，检查是否需要回到原始角度（增加空闲节流与阈值）
        if (targetQueue.Count == 0 && !isRotating)
        {
            if (Time.time < nextRotationIdleCheckTime)
            {
                return; // 空闲节流：减少无意义的每帧角度检测
            }
            nextRotationIdleCheckTime = Time.time + rotationIdleCheckInterval;

            // 检查当前角度是否偏离原始角度
            if (Quaternion.Angle(transform.rotation, originalRotation) > rotationReturnAngleThreshold)
            {
                StartRotateToOriginal();
            }
            return;
        }
    }

    /// <summary>
    /// 更新旋转进度（Update方式）
    /// </summary>
    private void UpdateRotationProgress()
    {
        float elapsedTime = Time.time - rotationStartTime;

        if (elapsedTime >= rotationDuration)
        {
            // 旋转完成
            transform.rotation = rotationTargetAngle;
            isRotating = false;

            // 执行完成回调
            rotationCompleteCallback?.Invoke();
            rotationCompleteCallback = null;

            ////Debug.Log($"炮台 {ObjectId} 旋转完成，目标角度: {rotationTargetAngle.eulerAngles}");
        }
        else
        {
            // 继续旋转
            float progress = elapsedTime / rotationDuration;
            transform.rotation = Quaternion.Slerp(rotationStartAngle, rotationTargetAngle, progress);
        }
    }

    /// <summary>
    /// 添加目标点到队列
    /// </summary>
    /// <param name="targetPosition">目标位置</param>
    public void AddTargetToQueue(Vector3 targetPosition)
    {
        // 将Y坐标设为自身Y坐标
        Vector3 adjustedTarget = new Vector3(targetPosition.x, transform.position.y, targetPosition.z);
        targetQueue.Add(adjustedTarget);

        ////Debug.Log($"炮台 {ObjectId} 添加目标点到队列: {adjustedTarget}，队列长度: {targetQueue.Count}");
    }

    /// <summary>
    /// 清空目标队列
    /// </summary>
    public void ClearTargetQueue()
    {
        targetQueue.Clear();
        ////Debug.Log($"炮台 {ObjectId} 清空目标队列");
    }

    /// <summary>
    /// 开始旋转到下一个目标
    /// </summary>
    private void StartRotateToNextTarget()
    {
        if (targetQueue.Count == 0)
        {
            return;
        }

        Vector3 targetPosition = targetQueue[0];
        Vector3 direction = targetPosition - transform.position;

        // 忽略Y轴差异，只在水平面上旋转
        direction.y = 0;

        if (direction.magnitude > 0.01f)
        {
            Quaternion targetRotation = Quaternion.LookRotation(direction);

            // 当与目标角度差过小，跳过以避免无意义的旋转
            float angleDiff = Quaternion.Angle(transform.rotation, targetRotation);
            if (angleDiff < rotationTargetAngleThreshold)
            {
                if (targetQueue.Count > 0)
                {
                    targetQueue.RemoveAt(0);
                }
                return;
            }

            StartRotation(targetRotation, rotationSpeed, () =>
            {
                // 旋转完成后移除第一个目标
                if (targetQueue.Count > 0)
                {
                    targetQueue.RemoveAt(0);
                }
            });
        }
        else
        {
            // 目标太近，直接移除
            targetQueue.RemoveAt(0);
        }
    }

    /// <summary>
    /// 开始旋转回原始角度
    /// </summary>
    private void StartRotateToOriginal()
    {
        if (!isRotating)
        {
            StartRotation(originalRotation, returnRotationSpeed, null);
        }
    }

    /// <summary>
    /// 开始旋转（Update方式）
    /// </summary>
    /// <param name="targetRotation">目标旋转</param>
    /// <param name="speedToUse">使用的旋转速度</param>
    /// <param name="onComplete">完成回调</param>
    private void StartRotation(Quaternion targetRotation, float speedToUse, System.Action onComplete)
    {
        // 如果已经在旋转，停止之前的旋转
        if (isRotating)
        {
            isRotating = false;
            rotationCompleteCallback = null;
        }

        // 设置旋转参数
        rotationStartAngle = transform.rotation;
        rotationTargetAngle = targetRotation;
        rotationStartTime = Time.time;
        currentRotationSpeed = speedToUse;
        rotationCompleteCallback = onComplete;

        // 计算旋转角度差异和持续时间
        float angleDifference = Quaternion.Angle(rotationStartAngle, rotationTargetAngle);
        rotationDuration = angleDifference / speedToUse;

        // 开始旋转
        isRotating = true;

        ////Debug.Log($"炮台 {ObjectId} 开始旋转，目标角度: {targetRotation.eulerAngles}，持续时间: {rotationDuration}秒");
    }

    /// <summary>
    /// 停止旋转系统
    /// </summary>
    private void StopRotationSystem()
    {
        isRotating = false;
        rotationCompleteCallback = null;
        targetQueue.Clear();

        ////Debug.Log($"炮台 {ObjectId} 停止旋转系统");
    }

    /// <summary>
    /// 获取当前目标队列数量
    /// </summary>
    /// <returns>队列中的目标数量</returns>
    public int GetTargetQueueCount()
    {
        return targetQueue.Count;
    }

    /// <summary>
    /// 检查是否正在旋转
    /// </summary>
    /// <returns>是否正在旋转</returns>
    public bool IsRotating()
    {
        return isRotating;
    }

    /// <summary>
    /// 检查是否正在回转到原始角度
    /// </summary>
    /// <returns>是否正在回转到原始角度</returns>
    private bool IsRotatingToOriginal()
    {
        if (!isRotating) return false;

        // 检查目标角度是否与原始角度相近
        float angleDifference = Quaternion.Angle(rotationTargetAngle, originalRotation);
        return angleDifference < 0.1f;
    }


    #endregion

    #region 炮身后退系统

    /// <summary>
    /// 更新后退动画系统
    /// </summary>
    public void UpdateRecoilSystem()
    {
        if (recoilState == RecoilState.None || connon_MeshObject == null)
        {
            return;
        }

        recoilTimer += Time.deltaTime;

        switch (recoilState)
        {
            case RecoilState.Recoiling:

                UpdateRecoilPhase();
                break;
            case RecoilState.Recovering:
                UpdateRecoveryPhase();
                break;
        }
    }

    /// <summary>
    /// 更新后退阶段
    /// </summary>
    private void UpdateRecoilPhase()
    {
        if (recoilTimer >= recoilDuration)
        {
            // 后退阶段完成，切换到回弹阶段
            connon_MeshObject.transform.localPosition = recoilTargetPosition;

            // 开始回弹阶段
            recoilState = RecoilState.Recovering;
            recoilTimer = 0f;
            recoilStartPosition = connon_MeshObject.transform.localPosition;
            recoilTargetPosition = originalCannonPosition;
        }
        else
        {
            // 继续后退动画
            float progress = recoilTimer / recoilDuration;
            float curveValue = recoilCurve.Evaluate(progress);
            connon_MeshObject.transform.localPosition = Vector3.Lerp(recoilStartPosition, recoilTargetPosition, curveValue);
        }
    }

    /// <summary>
    /// 更新回弹阶段
    /// </summary>
    private void UpdateRecoveryPhase()
    {
        if (recoilTimer >= recoilRecoveryDuration)
        {
            // 回弹阶段完成
            connon_MeshObject.transform.localPosition = originalCannonPosition;
            recoilState = RecoilState.None;
            isRecoiling = false;
            recoilTimer = 0f;
        }
        else
        {
            // 继续回弹动画
            float progress = recoilTimer / recoilRecoveryDuration;
            float curveValue = recoveryCurve.Evaluate(progress);
            connon_MeshObject.transform.localPosition = Vector3.Lerp(recoilStartPosition, recoilTargetPosition, curveValue);
        }
    }

    #endregion

    #region 子弹发射功能

    /// <summary>
    /// 创建子弹
    /// </summary>
    /// <param name="target">目标对象</param>
    /// <returns>创建的子弹对象</returns>
    public GridConnon_Bullet CreateBullet(GameObject target)
    {
        return CreateBulletWithOffset(target, Vector3.zero);
    }

    public GridConnon_Bullet CreateBulletWithOffset(GameObject target, Vector3 positionOffset)
    {
        // 获取子弹预制体路径
        string bulletPrefabPath = MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data?.bulletPrefabPath;
        if (string.IsNullOrEmpty(bulletPrefabPath))
        {
            ////Debug.LogError($"炮台 {ObjectId} 无法获取子弹预制体路径");
            return null;
        }

        // 从Resources加载子弹预制体
        GameObject bulletPrefab = Resources.Load<GameObject>(bulletPrefabPath);
        if (bulletPrefab == null)
        {
            ////Debug.LogError($"炮台 {ObjectId} 无法加载子弹预制体: {bulletPrefabPath}");
            return null;
        }

        // 计算子弹创建位置
        Vector3 spawnPosition = transform.position + transform.TransformDirection(bulletSpawnOffset);

        // 创建子弹实例
        GameObject bulletObj = Instantiate(bulletPrefab, spawnPosition, Quaternion.identity);
        GridConnon_Bullet bullet = bulletObj.GetComponent<GridConnon_Bullet>();

        if (bullet == null)
        {
            ////Debug.LogError($"炮台 {ObjectId} 创建的子弹对象没有GridConnon_Bullet组件");
            Destroy(bulletObj);
            return null;
        }

        // 设置子弹目标和偏移
        bullet.SetTargetWithOffset(target, positionOffset);

        ////Debug.Log($"炮台 {ObjectId} 创建子弹成功，目标: {target.name}，偏移: {positionOffset}");
        return bullet;
    }

    /// <summary>
    /// 发射子弹
    /// </summary>
    /// <param name="target">目标对象</param>
    /// <returns>是否成功发射</returns>
    public bool FireBullet(GameObject target)
    {
        return FireBulletWithOffset(target, Vector3.zero);
    }

    /// <summary>
    /// 发射子弹（带位置偏移）
    /// </summary>
    /// <param name="target">目标对象</param>
    /// <param name="positionOffset">目标位置偏移</param>
    /// <returns>是否成功发射</returns>
    public bool FireBulletWithOffset(GameObject target, Vector3 positionOffset)
    {
        if (target == null)
        {
            ////Debug.LogWarning($"炮台 {ObjectId} 发射子弹失败：目标为空");
            return false;
        }

        // 检查击打值是否为0
        if (IsHitCountZero())
        {
            ////Debug.LogWarning($"炮台 {ObjectId} 发射子弹失败：击打值为0");
            return false;
        }



        // 计算实际射击位置（目标位置 + 偏移）
        Vector3 shootPosition = target.transform.position + positionOffset;

        // 添加目标点到旋转队列（使用偏移后的位置）
        AddTargetToQueue(shootPosition);

        // 创建子弹（带偏移）
        GridConnon_Bullet bullet = CreateBulletWithOffset(target, positionOffset);
        if (bullet == null)
        {
            return false;
        }



        // 检查目标类型，射击GoldPig时不减少击打值
        var tileCubeObject = target.GetComponent<TileCube_Object>();
        bool isGoldPig = tileCubeObject != null && tileCubeObject.ObjectType == TileCube_Object.TileCube_ObjectType.GoldPig;

        if (!isGoldPig)
        {
            // 只有非GoldPig目标才减少击打值
            ReduceHitCount();
        }

        // 执行炮身后退效果
        StartRecoilEffect();

        // 创建发射特效（传入偏移后的射击位置）
        CreateFireEffect(shootPosition);

        ////Debug.Log($"炮台 {ObjectId} 发射子弹成功，目标: {target.name}，偏移: {positionOffset}，剩余击打值: {currentHitCount}");
        return true;
    }

    /// <summary>
    /// 创建发射特效
    /// </summary>
    /// <param name="targetPosition">射击目标位置</param>
    private void CreateFireEffect(Vector3 targetPosition)
    {

        GridConnon_Controller.On_PlaySound?.Invoke("CNT_发射子弹");
        GridConnon_Controller.On_PlayVib?.Invoke(MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data.Virb_Shoot);

        // 计算特效位置（相对于炮身的前方）
        Vector3 effectPosition = transform.position + transform.TransformDirection(fireEffectOffset);

        // 计算射击方向（从特效位置指向目标位置）
        Vector3 shootDirection = (new Vector3(targetPosition.x, effectPosition.y, targetPosition.z) - effectPosition).normalized;

        // 根据射击方向计算特效旋转角度
        Quaternion effectRotation = Quaternion.LookRotation(shootDirection);

        // 创建特效
        Tool_EffectPool.Instance.CreateEffect("CNT/Effect/Bullet_Fire", effectPosition, effectRotation, fireEffectDuration);

        ////Debug.Log($"炮台 {ObjectId} 创建发射特效，位置: {effectPosition}，射击方向: {shootDirection}");
    }

    /// <summary>
    /// 开始炮身后退效果
    /// </summary>
    private void StartRecoilEffect()
    {
        if (connon_MeshObject == null)
        {
            ////Debug.LogWarning($"炮台 {ObjectId} 没有设置connon_MeshObject，无法执行后退效果");
            return;
        }

        // 如果正在后撤期间，打断当前动画并立即回归原位
        if (isRecoiling)
        {
            ////Debug.Log($"炮台 {ObjectId} 在后撤期间收到新发射命令，打断当前动画");
            InterruptRecoilAndReset();
        }

        // 记录原始位置
        originalCannonPosition = connon_MeshObject.transform.localPosition;

        // 计算后退方向和目标位置
        Vector3 recoilDirection = -transform.forward;

        // 设置后退动画状态
        isRecoiling = true;
        recoilState = RecoilState.Recoiling;
        recoilTimer = 0f;
        recoilStartPosition = connon_MeshObject.transform.localPosition;
        recoilTargetPosition = originalCannonPosition + recoilDirection * recoilDistance;

        ////Debug.Log($"炮台 {ObjectId} 开始后退动画");
    }



    /// <summary>
    /// 停止后退效果（用于清理）
    /// </summary>
    private void StopRecoilEffect()
    {
        // 重置后退状态
        recoilState = RecoilState.None;
        isRecoiling = false;
        recoilTimer = 0f;

        if (connon_MeshObject != null)
        {
            connon_MeshObject.transform.localPosition = originalCannonPosition;
        }
    }

    /// <summary>
    /// 打断后退动画并立即回归原位
    /// </summary>
    private void InterruptRecoilAndReset()
    {
        // 重置后退状态
        recoilState = RecoilState.None;
        isRecoiling = false;
        recoilTimer = 0f;

        // 立即回归原位
        if (connon_MeshObject != null)
        {
            connon_MeshObject.transform.localPosition = originalCannonPosition;
        }

        ////Debug.Log($"炮台 {ObjectId} 后退动画已被打断，炮身已回归原位");
    }

    #endregion

    #region 宝箱炮专用方法

    /// <summary>
    /// 宝箱炮解锁方法
    /// </summary>
    public void TreasureCannon_Unlock()
    {
        if (ObjectType != GridConnon_ObjectType.TreasureCannon)
            return;

        if (TreasureCannon_IsUnlocked)
            return;

        TreasureCannon_IsUnlocked = true;

        // 使用智能切换方法，指定从FirstRow状态销毁（宝箱炮解锁通常在前排）
        ToDestroy(GridConnon_Cannon_State.FirstRow);
    }

    #endregion

    #region 通用销毁逻辑方法

    /// <summary>
    /// 销毁动画完成回调 - 通用方法
    /// </summary>
    /// <param name="zOffsetArray">Z偏移数组（来自之前的状态）</param>
    public void ExecuteDestroyingGoToDes(float[] zOffsetArray)
    {
        ////Debug.Log($"[通用销毁逻辑] 炮台 {ObjectId} 销毁动画完成，开始清理和移动销毁");

        // 清理格子占用
        if (stateManager.GetCurrentGridType() == GridConnon_CannonStateManager.GridType.ShotGrid)
        {
            stateManager.ClearShotGrid();
        }
        else
        {
            stateManager.ClearGridConnon_ControllerGrid();
        }

        stateManager.cannon.foot_MeshObject.gameObject.SetActive(true);

        // 开始移动到销毁位置
        ExecuteDestroyingMoveToDestroyPosition(zOffsetArray);
    }

    /// <summary>
    /// 移动到销毁位置 - 通用方法
    /// </summary>
    /// <param name="zOffsetArray">Z偏移数组（来自之前的状态）</param>
    public void ExecuteDestroyingMoveToDestroyPosition(float[] zOffsetArray)
    {
        Vector3 destroyPosition = CalculateDestroyPosition(zOffsetArray);

        // 开始移动到销毁位置
        bool moveStarted = MoveToPosition(destroyPosition, ExecuteDestroyingOnReachedDestroyPosition);

        if (moveStarted)
        {
            ////Debug.Log($"[通用销毁逻辑] 炮台 {ObjectId} 开始移动到销毁位置: {destroyPosition}");
        }
        else
        {
            ////Debug.LogWarning($"[通用销毁逻辑] 炮台 {ObjectId} 移动到销毁位置失败，直接销毁");
            ExecuteDestroyingDestroyCannonObject();
        }
    }

    /// <summary>
    /// 计算销毁目标位置 - 通用方法
    /// </summary>
    /// <param name="zOffsetArray">Z偏移数组（来自之前的状态）</param>
    public Vector3 CalculateDestroyPosition(float[] zOffsetArray = null)
    {
        var controller = GridConnon_Controller.Instance;
        if (controller == null || controller.CurrentLevel == null)
        {
            ////Debug.LogError("[通用销毁逻辑] 无法获取GridConnon_Controller或当前关卡数据");
            return transform.position;
        }

        // 获取当前世界坐标
        Vector3 currentWorldPos = transform.position;

        // 使用链接系统计算统一的移动方向
        var linkSystem = GridConnon_LinkSystem.Instance;
        bool moveLeft = true; // 默认向左

        if (linkSystem != null)
        {
            moveLeft = linkSystem.CalculateUnifiedMoveDirection(this);
        }
        else
        {
            // 链接系统不可用时使用原逻辑
            int fallbackGridColumns = controller.CurrentLevel.columns;
            float gridMiddleX = (fallbackGridColumns - 1) * 0.5f;
            Vector3 middleWorldPos = controller.GridToWorldPosition(Mathf.FloorToInt(gridMiddleX), GridZ);

            bool isLeft = currentWorldPos.x < middleWorldPos.x;
            bool isRight = currentWorldPos.x > middleWorldPos.x;

            if (isLeft)
            {
                moveLeft = true;
            }
            else if (isRight)
            {
                moveLeft = false;
            }
            else
            {
                moveLeft = Random.value < 0.5f;
            }
        }

        // 计算销毁目标X位置
        float targetX;
        Vector3 parentPos = controller.GridConnon_Parent.transform.position;
        float cellSizeX = controller.cellSizeX; // 使用X方向的网格大小
        float offset = destroying_targetXOffset;
        int gridColumns = controller.CurrentLevel.columns;

        if (moveLeft)
        {
            targetX = parentPos.x - offset;
            ////Debug.Log($"[通用销毁逻辑] 炮台 {ObjectId} 使用统一移动方向，移动到左边销毁位置");
        }
        else
        {
            targetX = parentPos.x + (gridColumns * cellSizeX) + offset;
            ////Debug.Log($"[通用销毁逻辑] 炮台 {ObjectId} 使用统一移动方向，移动到右边销毁位置");
        }

        // 计算Z坐标（保持当前Z坐标）
        float targetZ = currentWorldPos.z;

        // 应用传入的Z偏移数组
        if (zOffsetArray != null && zOffsetArray.Length > 0)
        {
            float zOffset = zOffsetArray[Random.Range(0, zOffsetArray.Length)];
            if (zOffset != 0f)
            {
                targetZ += zOffset;
                ////Debug.Log($"[通用销毁逻辑] 炮台 {ObjectId} 应用Z偏移: {zOffset}");
            }
        }

        // 返回目标位置
        return new Vector3(targetX, currentWorldPos.y, targetZ);
    }

    /// <summary>
    /// 到达销毁位置的回调 - 通用方法
    /// </summary>
    public void ExecuteDestroyingOnReachedDestroyPosition()
    {
        ////Debug.Log($"[通用销毁逻辑] 炮台 {ObjectId} 到达销毁位置，开始销毁");
        ExecuteDestroyingDestroyCannonObject();
    }

    /// <summary>
    /// 根据当前状态获取销毁Z偏移数组
    /// </summary>
    /// <returns>Z偏移数组</returns>
    public float[] GetDestroyingZOffsetArray(GridConnon_Cannon_State state)
    {
        switch (state)
        {
            case GridConnon_Cannon_State.BackRow:
                return destroying_BackRow_ZOffsets;
            case GridConnon_Cannon_State.FirstRow:
                return destroying_FirstRow_ZOffsets;
            case GridConnon_Cannon_State.Shooting:
                return destroying_Shooting_ZOffsets;
            case GridConnon_Cannon_State.ShootingSynthesis:
                return destroying_Shooting_ZOffsets;
            case GridConnon_Cannon_State.ShootingSynthesisFinish:
                return destroying_Shooting_ZOffsets;
            case GridConnon_Cannon_State.Moving:
                return destroying_FirstRow_ZOffsets;
            case GridConnon_Cannon_State.Prop_PickOut:
            case GridConnon_Cannon_State.Prop_Random:
                // 道具相关状态默认使用FirstRow偏移
                return destroying_FirstRow_ZOffsets;
            default:
                return destroying_FirstRow_ZOffsets;
        }
    }


    /// <summary>
    /// 销毁炮台对象 - 通用方法
    /// </summary>
    public void ExecuteDestroyingDestroyCannonObject()
    {
        GridConnon_Controller.Instance.DestroyCannonNoClearGrid(this);
    }

    #endregion

    #region 链接系统

    /// <summary>
    /// 获取炮台数据
    /// </summary>
    /// <returns>炮台数据</returns>
    public GridConnon_Data_Cannon GetCannonData()
    {
        return cannonData;
    }

    /// <summary>
    /// 初始化链接圆棍（为单个目标炮台创建圆棍）
    /// </summary>
    /// <param name="targetCannon">目标炮台</param>
    public void InitializeLinkStick(GridConnon_Cannon targetCannon)
    {
        if (targetCannon == null)
        {
            ////Debug.LogWarning($"[链接系统] 炮台 {ObjectId} 目标炮台为空");
            return;
        }

        int targetId = targetCannon.ObjectId;

        if (linkStickObjects.ContainsKey(targetId))
        {
            ////Debug.Log($"[链接系统] 炮台 {ObjectId} 到炮台 {targetId} 的圆棍已存在，跳过创建");
            return;
        }

        // 创建圆棍GameObject
        GameObject stickPrefab = Resources.Load<GameObject>(MXR_BRIGE.Cos_GameSetting.GridConnon_Const_Data?.linkstickPrefabPath);
        GameObject stickObj;

        if (stickPrefab == null)
        {
            // 如果没有预制体，创建基础圆棍
            stickObj = CreateBasicLinkStick(targetId);
        }
        else
        {
            // 使用预制体创建圆棍
            stickObj = Instantiate(stickPrefab, transform);
            stickObj.name = $"LinkStick_{ObjectId}_to_{targetId}";
        }

        // 存储圆棍对象和组件
        linkStickObjects[targetId] = stickObj;

        GridConnon_LinkStick stickComponent = stickObj.GetComponent<GridConnon_LinkStick>();
        if (stickComponent == null)
        {
            stickComponent = stickObj.AddComponent<GridConnon_LinkStick>();
        }
        linkStickComponents[targetId] = stickComponent;

        // 初始化时隐藏圆棍
        stickObj.SetActive(false);

        ////Debug.Log($"[链接系统] 炮台 {ObjectId} 到炮台 {targetId} 的圆棍初始化完成");
    }

    /// <summary>
    /// 兼容旧接口 - 初始化链接圆棍（空方法，因为新版本需要目标炮台参数）
    /// </summary>
    public void InitializeLinkStick()
    {
        ////Debug.LogWarning($"[链接系统] 炮台 {ObjectId} 调用了旧的InitializeLinkStick方法，请使用带参数的版本");
    }

    /// <summary>
    /// 创建基础链接圆棍
    /// </summary>
    /// <param name="targetId">目标炮台ID</param>
    /// <returns>创建的圆棍GameObject</returns>
    private GameObject CreateBasicLinkStick(int targetId)
    {
        // 创建基础圆棍GameObject
        GameObject stickObj = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        stickObj.name = $"LinkStick_{ObjectId}_to_{targetId}";
        stickObj.transform.SetParent(transform);
        stickObj.transform.localPosition = Vector3.zero;

        // 移除碰撞体
        Collider collider = stickObj.GetComponent<Collider>();
        if (collider != null)
        {
            DestroyImmediate(collider);
        }

        ////Debug.Log($"[链接系统] 为炮台 {ObjectId} 创建到炮台 {targetId} 的基础圆棍");
        return stickObj;
    }

    /// <summary>
    /// 设置链接圆棍的目标
    /// </summary>
    /// <param name="targetCannon">目标炮台</param>
    public void SetLinkStickTarget(GridConnon_Cannon targetCannon)
    {
        if (targetCannon == null)
        {
            ////Debug.LogWarning($"[链接系统] 炮台 {ObjectId} 目标炮台为空");
            return;
        }

        int targetId = targetCannon.ObjectId;

        // 如果没有对应的圆棍，先创建
        if (!linkStickComponents.ContainsKey(targetId))
        {
            InitializeLinkStick(targetCannon);
        }

        if (!linkStickComponents.ContainsKey(targetId))
        {
            ////Debug.LogWarning($"[链接系统] 炮台 {ObjectId} 没有到炮台 {targetId} 的圆棍组件");
            return;
        }

        // 设置链接的炮台
        linkStickComponents[targetId].SetLinkedCannons(this, targetCannon);

        // 显示圆棍
        if (linkStickObjects.ContainsKey(targetId))
        {
            linkStickObjects[targetId].SetActive(true);
        }

        ////Debug.Log($"[链接系统] 炮台 {ObjectId} 设置到炮台 {targetId} 的圆棍目标");
    }

    /// <summary>
    /// 清理所有链接圆棍
    /// </summary>
    public void ClearAllLinkSticks()
    {
        try
        {
            // 安全清理所有圆棍对象
            var stickObjectsToDestroy = new List<GameObject>();
            foreach (var kvp in linkStickObjects)
            {
                if (kvp.Value != null)
                {
                    stickObjectsToDestroy.Add(kvp.Value);
                }
            }

            foreach (var stickObj in stickObjectsToDestroy)
            {
                if (stickObj != null)
                {
                    DestroyImmediate(stickObj);
                }
            }

            // 清理字典
            linkStickObjects.Clear();
            linkStickComponents.Clear();

            ////Debug.Log($"[链接系统] 炮台 {ObjectId} 清理所有链接圆棍完成");
        }
        catch (System.Exception e)
        {
            ////Debug.LogError($"[链接系统] 炮台 {ObjectId} 清理链接圆棍时发生错误: {e.Message}");
        }
    }

    /// <summary>
    /// 更新所有链接棍的颜色
    /// </summary>
    public void UpdateAllLinkStickColors()
    {
        foreach (var kvp in linkStickComponents)
        {
            if (kvp.Value != null)
            {
                kvp.Value.ForceUpdateColor();
            }
        }

        ////Debug.Log($"[链接系统] 炮台 {ObjectId} 更新所有链接棍颜色完成");
    }

    /// <summary>
    /// 清理指定目标的链接圆棍
    /// </summary>
    /// <param name="targetId">目标炮台ID</param>
    public void ClearLinkStick(int targetId)
    {
        if (linkStickComponents.ContainsKey(targetId))
        {
            linkStickComponents[targetId].ClearLink();
        }

        if (linkStickObjects.ContainsKey(targetId))
        {
            linkStickObjects[targetId].SetActive(false);
        }

        ////Debug.Log($"[链接系统] 炮台 {ObjectId} 到炮台 {targetId} 的圆棍已清理");
    }

    /// <summary>
    /// 销毁所有链接圆棍
    /// </summary>
    public void DestroyAllLinkSticks()
    {
        foreach (var kvp in linkStickObjects)
        {
            if (kvp.Value != null)
            {
                DestroyImmediate(kvp.Value);
            }
        }

        linkStickObjects.Clear();
        linkStickComponents.Clear();

        ////Debug.Log($"[链接系统] 炮台 {ObjectId} 所有圆棍已销毁");
    }

    /// <summary>
    /// 销毁指定目标的链接圆棍
    /// </summary>
    /// <param name="targetId">目标炮台ID</param>
    public void DestroyLinkStick(int targetId)
    {
        if (linkStickObjects.ContainsKey(targetId))
        {
            DestroyImmediate(linkStickObjects[targetId]);
            linkStickObjects.Remove(targetId);
        }

        linkStickComponents.Remove(targetId);

        ////Debug.Log($"[链接系统] 炮台 {ObjectId} 到炮台 {targetId} 的圆棍已销毁");
    }

    /// <summary>
    /// 兼容旧接口 - 清理链接圆棍
    /// </summary>
    public void ClearLinkStick()
    {
        ClearAllLinkSticks();
    }

    /// <summary>
    /// 兼容旧接口 - 销毁链接圆棍
    /// </summary>
    public void DestroyLinkStick()
    {
        DestroyAllLinkSticks();
    }

    /// <summary>
    /// 检查炮台是否有链接
    /// </summary>
    public bool IsLinked()
    {
        return linkStickObjects != null && linkStickObjects.Count > 0;
    }

    /// <summary>
    /// 从射击格移动到奖励射击格
    /// </summary>
    public bool ShotGridToRewardShotGrid(GridConnon_ShotGrid_Grid targetRewardGrid, System.Action<GridConnon_ShotGrid_Grid> onReachDestination = null)
    {
        if (targetRewardGrid == null) return false;
        if (targetRewardGrid.gridType != GridConnon_ShotGrid_Type.RewardUnlock) return false;
        if (StateManager == null) return false;

        // 如果炮台正在移动，不允许重复操作
        if (IsMoving()) return false;

        // 1. 解除当前射击格的占用
        StateManager.ClearShotGrid();

        // 2. 预占有目标奖励格子
        if (targetRewardGrid.TryReserveGrid(gameObject))
        {
            // 停止呼吸效果
            StateManager.StopBreathe(true);

            // 3. 触发压缩动画
            StateManager.PlayCompressionAnimation(false);

            // 4. 移动到目标格子坐标（忽略Y）
            Vector3 targetPosition = targetRewardGrid.transform.position;
            targetPosition.y = transform.position.y; // 忽略Y轴

            // 开始移动，移动完成后的回调处理
            bool moveStarted = MoveToPosition(targetPosition, () =>
            {
                // 到达后的处理
                OnReachedRewardShotGrid(targetRewardGrid, onReachDestination);
            });

            if (moveStarted)
            {
                // 进入 Moving 状态
                StateManager.ChangeState(GridConnon_Cannon_State.Moving);
                return true;
            }
            else
            {
                // 移动失败，恢复格子状态
                targetRewardGrid.ClearReservation();
                return false;
            }
        }

        return false;
    }

    /// <summary>
    /// 到达奖励射击格后的处理
    /// </summary>
    private void OnReachedRewardShotGrid(GridConnon_ShotGrid_Grid rewardGrid, System.Action<GridConnon_ShotGrid_Grid> onReachDestination = null)
    {
        // 1. 设置Y旋转为0
        Vector3 currentRotation = transform.eulerAngles;
        currentRotation.y = 0f;
        transform.eulerAngles = currentRotation;

        // 2. 将预占状态升级为实际占用
        if (rewardGrid.UpgradeReservationToOccupation())
        {
            // 3. 更新炮台的格子引用
            StateManager.UpdateGridReference(rewardGrid);

            // 4. 执行自定义回调（如果有）
            onReachDestination?.Invoke(rewardGrid);

            // 5. 进入 Shooting 状态
            if (StateManager.GetCurrentState() == GridConnon_Cannon_State.Moving)
            {
                StateManager.ChangeState(GridConnon_Cannon_State.Shooting);
            }
        }
        else
        {
            // 如果升级失败，清除预占状态
            rewardGrid.ClearReservation();
        }
    }

    /// <summary>
    /// 从射击格移动到复活射击格
    /// </summary>
    public bool ShotGridToReviveShotGrid(GridConnon_ShotGrid_Grid targetReviveGrid, System.Action<GridConnon_ShotGrid_Grid> onReachDestination = null)
    {
        if (targetReviveGrid == null) return false;
        if (targetReviveGrid.gridType != GridConnon_ShotGrid_Type.ReviveUnlock) return false;
        if (StateManager == null) return false;

        // 如果炮台正在移动，不允许重复操作
        if (IsMoving()) return false;

        // 1. 解除当前射击格的占用
        StateManager.ClearShotGrid();

        // 2. 预占有目标复活格子
        if (targetReviveGrid.TryReserveGrid(gameObject))
        {
            // 停止呼吸效果
            StateManager.StopBreathe(true);

            // 3. 触发压缩动画
            StateManager.PlayCompressionAnimation(false);

            // 4. 移动到目标格子坐标（忽略Y）
            Vector3 targetPosition = targetReviveGrid.transform.position;
            targetPosition.y = transform.position.y; // 忽略Y轴

            // 开始移动，移动完成后的回调处理
            bool moveStarted = MoveToPosition(targetPosition, () =>
            {
                // 到达后的处理
                OnReachedReviveShotGrid(targetReviveGrid, onReachDestination);
            });

            if (moveStarted)
            {
                // 进入 Moving 状态
                StateManager.ChangeState(GridConnon_Cannon_State.Moving);
                return true;
            }
            else
            {
                // 移动失败，恢复格子状态
                targetReviveGrid.ClearReservation();
                return false;
            }
        }

        return false;
    }

    /// <summary>
    /// 到达复活射击格后的处理
    /// </summary>
    private void OnReachedReviveShotGrid(GridConnon_ShotGrid_Grid reviveGrid, System.Action<GridConnon_ShotGrid_Grid> onReachDestination = null)
    {
        // 1. 设置Y旋转为0
        Vector3 currentRotation = transform.eulerAngles;
        currentRotation.y = 0f;
        transform.eulerAngles = currentRotation;

        // 2. 将预占状态升级为实际占用
        if (reviveGrid.UpgradeReservationToOccupation())
        {
            // 3. 更新炮台的格子引用
            StateManager.UpdateGridReference(reviveGrid);

            // 4. 执行自定义回调（如果有）
            onReachDestination?.Invoke(reviveGrid);

            // 5. 进入 Shooting 状态
            if (StateManager.GetCurrentState() == GridConnon_Cannon_State.Moving)
            {
                StateManager.ChangeState(GridConnon_Cannon_State.Shooting);
            }
        }
        else
        {
            // 如果升级失败，清除预占状态
            reviveGrid.ClearReservation();
        }
    }

    /// <summary>
    /// 清理时也清理链接关系
    /// </summary>
    protected override void OnDestroy()
    {
        // 清理链接系统中的引用
        if (GridConnon_LinkSystem.Instance != null)
        {
            GridConnon_LinkSystem.Instance.ClearLinksForCannon(this);
        }

        // 清理圆棍
        DestroyAllLinkSticks();

        // 清理后退效果
        StopRecoilEffect();

        // 清理旋转系统
        StopRotationSystem();

        stateManager?.Cleanup();
        base.OnDestroy();
    }

    #endregion
}

public enum GridConnon_Cannon_State
{
    BackRow, FirstRow, Shooting, ShootingSynthesis, ShootingSynthesisFinish, Moving, Destroying, Prop_PickOut, Prop_Random
}