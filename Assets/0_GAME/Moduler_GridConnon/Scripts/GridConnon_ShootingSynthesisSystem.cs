using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;

/// <summary>
/// GridConnon 射击合成系统 - 负责检测和处理炮台合成逻辑
/// 只检测DirectShotGrids中的炮台，不需要连续排列
/// </summary>
public class GridConnon_ShootingSynthesisSystem
{
    [Header("合成动画配置")]
    [Tooltip("升高阶段时长")]
    public float riseAnimationDuration = 0.8f;

    [Tooltip("合成移动阶段时长")]
    public float synthesisAnimationDuration = 1.2f;

    [Tooltip("下落阶段时长")]
    public float fallAnimationDuration = 0.8f;

    [Tooltip("上升高度")]
    public float riseHeight = 2f;

    [Tooltip("左右偏移距离")]
    public float sideOffsetDistance = 1f;

    [Tooltip("动画曲线")]
    public AnimationCurve moveCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    // 当前正在进行的合成组
    private List<SynthesisGroup> activeSynthesisGroups = new List<SynthesisGroup>();

    /// <summary>
    /// 合成组数据
    /// </summary>
    private class SynthesisGroup
    {
        public GridConnon_Cannon leftCannon;    // 左侧炮台
        public GridConnon_Cannon centerCannon;  // 中间炮台
        public GridConnon_Cannon rightCannon;   // 右侧炮台
        public Vector3 leftOriginalPos;         // 左侧原始位置
        public Vector3 centerOriginalPos;       // 中间原始位置
        public Vector3 rightOriginalPos;        // 右侧原始位置
        public bool isActive;                   // 是否激活状态
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    public GridConnon_ShootingSynthesisSystem(GridConnon_Controller controller)
    {
        // 设置默认动画曲线
        if (moveCurve == null || moveCurve.keys.Length == 0)
        {
            moveCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        }
    }

    /// <summary>
    /// 无参构造函数（保持向后兼容）
    /// </summary>
    public GridConnon_ShootingSynthesisSystem()
    {
        // 设置默认动画曲线
        if (moveCurve == null || moveCurve.keys.Length == 0)
        {
            moveCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        }
    }

    /// <summary>
    /// 检测并开始合成（由GridConnon_Controller调用）
    /// </summary>
    public void CheckForSynthesis()
    {
        // 如果有正在进行的合成，不进行新的检测
        if (activeSynthesisGroups.Count > 0) return;

        // 获取DirectShotGrids中所有符合条件的炮台
        List<GridConnon_Cannon> eligibleCannons = GetEligibleCannonsFromDirectShotGrids();

        if (eligibleCannons.Count < 3)
        {
            return; // 不足3个符合条件的炮台
        }

        // 按类型和颜色分组
        var groups = GroupCannonsByTypeAndColor(eligibleCannons);

        // 调试信息：显示所有分组情况
        LogSynthesisGroups(groups);

        // 检查每个组是否有3个或以上的炮台
        foreach (var group in groups)
        {
            if (group.Value.Count >= 3)
            {
                // 选择前3个炮台进行合成
                List<GridConnon_Cannon> synthesisCannons = group.Value.Take(3).ToList();

                // 调试信息：显示参与合成的炮台类型组合
                string typeInfo = string.Join(", ", synthesisCannons.Select(c => c.ObjectType.ToString()));
                //Debug.Log($"[射击合成] 发现可合成组合 - 分组键: {group.Key}, 炮台类型: [{typeInfo}]");

                StartSynthesis(synthesisCannons);
                break; // 一次只处理一组合成
            }
        }
    }

    /// <summary>
    /// 获取DirectShotGrids中所有符合条件的炮台
    /// </summary>
    /// <returns>符合条件的炮台列表</returns>
    private List<GridConnon_Cannon> GetEligibleCannonsFromDirectShotGrids()
    {
        List<GridConnon_Cannon> eligibleCannons = new List<GridConnon_Cannon>();

        var shotGridController = GridConnon_ShotGrid_Controller.Instance;
        if (shotGridController == null || shotGridController.DirectShotGrids == null)
        {
            return eligibleCannons;
        }

        // 只检测DirectShotGrids
        foreach (var shotGrid in shotGridController.DirectShotGrids)
        {
            if (shotGrid == null || !shotGrid.IsOccupied) continue;

            // 获取格子上的炮台
            GameObject cannonObj = shotGrid.GetItem();
            if (cannonObj == null) continue;

            GridConnon_Cannon cannon = cannonObj.GetComponent<GridConnon_Cannon>();
            if (cannon == null) continue;

            // 检查是否符合合成条件
            if (IsCannonEligibleForSynthesis(cannon))
            {
                eligibleCannons.Add(cannon);
            }
        }

        return eligibleCannons;
    }

    /// <summary>
    /// 检查炮台是否符合合成条件
    /// </summary>
    /// <param name="cannon">要检查的炮台</param>
    /// <returns>是否符合条件</returns>
    private bool IsCannonEligibleForSynthesis(GridConnon_Cannon cannon)
    {
        if (cannon == null || cannon.StateManager == null) return false;

        // 检查是否处于Shooting状态
        if (cannon.StateManager.GetCurrentState() != GridConnon_Cannon_State.Shooting) return false;

        // 检查HitCount是否大于0
        if (cannon.GetCurrentHitCount() <= 0) return false;

        // 新增：检查是否有链接关系，有链接的不能合成
        if (GridConnon_LinkSystem.Instance != null && GridConnon_LinkSystem.Instance.IsInLinkGroup(cannon)) return false;

        return true;
    }

    /// <summary>
    /// 按类型和颜色对炮台进行分组
    /// 支持跨类型匹配：NormalCannon和QuestionNormalCannon可以匹配，TriangleCannon和QuestionTriangleCannon可以匹配
    /// </summary>
    /// <param name="cannons">炮台列表</param>
    /// <returns>分组结果</returns>
    private Dictionary<string, List<GridConnon_Cannon>> GroupCannonsByTypeAndColor(List<GridConnon_Cannon> cannons)
    {
        Dictionary<string, List<GridConnon_Cannon>> groups = new Dictionary<string, List<GridConnon_Cannon>>();

        foreach (var cannon in cannons)
        {
            // 获取规范化的类型用于分组
            string normalizedType = GetNormalizedTypeForSynthesis(cannon.ObjectType);
            string groupKey = $"{normalizedType}_{cannon.ColorId}";

            if (!groups.ContainsKey(groupKey))
            {
                groups[groupKey] = new List<GridConnon_Cannon>();
            }

            groups[groupKey].Add(cannon);
        }

        return groups;
    }

    /// <summary>
    /// 获取用于合成分组的规范化类型
    /// NormalCannon和QuestionNormalCannon归为同一组
    /// TriangleCannon和QuestionTriangleCannon归为同一组
    /// </summary>
    /// <param name="objectType">原始类型</param>
    /// <returns>规范化类型</returns>
    private string GetNormalizedTypeForSynthesis(GridConnon_ObjectType objectType)
    {
        switch (objectType)
        {
            case GridConnon_ObjectType.NormalCannon:
            case GridConnon_ObjectType.QuestionNormalCannon:
                return "Normal"; // NormalCannon和QuestionNormalCannon可以匹配

            case GridConnon_ObjectType.TriangleCannon:
            case GridConnon_ObjectType.QuestionTriangleCannon:
                return "Triangle"; // TriangleCannon和QuestionTriangleCannon可以匹配

            case GridConnon_ObjectType.TreasureCannon:
                return "Treasure"; // TreasureCannon单独分组

            default:
                return objectType.ToString(); // 其他类型使用原始名称
        }
    }

    /// <summary>
    /// 记录合成分组的详细信息（用于调试）
    /// </summary>
    /// <param name="groups">分组结果</param>
    private void LogSynthesisGroups(Dictionary<string, List<GridConnon_Cannon>> groups)
    {
        if (groups.Count == 0)
        {
            //Debug.Log("[射击合成] 没有找到符合条件的炮台分组");
            return;
        }

        //Debug.Log($"[射击合成] 当前分组情况（共{groups.Count}个分组）：");
        foreach (var group in groups)
        {
            var cannons = group.Value;
            string cannonDetails = string.Join(", ", cannons.Select(c => $"{c.ObjectType}(击打值:{c.GetCurrentHitCount()})"));
            //Debug.Log($"  - 分组键[{group.Key}]: {cannons.Count}个炮台 → [{cannonDetails}]");
        }
    }

    /// <summary>
    /// 开始合成过程
    /// </summary>
    /// <param name="cannons">要合成的3个炮台</param>
    private void StartSynthesis(List<GridConnon_Cannon> cannons)
    {
        if (cannons.Count != 3)
        {
            //Debug.LogError("[射击合成] 合成需要恰好3个炮台");
            return;
        }

        // 按X坐标排序，确定左、中、右
        cannons.Sort((a, b) => a.transform.position.x.CompareTo(b.transform.position.x));

        SynthesisGroup synthesisGroup = new SynthesisGroup
        {
            leftCannon = cannons[0],
            centerCannon = cannons[1],
            rightCannon = cannons[2],
            leftOriginalPos = cannons[0].transform.position,
            centerOriginalPos = cannons[1].transform.position,
            rightOriginalPos = cannons[2].transform.position,
            isActive = true
        };

        activeSynthesisGroups.Add(synthesisGroup);

        // 让三个炮台进入ShootingSynthesis状态
        foreach (var cannon in cannons)
        {
            cannon.StateManager.ChangeState(GridConnon_Cannon_State.ShootingSynthesis);
        }

        // 开始合成动画
        GridConnon_Controller.Instance.StartCoroutine(SynthesisAnimation(synthesisGroup));

        // 调试信息：显示具体的合成类型组合
        string typeDetails = $"左:{cannons[0].ObjectType}, 中:{cannons[1].ObjectType}, 右:{cannons[2].ObjectType}";
        //Debug.Log($"[射击合成] 开始合成：颜色{cannons[0].ColorId}，类型组合[{typeDetails}]");
    }

    /// <summary>
    /// 合成动画协程
    /// </summary>
    /// <param name="group">合成组</param>
    /// <returns>协程</returns>
    private IEnumerator SynthesisAnimation(SynthesisGroup group)
    {
        // 阶段1：升高
        yield return GridConnon_Controller.Instance.StartCoroutine(RisePhase(group));

        // 阶段2：合成移动
        yield return GridConnon_Controller.Instance.StartCoroutine(SynthesisPhaseAnimation(group));

        // 合成移动完成后立即处理合成逻辑
        CompleteSynthesis(group);

        // 阶段3：中间炮台下落
        yield return GridConnon_Controller.Instance.StartCoroutine(FallPhase(group));

        // 清理合成组
        FinalizeSynthesis(group);
    }

    /// <summary>
    /// 升高阶段
    /// </summary>
    /// <param name="group">合成组</param>
    /// <returns>协程</returns>
    private IEnumerator RisePhase(SynthesisGroup group)
    {
        Vector3 leftStartPos = group.leftOriginalPos;
        Vector3 centerStartPos = group.centerOriginalPos;
        Vector3 rightStartPos = group.rightOriginalPos;

        // 目标位置：左右两侧向外偏移并上升，中间垂直上升
        Vector3 leftTargetPos = leftStartPos + Vector3.up * riseHeight + Vector3.left * sideOffsetDistance;
        Vector3 centerTargetPos = centerStartPos + Vector3.up * riseHeight;
        Vector3 rightTargetPos = rightStartPos + Vector3.up * riseHeight + Vector3.right * sideOffsetDistance;

        float elapsedTime = 0f;

        while (elapsedTime < riseAnimationDuration)
        {
            if (!group.isActive) yield break;

            float t = elapsedTime / riseAnimationDuration;
            float curveValue = moveCurve.Evaluate(t);

            // 插值移动
            if (group.leftCannon != null)
                group.leftCannon.transform.position = Vector3.Lerp(leftStartPos, leftTargetPos, curveValue);

            if (group.centerCannon != null)
                group.centerCannon.transform.position = Vector3.Lerp(centerStartPos, centerTargetPos, curveValue);

            if (group.rightCannon != null)
                group.rightCannon.transform.position = Vector3.Lerp(rightStartPos, rightTargetPos, curveValue);

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        // 确保到达目标位置
        if (group.leftCannon != null)
            group.leftCannon.transform.position = leftTargetPos;
        if (group.centerCannon != null)
            group.centerCannon.transform.position = centerTargetPos;
        if (group.rightCannon != null)
            group.rightCannon.transform.position = rightTargetPos;

        //Debug.Log("[射击合成] 升高阶段完成");
    }

    /// <summary>
    /// 合成移动阶段
    /// </summary>
    /// <param name="group">合成组</param>
    /// <returns>协程</returns>
    private IEnumerator SynthesisPhaseAnimation(SynthesisGroup group)
    {
        // 左右两侧炮台移动到中间炮台位置
        Vector3 leftStartPos = group.leftCannon.transform.position;
        Vector3 rightStartPos = group.rightCannon.transform.position;
        Vector3 centerPos = group.centerCannon.transform.position;

        float elapsedTime = 0f;

        while (elapsedTime < synthesisAnimationDuration)
        {
            if (!group.isActive) yield break;

            float t = elapsedTime / synthesisAnimationDuration;
            float curveValue = moveCurve.Evaluate(t);

            // 左右炮台向中间移动
            if (group.leftCannon != null)
                group.leftCannon.transform.position = Vector3.Lerp(leftStartPos, centerPos, curveValue);

            if (group.rightCannon != null)
                group.rightCannon.transform.position = Vector3.Lerp(rightStartPos, centerPos, curveValue);

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        // 确保到达中间位置
        if (group.leftCannon != null)
            group.leftCannon.transform.position = centerPos;
        if (group.rightCannon != null)
            group.rightCannon.transform.position = centerPos;


        GridConnon_Controller.On_PlaySound?.Invoke("CNT_合成");



        //Debug.Log("[射击合成] 合成移动阶段完成");
    }

    /// <summary>
    /// 下落阶段（只处理中间炮台下落，左右炮台已被销毁）
    /// </summary>
    /// <param name="group">合成组</param>
    /// <returns>协程</returns>
    private IEnumerator FallPhase(SynthesisGroup group)
    {
        if (!group.isActive || group.centerCannon == null) yield break;

        // 中间炮台下落到原始位置
        Vector3 centerStartPos = group.centerCannon.transform.position;
        Vector3 centerTargetPos = group.centerOriginalPos;

        float elapsedTime = 0f;

        while (elapsedTime < fallAnimationDuration)
        {
            if (!group.isActive || group.centerCannon == null) yield break;

            float t = elapsedTime / fallAnimationDuration;

            // 使用渐加速曲线：t^2，开始慢后来快
            float acceleratedT = t * t * t;

            // 只处理中间炮台下落（左右炮台已被销毁）
            group.centerCannon.transform.position = Vector3.Lerp(centerStartPos, centerTargetPos, acceleratedT);

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        // 确保到达原始位置
        if (group.centerCannon != null)
            group.centerCannon.transform.position = centerTargetPos;

        //Debug.Log("[射击合成] 下落阶段完成，中间炮台已回到原位（渐加速下落）");
    }

    /// <summary>
    /// 完成合成（在合成移动阶段结束后立即调用）
    /// </summary>
    /// <param name="group">合成组</param>
    private void CompleteSynthesis(SynthesisGroup group)
    {
        if (!group.isActive) return;

        // 计算总击打值
        int totalHitCount = 0;
        if (group.leftCannon != null)
            totalHitCount += group.leftCannon.GetCurrentHitCount();
        if (group.rightCannon != null)
            totalHitCount += group.rightCannon.GetCurrentHitCount();

        // 将左右炮台的击打值加给中间炮台
        if (group.centerCannon != null && totalHitCount > 0)
        {
            group.centerCannon.AddHitCount(totalHitCount);
        }

        // 销毁左右炮台
        if (group.leftCannon != null)
        {
            GridConnon_Controller.Instance.DestroyCannonAndClearGrid(group.leftCannon);
            group.leftCannon = null; // 清空引用
        }
        if (group.rightCannon != null)
        {
            GridConnon_Controller.Instance.DestroyCannonAndClearGrid(group.rightCannon);
            group.rightCannon = null; // 清空引用
        }

        // 中间炮台进入ShootingSynthesisFinish状态
        if (group.centerCannon != null)
        {
            group.centerCannon.StateManager.ChangeState(GridConnon_Cannon_State.ShootingSynthesisFinish);
        }

        //Debug.Log($"[射击合成] 合成完成！中间炮台获得 {totalHitCount} 点击打值，左右炮台已销毁");
    }

    /// <summary>
    /// 完成合成清理工作（在下落阶段结束后调用）
    /// </summary>
    /// <param name="group">合成组</param>
    private void FinalizeSynthesis(SynthesisGroup group)
    {
        if (!group.isActive) return;

        // 从活跃列表中移除
        group.isActive = false;
        activeSynthesisGroups.Remove(group);

        //Debug.Log("[射击合成] 合成流程完全结束，合成组已清理");
    }

    /// <summary>
    /// 检查是否有正在进行的合成
    /// </summary>
    /// <returns>是否有活跃的合成</returns>
    public bool HasActiveSynthesis()
    {
        return activeSynthesisGroups.Count > 0;
    }

    /// <summary>
    /// 强制停止所有合成（用于清理）
    /// </summary>
    public void StopAllSynthesis()
    {
        foreach (var group in activeSynthesisGroups)
        {
            group.isActive = false;
        }
        activeSynthesisGroups.Clear();
        //Debug.Log("[射击合成] 所有合成已强制停止");
    }
}