Shader "GridConnon/GridConnon_UnlitColorWithQueue"
{
    Properties
    {
        _Color ("Main Color", Color) = (1,1,1,1)
    }
    SubShader
    {
        Tags 
        { 
            "RenderType"="Transparent"  // 改为透明类型
            "Queue"="Transparent"       // 默认透明队列（可手动覆盖）
            "IgnoreProjector"="True"    // 避免受投影器影响
        }
        
        // 关闭深度写入，避免不透明物体错误遮挡
        ZWrite Off
        // 混合模式（可选，如果需要半透明）
        Blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
            };

            fixed4 _Color;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                return _Color; // 直接输出颜色
            }
            ENDCG
        }
    }
}