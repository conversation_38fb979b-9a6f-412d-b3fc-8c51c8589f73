
using UnityEngine;
using System.Collections.Generic;

public static class GridConnon_InputManager
{
    private static List<GridConnon_IInputChecker> checkers = new List<GridConnon_IInputChecker>();

    public static void RegisterChecker(GridConnon_IInputChecker checker)
    {
        if (!checkers.Contains(checker))
        {
            checkers.Add(checker);
        }
    }

    public static void UnregisterChecker(GridConnon_IInputChecker checker)
    {
        checkers.Remove(checker);
    }

    public static bool Can_Connon_Click()
    {
        // 如果没有检查器，允许输入
        if (checkers.Count == 0) return true;

        // 所有检查器都必须允许输入才返回true
        foreach (var checker in checkers)
        {
            if (!checker.Can_Connon_Click())
                return false;
        }
        return true;
    }

    public static bool Can_Prop_PickOut_Click()
    {
        // 如果没有检查器，允许输入
        if (checkers.Count == 0) return true;

        // 所有检查器都必须允许输入才返回true
        foreach (var checker in checkers)
        {
            if (!checker.Can_Prop_PickOut_Click())
                return false;
        }
        return true;
    }

    public static bool Can_UnlockGrid_Click()
    {
        // 如果没有检查器，允许输入
        if (checkers.Count == 0) return true;

        // 所有检查器都必须允许输入才返回true
        foreach (var checker in checkers)
        {
            if (!checker.Can_UnlockGrid_Click())
                return false;
        }
        return true;
    }

    // 可选：清理所有检查器
    public static void ClearCheckers()
    {
        checkers.Clear();
    }
}

public interface GridConnon_IInputChecker
{
    bool Can_Connon_Click();
    bool Can_Prop_PickOut_Click();

    bool Can_UnlockGrid_Click();

}

