using System.Collections;
using System.Collections.Generic;
using UnityEngine;

//Move by Screen Reference System
public class PlayerControlPlatform : MonoBehaviour
{
    public Transform camera;
    private Rigidbody rigidbody;
    public float moveSpeed = 4;
    public float jumpForce = 200f;
    Animator anim;
    private int jumpLimit = 2;      //二段跳

    void Start()
    {
        rigidbody = GetComponent<Rigidbody>();
        anim = GetComponent<Animator>();
    }

    void FixedUpdate()
    {
        Move();
        Jump();
    }

    void Move()
    {
        float v = Input.GetAxis("Vertical");
        float h = Input.GetAxis("Horizontal");

        if (Input.GetKey(KeyCode.LeftShift))
        {
            h *= 0.5f;
            v *= 0.5f;
        }

        anim.SetFloat("SpeedX", h);
        anim.SetFloat("SpeedY", v);

        Vector3 screenRight = camera.right;             //以屏幕为参考系移动
        Vector3 screenForward = camera.forward;
        screenForward.y = 0;                            //不能有竖直分量

        Vector3 sumVector = screenForward * v + screenRight * h;                //矢量之和

        if (!(h == 0 && v == 0))
        {
            transform.rotation = Quaternion.LookRotation(sumVector);
        }
        transform.Translate(sumVector * moveSpeed * Time.deltaTime, Space.World);       //Space.World绝对不能少
    }

    bool IsGrounded()                   // 通过射线检测角色是在地面或者物体(角色的零点需要设置在脚底处)
    {
        return Physics.Raycast(transform.position, -Vector3.up, 0.1f);
    }

    void Jump()
    {
        if (IsGrounded())                       //如果接触地面，则恢复可跳跃次数
        {
            jumpLimit = 2;
            anim.SetBool("Jump", false);
        }

        if (Input.GetKeyDown(KeyCode.Space))
        {
            if (jumpLimit > 0)
            {
                rigidbody.AddForce(Vector3.up * jumpForce);
                anim.SetBool("Jump", true);
                jumpLimit--;
            }
        }
    }
}
