using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System;
using System.Text.RegularExpressions;
using UnityEngine.Rendering;


namespace QGMiniGame
{
    public class QGHelperWindow : EditorWindow
    {

        private string textStr = "构建参数说明：\r\n" +
                                 "1、使用该插件请先阅读VIVO-GAME-SDK/Doc中的使用文档《unity游戏适配基础教程（插件版）》。\r\n" +
                                 "2、自定义Loading地址：非必填项，wasm文件zip包网络地址，详见使用文档《unity游戏适配自定义loading教程》。\r\n" +
                                 "3、Addressable地址：非必填项，资源压缩网络地址，详见使用文档《unity游戏适配Addressable教程》。\r\n\r\n" +
                                 "构建错误说明：\r\n" +
                                 "1、当前一键打包插件仅适用Windows和Mac系统，如果遇到打包失败，可以选择手动构建，详见文档《unity游戏适配基础教程-手动版》。\r\n" +
                                 "2、当构建时提示 请先npm install ，请参考《unity游戏适配基础教程(插件版)》 构建环境配置一节。";


        private void OnGUI()
        {
            var inputStyle = new GUIStyle(EditorStyles.label);
            inputStyle.fontSize = 14;
            inputStyle.margin.left = 20;
            inputStyle.margin.top = 10;
            inputStyle.margin.right = 20;
            GUILayout.Label(textStr, inputStyle);
        }
    }

}
