using System;
using System.Reflection;

[assembly: AssemblyTitle("SharpZipLib")]
[assembly: AssemblyDescription("A free C# compression library")]
[assembly: AssemblyDefaultAlias("ICSharpCode.SharpZipLib")]
[assembly: AssemblyCulture("")]

[assembly: CLSCompliant(true)]

// If SharpZipLib is strongly named it still allows partially trusted callers
[assembly: System.Security.AllowPartiallyTrustedCallers]
