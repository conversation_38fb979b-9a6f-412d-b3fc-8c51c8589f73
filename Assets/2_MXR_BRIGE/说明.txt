-Unity�汾�ţ�2019.4.10f1

-Ŀ¼
1.0_Game�������Ϸ��ض�������
2.1_Frame��������Ҫ�õ����ɱ����SDK���룬�������
3.2_2_MXR_BRIGE�� ��Ҫ�Žӿ��

-MXR_BRIGEĿ¼
1.MXR_BRIGE�������Žӽӿڶ�������
2.Resources������Ž��õ����ز�
3.�汾��
4.˵��

-����
1.Ϊ�����滻����������ͳһʹ�� 1_FRAME/0_SDK_Eight/FrameRes/Fonts/Soul

-��������
1.MXR_BRIGE_Cos_GameSetting����Ҫ�������õ����ݶ����� ��Ҫ��������Ҫ�������ñ䶯�Ĳ������������Ѫ����
2.2_MXR_BRIGE/Resources/BRIGE_GameSetting���� MXR_BRIGE_Cos_GameSetting ��Ӧ�ı���json�ļ��������ڱ��ز���ʹ��
3.MXR_BRIGE.Cos_GameSetting��ͨ���������ȥ����������ò���

-���
1.MXR_BRIGE.AD_ShowReward�����ü�����Ƶ���ۿ���ɻ�ý��������ɹ��Ľ����ص���ȥ

-�洢����
1.�滻Unity��Ĵ洢���� ������� Playerprefs_SetInt �Ⱥ���

-��������
1.������������Ҫ���ã���Һ���Ϸ���д��� ���������CommondData_�Ⱥ���

-����
1.���λ�ã�ͳһ���� 0_GAME/Resources/Sound
2.��Ч���ţ�MXR_BRIGE.Sound_PlayEffect������Ϊ����
3.�������֣�����λ���£�����ΪBGM���������

-��
1.���� ���ڴ��֮�� MXR_BRIGE.Virb_Short
2.���� ����ͨ��֮�� MXR_BRIGE.Virb_Long

-ͨ����ʾ
1.ͨ�õ���Ϣ��ʾ������ MXR_BRIGE.MessageTip_Show

-�ڵ��¼�
1.��Ŀ��λ�õ���Ŀ�꺯��
2.�����ʼ��Ϸ��ťʱ���� MXR_BRIGE.Action_OnStartGameButtonClick������Ϊִ����ص�
3.��Ϸ����ʱ���� MXR_BRIGE.Action_OnGameOver������Ϊִ����ص�
4.������������水ťʱ���� MXR_BRIGE.Action_OnReturnMainMenuClick������Ϊִ����ص�
5.��Ϸ�ڼ�ʱ���� MXR_BRIGE.Action_OnGamingTimeCount������Ϊ��Ϸ����
6.���볡���ĵ���

-����
1.����λ�ã�0_GAME/Scene��� 1_START��Ӧ��ʼ���� 2_GAME��Ӧ��Ϸ��������� 3_END��Ӧ��������
2.���Ӵ��룺0_GAME/Scripts �����������UI�������
3.ִ��1_START�������ɿ�ʼ���ӳ���