using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BRIGE_MessageTip
{
    static GameObject Prefab_Tip
    {
        get
        {
            if (prefab_Tip == null)
                prefab_Tip = Resources.Load<GameObject>("BRIGE_MessageTip");
            return prefab_Tip;
        }
    }

    static GameObject prefab_Tip;

    public static float DestroyY = 150;
    public static float CreateY = -150;

    public static float MoveSpeed = 600;
    public static float MoveAddingSpeed = 50;

    public static float AlphaSpeed = 5;
    public static float AplhaAddingSpeed = 0.5f;

    public static float StopTime = 1;

    public static float Scale = 1.2f;

    static BRIGE_MessageTip_Item lastTip = null;

    public static void ShowMessageTip(string text)
    {
        if(lastTip!=null)
        {
            GameObject.Destroy(lastTip.gameObject);
            lastTip = null;
        }
       
        var canvas =  GameObject.Find("Canvas");
        BRIGE_MessageTip_Item tip = GameObject.Instantiate(Prefab_Tip, canvas.transform).GetComponent<BRIGE_MessageTip_Item>();
        tip.rtrans.localPosition = new Vector3(tip.rtrans.localPosition.x, CreateY, tip.rtrans.localPosition.z);
        tip.rtrans.localScale = new Vector3(Scale, Scale, Scale);
        tip.txtMessage.text = text;
        tip.currentDestroyY = DestroyY;

        lastTip = tip;
    }


}
