
using LitJson;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

//Define Tag ：MXR_BRIGE
public class MXR_BRIGE
{
 

    #region 在线配置
    public static MXR_BRIGE_Cos_GameSetting Cos_GameSetting
    {
        get
        {
            if (Cos_gameSetting == null)
            {
#if !MXR_BRIGE
                if (SDK_Common_Main.isNET)
                    Cos_gameSetting = SDK_Common_COSData.Instance.GetTrapDataToObject<MXR_BRIGE_Cos_GameSetting>("GameSetting");
                else
                    Cos_gameSetting = JsonMapper.ToObject<MXR_BRIGE_Cos_GameSetting>(JsonMapper.ToObject(Resources.Load<TextAsset>("BRIGE_GameSetting").ToString())["GameSetting"].ToJson());

#else
                Cos_gameSetting = JsonMapper.ToObject<MXR_BRIGE_Cos_GameSetting>(JsonMapper.ToObject(Resources.Load<TextAsset>("BRIGE_GameSetting").ToString())["GameSetting"].ToJson());
#endif
            }

            return Cos_gameSetting;
        }
    }

    static MXR_BRIGE_Cos_GameSetting Cos_gameSetting = null;

    public static string Cos_GameSettingJson
    {
        get
        {
            if (Cos_gameSettingJson == null)
            {
#if !MXR_BRIGE
                if (SDK_Common_Main.isNET)
                    Cos_gameSettingJson = SDK_Common_COSData.Instance.TrapSettingJsonStr;
                else
                    Cos_gameSettingJson = Resources.Load<TextAsset>("BRIGE_GameSetting").ToString();

#else
                Cos_gameSettingJson = Resources.Load<TextAsset>("BRIGE_GameSetting").ToString();
#endif
            }

            return Cos_gameSettingJson;
        }
    }

    static string Cos_gameSettingJson = null;



    #endregion

    #region 广告
    //展示激励视频 参数：看完视频领取奖励回调    
    public static void AD_ShowReward(Action finish)
    {

#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            SDK_Common_AD.ShowReward(finish, () => { }, () => { }, 0);
        else
            finish();
#else 
        finish();
#endif
    }
    #endregion

    #region 存储数据
    //存储数据
    public static void Playerprefs_SetInt(string key, int value)
    {
#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            SDK_Common_Playerprefs.SetInt(key, value);
        else
            UnityEngine.PlayerPrefs.SetInt(key, value);
#else
        UnityEngine.PlayerPrefs.SetInt(key, value);
#endif
    }

    public static int Playerprefs_GetInt(string key, int value)
    {

#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            return SDK_Common_Playerprefs.GetInt(key, value);
        else
            return UnityEngine.PlayerPrefs.GetInt(key, value);
#else
        return UnityEngine.PlayerPrefs.GetInt(key, value);
#endif
    }

    public static void Playerprefs_SetString(string key, string value)
    {
#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            SDK_Common_Playerprefs.SetString(key, value);
        else
            UnityEngine.PlayerPrefs.SetString(key, value);
#else
        UnityEngine.PlayerPrefs.SetString(key, value);
#endif
    }

    public static string Playerprefs_Getstring(string key, string value)
    {

#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            return SDK_Common_Playerprefs.GetString(key, value);
        else
            return UnityEngine.PlayerPrefs.GetString(key, value);
#else
        return UnityEngine.PlayerPrefs.GetString(key, value);
#endif
    }

    #endregion

    #region 共用数据

    public static void CommondData_SetCurrentCoin(int value)
    {
        Playerprefs_SetInt("BRIGE_GameCoin", value);
    }

    public static int CommondData_GetCurrentCoin(int defaultCoin = 0)
    {
        return Playerprefs_GetInt("BRIGE_GameCoin", defaultCoin);
    }

    public static void CommondData_AddCoin(int count, int defaultCoin = 0)
    {
         Playerprefs_SetInt("BRIGE_GameCoin", CommondData_GetCurrentCoin(defaultCoin) + count);
    }

    public static void CommondData_UseCoin(int count, int defaultCoin = 0)
    {
        Playerprefs_SetInt("BRIGE_GameCoin", CommondData_GetCurrentCoin(defaultCoin) - count);
    }

    public static int CommondData_GetGameRoundCount()
    {
        return Playerprefs_GetInt("BRIGE_GameRound", 0);
    }

    public static void CommondData_AddGameRound()
    {
        Playerprefs_SetInt("BRIGE_GameRound", CommondData_GetGameRoundCount() + 1);
    }

    public static void CommondData_SetGameRound(int value)
    {
        Playerprefs_SetInt("BRIGE_GameRound", value);
    }
    #endregion

    #region 声音
    static GameObject soundBrige;
    static AudioSource[] m_effectSound;
    static int effectCount = 10;
    static int currentEffectIndex = 0;

    //音效文件统一放在 0_GAME/Resources/Sound
    public static void Sound_PlayEffect(string audioName)
    {


#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            SDK_Common_Sound.Instance.PlayEffect(audioName);
        else
        {
            string path;

            path = "Sound" + "/" + audioName;

            AudioClip clip = Resources.Load<AudioClip>(path);

            if (soundBrige == null)
            {
                soundBrige = new GameObject("BRIGE_SOUND");
                m_effectSound = new AudioSource[effectCount];
                for (var i = 0; i < m_effectSound.Length; i++)
                {
                    m_effectSound[i] = soundBrige.AddComponent<AudioSource>();
                }
            }


            m_effectSound[currentEffectIndex].clip = clip;
            m_effectSound[currentEffectIndex].Play();

            currentEffectIndex++;
            if (currentEffectIndex == effectCount)
                currentEffectIndex = 0;
        }
#else
        string path;

        path = "Sound" + "/" + audioName;

        AudioClip clip = Resources.Load<AudioClip>(path);

        if (soundBrige == null)
        {
            soundBrige = new GameObject("BRIGE_SOUND");
            m_effectSound = new AudioSource[effectCount];
            for (var i = 0; i < m_effectSound.Length; i++)
            {
                m_effectSound[i] = soundBrige.AddComponent<AudioSource>();
            }
        }


        m_effectSound[currentEffectIndex].clip = clip;
        m_effectSound[currentEffectIndex].Play();

        currentEffectIndex++;
        if (currentEffectIndex == effectCount)
            currentEffectIndex = 0;
#endif


    }

    #endregion

    #region 震动

    //长震动 用于通关之类
    public static void Virb_Short()
    {


#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            SDK_Common_Vib.VirbShort();
        else
            Debug.Log("MXR_BRIGE ：Virb_Short");
#else
        Debug.Log("MXR_BRIGE ：Virb_Short");
#endif

    }

    //短震动 用于打击之类
    public static void Virb_Long()
    {


#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            SDK_Common_Vib.VirbLong();
        else
            Debug.Log("MXR_BRIGE ：Virb_Long");
#else
        Debug.Log("MXR_BRIGE ：Virb_Long");
#endif

    }

    #endregion

    #region 通用提示

    //通用的消息提示
    public static void MessageTip_Show(string text)
    {

#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            Tool_MessageTip.ShowMessageTip(text);
        else
            BRIGE_MessageTip.ShowMessageTip(text);
#else
        BRIGE_MessageTip.ShowMessageTip(text);
#endif

    }

    #endregion

    #region 节点事件回调

    public static Action<Action> act_OnStartGameButtonClick;
    public static Action<Action> act_OnGamingStartGame;
    public static Action<Action> act_OnReturnMainMenuClick;
    public static Action<Action> act_OnGameOver;

    public static Action act_OnEnterStartScene;
    public static Action act_OnEnterGameScene;
    public static Action act_OnEnterEndScene;

    //节点 点击开始游戏 参数：原执行操作
    public static void Action_OnStartGameButtonClick(Action callBack)
    {
#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            act_OnStartGameButtonClick?.Invoke(callBack);
        else
        {
            Debug.Log("MXR_BRIGE ：Action_OnStartGameButtonClick");
            callBack();
        }
#else
        Debug.Log("MXR_BRIGE ：Action_OnStartGameButtonClick");
        callBack();
#endif
    }

    //节点 游戏局内开始游戏 参数：原执行操作
    public static void Action_OnGamingStartGame(Action callBack)
    {
#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            act_OnGamingStartGame?.Invoke(callBack);
        else
        {
            Debug.Log("MXR_BRIGE ：Action_OnGamingStartGame");
            callBack();
        }
#else
        Debug.Log("MXR_BRIGE ：Action_OnGamingStartGame");
        callBack();
#endif
    }

    //节点 返回主菜单点击 参数：原执行操作
    public static void Action_OnReturnMainMenuClick(Action callBack)
    {
#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            act_OnReturnMainMenuClick?.Invoke(callBack);
        else
        {
            Debug.Log("MXR_BRIGE ：Action_OnReturnMainMenuClick");
            callBack();
        }
#else
        Debug.Log("MXR_BRIGE ：Action_OnReturnMainMenuClick");
        callBack();
#endif
    }
    //节点 游戏结束 参数：原执行操作
    public static void Action_OnGameOver(Action callBack)
    {
#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            act_OnGameOver?.Invoke(callBack);
        else
        {
            Debug.Log("MXR_BRIGE ：Action_OnGameOver");
            callBack();
        }
#else
        Debug.Log("MXR_BRIGE ：Action_OnGameOver");
        callBack();
#endif
    }


    //节点 进入开始场景 参数：是否竖屏 横屏为false
    public static void Action_OnEnterStartScene()
    {
#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            act_OnEnterStartScene?.Invoke();
        else
        {
            Debug.Log("MXR_BRIGE ：Action_OnEnterStartScene");
        }
#else
        Debug.Log("MXR_BRIGE ：Action_OnEnterStartScene");
#endif
    }

    //节点 进入游戏场景 参数：是否竖屏 横屏为false
    public static void Action_OnEnterGameScene()
    {
#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            act_OnEnterGameScene?.Invoke();
        else
        {
            Debug.Log("MXR_BRIGE ：Action_OnEnterGameScene");
        }
#else
        Debug.Log("MXR_BRIGE ：Action_OnEnterGameScene");
#endif
    }

    //节点 进入结束场景 参数：是否竖屏 横屏为false
    public static void Action_OnEnterEndScene()
    {
#if !MXR_BRIGE
        if (SDK_Common_Main.isNET)
            act_OnEnterEndScene?.Invoke();
        else
        {
            Debug.Log("MXR_BRIGE ：Action_OnEnterEndScene ");
        }
#else
        Debug.Log("MXR_BRIGE ：Action_OnEnterEndScene ");
#endif
    }

    #endregion


    //在线数据配置 如关卡配置等
    public class MXR_BRIGE_Cos_GameSetting
    {
        public Tool_CountlySendEvent_ConstData Tool_CountlySendEvent_ConstData;


        public GridConnon_Const_Data GridConnon_Const_Data;
        public TileCube_Const_Data TileCube_Const_Data;
        public MatchUI_Const_Data MatchUI_Const_Data;
        
        public int DefaultCoin;
    }


}

