-- <PERSON><PERSON> is pleased to support the open source community by making xLua available.
-- Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
-- Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
-- http://opensource.org/licenses/MIT
-- Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.

local util = require 'xlua.util'

local sync_alert = util.async_to_sync(CS.XLuaTest.MessageBox.ShowAlertBox)
local sync_confirm = util.async_to_sync(CS.XLuaTest.MessageBox.ShowConfirmBox) 

--构造alert和confirm函数
return {
    alert = function(message, title)
		 sync_alert(message, title)
    end;
	
	confirm = function(message, title)
		local ret = sync_confirm(message, title)
		return ret == true
    end;
 }











