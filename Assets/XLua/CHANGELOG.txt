v2.1.15 2020年6月24日
新增特性
1、生成代码过滤器
2、优化反射查找delegate匹配bridge的性能
3、unity 2019.2以上版本手机版本注入不了的问题

变更

bug修复
1、反射查找同名delegate桥接在不生成代码的时候表现不一致
2、嵌套struct标注为PackAsTable时生成代码报错
3、反射wrap代码加入栈空间检查
4、如果枚举定义了很多个值（几千个），会触发unity在android下的一个bug：函数体很大而且有很多分支，执行该函数会crash
5、chunkname和脚本文件名不一致的问题
6、最小生成模式枚举生成代码报错
7、当采用反射方式注册枚举值时，如果一个枚举有多个相同的值，比如A,B都是1，那么在lua里头访问B将会为空
8、sbyte[]在.net 4下push到lua变成字符串的问题
9、泛型导致生成代码失败的问题
10、非Assembly-CSharp程序集注入时，out参数处理有误
11、内嵌类通过xlua.private_accessible设置私有访问可能失败的问题
12、cecil插入指令后，并未自动更新offset，某种情况下会导致计算偏移量错误


v2.1.14 2019年2月27日
新增特性
1、新增nintento switch的支持
2、unity 2018兼容
3、android arm64支持
4、原生库的visual studio 2017编译支持
5、增加“XLua/Generate Minimize Code”菜单
6、防止有的工程有非法的dll导致生成代码中断
7、更高效的lua_pushstring（需要通过NATIVE_LUA_PUSHSTRING开启）

变更
1、window库默认编译器改为visual studio 2017

bug修复
1、修正枚举类型如果只加GCOptimize不加LuaCallCSharp会crash的问题
2、示例配置加入对Edtitor类的过滤
3、UWP兼容修复
4、接口继承引入的同签名方法实现
5、未生成代码，extension方法行为不一致
6、修复Nullable类型参数，如果最后一个参数是nil，会导致其他参数全是nil的问题


v2.1.13 2018年12月5日
新增特性
1、新增AdaptByDelegate注入模式；
2、新增xlua.get_generic_method，用于调用泛型函数；
3、支持类似CS.System.Collections.Generic.List(CS.System.Int32)的泛型写法；
4、注入新选项：忽略编译器自动生成代码，以及不生成base代理；
5、针对lua编程以及热补丁，均添加直接可用的自动化配置样例；
6、新增luajit的gc64支持；
7、加入兼容字节码（一份字节码支持32位和64位系统）的支持；
8、内置新lua内存泄漏检测工具；
9、delegate桥接动态实例化：delegate是4个参数以内，参数均引用类型，无返回值或者返回引用类型，不用配置CSharpCallLua也能调用lua函数；
10、提供util.print_func_ref_by_csharp函数，用于查看当前被C#引用的lua函数；
11、支持无CS全局变量的工作方式；


变更
1、虚拟机升级：lua5.3.4 -> lua5.3.5，luajit2.1b2 -> luajit2.1b3；
2、delegate bridge代码段占用优化；
3、改为PostProcessBuild事件检查是否生成代码；
4、适配xcode 10：osx平台不再支持32bit版本构建；
5、名字空间、类名拼写错误时，对静态成员的设置会报错；
6、防止CS全局table被删除导致xlua工作异常；
7、Windows下构建lib，若使用vs 2015参数执行cmake失败，则继续尝试使用vs 2017；
8、编辑器下不生成代码时，也检查Blacklist，维持和运行时一致；

bug修复
1、泛型的数组生成代码报错；
2、防止对TypeExtensions配置了LuaCallCSharp后，lua里头IsValueType之类的判断永真；
3、生成代码过滤掉含指针的函数和字段；
4、适应索引器属性名不是Item的情况；
5、解决attribute初始化异常会导致生成代码，注入终止的问题；
6、精简模式下空Enum生成代码错误；
7、通过把初始化函数分割成小函数，规避unity在android下执行大函数crash的bug；
8、Assignable处理obj为null情况；
9、内嵌类不Obsolete，但外层类Obsolete的生成代码报错
10、解决inline注入方式下，如果lua逻辑跑异常，看不到异常信息的问题；
11、修复xlua.private_accessible访问后，同名public的方法无法访问的Bug；
12、[Out]修饰的参数不应该生成out关键字；
13、通过反射查找合适的适配器时，有可能访问到非适配器函数；
14、精简模式导出代码无get_Item、set_Item；
15、IntKey方式下不自动xlua.private_accessible的问题；


v2.1.12 2018年7月9日
新增特性
1、Nullable的支持
2、支持Assembly-CSharp之外的dll注入（beta）
3、执行xlua.hotfix，会自动让该类private能访问
4、xlua.private_accessible优化：1、会把基类的也设置能私有访问；2、延迟到第一次访问类才私有化
5、新增xlua.util.state，可为一个c#对象新增状态
6、this[string field]或者this[object field]操作符重载新增get_Item和set_Item调用
7、正在编译时注入打印error信息
8、interface配置到CSharpCallLua时的事件跟索引映射的自动实现
9、unity5.5以上去掉WARNING: The runtime version supported by this application is unavailable打印

变更
1、去除Stateful方式（因为xlua.util.state已经可以达成类似的效果）
2、废弃掉内嵌模式模式

bug修复
1、生成代码局部变量加下划线，防止符号冲突
2、如果类没放到Hotfix列表，不生成base调用代理
3、代码重构，可读性优化
4、解决带params byte[]可能会导致生成代码编译错误的问题
5、解决类含有private event的时候，无法xlua.private_accessible的问题
6、构造函数注入，如果branch外紧跟Ret指令，注入逻辑应该在branch以及Ret之间
7、构造函数注入，如果注入指令后导致跳转范围大于一个字节，应修改为长跳转
8、解决一个delegate如果不是某个类的内嵌类型时，CS.namespace.classname为空的问题
9、防止Editor下的Util类名字冲突
10、泛型override有异常，先过滤掉
11、解决空enum导致生成代码编译错误
12、解决uwp平台下il2cpp方式打包无法访问任何类的问题
13、hotfix一个私有类型的params参数的函数，导致生成代码编译错误、注入失败的问题
14、如果两个LuaBase指向的是同一个Lua对象，GetHashCode应该返回的是同一个值
15、[Out]标记参数生成代码编译失败
16、交错数组+多维数组的复合，生成代码报错的问题

v2.1.11 2018年3月20日
新增特性
1、xlua.private_accessible支持私有内嵌类型
2、添加xlua.release，用于主动解除lua对c#某对象的引用
3、支持内嵌委托的显示构造
4、需要传class的地方（比如xlua.private_accessible），支持传C#的Type对象
5、支持用pairs遍历IEnumerable对象
6、热补丁场景下，支持override函数调用被override函数（对应c# base关键字）

变更
1、简化property的反射访问，简化后有更好的兼容性；

bug修复
1、ios 11兼容（去除system调用）
2、实现了interface的struct不走gc优化代码的问题
3、emit特性的.net兼容性
4、emit对于ulong的const值处理不当
5、interface桥接代码，interface继承时，父interface和子interface有同名不同类型属性时的生成代码报错
6、多虚拟机下，不断创建和销毁协程时，可能出现协程指针重复
7、当参数为泛型类型时，如ICollectio时，不应该生成代码

v2.1.10 2017年9月18日
新增特性
1、新增DoNotGen配置，支持一个类型部分函数用反射，部分用生成；
2、新增wrapper的emit；
3、webgl支持；
4、lua实现interface支持interface继承；
5、window下支持android编译（由xdestiny110提供）；
6、打包时，如果没执行过“Generate Code”将报错；

变更
1、	async_to_sync的改为resume错误时报错；
2、il2cpp下，暂时去掉泛型的反射调用；
3、升级到lua5.3.4并合入2017-9-1为止所有官方patch；

bug修复
1、C#仅声明delegate和MulticastDelegate，通过反射创建lua function映射时crash；
2、解决一些古老版本window（比如xp）的dll兼容问题；

v2.1.9 2017年8月10日
新增特性
1、新增最小生成模式（通过GEN_CODE_MINIMIZE切换），可以节省50%的text段空间；
2、新增xlua.util.createdelegate，支持在lua直接用C#函数创建delegate而不需要通过lua适配；
3、xlua.private_accessible支持public int Prop { get; private set; }
4、新增	xlua.getmetatable、xlua.setmetatable、xlua.setclass、xlua.genaccessor，用以支持lua使用C#类型直接在lua侧完成；
5、反射下扩展方法的支持；
6、lua53版本支持位操作符重载：C#侧的位操作符重载对应到lua的位操作符重载；enum全部加上&和|位操作符；

工程优化
1、加入travis持续集成；

变更
1、LuaCallCSharp自动去除匿名类型；
2、THREAD_SAFT改为THREAD_SAFE；
3、GenFlag.GCOptimize标记为过时；
4、删除过时的GenConfig配置方式；

bug修复
1、window phone下一些系统api是禁用的，源码中去掉；
2、泛型约束是struct的时候，生成代码失败；
3、unity2017 .net 4.6，枚举生成代码报错；

v2.1.8 2017年6月27日
新增特性
1、Hotfix标签添加几个订制参数：ValueTypeBoxing、IgnoreProperty、IgnoreNotPublic、Inline、IntKey
2、Hotfix代码注入优化，减少text段占用；
3、Hotfix配置支持放Editor目录，可以减少text段占用；
4、支持以指定类型传递object参数；
5、反射调用Obsolete方法在Editor下打印warning；

变更

bug修复
1、pinvoke独立设置的In，Out属性可能导致生成代码失败；
2、如果业务在全局名字空间有和xLua名字空间的同名类，生成代码编译失败；

v2.1.7 2017年5月17日
新增特性
1、支持发布UWP（含HoloLens，Xbox one，Win10 Mobile、Win10 PC）应用；
2、支持对lua源代码ras+sha1签名；
3、如果没安装Tools提示“please install the Tools”；
4、linxu版本的支持；
5、支持bitcode打包；
6、对所有struct新增无参数构造函数；
7、delegate的参数名改为p0到pn，防止hotfix时业务代码变量和生成代码冲突；
8、支持对成员名为C#关键字的情况；
9、新增util.loadpackage，和require类似，通过searcher加载文件，不同的是，它不执行，而且也不会cache到package.loaded；
10、优化模版引擎大文件的生成性能；
11、新增不需要生成代码的注入方式；
12、支持构造函数参数带ref和out修饰符；
13、构造函数也支持黑名单排除；

变更
1、this[object field]操作符重载；
2、反射的数据转换规则改成和生成代码一致；
3、忽略掉匿名类及匿名函数的注入；

bug修复
1、规避Unity的bug：List<CustomType>，CustomType是当前执行程序集的类型，这在.Net是不需要指明程序集就可以通过Type.GetType得到，但Unity下不行。
2、解决反射下，可变参数不提供时，传null的问题；
3、继承了另外一个程序集的类型，使用了protected类型会导致注入失败；
4、luajit去掉dlopen和dlsym的调用；
5、解决通用版本的生成代码工具找不到模版的问题；
6、修复通用版本反射导入泛化类型的问题；
7、反射调用含delegate参数的的api，会因为缓存而导致调用LuaEnv.Dispose失败；
8、兼容老版本的C编译器，声明要放开头；
9、生成代码对hotfix的检测算法和注入工具不一致导致的注入失败；
10、注入的nested类型是public，但其的外层类型非public，生成代码报错；
11、析构函数只判断名字可能出现误判；
12、构造函数是非public的，可能会导致找不到适配delegate而注入失败；
13、修正Extension method会在所有子类都生成代码的bug（2.1.6泛化特性引入）；
14、构造函数重载，只有一个能hotfix成功；
15、规避一个可能是il2cpp的bug（unity5.4）：字符串参数默认值是""，ios下在反射的default value也是Reflection.Missing；
16、将一个table传到List<>，取了最后一个参数，而不是那个table的长度；
17、ldarg指令在这种场景下il2cpp转换时会出现异常：1、采用模版注入；2、从4到255间有一个输出参数；改为兼容性更好的ldarg.s；
18、解决配置了System.Delegate到CSCallLua，执行生成代码会编辑器会crash的问题；
19、扩展函数可能和原来的函数同名，反射实现并未考虑到这种情况；
20、通用版本的可变参数delegate调用异常；
21、unity4规避lua53冲突的方式改为返回null更合适，异常方式会导致IsNull无法正常工作；
22、lua_tostring解码失败改为UTF8解码；

v2.1.6 2017年3月1日
新增特性
1、带约束的泛型支持（by forsakenyang）；
2、非Unity的.net环境支持；
3、代码注入支持小工具方式，该方式不用拷贝cecil库，可以解决拷错cecil库版本或者和Unity，VS插件冲突的问题；
4、Hotfix配置支持字段和属性
5、更方便的Unity协程hotfix
6、在hotfix触发事件；
7、LuaTable添加ForEach方法以及Length属性；
8、cmake生成项目优化：保留源文件目录结构；
9、对已经Dispose的LuaEnv的访问做保护；Dispose时检查callback是否已经都释放，没释放的话报错；
10、支持释放Hotfix回调；

变更
1、构造函数改为执行原有逻辑后调用lua；
2、this[string field]操作符重载会影响到继承调用，去掉该特性的支持；
3、编辑器下的代码注入改为手动方式；

bug修复
1、防止定义了同时定义get_xx方法以及xx属性的生成代码的重名。
2、struct注入代码无效；
3、Utils加名字空间，防止和业务冲突；
4、返回定长多维数组的delegate，生成代码可能会冲突；
5、interface，以及编辑器下不生成代码情况下，对可变参数的展开；
6、il2cpp下，如果不生成代码，会报ManifestModule不支持；
7、规避Unity4的bug：访问一个已经被Distroy的UnityEngine.Object，编辑器下会崩溃，这个问题在Unity5，或者luajit版本都不会出现；
8、修改上个版本引入的问题：xlua_setglobal会漏一个值在栈上，这会导致一些32位应用不稳定；
9、当delegate参数只有ref和out的区别的话，报重载冲突；

v2.1.5 2017年1月13日

新增特性
1、全平台热补丁；
2、新增线程安全模式，可通过THREAD_SAFT宏打开；
3、新增更简便的配置方式，具体参见XLua\Doc下《XLua的配置.doc》；
4、多虚拟机实例时的自动Dispose；
5、内存优化：减少匿名闭包到delegate映射的内存占用；减少LuaFunction以及LuaTable内存占用；减少lua table映射C#interface的gc；
6、生成代码速度优化；
7、支持直接在lua侧clone C#结构体；
8、LuaFunction新增无gc调用api；

变更
1、delegate必须都加[CSharpCallLua]才支持C#到lua的回调（以前参数和返回值都相同的delegate只要其中一个加了就可以）；
2、加回string/number到枚举的自动转换；

bug修复
1、枚举不生成代码时，第一次使用会产生两个不同的userdata；
2、数组和System.Type的相互引用导致System.Type生成代码无法加载；
3、更安全的异常处理，封装lua_setglobal,lua_getglobal的异常，C#回调保证所有C#异常都catch并转换到成lua error。


v2.1.4 2016年11月29日
新增特性
1、加了ReflectionUse会自动生成到link.xml，可以防止il2cpp下因stripping导致的反射不可用；
2、开放生成引擎，可二次开发自己生成插件，生成所需的代码或配置；
3、GetInPath和SetInPath无C# gc优化；
4、一个lua table自动转换为带GCOptimize标签的复杂类型以及该复杂类型的一维数组不使用反射，如果这复杂类型是纯值类型，无c# gc；

变更
1、基于一致性以及性能的考虑，不支持数字和字符串到枚举的静默转换，须主动调用起类下的__CastFrom；
2、名字空间从LuaInterface改为XLua；
3、LuaTable的几个可能导致gc的api标注为Obsolete；
4、在不指明返回类型的情况下，如果一个number是整数会优先转换成整数；

bug修复
1、含能隐式转换int，long，decimal的类型传到lua变成decimal；
2、反射的重载判断，如果可变参数的位置上是一个不匹配的参数，也会判断为匹配成功；
3、可变参数+重载的话，可变部分不传会报无效参数；
4、加了LuaCallCSharp的Extension method，在Editor下不生成代码不可用；

v2.1.3 2016年11月09日
新增特性
1、LuaTable新增Get<TKey, TValue>和Set<TKey, TValue>接口，table操作支持值类型无gc；
2、支持decimal，不丢失精度而且传递到lua无gc；
3、增加LuaEnv.LoadString<T>接口，用于指定返回的delegate类型；
4、例子刷新：新增Helloworld，无GC调用，Lua面向对象，协程例子；
5、enum优化：传递到lua无gc，从int或者string到枚举转换无gc；
6、event的+/-优化：性能提升一倍，而且无gc；
7、生成代码简化；

变更
1、uint在lua53映射到lua_Integer；
2、StreamingAssets加载改为优先级最低；

bug修复
1、生成代码下，如果LuaTable或者LuaFunction参数为null会抛异常；
2、lua5.3下，浮点到枚举的静默转换失败；
3、反射下struct类型参数带默认值抛异常；
4、lua53下Length返回浮点；

v2.1.2 2016年10月08日
新增特性
1、支持lua5.3，进而支持苹果bitcode，原生64位整数，位运算，utf8等特性；
2、CMake编译，更方便加入第三方插件
3、数组性能优化，包括访问性能以及gc
4、C#调用lua函数减少一次lua gc；
5、优化启动时间；
6、减少类型加载的gc；
7、优化ObjectPool的内存占用；
8、优化小字符串传入lua的gc；
9、LuaTable添加Cast接口，用于LuaTable到其它类型的转换，比如interface；
10、LuaFunction添加Cast接口，用于LuaFunction到delegate的转换；

变更
1、lua内部只有带符号的64整数类型，并增加无符号数库
2、如果不想对Extension Method生成代码，又希望在反射下用，需要添加ReflectionUse；

bug修复
1、对ObjectPool已经Destroy的UnityEngine.Object的引用自动解除功能的内存泄漏问题；
2、规避某些版本（已知是5.3.3）的Unity的bug导致的内存泄漏问题；
3、LuaTable或者LuaFunction做返回值的delegate生成代码可能报错；

v2.1.1 2016年08月29日
新增特性
1、支持编辑器下不用生成代码能运行；
2、新增IntPtr的支持
3、增加对ObjectPool已经Destroy的UnityEngine.Object的引用自动解除；
4、在LuaEnv添加对lua_gc一些封装；

bug修复
1、生成代码传送一个LuaFunction、LuaTable到lua和反射版本不一致，生成代码传送过去是一个C#对象，而反射是Lua函数、table对象，反射的处理更合适；
2、修复同名的静态以及成员方法冲突的问题；
3、修复对interface生成CSharpCallLua代码时，interface含indexer时的报错；
4、修复Editor在运行后会new一个xlua实例的bug；
5、修复通过生成代码调用同时含可变参数和默认值的函数，如果不传参数，将会出错的bug；
6、修复调试时，找不到socket库的bug；


变更
1、反射不做重载方法顺序调整，顺序改为固定且生成代码保持一致；
2、i64加上fade_id，参数传递时更安全；
3、重新加入tdr的from_file的支持；

v2.1.0 2016年08月08日
新增特性
1、满足条件struct传递到lua无gc，struct需要满足什么条件才能被优化呢？
a. struct允许嵌套其它struct，但它以及它嵌套的struct只能包含这几种基本类型：byte、sbyte、short、ushort、int、uint、long、ulong、float、double；
b. struct本身以及使用到该struct的地方需要加LuaCallCSharp，并且加了GCOptimize设置；
2、全新实现的反射机制，更容易和生成代码配合使用
a. 支持extension methods，Enum.__CastFrom；
b. ios下支持反射使用event；
c. 对类型映射、可变参数调用调整为和生成代码一致；
d. 性能更好，gc更少；
3、生成代码菜单简化，并增加“Generate Minimum”选项；
4、支持生成代码配置文件放Editor目录；

变更
1、luajit统一升级成2.1.0b2；
2、luasocket库改为按需加载；
3、重载的string，byte[]参数检查允许为nil；
4、子类访问不触发父类加载；
5、struct的ref参数的修改会修改lua测该参数的值；
6、生成代码加载改为静态（原来是反射）；
7、菜单改为更简洁；
8、tdr改为默认不加载；
9、StreamingAssets加载lua改为废弃特性；

bug修复
1、参数或者返回值是泛型类的数组，或者是二维数组，生成代码报编译错误；
2、抽象类生成代码报编译错误；
3、消除Clear生成代码的warning；
4、profiler、i64库不支持多实例；

v2.0.5 2016年05月18日
新增特性
1、util.async_to_sync，可以更好的利用lua的协程实现同步编程、异步执行；或者异步等待www等；
2、生成代码的规范度调整，消除一些工具的告警；
bug修复
1、解决在lua gc移除weak table和调用__gc的时间窗内push同一对象，会生成指向同一C#对象的不同userdata的问题；
2、上版本的的lua内存工具并未打包；
3、修正嵌套类型不能生成代码的问题；

v2.0.4 2016年05月04日
新增特性
1、新增函数调用时长报告功能；
2、新增lua内存泄漏定位工具；
3、lua测加入对64位无符号数的支持；
变更
1、支持多种delegate绑定到一个clousre。调整之前一个clousre只能对应一种delegate；
bug修复
1、tdr处理长度为1的数组的错误（本来解包应该是{[1] = {a = 1}}的，却是{{a=1}}）；
2、tdr数值处理错误（int的-1会解成一个很大的正数）

v2.0.3 2016年04月13日
新功能
1、添加“Advanced Gen”功能，用户可以自定义生成代码的范围；
2、支持对库生成Static pusher；
变更
1、LuaTable以及InterfaceBirdage改为触发metatable；
2、Extension Methods不自动加到被扩展类，需要加入生成列表；
3、移除特殊ValueType优化；
bug修复
1、Extension Methods为私有时，生成代码语法错误；
2、重载函数含ulong时，生成代码语法错误；
3、反射调用时的默认值处理错误；
4、C#向lua传中文字符的长度处理错误；

v2.0.2 2016年04月06日
变更
1、库的生成代码配置支持多份，方便项目的模块化；
2、enum的生成代码合并到一个文件里头；
3、优化异常处理；
4、发布包把库和教程、例子分离，更干净；
5、小bug修改；

升级指引
由于文件有点变动，直接覆盖原有lib会报错，需要：
1、删除原来的XLua目录；
2、解压xlua_v2.0.2.zip到Assets下；
3、重新执行代码生成；

v2.0.1 2016年03月24日
1、支持C# 的extension methods；
2、lua调试方面的支持；
3、android下require一个不存在的lua文件可能成功的bug；
4、TDR 4 Lua库的更新；
5、多机型的兼容性测试；

v2.0.0 2016年03月08日
1、性能优化，性能对比报告请看主页；
2、加入官方lua版本的tdr；
3、支持64位整数；
4、修正lua中对C#异常pcall引发的不稳定；
5、易用性的优化；
6、其它一些bug的修改。

1.0.2 2015年12月09日
1、解决新版本（已知5.2版本）下，streamAssetsPath不允许在构造函数访问导致的bug；
2、新增windows x64版本的支持；
3、对web版本才用到的代码加入条件编译，减少对手机版发布包的影响；
4、生成代码文件名去掉“+”号；
5、删除4.6的生成代码，以免在新版本报引用过时api的错；

v1.0.1 2015年11月30日
1、支持pcall捕捉C#异常；
2、新增cast方法，支持这种场景：实现类是internal声明，只提供interface；
3、解决interface下如果有event，生成代码编译报错的bug；
4、解决interface下有Obsolete的方法，字段，生成代码编译报错的bug；
5、解决含private的默认geter/setter生成代码编译报错的bug；
6、修正类在全局空间下生成代码不可用的bug；
7、修正bridge代码返回值处理错误。

v1.0.0 2015年03月30日
第一个版本