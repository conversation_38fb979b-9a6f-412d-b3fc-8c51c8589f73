#if USE_UNI_LUA
using LuaAPI = UniLua.Lua;
using RealStatePtr = UniLua.ILuaState;
using LuaCSFunction = UniLua.CSharpFunctionDelegate;
#else
using LuaAPI = XLua.LuaDLL.Lua;
using RealStatePtr = System.IntPtr;
using LuaCSFunction = XLua.LuaDLL.lua_CSFunction;
#endif

using XLua;
using System.Collections.Generic;
<%
require "TemplateCommon"

local parameters = delegate:GetParameters()
local in_num = CalcCsList(parameters, function(p) return not (p.IsOut and p.ParameterType.IsByRef) end)
local out_num = CalcCsList(parameters, function(p) return p.IsOut or p.ParameterType.IsByRef end)
local in_pos = 0
local has_return = (delegate.ReturnType.Name ~= "Void")
%>

namespace XLua.CSObjectWrap
{
    using Utils = XLua.Utils;
    public class <%=CSVariableName(type)%>Wrap
    {
		public static void __Register(RealStatePtr L)
        {
            ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
		    Utils.BeginObjectRegister(typeof(<%=CsFullTypeName(type)%>), L, translator, 0, 0, 0, 0);
			Utils.EndObjectRegister(typeof(<%=CsFullTypeName(type)%>), L, translator, null, null, null, null, null);
			
			Utils.BeginClassRegister(typeof(<%=CsFullTypeName(type)%>), L, null, 1, 0, 0);
            Utils.EndClassRegister(typeof(<%=CsFullTypeName(type)%>), L, translator);
        }
		
		static Dictionary<string, LuaCSFunction> __MetaFucntions_Dic = new Dictionary<string, LuaCSFunction>(){
            {"__call", __CallMeta},
			{"__add", __AddMeta},
			{"__sub", __SubMeta},
        };
		
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CallMeta(RealStatePtr L)
        {
			ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			try {
				if(LuaAPI.lua_gettop(L) == <%=in_num+1%> && <%=GetCheckStatement(type, 1)%><%
					ForEachCsList(parameters, function(parameter) 
						if not (parameter.IsOut and parameter.ParameterType.IsByRef) then in_pos = in_pos + 1; 
						%>&& <%=GetCheckStatement(parameter.ParameterType, in_pos+1)%><% 
						end 
					end)%>)
				{
                <% 
                in_pos = 0;
                ForEachCsList(parameters, function(parameter) 
                    %><% 
                    if not (parameter.IsOut and parameter.ParameterType.IsByRef) then 
                        in_pos = in_pos + 1
                        %><%=GetCasterStatement(parameter.ParameterType, in_pos+1, parameter.Name, true)%><%
					else%><%=CsFullTypeName(parameter.ParameterType)%> <%=parameter.Name%><%end%>;
                <%end)%>
				<%=GetSelfStatement(type)%>;
				
				<%
                if has_return then
                    %>    var __cl_gen_ret = <%
                end
                %>    __cl_gen_to_be_invoked( <%ForEachCsList(parameters, function(parameter, pi) if pi ~= 0 then %>, <% end; if parameter.IsOut then %>out <% elseif parameter.ParameterType.IsByRef then %>ref <% end %><%=parameter.Name%><% end) %> );
                <%
                if has_return then
                %>    <%=GetPushStatement(delegate.ReturnType, "__cl_gen_ret")%>;
                <%
                end
                local in_pos = 0
                ForEachCsList(parameters, function(parameter)
                    if not (parameter.IsOut and parameter.ParameterType.IsByRef) then 
                        in_pos = in_pos + 1
                    end
                    if parameter.IsOut or parameter.ParameterType.IsByRef then
                    %><%=GetPushStatement(parameter.ParameterType:GetElementType(), parameter.Name)%>;
                    <%if not parameter.IsOut and parameter.ParameterType.IsByRef and NeedUpdate(parameter.ParameterType) then 
                  %><%=GetUpdateStatement(parameter.ParameterType:GetElementType(), in_pos+param_offset, parameter.Name)%>;
                        <%end%>
                <%
                    end
                end)
                %>
					
					return <%=out_num+(has_return and 1 or 0)%>;
				}
			} catch(System.Exception __gen_e) {
				return LuaAPI.luaL_error(L, "c# exception:" + __gen_e);
			}
            return LuaAPI.luaL_error(L, "invalid arguments to Delegate <%=CsFullTypeName(type)%>!");
		}
		
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __AddMeta(RealStatePtr L)
		{
			ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			try {
				<%=GetCasterStatement(type, 1, "leftside", true)%>;
				<%=GetCasterStatement(type, 2, "rightside", true)%>;
				<%=GetPushStatement(type, "leftside + rightside")%>;
				return 1;
			} catch(System.Exception __gen_e) {
				return LuaAPI.luaL_error(L, "c# exception:" + __gen_e);
			}
		}
		
		[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __SubMeta(RealStatePtr L)
		{
			ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			try {
				<%=GetCasterStatement(type, 1, "leftside", true)%>;
				<%=GetCasterStatement(type, 2, "rightside", true)%>;
				<%=GetPushStatement(type, "leftside - rightside")%>;
				return 1;
			} catch(System.Exception __gen_e) {
				return LuaAPI.luaL_error(L, "c# exception:" + __gen_e);
			}
		}
	}
}