using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_DayCheck 
{
    
    public static bool canDayCheck(Tool_DayCheck_Data data)
    {
        if (data.DayCheckCount >= data.DayCheckCountMax)
            return false;

        int tday = System.DateTime.Today.Day;
        if (data.Day != tday)
        {
            data.DayCheck = false;
            return true;
        }
        else
        {
            return !data.DayCheck;
        }
    }

    public static void CheckDay(Tool_DayCheck_Data data)
    {
        data.Day = System.DateTime.Today.Day;
        data.DayCheck = true;
        data.DayCheckCount++;
    }


}

public class Tool_DayCheck_Data
{
    public int Day = -1;
    public bool DayCheck = false;
    public int DayCheckCount = 0;
    public int DayCheckCountMax = 999;
}