using System;
using System.IO;
using System.Text;
using UnityEngine;

public static class Tool_Code4927
{
    // 加密标识头（4字节"ENC1"）
    private static readonly byte[] MagicHeader = Encoding.ASCII.GetBytes("ENC1");

    // 加密密钥（需与加密端保持一致）
    private static readonly byte[] EncryptionKey = { 0xAA, 0xBB, 0xCC };

    /// <summary>
    /// 解密文本资源（自动检测加密状态）
    /// </summary>
    /// <param name="ass">Unity文本资源</param>
    /// <returns>解密后的文本内容，失败返回空字符串</returns>
    public static string DecryptTextFile(TextAsset ass)
    {
        if (ass == null)
        {
            Debug.LogError("TextAsset is null!");
            return string.Empty;
        }

        try
        {
            byte[] fileData = ass.bytes;

            if (IsEncrypted(fileData))
            {
                //                Debug.Log($"检测到加密资源: {ass.name}");
                return DecryptData(fileData);
            }

            //            Debug.Log($"未加密资源: {ass.name}");
            return ass.text;
        }
        catch (Exception ex)
        {
            Debug.LogError($"资源解密失败: {ex.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// 解密字符串（支持多种输入格式：十六进制、Base64或直接二进制字符串）
    /// </summary>
    /// <param name="encryptedString">加密的字符串数据</param>
    /// <returns>解密后的文本内容，失败返回空字符串</returns>
    public static string DecryptString(string encryptedString)
    {
        // 空字符串直接返回空
        if (string.IsNullOrEmpty(encryptedString))
            return string.Empty;

        try
        {
            byte[] dataBytes = null;

            // 1) 十六进制字符串
            if (IsHexString(encryptedString))
            {
                dataBytes = HexStringToBytes(encryptedString);

                if (IsEncrypted(dataBytes))
                    return DecryptData(dataBytes);

                // 非加密：按 UTF8 直接当文本返回
                return Encoding.UTF8.GetString(dataBytes);
            }

            // 2) 尝试 Base64
            try
            {
                dataBytes = Convert.FromBase64String(encryptedString);

                if (dataBytes != null && dataBytes.Length > 0)
                {
                    if (IsEncrypted(dataBytes))
                        return DecryptData(dataBytes);

                    // 非加密：按 UTF8 直接当文本返回
                    return Encoding.UTF8.GetString(dataBytes);
                }
            }
            catch
            {
                // 非 Base64，忽略，继续下一步
            }

            // 3) 直接按字符串内容视为字节（UTF8）
            dataBytes = Encoding.UTF8.GetBytes(encryptedString);
            if (IsEncrypted(dataBytes))
                return DecryptData(dataBytes);

            // 非加密：原样返回
            return encryptedString;
        }
        catch (Exception ex)
        {
            Debug.LogError($"字符串解密失败: {ex.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// 解密原始字节数据（网络/文件二进制直传）
    /// </summary>
    /// <param name="encryptedBytes">可能是加密或明文的原始字节</param>
    /// <returns>解密后的文本内容，失败返回空字符串</returns>
    public static string DecryptBytes(byte[] encryptedBytes)
    {
        if (encryptedBytes == null || encryptedBytes.Length == 0)
            return string.Empty;

        try
        {
            if (IsEncrypted(encryptedBytes))
                return DecryptData(encryptedBytes);

            // 非加密：按 UTF8 直接当文本返回
            return Encoding.UTF8.GetString(encryptedBytes);
        }
        catch (Exception ex)
        {
            Debug.LogError($"字节解密失败: {ex.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// 检查字符串是否为十六进制格式
    /// </summary>
    private static bool IsHexString(string str)
    {
        if (string.IsNullOrEmpty(str) || str.Length % 2 != 0)
            return false;

        foreach (char c in str)
        {
            if (!((c >= '0' && c <= '9') || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f')))
                return false;
        }
        return true;
    }

    /// <summary>
    /// 将十六进制字符串转换为字节数组
    /// </summary>
    private static byte[] HexStringToBytes(string hex)
    {
        byte[] result = new byte[hex.Length / 2];
        for (int i = 0; i < result.Length; i++)
        {
            result[i] = Convert.ToByte(hex.Substring(i * 2, 2), 16);
        }
        return result;
    }


    /// <summary>
    /// 校验加密状态（优化版）
    /// </summary>
    private static bool IsEncrypted(byte[] data)
    {
        if (data == null || data.Length < MagicHeader.Length) return false;

        // 快速校验魔法头
        for (int i = 0; i < MagicHeader.Length; i++)
        {
            if (data[i] != MagicHeader[i]) return false;
        }
        return true;
    }

    /// <summary>
    /// 执行解密操作（内存优化版）
    /// </summary>
    private static string DecryptData(byte[] encryptedData)
    {
        try
        {
            int bodyLength = encryptedData.Length - MagicHeader.Length;
            byte[] dataBody = new byte[bodyLength];

            // 使用高效内存拷贝
            Buffer.BlockCopy(encryptedData, MagicHeader.Length, dataBody, 0, bodyLength);

            // 异或解密
            byte[] decrypted = XorDecrypt(dataBody);

            // 返回UTF8文本（自动处理BOM）
            return Encoding.UTF8.GetString(decrypted);
        }
        catch (Exception ex)
        {
            Debug.LogError($"解密过程出错: {ex.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// 优化的异或解密算法
    /// </summary>
    private static byte[] XorDecrypt(byte[] data)
    {
        byte[] result = new byte[data.Length];
        for (int i = 0; i < data.Length; i++)
        {
            // 循环使用密钥字节进行异或
            int keyIndex = i % EncryptionKey.Length;
            result[i] = (byte)(data[i] ^ EncryptionKey[keyIndex]);
        }
        return result;
    }
}