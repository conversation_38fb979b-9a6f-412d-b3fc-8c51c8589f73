using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using System.Text;
using System;
using System.Globalization;

public static class Tool_CountlySendEvent
{
    // 静态方法：触发事件，内部执行协程
    public static void SendEvent(string url, string appKey, string eventName, Action<bool> onComplete = null, params KeyValuePair<string, object>[] additionalParams)
    {
        // 使用 Unity 的协程来执行请求
        EnsureRunner().StartCoroutine(SendEventCoroutine(url, appKey, eventName, onComplete, additionalParams));
    }

    // ============= 批量发送：配置与队列 =============
    // 批量事件单元
    private sealed class BatchedEvent
    {
        public string url;
        public string appKey;
        public string eventName;
        public KeyValuePair<string, object>[] parameters;
        public Action<bool> onComplete; // 可选：批量成功时逐个回调 true；失败则保留队列不回调
    }


    // 队列与状态
    private static readonly List<BatchedEvent> _batchQueue = new List<BatchedEvent>();
    private static bool _isFlushing = false;

    // 本地/外部配置统一入口
#if TOOL_COUNTLYLOCAL
    // 本地配置（在未接入 MXR_BRIGE 配置时使用）
    private static int _localEventBatchCount = 10; // 本地默认批量阈值
    private static int _localSessionHeartbeatSeconds = 60; // 本地默认心跳秒数
#endif

    // 获取批量阈值（根据是否定义 TOOL_COUNTLYLOCAL 切换来源）
    private static int GetEventBatchCount()
    {
#if TOOL_COUNTLYLOCAL
        return _localEventBatchCount;
#else
        return MXR_BRIGE.Cos_GameSetting.Tool_CountlySendEvent_ConstData.COUNTLY_EVENTBATCHCOUNT;
#endif
    }

    // 获取会话心跳秒数（根据是否定义 TOOL_COUNTLYLOCAL 切换来源）
    private static int GetSessionHeartbeatSeconds()
    {
#if TOOL_COUNTLYLOCAL
        return _localSessionHeartbeatSeconds;
#else
        return MXR_BRIGE.Cos_GameSetting.Tool_CountlySendEvent_ConstData.COUNTLY_SESSION_HEARTBEAT_SECONDS;
#endif
    }

    // ============= 会话（Session） =============
    // 会话状态与心跳
    private static bool _sessionActive = false;
    private static DateTime _sessionStartUtc;
    private static DateTime _lastResumeUtc;
    private static double _accumulatedSessionSeconds = 0d;
    private static string _sessionUrl;
    private static string _sessionAppKey;
    private static Coroutine _sessionHeartbeatRoutine;
    private static bool _isPaused = false; // 是否处于暂停（后台）态



    // 入队一个事件；达到阈值触发发送
    public static void EnqueueEvent(string url, string appKey, string eventName, Action<bool> onComplete = null, params KeyValuePair<string, object>[] additionalParams)
    {
        // 确保存在运行器，以便 OnApplicationQuit 能触发 FlushOnQuit
        EnsureRunner();

        _batchQueue.Add(new BatchedEvent
        {
            url = url,
            appKey = appKey,
            eventName = eventName,
            parameters = additionalParams,
            onComplete = onComplete
        });

        // 达到阈值立即尝试发送
        if (_batchQueue.Count >= GetEventBatchCount())
        {
            RequestFlush();
        }
    }


    // 手动触发一次批量发送
    public static void FlushNow()
    {
        RequestFlush();
    }

    // 供运行器在暂停/退出时调用
    internal static void RequestFlush()
    {
        if (_isFlushing || _batchQueue.Count == 0) return;
        EnsureRunner().StartCoroutine(FlushCoroutine());
    }

    // 在退出程序时同步尽量发送剩余事件（WebGL 无法阻塞，仅请求一次异步刷新）
    internal static void FlushOnQuit()
    {
#if UNITY_WEBGL
        RequestFlush();
        return;
#else
        if (_batchQueue.Count == 0) return;
        _isFlushing = true;

        while (_batchQueue.Count > 0)
        {
            // 以队头的 endpoint 分组
            string url = _batchQueue[0].url;
            string appKey = _batchQueue[0].appKey;

            var slice = new List<BatchedEvent>(GetEventBatchCount());
            for (int i = 0; i < _batchQueue.Count && slice.Count < GetEventBatchCount(); i++)
            {
                var e = _batchQueue[i];
                if (e.url == url && e.appKey == appKey)
                {
                    slice.Add(e);
                }
            }

            string deviceId = GetDeviceId();
            string eventsJson = BuildEventsJsonBatch(slice);
            string payload = "app_key=" + Escape(appKey)
                            + "&device_id=" + Escape(deviceId)
                            + "&events=" + Escape(eventsJson);

            byte[] bodyRaw = Encoding.UTF8.GetBytes(payload);

            using (var request = new UnityWebRequest(url, UnityWebRequest.kHttpVerbPOST))
            {
                request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");
                request.timeout = 3; // 最多等待 3 秒

                var op = request.SendWebRequest();
                // 阻塞至完成或超时
                var start = DateTime.UtcNow;
                while (!op.isDone)
                {
                    if ((DateTime.UtcNow - start).TotalSeconds > 3.5)
                    {
                        break;
                    }
                }

                bool success;
#if UNITY_2020_2_OR_NEWER
                success = request.result == UnityWebRequest.Result.Success;
#else
                success = !(request.isNetworkError || request.isHttpError);
#endif
                if (!success)
                {
                    // 失败则中止剩余发送，保留队列
                    break;
                }
            }

            // 成功则移除并逐个回调
            foreach (var e in slice)
            {
                int idx = _batchQueue.FindIndex(x => ReferenceEquals(x, e));
                if (idx >= 0) _batchQueue.RemoveAt(idx);
                e.onComplete?.Invoke(true);
            }
        }

        _isFlushing = false;
#endif
    }

    // 批量发送循环（已移除定时策略）
    private static IEnumerator FlushCoroutine()
    {
        if (_batchQueue.Count == 0) yield break;
        _isFlushing = true;

        // 每次循环发一批，直到队列清空或遇到失败
        while (_batchQueue.Count > 0)
        {
            // 以队头的 endpoint 作为本次分组
            string url = _batchQueue[0].url;
            string appKey = _batchQueue[0].appKey;

            // 选择同 endpoint 的前 N 条
            var slice = new List<BatchedEvent>(GetEventBatchCount());
            for (int i = 0; i < _batchQueue.Count && slice.Count < GetEventBatchCount(); i++)
            {
                var e = _batchQueue[i];
                if (e.url == url && e.appKey == appKey)
                {
                    slice.Add(e);
                }
            }

            // 构造 payload
            string deviceId = GetDeviceId();
            string eventsJson = BuildEventsJsonBatch(slice);
            string payload = "app_key=" + Escape(appKey)
                            + "&device_id=" + Escape(deviceId)
                            + "&events=" + Escape(eventsJson);

            byte[] bodyRaw = Encoding.UTF8.GetBytes(payload);

            using (var request = new UnityWebRequest(url, UnityWebRequest.kHttpVerbPOST))
            {
                request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");

                yield return request.SendWebRequest();

                bool success;
#if UNITY_2020_2_OR_NEWER
                success = request.result == UnityWebRequest.Result.Success;
#else
                success = !(request.isNetworkError || request.isHttpError);
#endif
                if (!success)
                {
                    // 失败则退出，留待下次重试
                    _isFlushing = false;
                    yield break;
                }
            }

            // 成功：从队列移除本批，并逐个回调
            foreach (var e in slice)
            {
                // 只移除匹配到的项（保持相对顺序），避免直接 RemoveRange 带来的错位
                int idx = _batchQueue.FindIndex(x => ReferenceEquals(x, e));
                if (idx >= 0) _batchQueue.RemoveAt(idx);
                e.onComplete?.Invoke(true);
            }
        }

        _isFlushing = false;
    }

    // ============= 运行器 =============
    // 内部持久化运行器，确保存在一个可用于启动协程的 MonoBehaviour
    private static Tool_CountlySendEvent_IE _runner;

    private static Tool_CountlySendEvent_IE EnsureRunner()
    {
        if (_runner != null) return _runner;

        // 查找或创建承载对象，并设置为跨场景不销毁
        var existing = GameObject.Find("Tool_CountlySendEvent_IE");
        if (existing == null)
        {
            existing = new GameObject("Tool_CountlySendEvent_IE");
            UnityEngine.Object.DontDestroyOnLoad(existing);
        }

        _runner = existing.GetComponent<Tool_CountlySendEvent_IE>();
        if (_runner == null)
        {
            _runner = existing.AddComponent<Tool_CountlySendEvent_IE>();
        }

        return _runner;
    }

    // 协程执行事件发送（不使用 async/await，直接在协程内完成）
    private static IEnumerator SendEventCoroutine(string url, string appKey, string eventName, Action<bool> onComplete, params KeyValuePair<string, object>[] additionalParams)
    {
        // 获取平台特定的设备 ID
        string deviceId = GetDeviceId();

        // 生成 events 的 JSON 字符串（一个元素的数组）
        string eventsJson = BuildEventsJson(eventName, additionalParams);

        // 组装 x-www-form-urlencoded 的请求体
        string payload = "app_key=" + Escape(appKey)
                        + "&device_id=" + Escape(deviceId)
                        + "&events=" + Escape(eventsJson);

        byte[] bodyRaw = Encoding.UTF8.GetBytes(payload);

        using (var request = new UnityWebRequest(url, UnityWebRequest.kHttpVerbPOST))
        {
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");

            yield return request.SendWebRequest();

            bool success;
#if UNITY_2020_2_OR_NEWER
            success = request.result == UnityWebRequest.Result.Success;
#else
            success = !(request.isNetworkError || request.isHttpError);
#endif
            onComplete?.Invoke(success);
        }
    }

    // 辅助：构建 Countly events 的 JSON（仅一个事件）
    private static string BuildEventsJson(string eventName, KeyValuePair<string, object>[] additionalParams)
    {
        var segmentation = new Dictionary<string, object>();

        double? sumValue = null; // 可选的顶层 sum
        double countValue = 1d;  // 顶层 count，默认 1

        if (additionalParams != null)
        {
            for (int i = 0; i < additionalParams.Length; i++)
            {
                var kv = additionalParams[i];
                var key = kv.Key;
                var value = kv.Value;

                if (string.IsNullOrEmpty(key))
                {
                    continue;
                }

                // 特殊处理 count：如果是数值，覆盖顶层 count；否则作为 segmentation 文本
                if (string.Equals(key, "count", StringComparison.OrdinalIgnoreCase))
                {
                    if (IsNumeric(value))
                    {
                        try { countValue = Convert.ToDouble(value, CultureInfo.InvariantCulture); } catch { countValue = 1d; }
                    }
                    else
                    {
                        segmentation[key] = value;
                    }
                    continue;
                }

                // 特殊处理 sum：如果是数值，写入顶层 sum；否则作为 segmentation 文本
                if (string.Equals(key, "sum", StringComparison.OrdinalIgnoreCase))
                {
                    if (IsNumeric(value))
                    {
                        try { sumValue = Convert.ToDouble(value, CultureInfo.InvariantCulture); } catch { sumValue = null; }
                    }
                    else
                    {
                        segmentation[key] = value;
                    }
                    continue;
                }

                // 其余字段：全部作为 segmentation（Countly 官方仅识别 segmentation、count、sum）
                segmentation[key] = value;
            }
        }

        var sb = new StringBuilder();
        sb.Append('[');
        sb.Append('{');
        sb.Append("\"key\":").Append(ToJsonString(eventName)).Append(',');
        sb.Append("\"count\":").Append(countValue.ToString(CultureInfo.InvariantCulture));

        if (sumValue.HasValue)
        {
            sb.Append(',').Append("\"sum\":").Append(sumValue.Value.ToString(CultureInfo.InvariantCulture));
        }

        if (segmentation.Count > 0)
        {
            sb.Append(',').Append("\"segmentation\":");
            AppendJsonObject(sb, segmentation);
        }

        sb.Append('}');
        sb.Append(']');
        return sb.ToString();
    }

    // 批量：构建 events 数组 JSON（多事件）
    private static string BuildEventsJsonBatch(List<BatchedEvent> items)
    {
        var sb = new StringBuilder();
        sb.Append('[');
        for (int i = 0; i < items.Count; i++)
        {
            if (i > 0) sb.Append(',');
            AppendSingleEventObject(sb, items[i].eventName, items[i].parameters);
        }
        sb.Append(']');
        return sb.ToString();
    }

    // 将单个事件以 JSON 对象形式追加到 StringBuilder
    private static void AppendSingleEventObject(StringBuilder sb, string eventName, KeyValuePair<string, object>[] additionalParams)
    {
        var segmentation = new Dictionary<string, object>();
        double? sumValue = null;
        double countValue = 1d;

        if (additionalParams != null)
        {
            for (int i = 0; i < additionalParams.Length; i++)
            {
                var kv = additionalParams[i];
                var key = kv.Key;
                var value = kv.Value;

                if (string.IsNullOrEmpty(key)) continue;

                if (string.Equals(key, "count", StringComparison.OrdinalIgnoreCase))
                {
                    if (IsNumeric(value))
                    {
                        try { countValue = Convert.ToDouble(value, CultureInfo.InvariantCulture); } catch { countValue = 1d; }
                    }
                    else
                    {
                        segmentation[key] = value;
                    }
                    continue;
                }

                if (string.Equals(key, "sum", StringComparison.OrdinalIgnoreCase))
                {
                    if (IsNumeric(value))
                    {
                        try { sumValue = Convert.ToDouble(value, CultureInfo.InvariantCulture); } catch { sumValue = null; }
                    }
                    else
                    {
                        segmentation[key] = value;
                    }
                    continue;
                }

                segmentation[key] = value;
            }
        }

        sb.Append('{');
        sb.Append("\"key\":").Append(ToJsonString(eventName)).Append(',');
        sb.Append("\"count\":").Append(countValue.ToString(CultureInfo.InvariantCulture));
        if (sumValue.HasValue)
        {
            sb.Append(',').Append("\"sum\":").Append(sumValue.Value.ToString(CultureInfo.InvariantCulture));
        }
        if (segmentation.Count > 0)
        {
            sb.Append(',').Append("\"segmentation\":");
            AppendJsonObject(sb, segmentation);
        }
        sb.Append('}');
    }

    // 辅助：将对象转为 JSON 值
    private static string ToJsonValue(object value)
    {
        if (value == null) return "null";

        switch (value)
        {
            case string s:
                return ToJsonString(s);
            case bool b:
                return b ? "true" : "false";
            case float f:
                return f.ToString(CultureInfo.InvariantCulture);
            case double d:
                return d.ToString(CultureInfo.InvariantCulture);
            case decimal m:
                return m.ToString(CultureInfo.InvariantCulture);
            default:
                // 其他对象统一转字符串
                return ToJsonString(value.ToString());
        }
    }

    // 辅助：转为带引号且转义的 JSON 字符串
    private static string ToJsonString(string s)
    {
        if (s == null) return "\"\"";
        var sb = new StringBuilder();
        sb.Append('"');
        for (int i = 0; i < s.Length; i++)
        {
            char c = s[i];
            switch (c)
            {
                case '\\': sb.Append("\\\\"); break;
                case '"': sb.Append("\\\""); break;
                case '\n': sb.Append("\\n"); break;
                case '\r': sb.Append("\\r"); break;
                case '\t': sb.Append("\\t"); break;
                case '\b': sb.Append("\\b"); break;
                case '\f': sb.Append("\\f"); break;
                default:
                    if (c < ' ')
                    {
                        sb.Append("\\u").Append(((int)c).ToString("x4"));
                    }
                    else
                    {
                        sb.Append(c);
                    }
                    break;
            }
        }
        sb.Append('"');
        return sb.ToString();
    }

    // 辅助：序列化字典为 JSON 对象
    private static void AppendJsonObject(StringBuilder sb, Dictionary<string, object> dict)
    {
        sb.Append('{');
        bool first = true;
        foreach (var kv in dict)
        {
            if (!first) sb.Append(',');
            first = false;
            sb.Append(ToJsonString(kv.Key)).Append(':').Append(ToJsonValue(kv.Value));
        }
        sb.Append('}');
    }

    // 辅助：判断是否为数值类型
    private static bool IsNumeric(object value)
    {
        return value is sbyte || value is byte || value is short || value is ushort ||
               value is int || value is uint || value is long || value is ulong ||
               value is float || value is double || value is decimal;
    }

    // 辅助：URL 编码
    private static string Escape(string s)
    {
        return Uri.EscapeDataString(s ?? string.Empty);
    }

    // 获取设备 ID
    public static string GetDeviceId()
    {
        string deviceId = string.Empty;

#if UNITY_ANDROID
        deviceId = SystemInfo.deviceUniqueIdentifier; // 对于 Android 使用设备唯一标识符
#elif UNITY_IOS
        var vid = UnityEngine.iOS.Device.vendorIdentifier;
        deviceId = string.IsNullOrEmpty(vid) ? GetPersistentDeviceId("iOS_DeviceID") : vid;
#elif UNITY_WEBGL
        deviceId = GetPersistentDeviceId("WebGL_DeviceID"); // WebGL 本地持久化
#else
        deviceId = GetPersistentDeviceId("Desktop_DeviceID"); // 桌面使用本地持久化 GUID，跨启动一致
#endif

        return deviceId;
    }

    // WebGL 设备 ID 获取方法（复用通用持久化）
    private static string GetWebGLDeviceId()
    {
        return GetPersistentDeviceId("WebGL_DeviceID");
    }

    // 通用：从 PlayerPrefs 获取/创建持久设备 ID
    private static string GetPersistentDeviceId(string prefsKey)
    {
        var stored = MXR_BRIGE.Playerprefs_Getstring(prefsKey, "");
        if (string.IsNullOrEmpty(stored))
        {
            string newDeviceId = System.Guid.NewGuid().ToString();
            MXR_BRIGE.Playerprefs_SetString(prefsKey, newDeviceId);
            return newDeviceId;
        }
        return stored;
    }

    // ============= Session：公开 API =============
    // 开始会话（begin_session=1），并启动心跳与留存检查
    public static void StartSession(string url, string appKey)
    {
        EnsureRunner(); // 确保运行器存在
        if (_sessionActive) return;

        _sessionUrl = url;
        _sessionAppKey = appKey;
        _sessionActive = true;
        _isPaused = false;
        _sessionStartUtc = DateTime.UtcNow;
        _lastResumeUtc = _sessionStartUtc;
        _accumulatedSessionSeconds = 0d;

        // 发送 begin_session 与 metrics
        _runner.StartCoroutine(BeginSessionCoroutine(_sessionUrl, _sessionAppKey));

        // 启动会话心跳
        if (_sessionHeartbeatRoutine != null)
        {
            _runner.StopCoroutine(_sessionHeartbeatRoutine);
        }
        _sessionHeartbeatRoutine = _runner.StartCoroutine(SessionHeartbeatCoroutine());
    }

    // 结束会话（end_session=1），携带最后一段未上报的时长
    public static void EndSession()
    {
        if (!_sessionActive) return;

        double delta = (DateTime.UtcNow - _lastResumeUtc).TotalSeconds;
        int lastSegmentSeconds = _isPaused ? 0 : (int)Math.Max(0, Math.Round(delta));
        if (!_isPaused && delta > 0) _accumulatedSessionSeconds += delta;
        _lastResumeUtc = DateTime.UtcNow;

        if (_sessionHeartbeatRoutine != null)
        {
            _runner.StopCoroutine(_sessionHeartbeatRoutine);
            _sessionHeartbeatRoutine = null;
        }

        var url = _sessionUrl;
        var appKey = _sessionAppKey;
        _sessionActive = false;

        _runner.StartCoroutine(EndSessionCoroutine(url, appKey, lastSegmentSeconds));
    }

    // 前后台切换时的会话更新
    internal static void OnAppPause(bool pause)
    {
        if (!_sessionActive) return;

        _isPaused = pause;

        if (pause)
        {
            // 累加本次可计时段并立即发送一次增量
            double delta = (DateTime.UtcNow - _lastResumeUtc).TotalSeconds;
            if (delta > 1d)
            {
                _accumulatedSessionSeconds += delta;
                _lastResumeUtc = DateTime.UtcNow;
                int seconds = (int)Math.Max(0, Math.Round(delta));
                _runner.StartCoroutine(UpdateSessionCoroutine(_sessionUrl, _sessionAppKey, seconds));
            }

            if (_sessionHeartbeatRoutine != null)
            {
                _runner.StopCoroutine(_sessionHeartbeatRoutine);
                _sessionHeartbeatRoutine = null;
            }
        }
        else
        {
            // 恢复计时与心跳
            _lastResumeUtc = DateTime.UtcNow;
            if (_sessionHeartbeatRoutine != null)
            {
                _runner.StopCoroutine(_sessionHeartbeatRoutine);
            }
            // 可选：是否开新会话。当前策略：不新开，仅继续上报增量（符合 Countly 留存统计）
            _sessionHeartbeatRoutine = _runner.StartCoroutine(SessionHeartbeatCoroutine());
        }
    }

    // 退出时尽量同步上报 end_session（WebGL 无法阻塞）
    internal static void EndSessionOnQuit()
    {
#if UNITY_WEBGL
        EndSession();
#else
        if (!_sessionActive) return;

        int lastSegmentSeconds = 0;
        if (!_isPaused)
        {
            double delta = (DateTime.UtcNow - _lastResumeUtc).TotalSeconds;
            lastSegmentSeconds = (int)Math.Max(0, Math.Round(delta));
            if (delta > 0) _accumulatedSessionSeconds += delta;
        }

        string deviceId = GetDeviceId();
        var payloadSb = new StringBuilder();
        payloadSb.Append("app_key=").Append(Escape(_sessionAppKey))
                 .Append("&device_id=").Append(Escape(deviceId))
                 .Append("&end_session=1");
        if (lastSegmentSeconds > 0)
        {
            payloadSb.Append("&session_duration=").Append(lastSegmentSeconds.ToString(CultureInfo.InvariantCulture));
        }
        string payload = payloadSb.ToString();

        byte[] bodyRaw = Encoding.UTF8.GetBytes(payload);

        using (var request = new UnityWebRequest(_sessionUrl, UnityWebRequest.kHttpVerbPOST))
        {
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");
            request.timeout = 3;

            var op = request.SendWebRequest();
            var start = DateTime.UtcNow;
            while (!op.isDone)
            {
                if ((DateTime.UtcNow - start).TotalSeconds > 3.5)
                {
                    break;
                }
            }
        }

        _sessionActive = false;
#endif
    }

    // ============= Session：内部协程与工具 =============
    // 心跳循环：每隔固定秒数发送一次增量时长
    private static IEnumerator SessionHeartbeatCoroutine()
    {
        int hb = GetSessionHeartbeatSeconds();
        if (hb <= 0) hb = 60; // 合理默认值
        var wait = new WaitForSeconds(hb);
        while (_sessionActive)
        {
            yield return wait;
            if (!_sessionActive) yield break;

            double delta = (DateTime.UtcNow - _lastResumeUtc).TotalSeconds;
            if (delta <= 0.1d) continue;

            int seconds = (int)Math.Max(0, Math.Round(delta));
            _accumulatedSessionSeconds += delta;
            _lastResumeUtc = DateTime.UtcNow;

            yield return _runner.StartCoroutine(UpdateSessionCoroutine(_sessionUrl, _sessionAppKey, seconds));
        }
    }

    // begin_session 协程
    private static IEnumerator BeginSessionCoroutine(string url, string appKey)
    {
        string deviceId = GetDeviceId();
        string metricsJson = BuildMetricsJson();

        string payload = "app_key=" + Escape(appKey)
                       + "&device_id=" + Escape(deviceId)
                       + "&begin_session=1"
                       + "&metrics=" + Escape(metricsJson);

        byte[] bodyRaw = Encoding.UTF8.GetBytes(payload);

        using (var request = new UnityWebRequest(url, UnityWebRequest.kHttpVerbPOST))
        {
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");

            yield return request.SendWebRequest();
        }
    }

    // update_session（session_duration 增量）协程
    private static IEnumerator UpdateSessionCoroutine(string url, string appKey, int seconds)
    {
        if (seconds <= 0) yield break;

        string deviceId = GetDeviceId();
        string payload = "app_key=" + Escape(appKey)
                       + "&device_id=" + Escape(deviceId)
                       + "&session_duration=" + seconds.ToString(CultureInfo.InvariantCulture);

        byte[] bodyRaw = Encoding.UTF8.GetBytes(payload);

        using (var request = new UnityWebRequest(url, UnityWebRequest.kHttpVerbPOST))
        {
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");

            yield return request.SendWebRequest();
        }
    }

    // end_session 协程
    private static IEnumerator EndSessionCoroutine(string url, string appKey, int totalSeconds)
    {
        string deviceId = GetDeviceId();
        var payloadSb = new StringBuilder();
        payloadSb.Append("app_key=").Append(Escape(appKey))
                 .Append("&device_id=").Append(Escape(deviceId))
                 .Append("&end_session=1");
        if (totalSeconds > 0)
        {
            payloadSb.Append("&session_duration=").Append(totalSeconds.ToString(CultureInfo.InvariantCulture));
        }
        string payload = payloadSb.ToString();

        byte[] bodyRaw = Encoding.UTF8.GetBytes(payload);

        using (var request = new UnityWebRequest(url, UnityWebRequest.kHttpVerbPOST))
        {
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");

            yield return request.SendWebRequest();
        }
    }

    // 构建 Countly metrics（设备信息）JSON
    private static string BuildMetricsJson()
    {
        var metrics = new Dictionary<string, object>();
        metrics["_app_version"] = Application.version;
        // 友好化 _os：仅取系统名（去掉平台枚举细节）
#if UNITY_ANDROID
        metrics["_os"] = "Android";
#elif UNITY_IOS
        metrics["_os"] = "iOS";
#elif UNITY_WEBGL
        metrics["_os"] = "WebGL";
#else
        metrics["_os"] = SystemInfo.operatingSystemFamily.ToString();
#endif
        metrics["_os_version"] = SystemInfo.operatingSystem;
        metrics["_device"] = SystemInfo.deviceModel;
        metrics["_resolution"] = Screen.currentResolution.width + "x" + Screen.currentResolution.height;
        // 友好化 _density：当 Screen.dpi<=0 时兜底为 160（1x 基准），否则四舍五入
        float dpi = Screen.dpi;
        int density = (dpi <= 0f) ? 160 : (int)Math.Round(dpi);
        metrics["_density"] = density;
        metrics["_locale"] = Application.systemLanguage.ToString();

        var sb = new StringBuilder();
        AppendJsonObject(sb, metrics);
        return sb.ToString();
    }


}

// 留存追踪工具（精简版）：统一通过 MXR_BRIGE 的 PlayerPrefs 封装进行持久化
public static class Tool_CountlySendEvent_RetentionTracker
{
    // 安装日期与首次打开标记键名（持久化）
    private const string InstallDateKey = "install_date";
    private const string FirstOpenKey = "first_open_sent";

    // 获取安装日期（UTC 日期，格式 yyyy-MM-dd），首次不存在则写入并返回今天
    public static DateTime GetInstallDate()
    {
        string saved = MXR_BRIGE.Playerprefs_Getstring(InstallDateKey, "");
        if (!string.IsNullOrEmpty(saved))
        {
            if (DateTime.TryParseExact(saved, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dt))
            {
                return dt;
            }
        }

        var todayUtc = DateTime.UtcNow.Date;
        MXR_BRIGE.Playerprefs_SetString(InstallDateKey, todayUtc.ToString("yyyy-MM-dd"));
        return todayUtc;
    }

    // 安装后的第几天（以 UTC 日期为准）
    public static int GetRetentionDay()
    {
        return (int)(DateTime.UtcNow.Date - GetInstallDate()).TotalDays;
    }

    // day 分组（仅上报关键节点，避免图表爆炸；>30 天不再上报）
    public static int? GroupRetentionDay()
    {
        int d = GetRetentionDay();
        if (d == 0) return 0;
        if (d == 1) return 1;
        if (d <= 3) return 3;
        if (d <= 6) return 6;
        if (d == 7) return 7;
        if (d <= 14) return 14;
        if (d <= 30) return 30;
        return null;
    }

    // 是否首次打开（只返回 true 一次）
    public static bool IsFirstTimeOpen()
    {
        if (MXR_BRIGE.Playerprefs_GetInt(FirstOpenKey, 0) == 1)
            return false;

        MXR_BRIGE.Playerprefs_SetInt(FirstOpenKey, 1);
        return true;
    }

    // 触发留存事件（建议在启动后或主界面尽早调用）
    public static void TrackOnLaunch(string countlyUrl, string countlyAppKey)
    {
        int? day = GroupRetentionDay();
        if (!day.HasValue)
            return; // >30 天不上报

        Tool_CountlySendEvent.EnqueueEvent(
            countlyUrl,
            countlyAppKey,
            "Retention",
            null,
            new KeyValuePair<string, object>("day", day.Value),
            new KeyValuePair<string, object>("install_date", GetInstallDate().ToString("yyyy-MM-dd")),
            new KeyValuePair<string, object>("first_open", IsFirstTimeOpen() ? "1" : "0")
        );
    }
}

// 专用运行器：仅用于承载协程并保持跨场景持久
internal sealed class Tool_CountlySendEvent_IE : MonoBehaviour
{
    // 空类，无需实现；仅作为协程运行器存在
    private void OnApplicationPause(bool pause)
    {
        if (pause)
        {
            // 应用进入后台时尝试刷一次
            Tool_CountlySendEvent.RequestFlush();
            // 会话处理：进入后台
            Tool_CountlySendEvent.OnAppPause(true);

        }
        else
        {
            // 会话处理：回到前台
            Tool_CountlySendEvent.OnAppPause(false);

        }
    }

    private void OnApplicationQuit()
    {
        // 退出前同步尽量把队列发出去
        // 先结束会话并上报 end_session
        Tool_CountlySendEvent.EndSessionOnQuit();
        Tool_CountlySendEvent.FlushOnQuit();
    }
}
/* 
Tool_CountlySendEvent.SendEvent(
"https://countly.celecat.com/i",
"4daaf6e665af77d84d0019c181952007f8484cbe",
"DDD",
null, // 不需要回调就传 null（或省略）
new KeyValuePair<string, object>("level", "5"),      // segmentation
new KeyValuePair<string, object>("difficulty", "H"), // segmentation
new KeyValuePair<string, object>("score", 888)
); */