using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System; // 为了使用 Action

public class Tool_CountlySendEvent_MatchUI_Record : MonoBehaviour
{
#if TOOL_COUNTLYLOCAL
    // 本地配置：当未使用 MXR_BRIGE 全局配置时，通过宏开关启用
    private static string _localApiKey = ""; // 本地默认 APIKEY（请按需填写）
    private static string _localLink = "";   // 本地默认 LINK（请按需填写）
#endif

    // 统一获取 APIKEY（根据是否定义 TOOL_COUNTLYLOCAL 切换来源）
    private static string GetApiKey()
    {
#if TOOL_COUNTLYLOCAL
        return _localApiKey;
#else
        return MXR_BRIGE.Cos_GameSetting.Tool_CountlySendEvent_ConstData.COUNTLY_APIKEY;
#endif
    }

    // 统一获取 LINK（根据是否定义 TOOL_COUNTLYLOCAL 切换来源）
    private static string GetLink()
    {
#if TOOL_COUNTLYLOCAL
        return _localLink;
#else
        return MXR_BRIGE.Cos_GameSetting.Tool_CountlySendEvent_ConstData.COUNTLY_LINK;
#endif
    }

    void Awake()
    {
        SDK_Common_Main.On_LoadCosFinished += On_LoadCosFinished;
    }

    void On_LoadCosFinished()
    {
        if (GetApiKey() == "" || GetLink() == "")
            return;

        DontDestroyOnLoad(gameObject);

        MatchUI_Controller.On_MainEvent += On_MtachUI_MainEvent;
        MatchUI_Controller.On_BtnClick += On_MatchUI_BtnClick;
        MatchUI_Controller.On_BuyPackSuccess += On_BuyPackSuccess;
        MatchUI_Controller.On_RemoveADSSuccess += On_RemoveADSSuccess;
        MatchUI_Controller.On_PanelShow += On_PanelShow;
        MatchUI_Controller.On_ReGameSuccess += On_ReGameSuccess;
        MatchUI_Controller.On_Ask_UseProp += On_Ask_UseProp;

        Tool_CountlySendEvent.StartSession(GetLink(),
        GetApiKey());

        Tool_CountlySendEvent_RetentionTracker.TrackOnLaunch(GetLink(), GetApiKey());

        Tool_CountlySendEvent.EnqueueEvent(
        GetLink(),
        GetApiKey(),
        "LoadCosFinished",
         null);

    }

    void OnDestroy()
    {
        if (GetApiKey() == "" || GetLink() == "")
            return;

        MatchUI_Controller.On_MainEvent -= On_MtachUI_MainEvent;
        MatchUI_Controller.On_BtnClick -= On_MatchUI_BtnClick;
        MatchUI_Controller.On_BuyPackSuccess -= On_BuyPackSuccess;
        MatchUI_Controller.On_RemoveADSSuccess -= On_RemoveADSSuccess;
        MatchUI_Controller.On_PanelShow -= On_PanelShow;
        MatchUI_Controller.On_ReGameSuccess -= On_ReGameSuccess;
        MatchUI_Controller.On_Ask_UseProp -= On_Ask_UseProp;

    }

    void On_MtachUI_MainEvent(string eventname, int level, int Level_Display)
    {



        Tool_CountlySendEvent.EnqueueEvent(
        GetLink(),
        GetApiKey(),
        eventname,
        null,
            new KeyValuePair<string, object>("Level", Level_Display + 1));

    }

    void On_MatchUI_BtnClick(MatchUI_BtnClick_Event_Data data)
    {
        /*   // 只上报按钮名
          Tool_CountlySendEvent.EnqueueEvent(
              MXR_BRIGE.Cos_GameSetting.Tool_CountlySendEvent_ConstData.COUNTLY_LINK,
              MXR_BRIGE.Cos_GameSetting.Tool_CountlySendEvent_ConstData.COUNTLY_APIKEY,
              "MatchUI_BtnClick",
              null,
              new KeyValuePair<string, object>("Name", data.PreName + " " + data.ButtonName)); */
    }

    void On_BuyPackSuccess(MatchUI_ShopPack pack)
    {

        Tool_CountlySendEvent.EnqueueEvent(
            GetLink(),
            GetApiKey(),
            "MatchUI_BuyPackSuccess",
            null,
            new KeyValuePair<string, object>("Name", pack.Data.Name));
    }

    void On_RemoveADSSuccess()
    {
        // 无附加字段
        Tool_CountlySendEvent.EnqueueEvent(
            GetLink(),
            GetApiKey(),
            "MatchUI_RemoveADSSuccess",
            null);
    }

    void On_PanelShow(GameObject go)
    {
        /*   // 只上报面板对象名
          Tool_CountlySendEvent.EnqueueEvent(
              MXR_BRIGE.Cos_GameSetting.Tool_CountlySendEvent_ConstData.COUNTLY_LINK,
              MXR_BRIGE.Cos_GameSetting.Tool_CountlySendEvent_ConstData.COUNTLY_APIKEY,
              "MatchUI_PanelShow",
              null,
              new KeyValuePair<string, object>("Name", go != null ? go.name : "")); */
    }


    void On_ReGameSuccess()
    {
        // 无附加字段
        Tool_CountlySendEvent.EnqueueEvent(
            GetLink(),
            GetApiKey(),
            "MatchUI_ReGameSuccess",
            null);
    }

    void On_Ask_UseProp(int index, Action onSuccess)
    {
        // 上报询问使用道具，附带道具索引
        Tool_CountlySendEvent.EnqueueEvent(
            GetLink(),
            GetApiKey(),
            "MatchUI_AskUseProp",
            null,
            new KeyValuePair<string, object>("Index", index));
    }



}
