using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.PlayerLoop;
using UnityEngine.UI;

public class Tool_StartCountText : MonoBehaviour
{
    public float StartCount_CountScale = 1.4f;
    public float StartCount_CountScaleSpeed = 5f;
    public float StartCount_CountTime = 0.7f;

    public string StartCount_CountSoundPath = "IO_计时";
    public string StartCount_GoSoundPath = "IO_计时结束";


    public Text txtCount;

     float CountScale;
     float CountScaleSpeed ;
    float CountTime;

    Action CountOverAct = null;

    public void StartCount(Action countoverAct)
    {
        CountScale = StartCount_CountScale;
        CountScaleSpeed = StartCount_CountScaleSpeed;
        CountTime = StartCount_CountTime;

        CountOverAct = countoverAct;

        StartCoroutine("CountIe");
    }
    void Update()
    {
        if(txtCount.transform.localScale.x > 1 || txtCount.transform.localScale.y > 1)
        {
            txtCount.transform.localScale -= new Vector3(CountScaleSpeed * Time.deltaTime, CountScaleSpeed * Time.deltaTime, 0);
        }
    }

    IEnumerator CountIe()
    {
        MXR_BRIGE.Sound_PlayEffect(StartCount_CountSoundPath);
        txtCount.text = "3";
        txtCount.transform.localScale = new Vector3(CountScale, CountScale, txtCount.transform.localScale.z);
        yield return new WaitForSeconds(CountTime);
        MXR_BRIGE.Sound_PlayEffect(StartCount_CountSoundPath);
        txtCount.text = "2";
        txtCount.transform.localScale = new Vector3(CountScale, CountScale, txtCount.transform.localScale.z);
        yield return new WaitForSeconds(CountTime);
        MXR_BRIGE.Sound_PlayEffect(StartCount_CountSoundPath);
        txtCount.text = "1";
        txtCount.transform.localScale = new Vector3(CountScale, CountScale, txtCount.transform.localScale.z);
        yield return new WaitForSeconds(CountTime);
        MXR_BRIGE.Sound_PlayEffect(StartCount_GoSoundPath);
        txtCount.text = "GO";
        txtCount.transform.localScale = new Vector3(CountScale, CountScale, txtCount.transform.localScale.z);
        yield return new WaitForSeconds(CountTime);
        gameObject.SetActive(false);

        CountOverAct?.Invoke();

        CountOverAct = null;

    }
}
