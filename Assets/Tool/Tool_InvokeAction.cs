using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_InvokeAction :MonoBehaviour
{

   public  static Tool_InvokeAction Invoker
    {
        get
        {
            if(invoker == null)
            {
                GameObject go = new GameObject();
                go.name = "Tool_InvokeAction";
                invoker = go.AddComponent<Tool_InvokeAction>();
                
                DontDestroyOnLoad(invoker);
            }
            return invoker;
        }

    }
    static Tool_InvokeAction invoker;

    public  void  InvokeAciton(Action act,float delay)
    {
        StartCoroutine(DelayedActionCoroutine(act, delay));
    }

    private  IEnumerator DelayedActionCoroutine(Action action, float delay)
    {
        yield return new WaitForSeconds(delay);
        action?.Invoke();
    }

}
