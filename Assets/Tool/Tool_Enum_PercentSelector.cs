
using System;
using System.Collections;
using System.Collections.Generic;
//using System.Linq;
using UnityEngine;

public class Tool_Enum_PercentSelector
{

    public static T GetRandomStateExcluding<T>(float[] percents, T? excludeState = null) where T : struct, Enum
    {
        if (!typeof(T).IsEnum)
        {
            throw new ArgumentException("T must be an enumerated type");
        }

        T[] states = Enum.GetValues(typeof(T)) as T[];
        float totalPercent = 0f;

        // Calculate the total valid percent (excluding the excludeState's percent if it's provided)
        for (int i = 0; i < percents.Length; i++)
        {
            if (excludeState.HasValue && EqualityComparer<T>.Default.Equals(states[i], excludeState.Value))
                continue;
            totalPercent += percents[i] - (i > 0 ? percents[i - 1] : 0f);
        }

        // If no exclude state is provided, or if the exclude state doesn't affect the total percent,
        // we can use the full range of 0 to 1 for the random number.
        // Otherwise, we use the adjusted total percent.
        float randomValueRange = excludeState.HasValue ? totalPercent : 1f;
        float randomValue = UnityEngine.Random.Range(0f, randomValueRange);

        // Determine which state to return based on the random number
        float currentCumulativePercent = 0f;
        for (int i = 0; i < percents.Length; i++)
        {
            if (excludeState.HasValue && EqualityComparer<T>.Default.Equals(states[i], excludeState.Value))
                continue;

            float nextStateCumulativePercent = currentCumulativePercent + (percents[i] - (i > 0 ? percents[i - 1] : 0f));
            if (randomValue >= currentCumulativePercent && randomValue < nextStateCumulativePercent)
            {
                return states[i];
            }

            currentCumulativePercent = nextStateCumulativePercent;
        }

        // This should never happen if percents are correctly set up and if the enum type T is properly used
        throw new InvalidOperationException("No valid state found to return.");
    }

    //public static Role_Evo_AI_State GetRandomAIStateExcluding(float[] percents, Role_Evo_AI_State excludeState)
    //{
    //    Role_Evo_AI_State[] states = Enum.GetValues(typeof(Role_Evo_AI_State)) as Role_Evo_AI_State[];
    //    float totalPercent = 0f;

    //    // Calculate the total valid percent (excluding the excludeState's percent)
    //    for (int i = 0; i < percents.Length; i++)
    //    {
    //        if (states[i] == excludeState)
    //            continue;
    //        totalPercent += percents[i] - (i > 0 ? percents[i - 1] : 0f);
    //    }

    //    // Generate a random number within the valid percent range
    //    float randomValue = UnityEngine.Random.value * totalPercent;

    //    // Determine which state to return based on the random number
    //    float currentCumulativePercent = 0f;
    //    for (int i = 0; i < percents.Length; i++)
    //    {
    //        if (states[i] == excludeState)
    //            continue;

    //        float nextStateCumulativePercent = currentCumulativePercent + (percents[i] - (i > 0 ? percents[i - 1] : 0f));
    //        if (randomValue >= currentCumulativePercent && randomValue < nextStateCumulativePercent)
    //        {
    //            return states[i];
    //        }

    //        currentCumulativePercent = nextStateCumulativePercent;
    //    }

    //    // This should never happen if percents are correctly set up
    //    throw new InvalidOperationException("No valid state found to return.");
    //}

}
    