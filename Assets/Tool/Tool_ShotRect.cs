using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.PlayerLoop;
using UnityEngine.UI;

public class Tool_ShotRect : MonoBehaviour
{
    public float Cue_ScaleBegin = 0;
    public float Cue_ScaleTarget = 1;
    public float Cue_ShowScaleSpeed = 20;
    public float Cue_HideScaleSpeed = 30;

    public RectTransform parent;

    float CurrentShowCount = -1;

    void Update()
    {
        switch (CurrentShowCount)
        {
            case 0:
                var target = new Vector3(Cue_ScaleTarget, Cue_ScaleTarget, Cue_ScaleTarget);
                parent.localScale = Vector3.Lerp(parent.localScale, target, Cue_ShowScaleSpeed * Time.deltaTime);

                if (Vector3.Distance(parent.localScale, target) < 0.02f)
                    CurrentShowCount = 1;
                break;
            case 1:
                var targetbegin = new Vector3(Cue_ScaleBegin, Cue_ScaleBegin, Cue_ScaleBegin);
                parent.localScale = Vector3.Lerp(parent.localScale, targetbegin, Cue_HideScaleSpeed * Time.deltaTime);
                if (Vector3.Distance(parent.localScale, targetbegin) < 0.02f)
                {
                    CurrentShowCount = -1;
                }

                break;
        }

    }

    public void Shot()
    {

        CurrentShowCount = 0;
        parent.localScale = new Vector3(Cue_ScaleBegin, Cue_ScaleBegin, Cue_ScaleBegin);

    }



}


