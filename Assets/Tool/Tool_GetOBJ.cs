using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_GetOBJ 
{

    public static GameObject GetRandOBJ(GameObject self,List<GameObject> objs,bool passHeight,float distance = -1)
    {
        float maxAttackDis = distance;

        //infinity
        if (maxAttackDis == -1)
            maxAttackDis = 10000;


        List<GameObject> temps = new List<GameObject>();


        for (var i = 0; i < objs.Count; i++)
        {
            if(passHeight)
            {
                if (Vector3.Distance(self.transform.position, new Vector3(objs[i].transform.position.x, self.transform.position.y, objs[i].transform.position.z)) < maxAttackDis)
                {
                    temps.Add(objs[i]);
                }
            }
            else
            {
                if (Vector3.Distance(self.transform.position, objs[i].transform.position) < maxAttackDis)
                {
                    temps.Add(objs[i]);
                }
            }   
        }

        if (temps.Count == 0)
            return null;

        var randindex = Random.Range(0, temps.Count);

        return temps[randindex];

    }

    public static GameObject GetNearestOBJ(GameObject self, List<GameObject> objs, bool passHeight, float distance = -1)
    {
        float maxAttackDis = distance;

        //infinity
        if (maxAttackDis == -1)
            maxAttackDis = 10000;



        int index = -1;
        if (!passHeight)
        {
            for (var i = 0; i < objs.Count; i++)
            {
                if (index == -1)
                {
                    if (Vector3.Distance(self.transform.position, objs[i].transform.position) < maxAttackDis)
                        index = i;
                }
                else
                {
                    float min = Vector3.Distance(self.transform.position, objs[index].transform.position);
                    if (Vector3.Distance(self.transform.position, objs[i].transform.position) < min)
                        index = i;
                }
            }
        }
        else

        {
            for (var i = 0; i < objs.Count; i++)
            {
                if (index == -1)
                {
                    if (Vector3.Distance(self.transform.position, new Vector3(objs[i].transform.position.x, self.transform.position.y, objs[i].transform.position.z)) < maxAttackDis)
                        index = i;
                }
                else
                {
                    float min = Vector3.Distance(self.transform.position, new Vector3(objs[index].transform.position.x, self.transform.position.y, objs[index].transform.position.z));
                    if (Vector3.Distance(self.transform.position, new Vector3(objs[i].transform.position.x, self.transform.position.y, objs[i].transform.position.z)) < min)
                        index = i;
                }
            }
        }


        if (index == -1)
            return null;

        return objs[index];

    }

}
