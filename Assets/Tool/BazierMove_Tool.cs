using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BazierMove_Tool : MonoBehaviour
{
    private static BazierMove_Tool instance = null;

    public static BazierMove_Tool Instance
    {
        get
        {
            if (instance == null)
            {
                instance = new GameObject(typeof(BazierMove_Tool).Name).AddComponent<BazierMove_Tool>();
                DontDestroyOnLoad(instance.gameObject);
            }
            return instance;
        }
    }



    public List<BazierMove_ItemData> CurrentItemObjs = new List<BazierMove_ItemData>();

    void Update()
    {
        for(var i = 0; i < CurrentItemObjs.Count;i++)
        {
            BazierMove_ItemData item = CurrentItemObjs[i];

            Vector3 targetPutPos = item.MoveData.TargetPos;
            Vector3 selfPos = item.go.transform.position;
            // Vector3 targetScale = new Vector3(Points[0].localScale.x, Points[0].localScale.y, Points[0].localScale.z);
            Vector3 targetScale = item.MoveData.TargetScale;
            Vector3 targetRotate =item.MoveData.TargetRot;




            float distanceToTarget = Vector3.Distance(item.Reach_startPoint, targetPutPos);

            float currentdis = Vector3.Distance(item.go.transform.position, targetPutPos);
            float percent = (1 - currentdis / distanceToTarget);


            // 计算已经经过的时间 当前位置
            float elapsedTime = Time.time - item.Reach_startTime;

            float journeyFraction = elapsedTime * (item.MoveData.Reach_Speed + item.MoveData.Reach_AddingSpeed * percent) / distanceToTarget;


            //Bazier
            if (!item.Reach_isBazierMoveFinished)
            {

                Vector3 currentPos = Vector3.Lerp(item.Reach_startPoint, targetPutPos, journeyFraction);

                // 计算抛物线高度
                currentPos.y += Mathf.Sin(journeyFraction * Mathf.PI) * item.MoveData.Reach_ParabolaHeight;

                // 移动物体
                item.go.transform.position = currentPos;

                // 如果已经到达目标点，结束移动
                if (journeyFraction >= item.MoveData.Reach_ArriveRange)
                {
                    // 确保物体在落点位置
                    item.go.transform.position = targetPutPos;
                    item.Reach_isBazierMoveFinished = true;
                }

            }
            //Scale
            if (!item.Reach_isScaleFinished)
            {

                item.go.transform.localScale = Vector3.Lerp(item.Reach_startScale, targetScale, journeyFraction);


                if (Vector3.Distance(item.go.transform.localScale, targetScale) < 0.05f)
                {
                    item.go.transform.localScale = targetScale;
                    item.Reach_isScaleFinished = true;
                }
            }
            //Ratote
            if (!item.Reach_isRotateFinished)
            {

                item.go.transform.rotation = Quaternion.Lerp(item.Reach_startRot, Quaternion.Euler(targetRotate), journeyFraction);
                if (Vector3.Distance(item.go.transform.eulerAngles, targetRotate) < 0.1f)
                {
                    item.go.transform.eulerAngles = targetRotate;
                    item.Reach_isRotateFinished = true;
                }
            }
            //reach
            if (item.Reach_isBazierMoveFinished && item.Reach_isScaleFinished && item.Reach_isRotateFinished)
            {
               if(item.MoveData.FinishCallBack != null )
                    item.MoveData.FinishCallBack();
                CurrentItemObjs.Remove(item);
            }
        }

      
    }


    public void JumpTo(GameObject mover, BazierMove_MoveData movedata)
    {

        BazierMove_ItemData gd = new BazierMove_ItemData();
        gd.Reach_startPoint = mover.transform.position;
        gd.Reach_startScale = mover.transform.localScale;
        gd.Reach_startRot = mover.transform.rotation;
        gd.Reach_startTime = Time.time;

        gd.go = mover;

        BazierMove_MoveData md = new BazierMove_MoveData();

        md.Reach_AddingSpeed = movedata.Reach_AddingSpeed;
        md.Reach_Speed = movedata.Reach_Speed;
        md.Reach_ParabolaHeight = movedata.Reach_ParabolaHeight;
        md.Reach_ArriveRange = movedata.Reach_ArriveRange;

        md.TargetPos = movedata.TargetPos;
        md.TargetRot = movedata.TargetRot;
        md.TargetScale = movedata.TargetScale;
        md.BeginCallBack = movedata.BeginCallBack;
        md.FinishCallBack = movedata.FinishCallBack;




        gd.MoveData = md;

        if (gd.MoveData.BeginCallBack != null)
            gd.MoveData.BeginCallBack();


        CurrentItemObjs.Add(gd);

   
    }

    public void PathTo(GameObject mover,List<BazierMove_MoveData> movedatas) 
    {
        StartCoroutine(PathToLogic(mover, movedatas));
    }

    IEnumerator PathToLogic(GameObject mover, List<BazierMove_MoveData> movedatas)
    {
        var cIndex = 0;
        var cmindex = -1;
        while(cIndex != movedatas.Count-1)
        {
    
            if(cmindex != cIndex)
            {

                var originFinishCallBack = movedatas[cIndex].FinishCallBack;
                movedatas[cIndex].FinishCallBack = () => {
                    if (originFinishCallBack != null)
                        originFinishCallBack();
                    cIndex++;
                };

                JumpTo(mover, movedatas[cIndex]);
                cmindex = cIndex;


            }
            yield return new WaitForEndOfFrame();

        }

        JumpTo(mover, movedatas[movedatas.Count - 1]);
    }

}

public class BazierMove_ItemData
{
    public GameObject go;

    //Reach
    public Vector3 Reach_startPoint;
    public Vector3 Reach_startScale;
    public Quaternion Reach_startRot;
    public float Reach_startTime;
    public bool Reach_isBazierMoveFinished = false;
    public bool Reach_isScaleFinished = false;
    public bool Reach_isRotateFinished = false;

    public BazierMove_MoveData MoveData;
}

public class BazierMove_MoveData
{
    public float Reach_Speed;
    public float Reach_AddingSpeed;
    public float Reach_ParabolaHeight;
    public float Reach_ArriveRange;


    public Vector3 TargetPos;
    public Vector3 TargetScale;
    public Vector3 TargetRot;

    public Action BeginCallBack;
    public Action FinishCallBack;
}