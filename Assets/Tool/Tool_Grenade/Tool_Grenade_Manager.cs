using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_Grenade_Manager
{
    //设置碰撞层 自身为 BulletOrGrenade 忽略物体为 BulletOrGrenade_PASSOBJ

    public static GameObject[] Resources_GrenadeGroup
    {
        get
        {
            if (resources_GrenadeGroup == null)
            {
                var gs = Resources.LoadAll<Tool_Grenade_Object>("GrenadePrefab/");
                resources_GrenadeGroup = new GameObject[gs.Length];
                for (var i = 0; i < gs.Length; i++)
                    resources_GrenadeGroup[i] = gs[i].gameObject;
            }
            return resources_GrenadeGroup;
        }
    }
    static GameObject[] resources_GrenadeGroup;

    public static Action<GameObject[]> OnBoom_Event;


    public static Tool_Grenade_Object Create(int grenadeIndex, GameObject shooter, Transform parent,Vector3 pos,Vector3 ang)
    {
        GameObject bg = GameObject.Instantiate(Resources_GrenadeGroup[grenadeIndex]);
        bg.transform.position = pos;
        bg.transform.eulerAngles = ang;

        bg.transform.SetParent(parent);

        Tool_Grenade_Object b = bg.GetComponent<Tool_Grenade_Object>();

        List<GameObject> passlist = new List<GameObject>();
        passlist.Add(shooter.gameObject);
        for (var i = 0; i < shooter.transform.childCount; i++)
        {
            passlist.Add(shooter.transform.GetChild(i).gameObject);
        }

        b.Init(shooter, passlist, pos, ang, grenadeIndex);

        return b;
    }


}
