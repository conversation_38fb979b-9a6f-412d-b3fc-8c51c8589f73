using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_Grenade_Object : MonoBehaviour
{
    //设置碰撞层 自身为 BulletOrGrenade 忽略物体为 BulletOrGrenade_PASSOBJ

    [HideInInspector]
    public int GrenadeType;
    [HideInInspector]
    public GameObject Shooter;
    public int Boom_Effect_Index;
  //  public float ColEffect_Time = 0.8f;
    public float BoomTime;
   // public float TriTime;
    public float DesTime;
    public Rigidbody rigBody;
    public Collider normalCol;
   // public Collider triCol;
    float BoomTimer;
  //  float TriTimer;
    float DesTimer;

    bool isCol = false;
    bool isBoom = false;
 //   List<Collider> triSameObj = new List<Collider>();


    List<GameObject> PassSelfObj = new List<GameObject>();



    public void Init(GameObject shooter, List<GameObject> passSelfObj, Vector3 pos, Vector3 ang, int type)
    {
        rigBody.isKinematic = true;
        normalCol.enabled = false;
      //  triCol.enabled = false;

        Shooter = shooter;
        PassSelfObj = passSelfObj;

        GrenadeType = type;


    }

    public void Release(Vector3 forward, Vector3 up, int forwardForce, int upForce)
    {
        transform.SetParent(null);
        normalCol.enabled = true;
        rigBody.isKinematic = false;

        rigBody.AddForce(forward * forwardForce);

        rigBody.AddForce(up * upForce);
    }


    private void Update()
    {
        if (isCol && !isBoom)
        {
            BoomTimer += Time.deltaTime;
            if (BoomTimer > BoomTime)
            {
                Boom();
                GameObject.Destroy(this.gameObject);
            }

        }

        //if (isCol && isBoom)
        //{
        //    TriTimer += Time.deltaTime;
        //    if (TriTimer > TriTime)
        //        GameObject.Destroy(this.gameObject);

        //}

        if (!isCol)
        {
            DesTimer += Time.deltaTime;
            if (DesTimer > DesTime)
                GameObject.Destroy(this.gameObject);


        }
    }

    void Boom()
    {
        isBoom = true;
        rigBody.isKinematic = true;
        normalCol.enabled = false;


   
        var n1 = gameObject.GetComponent<MeshRenderer>();
        var n2 = gameObject.GetComponentsInChildren<MeshRenderer>();

        if (n1 != null)
            n1.enabled = false;
        for (var i = 0; i < n2.Length; i++)
        {
            n2[i].enabled = false;
        }


        Tool_Boom_Effect.CreateBoom(Boom_Effect_Index, new List<GameObject> { Shooter, Tool_Grenade_Manager.Resources_GrenadeGroup[GrenadeType] }, transform.position, transform.eulerAngles);

        Tool_Grenade_Manager.OnBoom_Event?.Invoke(new GameObject[] { Shooter, Tool_Grenade_Manager.Resources_GrenadeGroup[GrenadeType] });
    }


    //private void OnTriggerEnter(Collider other)
    //{
    //    if (triSameObj.Contains(other))
    //        return;
    //    triSameObj.Add(other);

    //    Tool_Grenade_Manager.OnGrenadeCol_Event?.Invoke(Shooter, other.gameObject, GrenadeType);

    //}

    public void OnCollisionEnter(Collision collision)
    {

        if (isCol)
            return;

        for (var i = 0; i < PassSelfObj.Count; i++)
        {
            if (collision.collider.gameObject == PassSelfObj[i])
                return;
        }

        isCol = true;

    }



}
