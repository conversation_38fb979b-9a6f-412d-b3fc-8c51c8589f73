using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.PlayerLoop;
using UnityEngine.UI;

public class Tool_ComboText : MonoBehaviour
{
    //Combo
    public static float Combo_PerComboTime = 5;
    public static float Combo_ScaleBegin = 0;
    public static  float Combo_ShowScaleSpeed = 20;
    public static float Combo_HideScaleSpeed = 30;
    public static float Combo_ShowTime = 1;
    public static float Combo_EveryInterval_Time = 0.1f;

    public Text txtCombo;

    int ComboCount = 0;

     float CurrentShowTimer;
     float CurrentPerTimer;
     float CurrentShowCount = -1;
    float CurrentEveryIntervalTimer = 0;

   void Update()
    {
        switch (CurrentShowCount)
        {
            case 0:
                txtCombo.rectTransform.localScale = Vector3.Lerp(txtCombo.rectTransform.localScale, Vector3.one, Combo_ShowScaleSpeed * Time.deltaTime);

                if (txtCombo.rectTransform.localScale.x >= 0.98f)
                    CurrentShowCount = 1;
                break;
            case 1:
                CurrentShowTimer -= Time.deltaTime;
                if (CurrentShowTimer <= 0)
                    CurrentShowCount = 2;
                break;
            case 2:
                txtCombo.rectTransform.localScale = Vector3.Lerp(txtCombo.rectTransform.localScale, Vector3.zero, Combo_HideScaleSpeed * Time.deltaTime);
                if (txtCombo.rectTransform.localScale.x <= 0.05f)
                {
                    txtCombo.enabled = false;
                    CurrentShowCount = -1;
                }
                   
                break;
        }

        if (CurrentPerTimer > 0)
        {
            CurrentPerTimer -= Time.deltaTime;
            if (CurrentPerTimer <= 0)       
                ComboCount = 0;
        
        }


        if (CurrentEveryIntervalTimer > 0)
            CurrentEveryIntervalTimer -= Time.deltaTime;
    }

    public int Combo()
    {
        if (CurrentEveryIntervalTimer > 0)
            return ComboCount;
        CurrentEveryIntervalTimer = Combo_EveryInterval_Time;

        txtCombo.enabled = true;
        ComboCount++;
        txtCombo.text = "Combo X" + ComboCount;

        CurrentShowCount = 0;
        CurrentPerTimer = Combo_PerComboTime;
        CurrentShowTimer = Combo_ShowTime;
        txtCombo.rectTransform.localScale = new Vector3(Combo_ScaleBegin, Combo_ScaleBegin, Combo_ScaleBegin);


        return ComboCount;
    }



}


