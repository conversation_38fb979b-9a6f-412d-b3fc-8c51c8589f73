using System;
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using LitJson;
using Object = UnityEngine.Object;

#if UNITY_EDITOR
public class Tool_REPACKIMG : EditorWindow
{
    private static int atlasPadding = 100; // 图集缓冲区大小
    private static Vector2 scrollPos;
    private static int atlasSize = 2048; // 正方形图集的边长

    [MenuItem("Assets/Tool_REPACKIMG/Settings")]
    private static void ShowWindow()
    {
        var window = GetWindow<Tool_REPACKIMG>("Atlas Settings");
        window.Show();
    }

    private void OnGUI()
    {
        scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
        
        EditorGUILayout.LabelField("Atlas Packing Settings", EditorStyles.boldLabel);
        atlasPadding = EditorGUILayout.IntField("Atlas Padding", atlasPadding);
        atlasSize = EditorGUILayout.IntField("Atlas Size", atlasSize);
        
        EditorGUILayout.HelpBox(
            "Images will be packed efficiently based on their individual sizes.",
            MessageType.Info
        );

        EditorGUILayout.EndScrollView();
    }

    [MenuItem("Assets/Tool_REPACKIMG/Create Image Atlas", true)]
    private static bool ValidateCreateAtlas()
    {
        string path = AssetDatabase.GetAssetPath(Selection.activeObject);
        return Directory.Exists(path);
    }

    [MenuItem("Assets/Tool_REPACKIMG/Create Image Atlas")]
    private static void CreateAtlas()
    {
        string sourcePath = AssetDatabase.GetAssetPath(Selection.activeObject);
        ProcessImageAtlas(sourcePath);
    }

    [MenuItem("Assets/Tool_REPACKIMG/Restore From Atlas", true)]
    private static bool ValidateRestoreAtlas()
    {
        string path = AssetDatabase.GetAssetPath(Selection.activeObject);
        return path.EndsWith(".json");
    }

    [MenuItem("Assets/Tool_REPACKIMG/Restore From Atlas")]
    private static void RestoreAtlas()
    {
        string jsonPath = AssetDatabase.GetAssetPath(Selection.activeObject);
        RestoreFromAtlas(jsonPath);
    }

    private class ImageInfo
    {
        public string RelativePath { get; set; }
        public string FileName { get; set; }
        public Rect Position { get; set; }
        public Vector2 OriginalSize { get; set; }
        public int AtlasIndex { get; set; }
    }

    private class AtlasGroup
    {
        public List<Texture2D> textures = new List<Texture2D>();
        public List<ImageInfo> imageInfos = new List<ImageInfo>();
    }

    private static void ProcessImageAtlas(string sourceFolderPath)
    {
        List<string> imageFiles = GetAllImageFiles(sourceFolderPath);
        if (imageFiles.Count == 0)
        {
            EditorUtility.DisplayDialog("Error", "No images found in selected folder", "OK");
            return;
        }

        string outputFolderPath = EditorUtility.SaveFolderPanel(
            "Choose Output Folder",
            sourceFolderPath,
            ""
        );

        if (string.IsNullOrEmpty(outputFolderPath)) return;

        if (!outputFolderPath.StartsWith(Application.dataPath))
        {
            EditorUtility.DisplayDialog("Error", "Please select a folder inside the Assets folder", "OK");
            return;
        }

        List<Texture2D> allTextures = new List<Texture2D>();
        List<ImageInfo> allImageInfos = new List<ImageInfo>();
        
        foreach (string file in imageFiles)
        {
            byte[] fileData = File.ReadAllBytes(file);
            Texture2D tex = new Texture2D(2, 2);
            tex.LoadImage(fileData);
            allTextures.Add(tex);

            string relativePath = file.Replace(Application.dataPath, "Assets");
            allImageInfos.Add(new ImageInfo
            {
                RelativePath = GetRelativePath(sourceFolderPath, relativePath),
                FileName = Path.GetFileName(file),
                OriginalSize = new Vector2(tex.width, tex.height),
                AtlasIndex = 0
            });
        }

        List<AtlasGroup> atlasGroups = GroupTextures(allTextures, allImageInfos);

        string atlasBaseName = Path.GetFileName(sourceFolderPath);

        for (int groupIndex = 0; groupIndex < atlasGroups.Count; groupIndex++)
        {
            var group = atlasGroups[groupIndex];
            
            CalculateEfficientLayout(group.textures, group.imageInfos);

            foreach (var imageInfo in group.imageInfos)
            {
                imageInfo.AtlasIndex = groupIndex;
            }

            string atlasName = atlasBaseName;
            if (atlasGroups.Count > 1)
            {
                atlasName += $"_{groupIndex}";
            }
            
            string atlasPath = Path.Combine(outputFolderPath, atlasName + ".png");
            string debugAtlasPath = Path.Combine(outputFolderPath, atlasName + "_debug.png");
            
            Texture2D atlas = CreateAtlas(group.textures, group.imageInfos);
            Texture2D debugAtlas = CreateDebugAtlas(group.textures, group.imageInfos);
            
            byte[] bytes = atlas.EncodeToPNG();
            byte[] debugBytes = debugAtlas.EncodeToPNG();
            
            File.WriteAllBytes(atlasPath, bytes);
            File.WriteAllBytes(debugAtlasPath, debugBytes);

            Object.DestroyImmediate(atlas);
            Object.DestroyImmediate(debugAtlas);
        }

        string jsonPath = Path.Combine(outputFolderPath, atlasBaseName + ".json");
        string json = JsonMapper.ToJson(allImageInfos);
        File.WriteAllText(jsonPath, json);

        foreach (var tex in allTextures)
        {
            Object.DestroyImmediate(tex);
        }

        AssetDatabase.Refresh();
        EditorUtility.DisplayDialog("Success", 
            $"Atlas created successfully!\nNumber of atlas generated: {atlasGroups.Count}", 
            "OK");
    }

    private static List<AtlasGroup> GroupTextures(List<Texture2D> textures, List<ImageInfo> imageInfos)
    {
        List<AtlasGroup> groups = new List<AtlasGroup>();
        AtlasGroup currentGroup = new AtlasGroup();
        groups.Add(currentGroup);

        int currentX = atlasPadding;
        int currentY = atlasPadding;
        int rowHeight = 0;

        for (int i = 0; i < textures.Count; i++)
        {
            Texture2D tex = textures[i];
            
            if (currentX + tex.width + atlasPadding > atlasSize)
            {
                currentX = atlasPadding;
                currentY += rowHeight + atlasPadding;
                rowHeight = 0;
            }

            if (currentY + tex.height + atlasPadding > atlasSize)
            {
                currentGroup = new AtlasGroup();
                groups.Add(currentGroup);
                currentX = atlasPadding;
                currentY = atlasPadding;
                rowHeight = 0;
            }

            currentGroup.textures.Add(tex);
            currentGroup.imageInfos.Add(imageInfos[i]);

            currentX += tex.width + atlasPadding;
            rowHeight = Mathf.Max(rowHeight, tex.height);
        }

        return groups;
    }

    private static Texture2D CreateAtlas(List<Texture2D> textures, List<ImageInfo> imageInfos)
    {
        int maxWidth = 0;
        int maxHeight = 0;
        foreach (var info in imageInfos)
        {
            float right = info.Position.x + info.Position.width;
            float bottom = info.Position.y + info.Position.height;
            maxWidth = Mathf.Max(maxWidth, Mathf.CeilToInt(right));
            maxHeight = Mathf.Max(maxHeight, Mathf.CeilToInt(bottom));
        }
        
        maxWidth += atlasPadding;
        maxHeight += atlasPadding;

        maxWidth = Mathf.Min(maxWidth, atlasSize);
        maxHeight = Mathf.Min(maxHeight, atlasSize);

        Texture2D atlas = new Texture2D(maxWidth, maxHeight, TextureFormat.RGBA32, false);
        
        Color[] clearColors = new Color[maxWidth * maxHeight];
        for (int i = 0; i < clearColors.Length; i++) 
            clearColors[i] = new Color(0, 0, 0, 0);
        atlas.SetPixels(clearColors);
        atlas.Apply();

        for (int i = 0; i < textures.Count; i++)
        {
            Rect pos = imageInfos[i].Position;
            int targetX = Mathf.FloorToInt(pos.x);
            int targetY = Mathf.FloorToInt(pos.y);

            Color[] pixels = textures[i].GetPixels();
            atlas.SetPixels(targetX, targetY, textures[i].width, textures[i].height, pixels);
            atlas.Apply();
        }

        return atlas;
    }

    private static Texture2D CreateDebugAtlas(List<Texture2D> textures, List<ImageInfo> imageInfos)
    {
        // 创建与普通图集相同大小的调试图集
        int maxWidth = 0;
        int maxHeight = 0;
        foreach (var info in imageInfos)
        {
            float right = info.Position.x + info.Position.width;
            float bottom = info.Position.y + info.Position.height;
            maxWidth = Mathf.Max(maxWidth, Mathf.CeilToInt(right));
            maxHeight = Mathf.Max(maxHeight, Mathf.CeilToInt(bottom));
        }
        
        maxWidth += atlasPadding;
        maxHeight += atlasPadding;

        maxWidth = Mathf.Min(maxWidth, atlasSize);
        maxHeight = Mathf.Min(maxHeight, atlasSize);

        // 创建调试图集纹理
        Texture2D debugAtlas = new Texture2D(maxWidth, maxHeight, TextureFormat.RGBA32, false);
        
        // 填充透明背景
        Color[] clearColors = new Color[maxWidth * maxHeight];
        for (int i = 0; i < clearColors.Length; i++) 
            clearColors[i] = new Color(0, 0, 0, 0);
        debugAtlas.SetPixels(clearColors);
        debugAtlas.Apply();

        // 复制原始纹理
        for (int i = 0; i < textures.Count; i++)
        {
            Rect pos = imageInfos[i].Position;
            int targetX = Mathf.FloorToInt(pos.x);
            int targetY = Mathf.FloorToInt(pos.y);

            Color[] pixels = textures[i].GetPixels();
            debugAtlas.SetPixels(targetX, targetY, textures[i].width, textures[i].height, pixels);
        }

        // 绘制网格线
        Color gridColor = new Color(0, 0, 0, 1); // 黑色网格线
        int gridLineThickness = 1;

        foreach (var info in imageInfos)
        {
            int x = Mathf.FloorToInt(info.Position.x);
            int y = Mathf.FloorToInt(info.Position.y);
            int width = Mathf.FloorToInt(info.Position.width);
            int height = Mathf.FloorToInt(info.Position.height);

            // 绘制水平线
            for (int i = 0; i < width; i++)
            {
                for (int t = 0; t < gridLineThickness; t++)
                {
                    if (y + t < maxHeight)
                        debugAtlas.SetPixel(x + i, y + t, gridColor);
                    if (y + height + t < maxHeight)
                        debugAtlas.SetPixel(x + i, y + height + t, gridColor);
                }
            }

            // 绘制垂直线
            for (int i = 0; i < height; i++)
            {
                for (int t = 0; t < gridLineThickness; t++)
                {
                    if (x + t < maxWidth)
                        debugAtlas.SetPixel(x + t, y + i, gridColor);
                    if (x + width + t < maxWidth)
                        debugAtlas.SetPixel(x + width + t, y + i, gridColor);
                }
            }
        }

        debugAtlas.Apply();
        return debugAtlas;
    }

    private static void RestoreFromAtlas(string jsonPath)
    {
        string outputFolderPath = EditorUtility.SaveFolderPanel(
            "Choose Output Folder for Restored Images",
            Path.GetDirectoryName(jsonPath),
            ""
        );

        if (string.IsNullOrEmpty(outputFolderPath)) return;

        if (!outputFolderPath.StartsWith(Application.dataPath))
        {
            EditorUtility.DisplayDialog("Error", "Please select a folder inside the Assets folder", "OK");
            return;
        }

        string json = File.ReadAllText(jsonPath);
        List<ImageInfo> allImageInfos = JsonMapper.ToObject<List<ImageInfo>>(json);
        
        string atlasBaseName = Path.GetFileNameWithoutExtension(jsonPath);
        string finalOutputPath = Path.Combine(outputFolderPath, atlasBaseName);
        Directory.CreateDirectory(finalOutputPath);

        var uniqueAtlasIndices = allImageInfos.Select(info => info.AtlasIndex).Distinct().OrderBy(i => i).ToList();

        Dictionary<int, Texture2D> atlasDict = new Dictionary<int, Texture2D>();
        string jsonFolder = Path.GetDirectoryName(jsonPath);

        foreach (int index in uniqueAtlasIndices)
        {
            string atlasName = $"{atlasBaseName}_{index}";
            string atlasPath = Path.Combine(jsonFolder, atlasName + ".png");
            
            if (!File.Exists(atlasPath))
            {
                string assetPath = "Assets/" + atlasName + ".png";
                atlasPath = Path.Combine(Application.dataPath, "..", assetPath);
                
                if (!File.Exists(atlasPath))
                {
                    Debug.LogError($"Cannot find atlas file: {atlasName}.png");
                    EditorUtility.DisplayDialog("Error", $"Cannot find atlas file: {atlasName}.png", "OK");
                    return;
                }
            }

            Debug.Log($"Loading atlas: {atlasPath}");
            byte[] atlasBytes = File.ReadAllBytes(atlasPath);
            Texture2D atlas = new Texture2D(2, 2);
            atlas.LoadImage(atlasBytes);
            atlasDict[index] = atlas;
        }

        foreach (var imageInfo in allImageInfos)
        {
            string targetPath = Path.Combine(finalOutputPath, imageInfo.RelativePath);
            string targetFolder = Path.GetDirectoryName(targetPath);
            Directory.CreateDirectory(targetFolder);

            if (!atlasDict.ContainsKey(imageInfo.AtlasIndex))
            {
                Debug.LogError($"Cannot find atlas for index: {imageInfo.AtlasIndex}");
                continue;
            }

            Texture2D sourceAtlas = atlasDict[imageInfo.AtlasIndex];
            
            int originalWidth = Mathf.FloorToInt(imageInfo.OriginalSize.x);
            int originalHeight = Mathf.FloorToInt(imageInfo.OriginalSize.y);
            
            Texture2D tex = new Texture2D(originalWidth, originalHeight);

            int sourceX = Mathf.FloorToInt(imageInfo.Position.x);
            int sourceY = Mathf.FloorToInt(imageInfo.Position.y);

            try
            {
                Color[] pixels = sourceAtlas.GetPixels(
                    sourceX,
                    sourceY,
                    originalWidth,
                    originalHeight
                );
                
                tex.SetPixels(pixels);
                tex.Apply();

                byte[] bytes = tex.EncodeToPNG();
                File.WriteAllBytes(targetPath, bytes);
            }
            catch (Exception e)
            {
                Debug.LogError($"Error restoring image {imageInfo.RelativePath}: {e.Message}");
                Debug.LogError($"Atlas Index: {imageInfo.AtlasIndex}, Position: ({sourceX}, {sourceY}), Size: ({originalWidth}, {originalHeight})");
                continue;
            }
            finally
            {
                Object.DestroyImmediate(tex);
            }
        }

        foreach (var atlas in atlasDict.Values)
        {
            Object.DestroyImmediate(atlas);
        }

        AssetDatabase.Refresh();
        EditorUtility.DisplayDialog("Success", "Images restored successfully!", "OK");
    }

    private static List<string> GetAllImageFiles(string folderPath)
    {
        string[] files = Directory.GetFiles(folderPath, "*.*", SearchOption.AllDirectories)
            .Where(s => s.EndsWith(".png") || s.EndsWith(".jpg") || s.EndsWith(".jpeg"))
            .ToArray();
        return new List<string>(files);
    }

    private static string GetRelativePath(string rootPath, string fullPath)
    {
        string relativePath = fullPath.Replace(rootPath, "").TrimStart('/', '\\');
        return relativePath;
    }

    private static void CalculateEfficientLayout(List<Texture2D> textures, List<ImageInfo> imageInfos)
    {
        int currentX = atlasPadding;
        int currentY = atlasPadding;
        int rowHeight = 0;

        for (int i = 0; i < textures.Count; i++)
        {
            if (textures[i].width > atlasSize - atlasPadding * 2 || 
                textures[i].height > atlasSize - atlasPadding * 2)
            {
                Debug.LogWarning($"Texture {imageInfos[i].FileName} is too large for atlas. Skipping.");
                continue;
            }

            if (currentX + textures[i].width + atlasPadding > atlasSize)
            {
                currentX = atlasPadding;
                currentY += rowHeight + atlasPadding;
                rowHeight = 0;
            }

            imageInfos[i].Position = new Rect(currentX, currentY, textures[i].width, textures[i].height);
            
            currentX += textures[i].width + atlasPadding;
            rowHeight = Mathf.Max(rowHeight, textures[i].height);
        }
    }
}
#endif