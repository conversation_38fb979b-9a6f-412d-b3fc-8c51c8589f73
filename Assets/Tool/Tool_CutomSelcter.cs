using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_CutomSelcter : MonoBehaviour
{
    public int[] ShowSel;
    public GameObject[] Sel1;
    public GameObject[] Sel2;
    public GameObject[] Sel3;
    public GameObject[] Sel4;
    public GameObject[] Sel5;
    public GameObject[] Sel6;
    public GameObject[] Sel7;
    public GameObject[] Sel8;
    public GameObject[] Sel9;
    public GameObject[] Sel10;

    private void Awake()
    {
        for (var i = 0; i < Sel1.Length; i++)
            Sel1[i].gameObject.SetActive(false);
        for (var i = 0; i < Sel2.Length; i++)
            Sel2[i].gameObject.SetActive(false);
        for (var i = 0; i < Sel3.Length; i++)
            Sel3[i].gameObject.SetActive(false);
        for (var i = 0; i < Sel4.Length; i++)
            Sel4[i].gameObject.SetActive(false);
        for (var i = 0; i < Sel5.Length; i++)
            Sel5[i].gameObject.SetActive(false);
        for (var i = 0; i < Sel6.Length; i++)
            Sel6[i].gameObject.SetActive(false);
        for (var i = 0; i < Sel7.Length; i++)
            Sel7[i].gameObject.SetActive(false);
        for (var i = 0; i < Sel8.Length; i++)
            Sel8[i].gameObject.SetActive(false);
        for (var i = 0; i < Sel9.Length; i++)
            Sel9[i].gameObject.SetActive(false);
        for (var i = 0; i < Sel10.Length; i++)
            Sel10[i].gameObject.SetActive(false);

        for (var i = 0; i < ShowSel.Length;i++)
        {
            switch(i)
            {
                case 0:
                    Sel(ShowSel[i], Sel1);
                    break;
                case 1:
                    Sel(ShowSel[i], Sel2);
                    break;
                case 2:
                    Sel(ShowSel[i], Sel3);
                    break;
                case 3:
                    Sel(ShowSel[i], Sel4);
                    break;
                case 4:
                    Sel(ShowSel[i], Sel5);
                    break;
                case 5:
                    Sel(ShowSel[i], Sel6);
                    break;
                case 6:
                    Sel(ShowSel[i], Sel7);
                    break;
                case 7:
                    Sel(ShowSel[i], Sel8);
                    break;
                case 8:
                    Sel(ShowSel[i], Sel9);
                    break;
                case 9:
                    Sel(ShowSel[i], Sel10);
                    break;
            }
        }

    }

    void Sel(int type,GameObject[] objs)
    {
        if (type == -1)
            return;
        objs[type].gameObject.SetActive(true);

    }
}
