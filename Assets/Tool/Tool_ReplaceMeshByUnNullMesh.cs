using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_ReplaceMeshByUnNullMesh : MonoBehaviour
{
    public MeshFilter meshFilter1;
    public MeshFilter meshFilter2;
    public MeshFilter meshFilter3;
    public MeshFilter meshFilter4;
    public MeshFilter meshFilter5;
    public Mesh[] meshs1;
    public Mesh[] meshs2;
    public Mesh[] meshs3;
    public Mesh[] meshs4;
    public Mesh[] meshs5;

    void Awake()
    {
        Show(meshFilter1, meshs1);
        Show(meshFilter2, meshs2);
        Show(meshFilter3, meshs3);
        Show(meshFilter4, meshs4);
        Show(meshFilter5, meshs5);

    //    GameObject.Destroy(this);
    }


    void Show(MeshFilter mf, Mesh[] ms)
    {
        if(mf != null )
        {
            for(var i = 0; i <ms.Length;i++)
                if (ms[i] != null)
                {
                    mf.mesh = ms[i];
                    return;
                }
        }
    }
}
