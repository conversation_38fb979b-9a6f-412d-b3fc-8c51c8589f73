
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

public class Tool_Scene_EatObjPoint_Creater : MonoBehaviour
{
    private static Tool_Scene_EatObjPoint_Creater instance = null;
    public static Tool_Scene_EatObjPoint_Creater Instance
    {
        get
        {
            if (instance == null)
                instance = GameObject.Find("Tool_Scene_EatObjPoint_Creater").GetComponent<Tool_Scene_EatObjPoint_Creater>();

            return instance;
        }
    }


    public Vector2 DisX;
    public Vector2 DisZ;

    public float DisY;

    public int Count;

    public string[] NeedLandFlag;

    public GameObject Prefab;

  //  [HideInInspector]
    public List<GameObject> creaters = new List<GameObject>();

    public string OutStrName;


    void Update()
    {
        if (Input.GetKeyDown(KeyCode.O))
            OutPosAndRot();
        if (creaters.Count < Count)
        {
            var go = GameObject.Instantiate(Prefab);
            go.transform.position = new Vector3(Random.Range(DisX.x, DisX.y), DisY, Random.Range(DisZ.x, DisZ.y));
            go.transform.parent = this.transform;
            creaters.Add(go);
        }
    }

    void OutPosAndRot()
    {
        string filePath = Application.dataPath + "/" + OutStrName + ".txt";

        string a = "";
        for (var i = 0; i < creaters.Count; i++)
        {
            if(creaters[i] != null)
            {
                var t = creaters[i].transform.position + ";" + creaters[i].transform.eulerAngles;

                if (i != creaters.Count - 1)
                    t += "\n";

                a += t;
            }
 
        }


        using (StreamWriter writer = new StreamWriter(filePath))
        {

            writer.Write(a);   // F2表示保留两位小数

            writer.WriteLine();

        }

        Debug.LogError("Tool_Scene_EatObjPoint_Creater ： 输出完成");

    }


    public static List<Tool_Scene_EatObjPoint_Data> ParseFile(string txt,Vector2 clampX, Vector2 calmpZ,float per)
    {
        List<Tool_Scene_EatObjPoint_Data> datas = new List<Tool_Scene_EatObjPoint_Data>();

        string str = txt;
        var str1 = str.Split('\n');
        for (var i = 0; i < str1.Length - 1; i++)
        {
            var str2 = str1[i].Split(';');

            str2[0] = str2[0].Replace("(", "");
            str2[0] = str2[0].Replace(")", "");
            str2[1] = str2[1].Replace("(", "");
            str2[1] = str2[1].Replace(")", "");

            var posstr = str2[0].Split(',');
            var angstr = str2[1].Split(',');



            Vector3 pos = new Vector3(float.Parse(posstr[0]), float.Parse(posstr[1]), float.Parse(posstr[2]));
            Vector3 ang = new Vector3(float.Parse(angstr[0]), float.Parse(angstr[1]), float.Parse(angstr[2]));



            if (pos.x >= clampX.x && pos.x <= clampX.y && pos.z >= calmpZ.x && pos.z <= calmpZ.y)
            {
                Tool_Scene_EatObjPoint_Data data = new Tool_Scene_EatObjPoint_Data();

                data.Pos = pos;
                data.Ang = ang;
                datas.Add(data);
            }


        }

        return Tool_List_RandPercent.ShuffleAndSelectProportion(datas, 0.5f); ;
    }



  

}


public class Tool_Scene_EatObjPoint_Data
{
  public  Vector3 Pos;
   public  Vector3 Ang;
}

