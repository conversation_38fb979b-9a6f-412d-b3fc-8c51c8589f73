using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_Scene_EatObjPoint_Obj : MonoBehaviour
{
    public Vector2 Rot_X_Range;
    public Vector2 Rot_Y_Range;
    public Vector2 Rot_Z_Range;

    void Awake()
    {
        transform.eulerAngles = new Vector3(Random.Range(Rot_X_Range.x, Rot_X_Range.y), Random.Range(Rot_Y_Range.x, Rot_Y_Range.y), Random.Range(Rot_Z_Range.x, Rot_Z_Range.y));
    }

    void OnCollisionEnter(Collision o)
    {
        bool delete = true;
        for (var i = 0; i < Tool_Scene_EatObjPoint_Creater.Instance.NeedLandFlag.Length; i++)
        {
            if (o.collider.name.IndexOf(Tool_Scene_EatObjPoint_Creater.Instance.NeedLandFlag[i]) != -1)
                delete = false;
        }

        if (delete)
        {
            Tool_Scene_EatObjPoint_Creater.Instance.creaters.Remove(this.gameObject);
            GameObject.Destroy(this.gameObject);
        }

    }

}
