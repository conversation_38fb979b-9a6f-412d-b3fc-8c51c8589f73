using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_List_RandPercent 
{

    // 方法：打乱列表并返回一定比例的数量
   public static List<T> ShuffleAndSelectProportion<T>(List<T> inputList, float proportion)
    {
        // 验证输入
        if (inputList == null || inputList.Count == 0 || proportion <= 0 || proportion > 1)
        {
            Debug.LogError("Invalid input parameters.");
            return new List<T>();
        }

        // 打乱列表
        List<T> shuffledList = new List<T>(inputList);
        shuffledList.Shuffle();

        // 计算所需数量
        int countToSelect = Mathf.RoundToInt(proportion * shuffledList.Count);

        // 返回所需数量的列表
        List<T> resultList = shuffledList.GetRange(0, countToSelect);
        return resultList;
    }


}
// 扩展方法：为List添加Shuffle方法
public static class ListExtensions
{
    public static void Shuffle<T>(this List<T> list)
    {
        int n = list.Count;
        while (n > 1)
        {
            n--;
            int k = Random.Range(0, n + 1);
            T value = list[k];
            list[k] = list[n];
            list[n] = value;
        }
    }
}