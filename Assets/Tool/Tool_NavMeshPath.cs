using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;

public class Tool_NavMeshPath 
{
    static float Setting_Ray_OriginYOff = 100;
    static float Setting_Ray_Length = 1000;
    static string Setting_Ray_GrundLayerName = "Ground";

    //static float Setting_Calculate_Check_Distance = 0.3f;
    //static float Setting_Calculate_Check_UpdateTime = 0.1f;



    //public static NavMeshPath CalculatePath(Tool_NavMeshPath_Data navData, Vector3 startPos, Vector3 targetPos)
    //{


    //    if (navData != null)
    //        if (Vector3.Distance(navData.LastStartPos, startPos) < 0.3f && Vector3.Distance(navData.LastTargetPos, targetPos) < 0.3f)
    //        {
    //            return navData.Last_NavMeshPath;
    //        }

    //    if (navData == null)
    //        navData = new Tool_NavMeshPath_Data();
    //    navData.LastStartPos = startPos;
    //    navData.LastTargetPos = targetPos;
    //    navData.Last_NavMeshPath = Tool_NavMeshPath.CalculatePath(startPos, targetPos);

    //    return navData.Last_NavMeshPath;

    //}


    //NO TOO OFFTEN
    public static NavMeshPath CalculatePath(Vector3 startPos, Vector3 targetPos)
    {


        NavMeshPath path = new NavMeshPath();



        if (NavMesh.CalculatePath(startPos, targetPos, NavMesh.AllAreas, path))
        {
            return path;
        }
        else
            return null;
    }


    //用来找对应地图Y点位
    public static Vector3 GetRayGroundPoint(Vector3 originPos)
    {
        RaycastHit hit;
        Ray ray = new Ray(new Vector3(originPos.x, originPos.y+ Setting_Ray_OriginYOff, originPos.z), Vector3.down); 

        if (Physics.Raycast(ray, out hit, Setting_Ray_Length, LayerMask.GetMask(Setting_Ray_GrundLayerName))) 
            return hit.point;
        else
            return new Vector3(-999,-999,-999);
    }
}

//public class Tool_NavMeshPath_Data
//{
//    public Vector3 LastStartPos = Vector3.zero;
//    public Vector3 LastTargetPos = Vector3.zero;
//    public NavMeshPath Last_NavMeshPath = null;
//    public float UpdateTimer = 0.1f;
//}