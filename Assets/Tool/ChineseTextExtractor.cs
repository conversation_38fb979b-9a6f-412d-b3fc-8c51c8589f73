using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEditor;
#if UNITY_EDITOR
// 中文文本提取工具
public class ChineseTextExtractor : EditorWindow
{
    private string rootDirectory = "";
    private string outputFilePath = "";
    private bool includeComments = true;
    private bool includeStrings = true;
    private bool includeVariableNames = true;
    private bool processing = false;
    private string statusMessage = "";
    private Vector2 scrollPosition;
    private List<string> extractedTexts = new List<string>();

    [MenuItem("Tools/Chinese Text Extractor")]
    public static void ShowWindow()
    {
        GetWindow<ChineseTextExtractor>("中文文本提取工具");
    }

    private void OnGUI()
    {
        GUILayout.Label("中文文本提取工具", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        rootDirectory = EditorGUILayout.TextField("扫描目录路径:", rootDirectory);
        if (GUILayout.Button("选择目录"))
        {
            string path = EditorUtility.OpenFolderPanel("选择要扫描的目录", Application.dataPath, "");
            if (!string.IsNullOrEmpty(path))
            {
                rootDirectory = path;
            }
        }

        outputFilePath = EditorGUILayout.TextField("输出文件路径:", outputFilePath);
        if (GUILayout.Button("选择输出文件"))
        {
            string path = EditorUtility.SaveFilePanel("选择输出文件", Application.dataPath, "ChineseTexts", "txt");
            if (!string.IsNullOrEmpty(path))
            {
                outputFilePath = path;
            }
        }

        EditorGUILayout.Space();
        GUILayout.Label("提取选项:", EditorStyles.boldLabel);
        includeComments = EditorGUILayout.Toggle("包含注释中的中文", includeComments);
        includeStrings = EditorGUILayout.Toggle("包含字符串中的中文", includeStrings);
        includeVariableNames = EditorGUILayout.Toggle("包含变量名中的中文", includeVariableNames);

        EditorGUILayout.Space();
        GUI.enabled = !processing && !string.IsNullOrEmpty(rootDirectory) && !string.IsNullOrEmpty(outputFilePath);
        if (GUILayout.Button("开始提取"))
        {
            processing = true;
            extractedTexts.Clear();
            statusMessage = "正在处理...";
            EditorApplication.delayCall += () => ExtractChineseText();
        }
        GUI.enabled = true;

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("状态:", statusMessage);

        // 显示提取结果预览
        if (extractedTexts.Count > 0)
        {
            EditorGUILayout.Space();
            GUILayout.Label("提取结果预览:", EditorStyles.boldLabel);
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));
            foreach (string text in extractedTexts.Take(100))  // 只显示前100条以避免性能问题
            {
                EditorGUILayout.LabelField(text);
            }
            if (extractedTexts.Count > 100)
            {
                EditorGUILayout.LabelField("...(更多结果已省略)");
            }
            EditorGUILayout.EndScrollView();
        }
    }

    private void ExtractChineseText()
    {
        try
        {
            if (!Directory.Exists(rootDirectory))
            {
                statusMessage = "错误: 指定的目录不存在!";
                processing = false;
                return;
            }

            // 获取所有C#文件
            string[] files = Directory.GetFiles(rootDirectory, "*.cs", SearchOption.AllDirectories);
            statusMessage = $"找到 {files.Length} 个C#文件，开始处理...";
            
            HashSet<string> uniqueTexts = new HashSet<string>();
            int processedFiles = 0;

            foreach (string file in files)
            {
                string content = File.ReadAllText(file, Encoding.UTF8);
                List<string> chineseTexts = ExtractChineseFromFile(content);
                
                foreach (string text in chineseTexts)
                {
                    if (!string.IsNullOrWhiteSpace(text) && !uniqueTexts.Contains(text))
                    {
                        uniqueTexts.Add(text);
                    }
                }

                processedFiles++;
                if (processedFiles % 100 == 0)
                {
                    statusMessage = $"已处理 {processedFiles}/{files.Length} 个文件...";
                    Repaint();
                }
            }

            // 将结果写入文件
            extractedTexts = uniqueTexts.ToList();
            File.WriteAllLines(outputFilePath, extractedTexts, Encoding.UTF8);

            statusMessage = $"提取完成! 共找到 {extractedTexts.Count} 条中文文本，已保存到: {outputFilePath}";
        }
        catch (Exception ex)
        {
            statusMessage = $"错误: {ex.Message}";
        }
        finally
        {
            processing = false;
            Repaint();
        }
    }

    private List<string> ExtractChineseFromFile(string content)
    {
        List<string> results = new List<string>();

        // 中文字符的Unicode范围
        string chinesePattern = @"[\u4e00-\u9fa5]+";
        
        // 提取注释中的中文
        if (includeComments)
        {
            // 单行注释
            string singleLineCommentPattern = @"//.*?(?=\r|\n|$)";
            MatchCollection singleLineComments = Regex.Matches(content, singleLineCommentPattern);
            foreach (Match comment in singleLineComments)
            {
                ExtractChineseFromText(comment.Value, results);
            }

            // 多行注释
            string multiLineCommentPattern = @"/\*[\s\S]*?\*/";
            MatchCollection multiLineComments = Regex.Matches(content, multiLineCommentPattern);
            foreach (Match comment in multiLineComments)
            {
                ExtractChineseFromText(comment.Value, results);
            }
        }

        // 提取字符串中的中文
        if (includeStrings)
        {
            // 字符串文本
            string stringPattern = @"""(?:[^""\\]|\\.)*""";
            MatchCollection strings = Regex.Matches(content, stringPattern);
            foreach (Match str in strings)
            {
                string value = str.Value.Trim('"');
                ExtractChineseFromText(value, results);
            }
        }

        // 提取变量名中的中文（这个比较特殊，因为C#变量名通常不允许中文，但Unity中的GameObject名称等可能包含中文）
        if (includeVariableNames)
        {
            // 尝试匹配可能包含中文的变量名或属性名
            string variablePattern = @"\b\w+[\u4e00-\u9fa5]+\w*\b";
            MatchCollection variables = Regex.Matches(content, variablePattern);
            foreach (Match variable in variables)
            {
                results.Add(variable.Value);
            }
        }

        return results;
    }

    private void ExtractChineseFromText(string text, List<string> results)
    {
        string chinesePattern = @"[\u4e00-\u9fa5]+";
        MatchCollection matches = Regex.Matches(text, chinesePattern);
        
        foreach (Match match in matches)
        {
            // 获取包含中文的完整短语
            string chineseText = GetCompletePhraseContainingChinese(text, match.Index, match.Length);
            if (!string.IsNullOrWhiteSpace(chineseText))
            {
                results.Add(chineseText);
            }
        }
    }

    private string GetCompletePhraseContainingChinese(string text, int startIndex, int length)
    {
        // 向前查找短语的开始（空格、标点或行首）
        int phraseStart = startIndex;
        while (phraseStart > 0)
        {
            char c = text[phraseStart - 1];
            if (char.IsWhiteSpace(c) || char.IsPunctuation(c))
            {
                break;
            }
            phraseStart--;
        }

        // 向后查找短语的结束（空格、标点或行尾）
        int phraseEnd = startIndex + length;
        while (phraseEnd < text.Length)
        {
            char c = text[phraseEnd];
            if (char.IsWhiteSpace(c) || char.IsPunctuation(c))
            {
                break;
            }
            phraseEnd++;
        }

        // 提取完整短语
        return text.Substring(phraseStart, phraseEnd - phraseStart).Trim();
    }
}

// 命令行版本的中文文本提取工具
public class ChineseTextExtractorCLI
{
    [MenuItem("Tools/Extract Chinese Text (CLI)")]
    public static void ExtractChineseText()
    {
        string rootDirectory = Application.dataPath;
        string outputFilePath = Path.Combine(Application.dataPath, "../ChineseTexts.txt");
        
        Debug.Log($"开始从 {rootDirectory} 提取中文文本...");
        
        try
        {
            // 获取所有C#文件
            string[] files = Directory.GetFiles(rootDirectory, "*.cs", SearchOption.AllDirectories);
            Debug.Log($"找到 {files.Length} 个C#文件");
            
            HashSet<string> uniqueTexts = new HashSet<string>();
            
            foreach (string file in files)
            {
                string content = File.ReadAllText(file, Encoding.UTF8);
                List<string> chineseTexts = ExtractChineseFromFile(content);
                
                foreach (string text in chineseTexts)
                {
                    if (!string.IsNullOrWhiteSpace(text) && !uniqueTexts.Contains(text))
                    {
                        uniqueTexts.Add(text);
                    }
                }
            }
            
            // 将结果写入文件
            List<string> sortedTexts = uniqueTexts.OrderBy(t => t).ToList();
            File.WriteAllLines(outputFilePath, sortedTexts, Encoding.UTF8);
            
            Debug.Log($"提取完成! 共找到 {sortedTexts.Count} 条中文文本，已保存到: {outputFilePath}");
        }
        catch (Exception ex)
        {
            Debug.LogError($"提取中文文本时出错: {ex.Message}");
        }
    }
    
    private static List<string> ExtractChineseFromFile(string content)
    {
        List<string> results = new List<string>();
        
        // 中文字符的Unicode范围
        string chinesePattern = @"[\u4e00-\u9fa5]+";
        
        // 提取注释中的中文
        // 单行注释
        string singleLineCommentPattern = @"//.*?(?=\r|\n|$)";
        MatchCollection singleLineComments = Regex.Matches(content, singleLineCommentPattern);
        foreach (Match comment in singleLineComments)
        {
            ExtractChineseFromText(comment.Value, results);
        }
        
        // 多行注释
        string multiLineCommentPattern = @"/\*[\s\S]*?\*/";
        MatchCollection multiLineComments = Regex.Matches(content, multiLineCommentPattern);
        foreach (Match comment in multiLineComments)
        {
            ExtractChineseFromText(comment.Value, results);
        }
        
        // 提取字符串中的中文
        string stringPattern = @"""(?:[^""\\]|\\.)*""";
        MatchCollection strings = Regex.Matches(content, stringPattern);
        foreach (Match str in strings)
        {
            string value = str.Value.Trim('"');
            ExtractChineseFromText(value, results);
        }
        
        return results;
    }
    
    private static void ExtractChineseFromText(string text, List<string> results)
    {
        string chinesePattern = @"[\u4e00-\u9fa5]+";
        MatchCollection matches = Regex.Matches(text, chinesePattern);
        
        foreach (Match match in matches)
        {
            // 获取包含中文的完整短语
            string chineseText = GetCompletePhraseContainingChinese(text, match.Index, match.Length);
            if (!string.IsNullOrWhiteSpace(chineseText))
            {
                results.Add(chineseText);
            }
        }
    }
    
    private static string GetCompletePhraseContainingChinese(string text, int startIndex, int length)
    {
        // 向前查找短语的开始（空格、标点或行首）
        int phraseStart = startIndex;
        while (phraseStart > 0)
        {
            char c = text[phraseStart - 1];
            if (char.IsWhiteSpace(c) || char.IsPunctuation(c))
            {
                break;
            }
            phraseStart--;
        }
        
        // 向后查找短语的结束（空格、标点或行尾）
        int phraseEnd = startIndex + length;
        while (phraseEnd < text.Length)
        {
            char c = text[phraseEnd];
            if (char.IsWhiteSpace(c) || char.IsPunctuation(c))
            {
                break;
            }
            phraseEnd++;
        }
        
        // 提取完整短语
        return text.Substring(phraseStart, phraseEnd - phraseStart).Trim();
    }
}

// 命令行工具的独立版本（可以从菜单直接运行）
public class ChineseTextExtractorSimple : MonoBehaviour
{
    [MenuItem("Tools/Extract Chinese Text (Simple)")]
    public static void ExtractChineseText()
    {
        string rootDirectory = Application.dataPath;
        string outputFilePath = Path.Combine(Application.dataPath, "../ChineseTexts.txt");
        
        Debug.Log($"开始从 {rootDirectory} 提取中文文本...");
        
        try
        {
            // 获取所有C#文件
            string[] files = Directory.GetFiles(rootDirectory, "*.cs", SearchOption.AllDirectories);
            Debug.Log($"找到 {files.Length} 个C#文件");
            
            List<string> allChineseTexts = new List<string>();
            
            foreach (string file in files)
            {
                string content = File.ReadAllText(file, Encoding.UTF8);
                
                // 使用正则表达式查找所有包含中文字符的文本
                string pattern = @"[^\r\n]*[\u4e00-\u9fa5]+[^\r\n]*";
                MatchCollection matches = Regex.Matches(content, pattern);
                
                foreach (Match match in matches)
                {
                    string text = match.Value.Trim();
                    if (!string.IsNullOrWhiteSpace(text))
                    {
                        allChineseTexts.Add(text);
                    }
                }
            }
            
            // 去重并排序
            List<string> uniqueTexts = allChineseTexts.Distinct().OrderBy(t => t).ToList();
            
            // 将结果写入文件
            File.WriteAllLines(outputFilePath, uniqueTexts, Encoding.UTF8);
            
            Debug.Log($"提取完成! 共找到 {uniqueTexts.Count} 条中文文本，已保存到: {outputFilePath}");
        }
        catch (Exception ex)
        {
            Debug.LogError($"提取中文文本时出错: {ex.Message}");
        }
    }
}
#endif