using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_Animator_StateEnter : StateMachineBehaviour
{

    public string SendStr = "";

    //public ThirdShotRole_ShootState T_RoleShootState;

    //ThirdShotRole_Role T_baseobj;

    //public FirstShotRole_ShootState F_RoleShootState;

    //FirstShotRole_Role F_baseobj;

    //public ThirdMechRole_ShootState M_RoleShootState;

    //ThirdMechRole_Role M_baseobj;

    //string RoleType = "";

    //bool isnullOBJ = false;

    // OnStateEnter is called when a transition starts and the state machine starts to evaluate this state
    override public void OnStateEnter(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
    {
        animator.SendMessage("On_Tool_Animator_StateEnter", SendStr);


        //if (isnullOBJ)
        //    return;

        //if(RoleType == "")
        //{
        //    T_baseobj = animator.GetComponent<ThirdShotRole_Animator>().baserole;
        //    F_baseobj = animator.GetComponent<FirstShotRole_Animator>().baserole;
        //    M_baseobj = animator.GetComponent<ThirdMechRole_Animator>().baserole;

        //    if (T_baseobj != null && F_baseobj == null && M_baseobj == null)
        //        RoleType = "T";
        //    else if (T_baseobj == null && F_baseobj != null && M_baseobj == null)
        //        RoleType = "F";
        //    else if (T_baseobj == null && F_baseobj == null && M_baseobj != null)
        //        RoleType = "M";
        //    else
        //        RoleType = "NULL";

        //}

        //if (RoleType == "NULL")
        //    return;
        //switch(RoleType)
        //{
        //    case "T":
        //        T_baseobj.OnAnimatorEnterShootState(T_RoleShootState);
        //        break;
        //    case "F":
        //        F_baseobj.OnAnimatorEnterShootState(F_RoleShootState);
        //        break;
        //    case "M":
        //        M_baseobj.OnAnimatorEnterShootState(M_RoleShootState);
        //        break;

        //}



    }

    // OnStateUpdate is called on each Update frame between OnStateEnter and OnStateExit callbacks
    //override public void OnStateUpdate(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
    //{
    //    
    //}

    // OnStateExit is called when a transition ends and the state machine finishes evaluating this state
    //override public void OnStateExit(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
    //{
    //    
    //}

    // OnStateMove is called right after Animator.OnAnimatorMove()
    //override public void OnStateMove(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
    //{
    //    // Implement code that processes and affects root motion
    //}

    // OnStateIK is called right after Animator.OnAnimatorIK()
    //override public void OnStateIK(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
    //{
    //    // Implement code that sets up animation IK (inverse kinematics)
    //}
}
