using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_BrokeComponent : MonoBehaviour
{

    public List<GameObject> BrokeObjs;
    public List<Combat_BrokeObjectProperty> BrokePropertys;
    public GameObject OriginObj;

    private bool isBroke = false;

    //参数
    //横向爆破力
    static float BoomHVMin = 0.0006f * 20;
    static float BoomHVMax = 0.0009f * 20;
    //竖向爆破力
    static float BoomVVMin = 0.0006f * 20;
    static float BoomVVMax = 0.0009f * 20;
    //上升爆破力
    static float BoomUPMin = 0.0003f * 60;
    static float BoomUPMax = 0.0007f * 60;
    //重力
    static float Gravity = 0.000105f * 20;
    static float DestoryHeight = -50f;
    //旋转
    static float RotMin = -0.02f;
    static float RotMax = 0.02f;

    private void Awake()
    {

        BrokeObjs = new List<GameObject>();
        BrokePropertys = new List<Combat_BrokeObjectProperty>();

        List<GameObject> boomTempList = new List<GameObject>();
        var childCount = transform.childCount;


        for (var i = 0; i < childCount; i++)
        {
            //if (transform.GetChild(i).gameObject.name.IndexOf("Effect") == -1)
            //    boomTempList.Add(transform.GetChild(i).gameObject);
            boomTempList.Add(transform.GetChild(i).gameObject);
        }

        for (var i = 0; i < boomTempList.Count; i++)
        {
            //if (i != boomTempList.Count - 1)
            //{
                var obj = boomTempList[i];
                BrokeObjs.Add(obj);
           // }
           // else
              
        }
        OriginObj = gameObject;

        for (var i = 0; i < this.BrokeObjs.Count; i++)
        {
            Combat_BrokeObjectProperty p = new Combat_BrokeObjectProperty();
            var oriPos = new Vector3(OriginObj.transform.position.x, 0, OriginObj.transform.position.z);
            var objPos = new Vector3(BrokeObjs[i].transform.position.x, 0, BrokeObjs[i].transform.position.z);
            p.dire = Vector3.Normalize(objPos - oriPos);
            p.upforce = Random.Range(BoomUPMin, BoomUPMax);
            p.forwardfoce = Random.Range(BoomHVMin, BoomHVMax);
            p.horizontalforre = Random.Range(BoomVVMin, BoomVVMax);
           //p.rotate = new Vector3(Random.Range(RotMin, RotMax), Random.Range(RotMin, RotMax), Random.Range(RotMin, RotMax));
            BrokePropertys.Add(p);
        }



    }

    private void FixedUpdate()
    {
        if (!isBroke)
            return;


        for (var i = 0; i < this.BrokeObjs.Count; i++)
        {
            var tiper = 500;
            this.BrokePropertys[i].upforce -= Gravity;
            //  this.BrokeObjs[i].transform.Translate(new Vector3(this.BrokePropertys[i].dire.x * this.BrokePropertys[i].forwardfoce*Time.fixedDeltaTime* tiper, this.BrokePropertys[i].upforce * Time.fixedDeltaTime * tiper, this.BrokePropertys[i].dire.z * this.BrokePropertys[i].horizontalforre * Time.fixedDeltaTime * tiper));
            // this.BrokeObjs[i].transform.Rotate(this.BrokePropertys[i].rotate * Time.fixedDeltaTime * tiper);

            this.BrokeObjs[i].transform.position+= new Vector3(this.BrokePropertys[i].dire.x * this.BrokePropertys[i].forwardfoce * Time.fixedDeltaTime * tiper, this.BrokePropertys[i].upforce * Time.fixedDeltaTime * tiper, this.BrokePropertys[i].dire.z * this.BrokePropertys[i].horizontalforre * Time.fixedDeltaTime * tiper);

            if (CheckDestroy(this.BrokeObjs[i]))
                return;
        }

    }

    public void Broke()
    {
        isBroke = true;
        OriginObj.gameObject.GetComponent<MeshRenderer>().enabled = false;
        OriginObj.gameObject.GetComponent<Collider>().enabled = false;
        for (var i = 0; i < BrokeObjs.Count; i++)
            BrokeObjs[i].gameObject.SetActive(true);
    }

    private bool CheckDestroy(GameObject obj)
    {
        if(obj.transform.position.y <DestoryHeight)
        {
            isBroke = false;
            SendMessage("BrokeOver");
            return true;
        }
        return false;
    }

    public class Combat_BrokeObjectProperty
    {
        public Vector3 dire;
        public float upforce;
        public float forwardfoce;
        public float horizontalforre;
        public Vector3 rotate;
    }

}

