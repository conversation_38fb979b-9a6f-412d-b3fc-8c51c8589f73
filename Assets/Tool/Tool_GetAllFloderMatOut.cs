using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Linq;
#if UNITY_EDITOR
public class Tool_GetAllFloderMatOut
{
    [MenuItem("Assets/Tool_GetAllFloderMatOut", true)]
    private static bool ValidateExtractMaterials()
    {
        return Selection.objects.Length > 0;
    }

    [MenuItem("Assets/Tool_GetAllFloderMatOut")]
    private static void ExtractMaterials()
    {
        foreach (Object obj in Selection.objects)
        {
            string folderPath = AssetDatabase.GetAssetPath(obj);
            if (!Directory.Exists(folderPath)) continue;

            // Get parent folder path
            string parentPath = Path.GetDirectoryName(folderPath);
            string outMatsFolderPath = Path.Combine(parentPath, "Out_Mats");

            // Create Out_Mats folder if it doesn't exist
            if (!Directory.Exists(outMatsFolderPath))
            {
                AssetDatabase.CreateFolder(parentPath, "Out_Mats");
            }

            // Get all material files in the selected folder and subfolders
            string[] materialGuids = AssetDatabase.FindAssets("t:Material", new[] { folderPath });
            Dictionary<string, int> materialNameCount = new Dictionary<string, int>();

            foreach (string guid in materialGuids)
            {
                string sourcePath = AssetDatabase.GUIDToAssetPath(guid);
                string materialName = Path.GetFileNameWithoutExtension(sourcePath);
                string targetFileName = materialName;
                string targetPath;

                // Keep trying new names until we find one that doesn't exist
                int counter = 0;
                do
                {
                    targetPath = Path.Combine(outMatsFolderPath, 
                        counter == 0 ? targetFileName + ".mat" : $"{targetFileName}_{counter}.mat");
                    counter++;
                } while (File.Exists(targetPath) || AssetDatabase.LoadAssetAtPath<Material>(targetPath) != null);

                // Move material to Out_Mats folder
                string error = AssetDatabase.MoveAsset(sourcePath, targetPath);
                if (!string.IsNullOrEmpty(error))
                {
                    Debug.LogError($"Failed to move {sourcePath}: {error}");
                }
            }
        }

        AssetDatabase.Refresh();
        Debug.Log("Materials extraction completed!");
    }
}
#endif