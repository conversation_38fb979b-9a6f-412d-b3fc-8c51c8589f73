using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class Tool_CheckTrigger : MonoBehaviour
{
    public float SaveTime = 0.1f;

    public List<GameObject> TriObjs = new List<GameObject>();

    public Dictionary<GameObject, float> TriObjsDesTimers = new Dictionary<GameObject, float>();

    public Action<GameObject, GameObject> OnTriggerObj_Event;

    public Action<GameObject> OnTriggerOutObj_Event;

    Collider collider;

   // public GameObject[] tt;
   // public Vector3[] closepos;

    private void OnEnable()
    {
        TriObjs = new List<GameObject>();
        TriObjsDesTimers = new Dictionary<GameObject, float>();
        collider = GetComponent<Collider>();
    }

    private void Update()
    {
       
        List<GameObject> DesObj = new List<GameObject>();
        for (var i = 0; i < TriObjs.Count; i++)
        {
            TriObjsDesTimers[TriObjs[i]] -= Time.deltaTime;
            if (TriObjsDesTimers[TriObjs[i]] <= 0)
            {
                DesObj.Add(TriObjs[i]);
            }
        }

        for (var i = 0; i < DesObj.Count; i++)
        {
            if (DesObj[i] != null)
                OnTriggerOutObj_Event?.Invoke(DesObj[i]);
            TriObjsDesTimers.Remove(DesObj[i]);
            TriObjs.Remove(DesObj[i]);
        }



        //string s= " ";
        //for (var i = 0; i < TriObjs.Count; i++)
        //{
        //    s += TriObjs[i].name + " ";
        //}
        //Debug.LogError(TriObjs.Count+ "  "  + s);

        //Debug.LogError( "----");
        //var temp = SortObjsByDistanceToParent();
        //for(var i = 0; i <temp.Length;i++)
        //{
        //    Debug.LogError(temp[i].name);
        //}
        //Debug.LogError("----");
    }


    private void OnTriggerStay(Collider other)
    {

        if (TriObjs.Contains(other.gameObject))
        {
            TriObjsDesTimers[other.gameObject] = SaveTime;
        }
        else
        {
           TriObjsDesTimers[other.gameObject] = SaveTime;
            TriObjs.Add(other.gameObject);
            OnTriggerObj_Event?.Invoke(this.gameObject, other.gameObject);
        }
    }

    public GameObject[] SortObjsByDistanceToParent()
    {
        // 检查列表和共同父对象是否为空  
        if (TriObjs == null || TriObjs.Count == 0 || transform.parent == null)
            return null;

        List<GameObject> objs = new List<GameObject>();
        List<Vector3> closeColPos = new List<Vector3>();

        for(var i = 0; i <TriObjs.Count;i++)
        {
            objs.Add(TriObjs[i]);
            // 获取当前物体的边界框上的最近点  
            var temppos = transform.InverseTransformPoint(
           collider.ClosestPointOnBounds(TriObjs[i].transform.position));
            // 将最近点转换回世界坐标  
            closeColPos.Add(transform.TransformPoint(temppos));
        }


        // 使用冒泡排序根据 closeColPos 到 transform.parent 的距离排序 objs  
        for (int i = 0; i < objs.Count - 1; i++)
        {
            for (int j = 0; j < objs.Count - i - 1; j++)
            {
                float distanceA = Vector3.Distance(closeColPos[j], transform.parent.position);
                float distanceB = Vector3.Distance(closeColPos[j + 1], transform.parent.position);

                if (distanceA > distanceB)
                {
                    // 交换 objs 和 closeColPos 中的元素  
                    GameObject tempObj = objs[j];
                    objs[j] = objs[j + 1];
                    objs[j + 1] = tempObj;

                    Vector3 tempPos = closeColPos[j];
                    closeColPos[j] = closeColPos[j + 1];
                    closeColPos[j + 1] = tempPos;
                }
            }
        }



        // 将排序后的列表转换为数组  
        return objs.ToArray();
    }

}

