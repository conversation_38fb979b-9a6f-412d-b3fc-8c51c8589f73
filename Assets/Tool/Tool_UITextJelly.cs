using System;
using System.Collections;
using UnityEngine;
using TMPro;

/// <summary>
/// 文本果冻动画工具 - 使文本字符缩放产生果冻效果
/// </summary>
public class Tool_UITextJelly : MonoBehaviour
{
    [SerializeField, Tooltip("文本组件")]
    private TextMeshProUGUI targetText;

    [Serialize<PERSON>ield, Toolt<PERSON>("最大缩放倍数")]
    private float maxScale = 1.05f;

    [SerializeField, Toolt<PERSON>("动画周期(秒)")]
    private float animationDuration = 0.7f;

    [SerializeField, Tooltip("字符间动画偏移")]
    private float characterOffset = 0.4f;

    private void OnEnable()
    {
        if (targetText == null)
        {
            targetText = GetComponent<TextMeshProUGUI>();
            if (targetText == null)
            {
                Debug.LogError("Tool_UITextJelly: 未找到TextMeshProUGUI组件");
                return;
            }
        }

        StartCoroutine(AnimateText());
    }

    /// <summary>
    /// 文本动画协程
    /// </summary>
    private IEnumerator AnimateText()
    {
        if (targetText == null) yield break;

        targetText.ForceMeshUpdate();

        TMP_TextInfo textInfo = targetText.textInfo;
        if (textInfo == null || textInfo.meshInfo == null || textInfo.meshInfo.Length == 0)
        {
            Debug.LogError("TextMeshPro 文本信息获取失败");
            yield break;
        }

        Vector3[] originalVertices = new Vector3[textInfo.meshInfo[0].vertices.Length];
        Array.Copy(textInfo.meshInfo[0].vertices, originalVertices, originalVertices.Length);

        float totalTime = 0;
        while (true)
        {
            totalTime += Time.deltaTime;

            targetText.ForceMeshUpdate();

            for (int i = 0; i < textInfo.characterCount; i++)
            {
                TMP_CharacterInfo charInfo = textInfo.characterInfo[i];
                if (!charInfo.isVisible) continue;

                float charTime = totalTime - (i * characterOffset);

                // 使用正弦函数计算当前缩放值
                float scaleMultiplier = 1 + (Mathf.Sin(charTime * Mathf.PI / animationDuration) + 1) * (maxScale - 1) / 2;

                int vertexIndex = charInfo.vertexIndex;

                // 计算字符中心点
                Vector3 centerPoint = Vector3.zero;
                for (int j = 0; j < 4; j++)
                {
                    centerPoint += originalVertices[vertexIndex + j];
                }
                centerPoint /= 4;

                // 对每个顶点应用缩放
                for (int j = 0; j < 4; j++)
                {
                    Vector3 originalVertex = originalVertices[vertexIndex + j];
                    Vector3 offset = originalVertex - centerPoint;
                    Vector3 scaledOffset = offset * scaleMultiplier;
                    textInfo.meshInfo[0].vertices[vertexIndex + j] = centerPoint + scaledOffset;
                }
            }

            // 更新网格
            for (int i = 0; i < textInfo.meshInfo.Length; i++)
            {
                textInfo.meshInfo[i].mesh.vertices = textInfo.meshInfo[i].vertices;
                targetText.UpdateGeometry(textInfo.meshInfo[i].mesh, i);
            }

            yield return null;
        }
    }
}