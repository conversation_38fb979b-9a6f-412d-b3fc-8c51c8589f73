using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_FindObjectInParent
{
    // 递归查找
   public static Transform FindChildByNameRecursive(Transform parent, string targetName)
    {
        foreach (Transform child in parent)
        {
            // 检查当前子物体是否为目标孙物体  
            if (child.name == targetName)
            {
                
                    return child;
                
            }
            else
            {
                // 如果当前子物体不是目标，则递归查找它的子物体  
                Transform found = FindChildByNameRecursive(child, targetName);
                if (found != null)
                {
                    return found;
                }
            }
        }

        // 如果没有找到，返回null  
        return null;
    }

}
