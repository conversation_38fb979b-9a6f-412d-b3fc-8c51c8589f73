using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_GetScreenCenterToPoint 
{

   // public static float Default_Distance = 15;

    public static Vector3 Get(Camera camera,float distance)
    {
        //,float distance,bool useHit
        Ray ray = camera.ScreenPointToRay(new Vector3(Screen.width / 2.0f, Screen.height / 2.0f, 0));

       // RaycastHit hitinfo;
        //if (Physics.Raycast(ray, out hitinfo, Default_Distance))
        //{
        //    ray.GetPoint(Default_Distance);
        //    return hitinfo.point;
        //}
        return ray.GetPoint(distance);
    }

}
