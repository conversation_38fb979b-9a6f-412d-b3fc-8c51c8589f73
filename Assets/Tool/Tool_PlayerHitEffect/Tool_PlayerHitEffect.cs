using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;
using UnityEngine.UI;

public class Tool_PlayerHitEffect : MonoBehaviour
{
    public static Tool_PlayerHitEffect Instance
    {
        get
        {
            if (instance == null)
            {
                GameObject root = GameObject.Find("Canvas");
                instance = root.transform.Find("Tool_PlayerHitEffect").GetComponent<Tool_PlayerHitEffect>();
            }
            return instance;
        }
        set
        {
            instance = value;
        }
    }

    private static Tool_PlayerHitEffect instance;



    public static float ShowAlphaSpeed = 6;
    public static float HideAlphaSpeed = 3;
    public static float TagetAlpha = 0.3f;
    public static float ShowAlphaTopTime = 0.1f;


    public Image[] hitscreens;

    int State = 0;

    float ShowAlphaTopTimer = 0;

    void Awake()
    {
        hitscreens = GetComponentsInChildren<Image>();
    }

    void Update()
    {
        switch(State)
        {
            case 1:
                bool allget = true;
                for (var i = 0; i < hitscreens.Length; i++)
                {
                    hitscreens[i].color = new Color(hitscreens[i].color.r, hitscreens[i].color.g, hitscreens[i].color.b, hitscreens[i].color.a + ShowAlphaSpeed * Time.deltaTime);
                    if(hitscreens[i].color.a <= TagetAlpha)
                        allget = false;
                }


                    if (allget)
                    State = 2;

                break;
            case 2:
                ShowAlphaTopTimer -= Time.deltaTime;
                if (ShowAlphaTopTimer <= 0)
                    State = 3;
                break;
            case 3:
                bool allhide = true;
                for (var i = 0; i < hitscreens.Length; i++)
                {
                    hitscreens[i].color = new Color(hitscreens[i].color.r, hitscreens[i].color.g, hitscreens[i].color.b, hitscreens[i].color.a - HideAlphaSpeed * Time.deltaTime);
                    if (hitscreens[i].color.a >= 0)
                        allhide = false;
                }

                if (allhide)
                {
                    Instance.gameObject.SetActive(false);
                    State = 0;
                }
         
                break;
        }
    }

    public void Show()
    {
        for (var i = 0; i < hitscreens.Length; i++)
        {
            hitscreens[i].color = new Color(hitscreens[i].color.r, hitscreens[i].color.g, hitscreens[i].color.b, 0);
        }
        Instance.gameObject.SetActive(true);
        State = 1;
        ShowAlphaTopTimer = ShowAlphaTopTime;


    }
}

