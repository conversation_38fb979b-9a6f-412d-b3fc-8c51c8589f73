using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_BulletCounter_Data
{
    public int MaxCount;
    public int CurrentCount;

    public Tool_BulletCounter_Data(int max)
    {
        MaxCount = max;
        CurrentCount = max;
    }

    public bool Enough()
    {
        return CurrentCount > 0;
    }

    public bool Cut(int count)
    {
        if (CurrentCount <= 0)
            return false;

        CurrentCount -= count;

        if (CurrentCount <= 0)
            return false;

        return true;
    }
   
    public void Reload()
    {
        CurrentCount = MaxCount;
    }


}
