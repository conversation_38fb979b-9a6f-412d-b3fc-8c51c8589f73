using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_ReplaceMeshByUnNullMesh_Cut : MonoBehaviour
{
    public int[] CutTos;

    public bool NeedWork = false;


    void Start()
    {
        var getchild = GetComponentsInChildren<Tool_ReplaceMeshByUnNullMesh>(true);
        for (var i = 0; i < getchild.Length; i++)
        {
            Make(getchild[i].meshFilter1, getchild[i].meshs1, CutTos[i]);
            Make(getchild[i].meshFilter2, getchild[i].meshs2, CutTos[i]);
            Make(getchild[i].meshFilter3, getchild[i].meshs3, CutTos[i]);
            Make(getchild[i].meshFilter4, getchild[i].meshs4, CutTos[i]);
            Make(getchild[i].meshFilter5, getchild[i].meshs5, CutTos[i]);
            if (NeedWork)
                GameObject.Destroy(getchild[i]);
        }
        if (NeedWork)
            GameObject.Destroy(this);
    }

    void Make(MeshFilter mf, Mesh[] ms, int index)
    {
        if (mf != null)
        {
            mf.mesh = ms[index];
            for (var i = 0; i < ms.Length; i++)
                if (i != index && NeedWork)
                    ms[i] = null;
        }
    }

}
