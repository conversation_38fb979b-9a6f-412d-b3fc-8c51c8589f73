using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_GetOrAddComponent : MonoBehaviour
{

   public  static Tool_GetOrAddComponent Instance
    {
        get
        {
            if(instance == null)
            {

                GameObject go = new GameObject();
                go.name = "Tool_GetOrAddComponent";
                instance = go.AddComponent<Tool_GetOrAddComponent>();
            }
            return instance;
        }

    }
    static Tool_GetOrAddComponent instance;

    float DeleteTimer = 0;
    float DeleteTime = 1;

    private void Update()
    {
        DeleteTimer += Time.deltaTime;
        if (DeleteTimer > 1f)
        {
            CleanupDictionary(cache);
            Debug.LogError("delete" + cache.Count);
            DeleteTimer = 0;
        }
    }


    // 字典，用于存储游戏对象和组件的映射关系
    private Dictionary<GameObject, Dictionary<Type, Component>> cache = new Dictionary<GameObject, Dictionary<Type, Component>>();

    // 获取指定游戏对象上的指定类型的组件
    public T GetOrAddComponent<T>(GameObject gameObject) where T : Component
    {
        // 检查字典中是否已经包含该游戏对象的缓存
        if (!cache.ContainsKey(gameObject))
        {
            // 如果不包含，则添加一个新的字典项
            cache[gameObject] = new Dictionary<Type, Component>();
        }

        // 获取指定类型的组件类型
        Type componentType = typeof(T);

        // 检查是否已经缓存了该类型的组件
        if (!cache[gameObject].ContainsKey(componentType))
        {
            // 如果不包含，则获取组件并缓存它
            Component component = gameObject.GetComponent(componentType);
            cache[gameObject][componentType] = component;
        }



        // 返回缓存的组件（可能为 null，如果游戏对象上没有该组件）
        return cache[gameObject][componentType] as T;
    }


    void CleanupDictionary<TValue>(Dictionary<GameObject, TValue> dictionary)
    {
        List<GameObject> keysToRemove = new List<GameObject>();

        // 遍历字典的键和值
        foreach (KeyValuePair<GameObject, TValue> kvp in dictionary)
        {
            GameObject go = kvp.Key;
            TValue value = kvp.Value;
            if (go.transform == null) // 通常go == null的检查是多余的，因为Dictionary不允许null键，除非外部干预
            {
                // 将需要删除的键添加到列表中
                keysToRemove.Add(go);
            }

        }

        // 从字典中删除无效的键
        foreach (GameObject key in keysToRemove)
        {
            dictionary.Remove(key);
        }
    }



}
