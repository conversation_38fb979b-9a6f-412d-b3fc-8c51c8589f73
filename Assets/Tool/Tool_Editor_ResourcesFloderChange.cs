using System;
using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;
using Object = UnityEngine.Object;

#if UNITY_EDITOR
public class Tool_Editor_ResourcesFloderChange : EditorWindow
{
    private static string logMessages = "";
    private Vector2 scrollPosition;
    
    // 创建菜单项 - 显示日志窗口
    [MenuItem("Tools/Resources文件夹处理工具/显示处理日志")]
    public static void ShowWindow()
    {
        Tool_Editor_ResourcesFloderChange window = GetWindow<Tool_Editor_ResourcesFloderChange>("Resources文件夹处理日志");
        window.minSize = new Vector2(500, 300);
    }
    
    // 右键菜单 - 将带Resources字符的文件夹改为纯Resources
    [MenuItem("Assets/Tool_Editor_ResourcesFloderChange/将带Resources字符的文件夹改为纯Resources", false, 30)]
    public static void ConvertToResources()
    {
        List<string> folderPaths = GetSelectedFolderPaths();
        if (folderPaths.Count == 0)
        {
            Debug.LogWarning("请选择至少一个文件夹！");
            return;
        }
        
        logMessages = "";
        int totalProcessed = 0;
        
        logMessages += $"开始处理 {folderPaths.Count} 个选中的文件夹...\n";
        
        foreach (string folderPath in folderPaths)
        {
            if (Directory.Exists(folderPath))
            {
                logMessages += $"正在处理文件夹: {folderPath}\n";
                int processed = ProcessFoldersToResources(folderPath);
                totalProcessed += processed;
            }
            else
            {
                logMessages += $"错误: {folderPath} 不是有效的文件夹\n";
            }
        }
        
        logMessages += $"功能1完成: 总共处理 {totalProcessed} 个文件夹\n";
        AssetDatabase.Refresh();
        
        // 如果有处理结果，显示日志窗口
       // ShowWindow();
        
        Debug.Log($"处理完成: 将 {totalProcessed} 个带Resources字符的文件夹改为纯Resources");
    }
    
    // 右键菜单 - 将Resources文件夹改为Resources_TOOLRESOURCE
    [MenuItem("Assets/Tool_Editor_ResourcesFloderChange/将Resources文件夹改为Resources_TOOLRESOURCE", false, 31)]
    public static void ConvertToToolResource()
    {
        List<string> folderPaths = GetSelectedFolderPaths();
        if (folderPaths.Count == 0)
        {
            Debug.LogWarning("请选择至少一个文件夹！");
            return;
        }
        
        logMessages = "";
        int totalProcessed = 0;
        
        logMessages += $"开始处理 {folderPaths.Count} 个选中的文件夹...\n";
        
        foreach (string folderPath in folderPaths)
        {
            if (Directory.Exists(folderPath))
            {
                logMessages += $"正在处理文件夹: {folderPath}\n";
                int processed = ProcessResourcesToToolResource(folderPath);
                totalProcessed += processed;
            }
            else
            {
                logMessages += $"错误: {folderPath} 不是有效的文件夹\n";
            }
        }
        
        logMessages += $"功能2完成: 总共处理 {totalProcessed} 个文件夹\n";
        AssetDatabase.Refresh();
        
        // 如果有处理结果，显示日志窗口
     //   ShowWindow();
        
        Debug.Log($"处理完成: 将 {totalProcessed} 个Resources文件夹改为Resources_TOOLRESOURCE");
    }
    
    // 验证菜单项 - 确保只有在选择了文件夹时才启用菜单
    [MenuItem("Assets/Tool_Editor_ResourcesFloderChange/将带Resources字符的文件夹改为纯Resources", true)]
    [MenuItem("Assets/Tool_Editor_ResourcesFloderChange/将Resources文件夹改为Resources_TOOLRESOURCE", true)]
    private static bool ValidateSelection()
    {
        return GetSelectedFolderPaths().Count > 0;
    }
    
    // 获取当前选中的所有文件夹路径
    private static List<string> GetSelectedFolderPaths()
    {
        List<string> folderPaths = new List<string>();
        
        foreach (Object obj in Selection.objects)
        {
            string path = AssetDatabase.GetAssetPath(obj);
            if (Directory.Exists(path))
            {
                folderPaths.Add(path);
            }
        }
        
        return folderPaths;
    }
    
    // 窗口GUI
    private void OnGUI()
    {
        EditorGUILayout.LabelField("Resources文件夹处理日志", EditorStyles.boldLabel);
        EditorGUILayout.Space(10);
        
        // 显示日志信息
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        EditorGUILayout.TextArea(logMessages, GUILayout.ExpandHeight(true));
        EditorGUILayout.EndScrollView();
        
        // 清空日志按钮
        if (GUILayout.Button("清空日志"))
        {
            logMessages = "";
        }
    }

    // 功能1: 将带有Resources字符的文件夹名称修改为纯Resources
    private static int ProcessFoldersToResources(string rootPath)
    {
        // 获取所有子文件夹
        string[] allDirectories = Directory.GetDirectories(rootPath, "*", SearchOption.AllDirectories);
        logMessages += $"在 {rootPath} 下找到 {allDirectories.Length} 个子文件夹\n";
        
        // 调试信息：列出所有子文件夹
        logMessages += "所有子文件夹列表:\n";
        foreach (string dir in allDirectories)
        {
            logMessages += $"- {dir} (名称: {Path.GetFileName(dir)})\n";
        }
        
        // 先收集所有需要处理的文件夹，避免在遍历过程中修改导致问题
        List<string> directoriesToProcess = new List<string>();
        
        foreach (string dir in allDirectories)
        {
            string dirName = Path.GetFileName(dir);
            
            // 检查文件夹名称是否包含"Resources"但不等于"Resources"
            bool containsResources = dirName.IndexOf("Resources", StringComparison.OrdinalIgnoreCase) >= 0;
            bool isExactlyResources = dirName.Equals("Resources", StringComparison.OrdinalIgnoreCase);
            
            logMessages += $"检查文件夹: {dir} (名称: {dirName})\n";
            logMessages += $"  - 包含Resources: {containsResources}\n";
            logMessages += $"  - 是否为纯Resources: {isExactlyResources}\n";
            
            if (containsResources && !isExactlyResources)
            {
                directoriesToProcess.Add(dir);
                logMessages += $"  => 符合条件，添加到处理列表\n";
            }
            else
            {
                logMessages += $"  => 不符合条件，跳过\n";
            }
        }
        
        // 检查当前文件夹本身是否符合条件
        string currentDirName = Path.GetFileName(rootPath);
        bool rootContainsResources = currentDirName.IndexOf("Resources", StringComparison.OrdinalIgnoreCase) >= 0;
        bool rootIsExactlyResources = currentDirName.Equals("Resources", StringComparison.OrdinalIgnoreCase);
        
        logMessages += $"检查当前文件夹: {rootPath} (名称: {currentDirName})\n";
        logMessages += $"  - 包含Resources: {rootContainsResources}\n";
        logMessages += $"  - 是否为纯Resources: {rootIsExactlyResources}\n";
        
        if (rootContainsResources && !rootIsExactlyResources)
        {
            directoriesToProcess.Add(rootPath);
            logMessages += $"  => 当前文件夹符合条件，添加到处理列表\n";
        }
        else
        {
            logMessages += $"  => 当前文件夹不符合条件，跳过\n";
        }
        
        if (directoriesToProcess.Count == 0)
        {
            logMessages += $"在 {rootPath} 及其子文件夹中没有找到符合条件的文件夹\n";
            logMessages += "符合条件的文件夹需要：1) 包含'Resources'字符; 2) 不等于'Resources'\n";
            return 0;
        }
        
        logMessages += $"找到 {directoriesToProcess.Count} 个需要处理的文件夹:\n";
        foreach (string dir in directoriesToProcess)
        {
            logMessages += $"- {dir}\n";
        }
        
        // 按照路径长度降序排序，确保先处理最深层的文件夹
        directoriesToProcess.Sort((a, b) => b.Length.CompareTo(a.Length));
        
        int count = 0;
        
        // 处理收集到的所有文件夹
        foreach (string dir in directoriesToProcess)
        {
            // 检查当前路径是否仍然存在（可能由于父文件夹已被重命名而不存在）
            if (!Directory.Exists(dir))
            {
                logMessages += $"跳过: {dir} - 文件夹不再存在（可能已被处理）\n";
                continue;
            }
            
            string dirName = Path.GetFileName(dir);
            string parentDir = Path.GetDirectoryName(dir);
            string newPath = Path.Combine(parentDir, "Resources");
            
            // 检查目标路径是否已存在
            if (Directory.Exists(newPath))
            {
                logMessages += $"跳过: {dir} - 目标路径已存在Resources文件夹\n";
                continue;
            }
            
            try
            {
                // 重命名文件夹
                AssetDatabase.DisallowAutoRefresh();
                
                // 获取.meta文件路径
                string metaPath = dir + ".meta";
                string newMetaPath = newPath + ".meta";
                
                logMessages += $"准备重命名: {dir} -> {newPath}\n";
                
                // 重命名文件夹
                Directory.Move(dir, newPath);
                
                // 如果meta文件存在，也需要重命名
                if (File.Exists(metaPath))
                {
                    File.Move(metaPath, newMetaPath);
                    logMessages += $"已重命名meta文件: {metaPath} -> {newMetaPath}\n";
                }
                
                count++;
                logMessages += $"成功: {dir} -> {newPath}\n";
                
                AssetDatabase.AllowAutoRefresh();
            }
            catch (System.Exception e)
            {
                AssetDatabase.AllowAutoRefresh();
                logMessages += $"错误: 无法重命名 {dir}: {e.Message}\n";
            }
        }
        
        return count;
    }

    // 功能2: 将名为Resources的文件夹重命名为Resources_TOOLRESOURCE
    private static int ProcessResourcesToToolResource(string rootPath)
    {
        // 获取所有子文件夹
        string[] allDirectories = Directory.GetDirectories(rootPath, "*", SearchOption.AllDirectories);
        logMessages += $"在 {rootPath} 下找到 {allDirectories.Length} 个子文件夹\n";
        
        // 调试信息：列出所有子文件夹
        logMessages += "所有子文件夹列表:\n";
        foreach (string dir in allDirectories)
        {
            logMessages += $"- {dir} (名称: {Path.GetFileName(dir)})\n";
        }
        
        // 先收集所有需要处理的文件夹，避免在遍历过程中修改导致问题
        List<string> directoriesToProcess = new List<string>();
        
        foreach (string dir in allDirectories)
        {
            string dirName = Path.GetFileName(dir);
            
            // 检查文件夹名称是否为"Resources"
            bool isExactlyResources = dirName.Equals("Resources", StringComparison.OrdinalIgnoreCase);
            
            logMessages += $"检查文件夹: {dir} (名称: {dirName})\n";
            logMessages += $"  - 是否为纯Resources: {isExactlyResources}\n";
            
            if (isExactlyResources)
            {
                directoriesToProcess.Add(dir);
                logMessages += $"  => 符合条件，添加到处理列表\n";
            }
            else
            {
                logMessages += $"  => 不符合条件，跳过\n";
            }
        }
        
        // 检查当前文件夹本身是否符合条件
        string currentDirName = Path.GetFileName(rootPath);
        bool rootIsExactlyResources = currentDirName.Equals("Resources", StringComparison.OrdinalIgnoreCase);
        
        logMessages += $"检查当前文件夹: {rootPath} (名称: {currentDirName})\n";
        logMessages += $"  - 是否为纯Resources: {rootIsExactlyResources}\n";
        
        if (rootIsExactlyResources)
        {
            directoriesToProcess.Add(rootPath);
            logMessages += $"  => 当前文件夹符合条件，添加到处理列表\n";
        }
        else
        {
            logMessages += $"  => 当前文件夹不符合条件，跳过\n";
        }
        
        if (directoriesToProcess.Count == 0)
        {
            logMessages += $"在 {rootPath} 及其子文件夹中没有找到符合条件的文件夹\n";
            logMessages += "符合条件的文件夹需要：名称完全等于'Resources'\n";
            return 0;
        }
        
        logMessages += $"找到 {directoriesToProcess.Count} 个需要处理的文件夹:\n";
        foreach (string dir in directoriesToProcess)
        {
            logMessages += $"- {dir}\n";
        }
        
        // 按照路径长度降序排序，确保先处理最深层的文件夹
        directoriesToProcess.Sort((a, b) => b.Length.CompareTo(a.Length));
        
        int count = 0;
        
        // 处理收集到的所有文件夹
        foreach (string dir in directoriesToProcess)
        {
            // 检查当前路径是否仍然存在（可能由于父文件夹已被重命名而不存在）
            if (!Directory.Exists(dir))
            {
                logMessages += $"跳过: {dir} - 文件夹不再存在（可能已被处理）\n";
                continue;
            }
            
            string parentDir = Path.GetDirectoryName(dir);
            string newPath = Path.Combine(parentDir, "Resources_TOOLRESOURCE");
            
            // 检查目标路径是否已存在
            if (Directory.Exists(newPath))
            {
                logMessages += $"跳过: {dir} - 目标路径已存在Resources_TOOLRESOURCE文件夹\n";
                continue;
            }
            
            try
            {
                // 重命名文件夹
                AssetDatabase.DisallowAutoRefresh();
                
                // 获取.meta文件路径
                string metaPath = dir + ".meta";
                string newMetaPath = newPath + ".meta";
                
                logMessages += $"准备重命名: {dir} -> {newPath}\n";
                
                // 重命名文件夹
                Directory.Move(dir, newPath);
                
                // 如果meta文件存在，也需要重命名
                if (File.Exists(metaPath))
                {
                    File.Move(metaPath, newMetaPath);
                    logMessages += $"已重命名meta文件: {metaPath} -> {newMetaPath}\n";
                }
                
                count++;
                logMessages += $"成功: {dir} -> {newPath}\n";
                
                AssetDatabase.AllowAutoRefresh();
            }
            catch (System.Exception e)
            {
                AssetDatabase.AllowAutoRefresh();
                logMessages += $"错误: 无法重命名 {dir}: {e.Message}\n";
            }
        }
        
        return count;
    }
}
#endif
