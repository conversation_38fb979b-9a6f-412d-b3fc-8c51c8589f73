
using UnityEngine;
using System.Collections.Generic;

public static class Tool_InputManager
{
    private static List<Tool_IInputChecker> checkers = new List<Tool_IInputChecker>();
    
    public static void RegisterChecker(Tool_IInputChecker checker)
    {
        if (!checkers.Contains(checker))
        {
            checkers.Add(checker);
        }
    }

    public static void UnregisterChecker(Tool_IInputChecker checker)
    {
        checkers.Remove(checker);
    }

    public static bool CanProcessInput()
    {
        // 如果没有检查器，允许输入
        if (checkers.Count == 0) return true;

        // 所有检查器都必须允许输入才返回true
        foreach (var checker in checkers)
        {
            if (!checker.CanProcessInput())
                return false;
        }
        return true;
    }

    // 可选：清理所有检查器
    public static void ClearCheckers()
    {
        checkers.Clear();
    }
}

public interface Tool_IInputChecker
{
    bool CanProcessInput();
}

// void Start()
// {
//     InputManager.RegisterChecker(this);
// }

// void OnDestroy()
// {
//     InputManager.UnregisterChecker(this);
// }

// public bool CanProcessInput()
// {
//     // 在这里实现你的输入控制逻辑
//     return !isPaused && !isInDialog && !isLoading; // 示例条件
// }


