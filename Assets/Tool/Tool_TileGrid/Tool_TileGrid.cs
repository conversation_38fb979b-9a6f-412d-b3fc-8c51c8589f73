using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

public class Tool_TileGrid : MonoBehaviour
{
    [Header("网格配置")]
    public Vector3 cellSize = Vector3.one;    // 每格大小（xyz方向）
    public Vector3 gridOrigin = Vector3.zero; // 网格起始点
    public int maxHeightLayers = 10;  // 最大高度层数

    [Header("推进配置")]
    public float pushSpeed = 2f;      // 推进速度
    // 移除checkAllHeightLayers字段，因为现在恒定为true - 任何高度层有物体就阻挡，同位置所有高度层一起移动
    [Tooltip("是否启用事件驱动推进（推荐）：true = 仅在网格变化时检查推进，false = 持续轮询检查")]
    public bool enableEventDrivenPush = true; // 事件驱动推进开关
    [Tooltip("无推进区域边界：gridZ >= 此值的物体不进行推进动画，直接设置到目标位置（-1表示禁用此功能）")]
    public int noPushZoneThreshold = -1; // 无推进区域阈值

    [Header("性能优化")]
    [Tooltip("启用缓存优化：减少重复计算，提高大量物体时的性能")]
    public bool enableCacheOptimization = true; // 是否启用缓存优化
    [Tooltip("推进检测间隔（仅用于轮询模式）")]
    public float pushCheckInterval = 0.1f; // 推进检测间隔

    /// <summary>
    /// 推进完成事件 - 当物体推进完成并停下时触发
    /// </summary>
    public static System.Action<GridTileObject> OnPushCompleted;

    // 核心数据结构
    private Dictionary<Vector3Int, GridTileObject> gridData;
    private Dictionary<GameObject, GridTileObject> objectToTileMap;
    private List<GridTileObject> activeTileObjects;

    // 性能优化：缓存和索引
    private Dictionary<Vector2Int, List<GridTileObject>> positionGroupCache;
    private HashSet<Vector2Int> dirtyPositions;
    private Dictionary<Vector2Int, HashSet<GridTileObject>> frontPositionIndex;
    private List<List<GridTileObject>> reusableGroupList;
    private bool needsGroupCacheUpdate = true;
    private bool needsPushCheck = false;

    // 新增：聚合占位与分组索引（不分高度层），用于 O(1) 前方阻挡与快速分组
    private Dictionary<Vector2Int, List<GridTileObject>> cellOccupantsIndex; // key=(x,z) 任一高度层的占用物体集合（使用列表更轻量）
    private Dictionary<Vector2Int, HashSet<GridTileObject>> positionGroupIndex; // key=(gridX,gridZ) 该位置的物体集合

    // GC优化：合并的静态缓存对象池
    private static class CachePool
    {
        // 字典池
        public static readonly Dictionary<Vector2Int, List<GridTileObject>> positionGroups = new Dictionary<Vector2Int, List<GridTileObject>>();

        // 集合池
        public static readonly HashSet<GridTileObject> obstacles = new HashSet<GridTileObject>();
        public static readonly HashSet<GridTileObject> obstaclesDepth = new HashSet<GridTileObject>();
        public static readonly HashSet<GridTileObject> checkedObjects = new HashSet<GridTileObject>();
        public static readonly List<GridTileObject> group = new List<GridTileObject>();

        // 对象池
        public static readonly List<List<GridTileObject>> listPool = new List<List<GridTileObject>>();
        public static readonly List<HashSet<GridTileObject>> hashSetPool = new List<HashSet<GridTileObject>>();

        // 缓存数组
        public static GridTileObject[] objArray = new GridTileObject[32];
        public static GridTileObject[] objArrayDepth = new GridTileObject[32];

        // 批量更新缓存
        public static BatchUpdateData[] batchData = new BatchUpdateData[256];
        public static Transform[] transforms = new Transform[256];
        public static Vector3[] startPositions = new Vector3[256];
        public static Vector3[] targetPositions = new Vector3[256];

        // 新增：可复用的排序索引数组，避免每次分配
        public static int[] sortedIndices = new int[256];
    }

    // 边界数据
    private int minX = int.MaxValue, maxX = int.MinValue;
    private int minZ = int.MaxValue, maxZ = int.MinValue;
    private int maxHeight = 0;

    // 推进检查相关
    private float lastPushCheckTime = 0f;
    private float lastPushRequestTime = 0f;
    private const float UPDATE_IDLE_TIMEOUT = 2f;
    private bool isPushingInProgress = false; // 推进进行中标志
    private int pushRecursionDepth = 0; // 推进递归深度（保留字段，但不再使用递归）
    private const int MAX_PUSH_RECURSION = 100; // 最大递归深度

    void Awake()
    {
        gridData = new Dictionary<Vector3Int, GridTileObject>();
        objectToTileMap = new Dictionary<GameObject, GridTileObject>();
        activeTileObjects = new List<GridTileObject>();
        positionGroupCache = new Dictionary<Vector2Int, List<GridTileObject>>();
        dirtyPositions = new HashSet<Vector2Int>();
        reusableGroupList = new List<List<GridTileObject>>();
        frontPositionIndex = new Dictionary<Vector2Int, HashSet<GridTileObject>>();
        // 新增索引初始化
        cellOccupantsIndex = new Dictionary<Vector2Int, List<GridTileObject>>();
        positionGroupIndex = new Dictionary<Vector2Int, HashSet<GridTileObject>>();
    }

    void Update()
    {


        // 早期退出：如果有物体正在移动或推进正在进行中，不进行新的推进检查
        if (HasAnyObjectMoving() || isPushingInProgress) return;

        // 事件驱动：只在需要时检查
        if (enableEventDrivenPush)
        {
            if (needsPushCheck)
            {
                needsPushCheck = false;
                lastPushRequestTime = Time.time;
                CheckAndPushObjects();
            }
            else if (Time.time - lastPushRequestTime > UPDATE_IDLE_TIMEOUT)
            {
                return; // 智能休眠
            }
        }
        else
        {
            // 传统轮询：按间隔检查
            if (Time.time - lastPushCheckTime >= pushCheckInterval)
            {
                lastPushCheckTime = Time.time;
                CheckAndPushObjects();
            }
        }
    }

    /// <summary>
    /// 在网格中放置物体
    /// </summary>
    public GridTileObject PlaceObject(GameObject gameObject, int gridX, int gridZ, int height = 0, int sizeX = 1, int sizeZ = 1, bool isImmovable = false)
    {
        if (height < 0 || height >= maxHeightLayers || !IsAreaAvailable(gridX, gridZ, sizeX, sizeZ, height))
            return null;

        var tileObject = new GridTileObject
        {
            gameObject = gameObject,
            gridX = gridX,
            gridZ = gridZ,
            height = height,
            sizeX = sizeX,
            sizeZ = sizeZ,
            isMoving = false,
            isImmovable = isImmovable
        };

        // 占用网格位置
        ForEachGridPosition(tileObject, (x, z, h) => gridData[new Vector3Int(x, z, h)] = tileObject);
        // 新增：更新占位与分组聚合索引
        AddOccupancy(tileObject);
        AddPositionGroupIndex(tileObject);

        // 更新边界
        UpdateBounds(gridX, gridZ, height, sizeX, sizeZ);

        // 设置物体世界位置
        gameObject.transform.position = GridToWorldPosition(gridX, gridZ, height, sizeX, sizeZ);

        // 添加到映射和列表
        objectToTileMap[gameObject] = tileObject;
        activeTileObjects.Add(tileObject);

        // 更新缓存和索引
        MarkPositionDirty(gridX, gridZ);
        UpdateFrontPositionIndex(tileObject, true);

        // 事件驱动：标记需要检查推进
        if (enableEventDrivenPush)
        {
            needsPushCheck = true;
            lastPushRequestTime = Time.time;
        }

        return tileObject;
    }

    /// <summary>
    /// 移除物体
    /// </summary>
    public bool RemoveObject(GameObject gameObject)
    {
        if (!objectToTileMap.TryGetValue(gameObject, out GridTileObject tileObject))
            return false;

        // 清除网格占用
        // 先更新聚合索引，再从 gridData 移除
        RemoveOccupancy(tileObject);
        ForEachGridPosition(tileObject, (x, z, h) => gridData.Remove(new Vector3Int(x, z, h)));

        // 从映射和列表中移除
        objectToTileMap.Remove(gameObject);
        activeTileObjects.Remove(tileObject);

        // 更新缓存和索引
        MarkPositionDirty(tileObject.gridX, tileObject.gridZ);
        RecalculateBounds();
        UpdateFrontPositionIndex(tileObject, false);
        RemovePositionGroupIndex(tileObject);

        // 事件驱动：标记需要检查推进
        if (enableEventDrivenPush)
        {
            needsPushCheck = true;
            lastPushRequestTime = Time.time;
        }

        return true;
    }

    /// <summary>
    /// 网格坐标转世界坐标
    /// </summary>
    public Vector3 GridToWorldPosition(int gridX, int gridZ, int height, int sizeX = 1, int sizeZ = 1)
    {
        float worldX = gridOrigin.x + (gridX + sizeX * 0.5f - 0.5f) * cellSize.x;
        float worldZ = gridOrigin.z + (gridZ + sizeZ * 0.5f - 0.5f) * cellSize.z;
        float worldY = gridOrigin.y + height * cellSize.y;
        return new Vector3(worldX, worldY, worldZ);
    }

    /// <summary>
    /// 世界坐标转网格坐标
    /// </summary>
    public Vector2Int WorldToGridPosition(Vector3 worldPos)
    {
        int gridX = Mathf.FloorToInt((worldPos.x - gridOrigin.x) / cellSize.x);
        int gridZ = Mathf.FloorToInt((worldPos.z - gridOrigin.z) / cellSize.z);
        return new Vector2Int(gridX, gridZ);
    }

    /// <summary>
    /// 获取指定位置的物体
    /// </summary>
    public GameObject GetObjectAtPosition(int gridX, int gridZ, int height = 0)
    {
        Vector3Int pos = new Vector3Int(gridX, gridZ, height);
        return gridData.TryGetValue(pos, out GridTileObject tileObject) ? tileObject.gameObject : null;
    }

    #region 核心推进逻辑

    private bool HasAnyObjectMoving()
    {
        for (int i = 0; i < activeTileObjects.Count; i++)
        {
            if (activeTileObjects[i].isMoving)
                return true;
        }
        return false;
    }

    private void CheckAndPushObjects()
    {
        if (HasAnyObjectMoving()) return;

        var movableGroups = GetMovableGroups();
        if (movableGroups.Count > 0)
        {
            StartCoroutine(PushPositionGroupsBatch(movableGroups));
        }
    }

    private List<List<GridTileObject>> GetMovableGroups()
    {
        // 清理可重用列表
        foreach (var list in reusableGroupList)
        {
            list.Clear();
            CachePool.listPool.Add(list);
        }
        reusableGroupList.Clear();

        if (activeTileObjects.Count == 0) return reusableGroupList;

        // 统一策略：逐物体独立检查，避免任何 O(N^2) 重建
        for (int i = 0; i < activeTileObjects.Count; i++)
        {
            var obj = activeTileObjects[i];
            if (obj.isMoving || obj.isImmovable) continue;

            var singleObjGroup = GetListFromPool();
            singleObjGroup.Add(obj);

            if (CanSingleObjectMove(obj))
                reusableGroupList.Add(singleObjGroup);
            else
            {
                singleObjGroup.Clear();
                CachePool.listPool.Add(singleObjGroup);
            }
        }

        return reusableGroupList;
    }

    private bool CanPositionGroupMove(List<GridTileObject> group)
    {
        if (group.Count == 0) return false;

        var representative = group[0];
        if (representative.gridZ == 0) return false;

        // 无推进区域检查
        if (noPushZoneThreshold >= 0 && representative.gridZ >= noPushZoneThreshold)
            return CanMoveToFrontPositionDirectly(group);

        // 检查组中物体状态
        for (int i = 0; i < group.Count; i++)
        {
            if (group[i].isMoving || group[i].isImmovable)
                return false;
        }

        // 检查前方阻挡（使用聚合索引，O(sizeX)）
        CachePool.obstacles.Clear();
        CheckFrontObstacles(representative, CachePool.obstacles);

        if (CachePool.obstacles.Count > 0)
        {
            // 扩展缓存数组
            if (CachePool.objArray.Length < CachePool.obstacles.Count)
                CachePool.objArray = new GridTileObject[CachePool.obstacles.Count * 2];

            CachePool.obstacles.CopyTo(CachePool.objArray);
            int count = CachePool.obstacles.Count;

            for (int i = 0; i < count; i++)
            {
                var frontObj = CachePool.objArray[i];
                if (frontObj.isImmovable) return false;

                if (!frontObj.isMoving)
                {
                    var frontGroup = GetPositionGroup(frontObj);
                    if (!CanPositionGroupMoveWithDepthLimit(frontGroup, 1, 10))
                        return false;
                }
            }
        }

        return true;
    }

    /// <summary>
    /// 检查单个物体是否可以移动（不考虑同位置其他物体的影响）
    /// </summary>
    private bool CanSingleObjectMove(GridTileObject obj)
    {
        if (obj == null || obj.isMoving || obj.isImmovable) return false;
        if (obj.gridZ == 0) return false;

        // 无推进区域检查
        if (noPushZoneThreshold >= 0 && obj.gridZ >= noPushZoneThreshold)
        {
            // 检查前方位置是否完全空闲（使用聚合索引）
            return IsObjectFrontAreaClear(obj);
        }

        // 检查前方障碍物（使用聚合索引）
        CachePool.obstacles.Clear();
        CheckFrontObstacles(obj, CachePool.obstacles);

        if (CachePool.obstacles.Count > 0)
        {
            // 扩展缓存数组
            if (CachePool.objArray.Length < CachePool.obstacles.Count)
                CachePool.objArray = new GridTileObject[CachePool.obstacles.Count * 2];

            CachePool.obstacles.CopyTo(CachePool.objArray);
            int count = CachePool.obstacles.Count;

            // 检查每个前方物体是否可以被推动
            for (int i = 0; i < count; i++)
            {
                var frontObj = CachePool.objArray[i];
                if (frontObj.isImmovable) return false;

                if (!frontObj.isMoving)
                {
                    // 递归检查前方物体是否可以移动
                    if (!CanSingleObjectMoveWithDepthLimit(frontObj, 1, 10))
                        return false;
                }
            }
        }

        return true;
    }

    /// <summary>
    /// 检查物体前方区域是否完全清空（用于无推进区域）
    /// </summary>
    private bool IsObjectFrontAreaClear(GridTileObject obj)
    {
        if (obj.gridZ == 0) return false;

        int frontZ = obj.gridZ - 1;
        int endX = obj.gridX + obj.sizeX;

        for (int x = obj.gridX; x < endX; x++)
        {
            Vector2Int key = new Vector2Int(x, frontZ);
            if (cellOccupantsIndex.TryGetValue(key, out var list) && list.Count > 0)
                return false;
        }

        return true;
    }

    /// <summary>
    /// 带深度限制的单个物体移动检查（防止无限递归）
    /// </summary>
    private bool CanSingleObjectMoveWithDepthLimit(GridTileObject obj, int currentDepth, int maxDepth)
    {
        if (currentDepth > maxDepth || obj == null || obj.isMoving || obj.isImmovable) return false;
        if (obj.gridZ == 0) return false;

        // 检查前方障碍物（使用聚合索引）
        CachePool.obstaclesDepth.Clear();
        CheckFrontObstacles(obj, CachePool.obstaclesDepth);

        if (CachePool.obstaclesDepth.Count > 0)
        {
            // 扩展缓存数组
            if (CachePool.objArrayDepth.Length < CachePool.obstaclesDepth.Count)
                CachePool.objArrayDepth = new GridTileObject[CachePool.obstaclesDepth.Count * 2];

            CachePool.obstaclesDepth.CopyTo(CachePool.objArrayDepth);
            int count = CachePool.obstaclesDepth.Count;

            for (int i = 0; i < count; i++)
            {
                var frontObj = CachePool.objArrayDepth[i];
                if (frontObj.isImmovable) return false;

                if (!frontObj.isMoving)
                {
                    if (!CanSingleObjectMoveWithDepthLimit(frontObj, currentDepth + 1, maxDepth))
                        return false;
                }
            }
        }

        return true;
    }

    private bool CanMoveToFrontPositionDirectly(List<GridTileObject> group)
    {
        if (group.Count == 0) return false;

        var representative = group[0];

        // 检查组中物体状态
        for (int i = 0; i < group.Count; i++)
        {
            if (group[i].isMoving || group[i].isImmovable)
                return false;
        }

        // 检查前方位置是否空闲（使用聚合占位索引，不再逐层扫描）
        int frontZ = representative.gridZ - 1;
        int endX = representative.gridX + representative.sizeX;
        for (int x = representative.gridX; x < endX; x++)
        {
            Vector2Int key = new Vector2Int(x, frontZ);
            if (cellOccupantsIndex.TryGetValue(key, out var list) && list.Count > 0)
                return false;
        }

        return true;
    }

    private bool CanPositionGroupMoveWithDepthLimit(List<GridTileObject> group, int currentDepth, int maxDepth)
    {
        if (currentDepth > maxDepth || group.Count == 0) return false;

        var representative = group[0];
        if (representative.gridZ == 0) return false;

        // 检查组中物体状态
        for (int i = 0; i < group.Count; i++)
        {
            if (group[i].isMoving || group[i].isImmovable)
                return false;
        }

        // 检查前方阻挡（使用聚合索引）
        CachePool.obstaclesDepth.Clear();
        CheckFrontObstacles(representative, CachePool.obstaclesDepth);

        if (CachePool.obstaclesDepth.Count > 0)
        {
            // 扩展缓存数组
            if (CachePool.objArrayDepth.Length < CachePool.obstaclesDepth.Count)
                CachePool.objArrayDepth = new GridTileObject[CachePool.obstaclesDepth.Count * 2];

            CachePool.obstaclesDepth.CopyTo(CachePool.objArrayDepth);
            int count = CachePool.obstaclesDepth.Count;

            for (int i = 0; i < count; i++)
            {
                var frontObj = CachePool.objArrayDepth[i];
                if (frontObj.isImmovable) return false;

                if (!frontObj.isMoving)
                {
                    var frontGroup = GetPositionGroup(frontObj);
                    if (!CanPositionGroupMoveWithDepthLimit(frontGroup, currentDepth + 1, maxDepth))
                        return false;
                }
            }
        }

        return true;
    }

    private List<GridTileObject> GetPositionGroup(GridTileObject obj)
    {
        Vector2Int pos = new Vector2Int(obj.gridX, obj.gridZ);

        // 直接从分组索引读取（O(1)），拷贝到可复用列表
        if (positionGroupIndex.TryGetValue(pos, out var set))
        {
            CachePool.group.Clear();
            foreach (var t in set) CachePool.group.Add(t);
            return CachePool.group;
        }

        // 兜底：无对象
        CachePool.group.Clear();
        return CachePool.group;
    }

    #endregion

    #region 批量推进处理

    private struct BatchUpdateData
    {
        public GridTileObject obj;
        public Vector3Int oldGridPos;
        public Vector3Int newGridPos;
        public Vector3 startWorldPos;
        public Vector3 targetWorldPos;
        public Transform transform;
    }

    // 自定义索引比较器，避免每次创建闭包
    private class IndexByGridZComparer : IComparer<int>
    {
        public static readonly IndexByGridZComparer Instance = new IndexByGridZComparer();
        public int Compare(int i1, int i2)
        {
            int gridZ1 = CachePool.batchData[i1].obj.gridZ;
            int gridZ2 = CachePool.batchData[i2].obj.gridZ;
            return gridZ1.CompareTo(gridZ2);
        }
    }

    IEnumerator PushPositionGroupsBatch(List<List<GridTileObject>> groups)
    {
        if (groups == null || groups.Count == 0) yield break;
        if (isPushingInProgress) yield break;

        // 改为迭代而非递归，避免同帧重入
        isPushingInProgress = true;
        pushRecursionDepth = 0;

        while (groups != null && groups.Count > 0)
        {
            // 分离动画组和直接移动组
            List<List<GridTileObject>> animationGroups = new List<List<GridTileObject>>();
            List<List<GridTileObject>> directMoveGroups = new List<List<GridTileObject>>();

            for (int g = 0; g < groups.Count; g++)
            {
                var group = groups[g];
                if (group.Count == 0) continue;
                var representative = group[0];
                if (noPushZoneThreshold >= 0 && representative.gridZ >= noPushZoneThreshold)
                    directMoveGroups.Add(group);
                else
                    animationGroups.Add(group);
            }

            // 处理直接移动的组
            if (directMoveGroups.Count > 0)
                ProcessDirectMoveGroups(directMoveGroups);

            // 处理需要动画的组
            if (animationGroups.Count > 0)
                yield return StartCoroutine(ProcessAnimationGroups(animationGroups));

            // 事件驱动：同一协程内继续下一轮
            if (enableEventDrivenPush)
            {
                groups = GetMovableGroups();
            }
            else
            {
                break;
            }
        }

        // 顶层收尾
        isPushingInProgress = false;
        needsPushCheck = false;
        lastPushRequestTime = Time.time;
    }

    private void ProcessDirectMoveGroups(List<List<GridTileObject>> directMoveGroups)
    {
        // 两阶段：先移除所有旧占用，再统一写回新占用，避免排序与潜在冲突
        foreach (var group in directMoveGroups)
        {
            foreach (var obj in group)
            {
                if (obj.gameObject == null) continue;
                obj.isMoving = true;
                UpdateFrontPositionIndex(obj, false);
                RemoveOccupancy(obj);
                RemovePositionGroupIndex(obj);
                ForEachGridPosition(obj, (x, z, h) => gridData.Remove(new Vector3Int(x, z, h)));
            }
        }

        foreach (var group in directMoveGroups)
        {
            foreach (var obj in group)
            {
                if (obj.gameObject == null) continue;
                int oldGridZ = obj.gridZ;
                int targetGridZ = obj.gridZ - 1;
                if (targetGridZ < 0) { obj.isMoving = false; continue; }

                obj.gridZ = targetGridZ;
                ForEachGridPosition(obj, (x, z, h) => gridData[new Vector3Int(x, z, h)] = obj);
                AddOccupancy(obj);
                AddPositionGroupIndex(obj);
                UpdateFrontPositionIndex(obj, true);

                MarkPositionDirty(obj.gridX, oldGridZ);
                MarkPositionDirty(obj.gridX, obj.gridZ);
                UpdateBoundsForObject(obj);

                Vector3 targetPos = GridToWorldPosition(obj.gridX, obj.gridZ, obj.height, obj.sizeX, obj.sizeZ);
                obj.gameObject.transform.position = targetPos;

                obj.isMoving = false;
                OnPushCompleted?.Invoke(obj);
            }
        }
    }

    private IEnumerator ProcessAnimationGroups(List<List<GridTileObject>> animationGroups)
    {
        // 计算总物体数量
        int totalObjectCount = 0;
        for (int i = 0; i < animationGroups.Count; i++)
            totalObjectCount += animationGroups[i].Count;

        if (totalObjectCount == 0) yield break;

        // 扩展缓存数组
        EnsureCacheCapacity(totalObjectCount);

        // 收集移动物体数据
        int validObjectCount = CollectMovingObjects(animationGroups, totalObjectCount);
        if (validObjectCount == 0) yield break;

        // 标记移动状态
        for (int i = 0; i < validObjectCount; i++)
            CachePool.batchData[i].obj.isMoving = true;

        // 按顺序更新网格数据
        BatchUpdateGridDataWithOrder(validObjectCount);

        // 执行动画
        yield return StartCoroutine(ExecuteZeroGCAnimation(validObjectCount));

        // 完成移动
        for (int i = 0; i < validObjectCount; i++)
            CachePool.batchData[i].obj.isMoving = false;

        // 触发事件
        if (OnPushCompleted != null)
        {
            for (int i = 0; i < validObjectCount; i++)
            {
                var obj = CachePool.batchData[i].obj;
                if (obj.gameObject != null)
                    OnPushCompleted.Invoke(obj);
            }
        }
    }

    private void EnsureCacheCapacity(int requiredCapacity)
    {
        if (CachePool.batchData.Length < requiredCapacity)
        {
            int newSize = Mathf.NextPowerOfTwo(requiredCapacity);
            CachePool.batchData = new BatchUpdateData[newSize];
            CachePool.transforms = new Transform[newSize];
            CachePool.startPositions = new Vector3[newSize];
            CachePool.targetPositions = new Vector3[newSize];
        }
        // 同时扩容排序索引
        if (CachePool.sortedIndices.Length < requiredCapacity)
        {
            int newSize = Mathf.NextPowerOfTwo(requiredCapacity);
            CachePool.sortedIndices = new int[newSize];
        }
    }

    private int CollectMovingObjects(List<List<GridTileObject>> groups, int totalObjectCount)
    {
        int validIndex = 0;

        for (int i = 0; i < groups.Count; i++)
        {
            var group = groups[i];
            for (int j = 0; j < group.Count; j++)
            {
                var obj = group[j];
                if (obj.gameObject != null && obj.gameObject.transform != null)
                {
                    var transform = obj.gameObject.transform;
                    var startPos = GridToWorldPosition(obj.gridX, obj.gridZ, obj.height, obj.sizeX, obj.sizeZ);
                    var targetPos = GridToWorldPosition(obj.gridX, obj.gridZ - 1, obj.height, obj.sizeX, obj.sizeZ);

                    CachePool.batchData[validIndex] = new BatchUpdateData
                    {
                        obj = obj,
                        oldGridPos = new Vector3Int(obj.gridX, obj.gridZ, obj.height),
                        newGridPos = new Vector3Int(obj.gridX, obj.gridZ - 1, obj.height),
                        startWorldPos = startPos,
                        targetWorldPos = targetPos,
                        transform = transform
                    };

                    CachePool.transforms[validIndex] = transform;
                    CachePool.startPositions[validIndex] = startPos;
                    CachePool.targetPositions[validIndex] = targetPos;

                    validIndex++;
                }
            }
        }

        return validIndex;
    }

    private void BatchUpdateGridDataWithOrder(int objectCount)
    {
        // 两阶段更新：先移除所有旧占用，再统一写回新占用，避免排序与先后依赖
        for (int i = 0; i < objectCount; i++)
        {
            var obj = CachePool.batchData[i].obj;
            UpdateFrontPositionIndex(obj, false);
            RemoveOccupancy(obj);
            RemovePositionGroupIndex(obj);
            ForEachGridPosition(obj, (x, z, h) => gridData.Remove(new Vector3Int(x, z, h)));
        }

        for (int i = 0; i < objectCount; i++)
        {
            var obj = CachePool.batchData[i].obj;
            int oldGridZ = obj.gridZ;
            int targetGridZ = obj.gridZ - 1;
            if (targetGridZ < 0) continue;

            obj.gridZ = targetGridZ;
            ForEachGridPosition(obj, (x, z, h) => gridData[new Vector3Int(x, z, h)] = obj);
            AddOccupancy(obj);
            AddPositionGroupIndex(obj);
            UpdateFrontPositionIndex(obj, true);

            MarkPositionDirty(obj.gridX, oldGridZ);
            MarkPositionDirty(obj.gridX, obj.gridZ);
            UpdateBoundsForObject(obj);
        }
    }

    private IEnumerator ExecuteZeroGCAnimation(int objectCount)
    {
        float moveDistance = cellSize.z;
        float moveTime = moveDistance / pushSpeed;
        float elapsed = 0f;

        // 确保起始位置正确
        for (int i = 0; i < objectCount; i++)
        {
            var transform = CachePool.transforms[i];
            if (transform != null)
                transform.position = CachePool.startPositions[i];
        }

        // 动画循环
        while (elapsed < moveTime)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.Clamp01(elapsed / moveTime);

            for (int i = 0; i < objectCount; i++)
            {
                var transform = CachePool.transforms[i];
                if (transform != null)
                {
                    var start = CachePool.startPositions[i];
                    var target = CachePool.targetPositions[i];

                    transform.position = new Vector3(
                        start.x + (target.x - start.x) * t,
                        start.y + (target.y - start.y) * t,
                        start.z + (target.z - start.z) * t
                    );
                }
            }

            yield return null;
        }

        // 确保最终位置精确
        for (int i = 0; i < objectCount; i++)
        {
            var transform = CachePool.transforms[i];
            if (transform != null)
                transform.position = CachePool.targetPositions[i];
        }
    }

    #endregion

    #region 辅助方法

    private void ForEachGridPosition(GridTileObject obj, System.Action<int, int, int> action)
    {
        int endX = obj.gridX + obj.sizeX;
        int endZ = obj.gridZ + obj.sizeZ;

        for (int x = obj.gridX; x < endX; x++)
        {
            for (int z = obj.gridZ; z < endZ; z++)
            {
                action(x, z, obj.height);
            }
        }
    }

    private void CheckFrontObstacles(GridTileObject obj, HashSet<GridTileObject> obstacles)
    {
        if (obj.gridZ == 0) return;

        int frontZ = obj.gridZ - 1;
        int endX = obj.gridX + obj.sizeX;

        // 使用聚合占位索引：不再逐层扫描
        for (int x = obj.gridX; x < endX; x++)
        {
            Vector2Int key = new Vector2Int(x, frontZ);
            if (cellOccupantsIndex.TryGetValue(key, out var list))
            {
                foreach (var frontObj in list)
                    obstacles.Add(frontObj);
            }
        }
    }

    private bool IsAreaAvailable(int gridX, int gridZ, int sizeX, int sizeZ, int height)
    {
        int endX = gridX + sizeX;
        int endZ = gridZ + sizeZ;

        for (int x = gridX; x < endX; x++)
        {
            for (int z = gridZ; z < endZ; z++)
            {
                Vector3Int pos = new Vector3Int(x, z, height);
                if (gridData.ContainsKey(pos))
                    return false;
            }
        }
        return true;
    }

    private void MarkPositionDirty(int gridX, int gridZ)
    {
        dirtyPositions.Add(new Vector2Int(gridX, gridZ));
        needsGroupCacheUpdate = true;
    }

    private void UpdateBounds(int gridX, int gridZ, int height, int sizeX, int sizeZ)
    {
        minX = Mathf.Min(minX, gridX);
        maxX = Mathf.Max(maxX, gridX + sizeX - 1);
        minZ = Mathf.Min(minZ, gridZ);
        maxZ = Mathf.Max(maxZ, gridZ + sizeZ - 1);
        maxHeight = Mathf.Max(maxHeight, height);
    }

    private void RecalculateBounds()
    {
        if (gridData.Count == 0)
        {
            minX = int.MaxValue;
            maxX = int.MinValue;
            minZ = int.MaxValue;
            maxZ = int.MinValue;
            maxHeight = 0;
            return;
        }

        minX = int.MaxValue;
        maxX = int.MinValue;
        minZ = int.MaxValue;
        maxZ = int.MinValue;
        maxHeight = 0;

        foreach (var kvp in gridData)
        {
            Vector3Int pos = kvp.Key;
            minX = Mathf.Min(minX, pos.x);
            maxX = Mathf.Max(maxX, pos.x);
            minZ = Mathf.Min(minZ, pos.y);
            maxZ = Mathf.Max(maxZ, pos.y);
            maxHeight = Mathf.Max(maxHeight, pos.z);
        }
    }

    private void UpdateBoundsForObject(GridTileObject obj)
    {
        int objMinX = obj.gridX;
        int objMaxX = obj.gridX + obj.sizeX - 1;
        int objMinZ = obj.gridZ;
        int objMaxZ = obj.gridZ + obj.sizeZ - 1;
        int objHeight = obj.height;

        if (objMinX < minX) minX = objMinX;
        if (objMaxX > maxX) maxX = objMaxX;
        if (objMinZ < minZ) minZ = objMinZ;
        if (objMaxZ > maxZ) maxZ = objMaxZ;
        if (objHeight > maxHeight) maxHeight = objHeight;
    }

    private void UpdateFrontPositionIndex(GridTileObject obj, bool isAdding)
    {
        int endX = obj.gridX + obj.sizeX;
        for (int x = obj.gridX; x < endX; x++)
        {
            Vector2Int backPos = new Vector2Int(x, obj.gridZ + 1);

            if (isAdding)
            {
                if (!frontPositionIndex.ContainsKey(backPos))
                    frontPositionIndex[backPos] = GetHashSetFromPool();
                frontPositionIndex[backPos].Add(obj);
            }
            else
            {
                if (frontPositionIndex.ContainsKey(backPos))
                {
                    frontPositionIndex[backPos].Remove(obj);
                    if (frontPositionIndex[backPos].Count == 0)
                    {
                        var emptyHashSet = frontPositionIndex[backPos];
                        CachePool.hashSetPool.Add(emptyHashSet);
                        frontPositionIndex.Remove(backPos);
                    }
                }
            }
        }
    }

    private void UpdatePositionGroupCache()
    {
        // 兼容旧接口：启用优化时不再进行全表扫描，只清理标记。
        if (!enableCacheOptimization) return;
        dirtyPositions.Clear();
        needsGroupCacheUpdate = false;
    }

    private List<GridTileObject> GetListFromPool()
    {
        if (CachePool.listPool.Count > 0)
        {
            var list = CachePool.listPool[CachePool.listPool.Count - 1];
            CachePool.listPool.RemoveAt(CachePool.listPool.Count - 1);
            return list;
        }
        return new List<GridTileObject>();
    }

    private HashSet<GridTileObject> GetHashSetFromPool()
    {
        if (CachePool.hashSetPool.Count > 0)
        {
            var hashSet = CachePool.hashSetPool[CachePool.hashSetPool.Count - 1];
            CachePool.hashSetPool.RemoveAt(CachePool.hashSetPool.Count - 1);
            hashSet.Clear();
            return hashSet;
        }
        return new HashSet<GridTileObject>();
    }

    // 新增：聚合占位索引维护（按 footprint 逐格增删）
    private void AddOccupancy(GridTileObject obj)
    {
        int endX = obj.gridX + obj.sizeX;
        int endZ = obj.gridZ + obj.sizeZ;
        for (int x = obj.gridX; x < endX; x++)
        {
            for (int z = obj.gridZ; z < endZ; z++)
            {
                Vector2Int key = new Vector2Int(x, z);
                if (!cellOccupantsIndex.TryGetValue(key, out var list))
                {
                    list = GetListFromPool();
                    cellOccupantsIndex[key] = list;
                }
                list.Add(obj);
            }
        }
    }

    private void RemoveOccupancy(GridTileObject obj)
    {
        int endX = obj.gridX + obj.sizeX;
        int endZ = obj.gridZ + obj.sizeZ;
        for (int x = obj.gridX; x < endX; x++)
        {
            for (int z = obj.gridZ; z < endZ; z++)
            {
                Vector2Int key = new Vector2Int(x, z);
                if (cellOccupantsIndex.TryGetValue(key, out var list))
                {
                    list.Remove(obj);
                    if (list.Count == 0)
                    {
                        list.Clear();
                        CachePool.listPool.Add(list);
                        cellOccupantsIndex.Remove(key);
                    }
                }
            }
        }
    }

    // 新增：分组索引维护（按基准格）
    private void AddPositionGroupIndex(GridTileObject obj)
    {
        Vector2Int pos = new Vector2Int(obj.gridX, obj.gridZ);
        if (!positionGroupIndex.TryGetValue(pos, out var set))
        {
            set = GetHashSetFromPool();
            positionGroupIndex[pos] = set;
        }
        set.Add(obj);
    }

    private void RemovePositionGroupIndex(GridTileObject obj)
    {
        Vector2Int pos = new Vector2Int(obj.gridX, obj.gridZ);
        if (positionGroupIndex.TryGetValue(pos, out var set))
        {
            set.Remove(obj);
            if (set.Count == 0)
            {
                CachePool.hashSetPool.Add(set);
                positionGroupIndex.Remove(pos);
            }
        }
    }

    #endregion

    #region 公共接口

    /// <summary>
    /// 根据物体当前世界位置自动绑定到网格
    /// </summary>
    public bool BindObjectByWorldPosition(GameObject gameObject, int height = 0, int sizeX = 1, int sizeZ = 1, bool isImmovable = false, bool snapToGrid = true)
    {
        if (gameObject == null) return false;

        Vector2Int gridPos = WorldToGridPosition(gameObject.transform.position);
        GridTileObject tileObject = PlaceObject(gameObject, gridPos.x, gridPos.y, height, sizeX, sizeZ, isImmovable);

        if (tileObject != null && snapToGrid)
        {
            Vector3 targetPos = GridToWorldPosition(gridPos.x, gridPos.y, height, sizeX, sizeZ);
            gameObject.transform.position = targetPos;
        }

        return tileObject != null;
    }

    /// <summary>
    /// 手动指定坐标绑定物体
    /// </summary>
    public bool BindObjectToGrid(GameObject gameObject, int gridX, int gridZ, int height = 0, int sizeX = 1, int sizeZ = 1, bool isImmovable = false, bool moveToPosition = true)
    {
        if (gameObject == null) return false;

        GridTileObject tileObject = PlaceObject(gameObject, gridX, gridZ, height, sizeX, sizeZ, isImmovable);

        if (tileObject != null && moveToPosition)
        {
            Vector3 targetPos = GridToWorldPosition(gridX, gridZ, height, sizeX, sizeZ);
            gameObject.transform.position = targetPos;
        }

        return tileObject != null;
    }

    /// <summary>
    /// 批量绑定多个物体到网格
    /// </summary>
    public int BindObjectsBatch(GameObject[] objects, int defaultHeight = 0, bool snapToGrid = true)
    {
        int successCount = 0;
        foreach (GameObject obj in objects)
        {
            if (obj == null) continue;

            bool isImmovable = obj.CompareTag("Immovable");
            if (BindObjectByWorldPosition(obj, defaultHeight, 1, 1, isImmovable, snapToGrid))
                successCount++;
        }
        return successCount;
    }

    /// <summary>
    /// 解除物体与网格的绑定
    /// </summary>
    public bool UnbindObject(GameObject gameObject) => RemoveObject(gameObject);

    /// <summary>
    /// 检查物体是否已绑定到网格
    /// </summary>
    public bool IsObjectBound(GameObject gameObject) => objectToTileMap.ContainsKey(gameObject);

    /// <summary>
    /// 获取物体在网格中的信息
    /// </summary>
    public GridTileObject GetObjectGridInfo(GameObject gameObject)
    {
        objectToTileMap.TryGetValue(gameObject, out GridTileObject tileObject);
        return tileObject;
    }

    /// <summary>
    /// 移动已绑定的物体到新的网格位置
    /// </summary>
    public bool MoveObjectToGridPosition(GameObject gameObject, int newGridX, int newGridZ, int newHeight = -1)
    {
        if (!objectToTileMap.TryGetValue(gameObject, out GridTileObject tileObject))
            return false;

        if (newHeight == -1) newHeight = tileObject.height;

        if (!IsAreaAvailable(newGridX, newGridZ, tileObject.sizeX, tileObject.sizeZ, newHeight))
            return false;

        RemoveObject(gameObject);
        GridTileObject newTileObject = PlaceObject(gameObject, newGridX, newGridZ, newHeight, tileObject.sizeX, tileObject.sizeZ, tileObject.isImmovable);
        return newTileObject != null;
    }

    /// <summary>
    /// 设置物体的不可移动状态
    /// </summary>
    public bool SetObjectImmovable(GameObject gameObject, bool isImmovable)
    {
        if (!objectToTileMap.TryGetValue(gameObject, out GridTileObject tileObject))
            return false;

        tileObject.isImmovable = isImmovable;
        return true;
    }

    /// <summary>
    /// 检查物体是否为不可移动块
    /// </summary>
    public bool IsObjectImmovable(GameObject gameObject)
    {
        if (objectToTileMap.TryGetValue(gameObject, out GridTileObject tileObject))
            return tileObject.isImmovable;
        return false;
    }

    /// <summary>
    /// 获取当前网格大小
    /// </summary>
    public Vector2Int GetGridSize()
    {
        if (gridData.Count == 0) return Vector2Int.zero;
        return new Vector2Int(maxX - minX + 1, maxZ - minZ + 1);
    }

    /// <summary>
    /// 获取网格边界
    /// </summary>
    public void GetGridBounds(out int minX, out int maxX, out int minZ, out int maxZ)
    {
        minX = this.minX;
        maxX = this.maxX;
        minZ = this.minZ;
        maxZ = this.maxZ;
    }

    /// <summary>
    /// 设置无推进区域阈值
    /// </summary>
    public void SetNoPushZoneThreshold(int threshold)
    {
        noPushZoneThreshold = threshold;
        if (enableEventDrivenPush)
        {
            needsPushCheck = true;
            lastPushRequestTime = Time.time;
        }
    }

    /// <summary>
    /// 获取当前无推进区域阈值
    /// </summary>
    public int GetNoPushZoneThreshold() => noPushZoneThreshold;

    /// <summary>
    /// 检查指定位置是否在无推进区域内
    /// </summary>
    public bool IsInNoPushZone(int gridZ) => noPushZoneThreshold >= 0 && gridZ >= noPushZoneThreshold;

    /// <summary>
    /// 检查指定物体是否在无推进区域内
    /// </summary>
    public bool IsObjectInNoPushZone(GameObject gameObject)
    {
        if (!objectToTileMap.TryGetValue(gameObject, out GridTileObject tileObject))
            return false;
        return IsInNoPushZone(tileObject.gridZ);
    }

    // 静态便捷方法
    public static Vector3Int? GetMyGridPosition(GameObject gameObject)
    {
        Tool_TileGrid tileGrid = FindObjectOfType<Tool_TileGrid>();
        if (tileGrid == null) return null;

        GridTileObject gridInfo = tileGrid.GetObjectGridInfo(gameObject);
        if (gridInfo == null) return null;

        return new Vector3Int(gridInfo.gridX, gridInfo.gridZ, gridInfo.height);
    }

    public static GridTileObject GetMyGridInfo(GameObject gameObject)
    {
        Tool_TileGrid tileGrid = FindObjectOfType<Tool_TileGrid>();
        return tileGrid?.GetObjectGridInfo(gameObject);
    }

    public static bool IsMyObjectBound(GameObject gameObject)
    {
        Tool_TileGrid tileGrid = FindObjectOfType<Tool_TileGrid>();
        return tileGrid?.IsObjectBound(gameObject) ?? false;
    }

    public static bool IsMyObjectInNoPushZone(GameObject gameObject)
    {
        Tool_TileGrid tileGrid = FindObjectOfType<Tool_TileGrid>();
        return tileGrid?.IsObjectInNoPushZone(gameObject) ?? false;
    }

    #endregion
}

/// <summary>
/// 网格瓦片物体数据结构
/// </summary>
[System.Serializable]
public class GridTileObject
{
    public GameObject gameObject;    // 关联的游戏物体
    public int gridX;               // 网格X坐标
    public int gridZ;               // 网格Z坐标
    public int height;               // 高度层
    public int sizeX;               // X方向大小
    public int sizeZ;               // Z方向大小
    public bool isMoving;           // 是否正在移动
    public bool isImmovable;        // 是否为不可移动块
}
