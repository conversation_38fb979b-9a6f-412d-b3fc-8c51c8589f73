using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using UnityEngine.EventSystems;

[RequireComponent(typeof(Button))]
public class Tool_BtnClickScale : MonoBehaviour, IPointerDownHandler, IPointerUpHandler
{
    [Tooltip("Scale amount when button is clicked")]
    float clickedScale = 0.9f;

    [Tooltip("Animation duration in seconds")]
    float animationDuration = 0.15f;

    [Tooltip("Vib")]
    public bool useVib = true;

    private Button button;
    private Vector3 originalScale;
    private Coroutine animationCoroutine;

    private void Awake()
    {
        button = GetComponent<Button>();
        button.transition = Selectable.Transition.None;
        originalScale = transform.localScale;
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        if (!gameObject.activeInHierarchy || !button.interactable)
            return;

        if (animationCoroutine != null)
            StopCoroutine(animationCoroutine);

        animationCoroutine = StartCoroutine(AnimateScaleDown());

        if (useVib)
            MXR_BRIGE.Virb_Short();
    }

    public void OnPointerUp(PointerEventData eventData)
    {
        if (!gameObject.activeInHierarchy || !button.interactable)
            return;

        if (animationCoroutine != null)
            StopCoroutine(animationCoroutine);

        animationCoroutine = StartCoroutine(AnimateScaleUp());
    }

    private IEnumerator AnimateScaleDown()
    {
        // Scale down
        float elapsedTime = 0f;
        Vector3 targetScale = originalScale * clickedScale;
        Vector3 startScale = transform.localScale;

        while (elapsedTime < animationDuration)
        {
            elapsedTime += Time.deltaTime;
            float t = Mathf.Clamp01(elapsedTime / animationDuration);
            transform.localScale = Vector3.Lerp(startScale, targetScale, t);
            yield return null;
        }

        transform.localScale = targetScale;
        animationCoroutine = null;
    }

    private IEnumerator AnimateScaleUp()
    {
        // Scale up
        float elapsedTime = 0f;
        Vector3 startScale = transform.localScale;

        while (elapsedTime < animationDuration)
        {
            elapsedTime += Time.deltaTime;
            float t = Mathf.Clamp01(elapsedTime / animationDuration);
            transform.localScale = Vector3.Lerp(startScale, originalScale, t);
            yield return null;
        }

        transform.localScale = originalScale;
        animationCoroutine = null;
    }

    private void OnDestroy()
    {
        if (animationCoroutine != null)
            StopCoroutine(animationCoroutine);
    }
}