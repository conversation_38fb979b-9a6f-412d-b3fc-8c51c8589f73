using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class TextFlyTool : MonoBehaviour
{
    private static TextFlyTool instance = null;

    public static TextFlyTool Instance
    {
        get
        {
            if (instance == null)
            {
                instance = GameObject.Find("TextFlyTool").gameObject.GetComponent<TextFlyTool>();
            }
            return instance;
        }
    }

    public UnityEngine.UI.CanvasScaler _canvasScaler;
    public Canvas canvas;
    [HideInInspector]
    public Camera came;
    public GameObject[] prefabs;

    public int count = 0;

    private float speed = 220f;
    public float desTime = 0.6f;
    private float alpSpeed = 1f;

    private void Awake()
    {
        came = Camera.main;
    }

    public List<GameObject> flyTexts;

    public void FixedUpdate()
    {
        for(var i = 0;i<flyTexts.Count;i++)
        {
            if(flyTexts[i])
            {
                flyTexts[i].transform.position += new Vector3(0, speed*Time.fixedDeltaTime, 0);
                flyTexts[i].GetComponent<Text>().color = new Color(flyTexts[i].GetComponent<Text>().color.r, flyTexts[i].GetComponent<Text>().color.g, flyTexts[i].GetComponent<Text>().color.b, flyTexts[i].GetComponent<Text>().color.a - alpSpeed * Time.fixedDeltaTime);
            }
            else
            {
                RemoveObject(flyTexts[i]);
            }
        }
    }

    public void CreateText(string text,int type,Vector2 uipos)
    {
        GameObject go = GameObject.Instantiate(prefabs[type], this.transform);
        //go.transform.position = getScreenPos(cam, pos);
        go.GetComponent<RectTransform>().localPosition = uipos;
        go.GetComponent<Text>().text = text;
        flyTexts.Add(go);
        GameObject.Destroy(go, desTime);
    }
    
    public void CreateText(string text,int type,Vector3 pos)
    {
        //return;
        //if (count > 35)
        //    return;
        var tarpos = WorldPosToUIPos(pos, canvas);


        if (tarpos.x < 0 || tarpos.y < 0)
            return;

        GameObject go = GameObject.Instantiate(prefabs[type], this.transform);
        //go.transform.position = getScreenPos(cam, pos);
        go.transform.position = tarpos;


        go.GetComponent<Text>().text = text;
        count++;
        flyTexts.Add(go);
        GameObject.Destroy(go, desTime);
       // Invoke("CountMinus", 0.6f);
    }

    private void CountMinus()
    {
        count--;
    }

    // 将精灵的世界坐标转换成屏幕坐标
    public Vector3 getScreenPos(Camera cam, Vector3 worldPos)
    {

        Vector2 uisize = canvas.GetComponent<RectTransform>().sizeDelta;//得到画布的尺寸
        Vector2 screenpos = cam.WorldToScreenPoint(worldPos);//将世界坐标转换为屏幕坐标
        Vector2 screenpos2;
        screenpos2.x = screenpos.x - (Screen.width / 2);//转换为以屏幕中心为原点的屏幕坐标
        screenpos2.y = screenpos.y - (Screen.height / 2);
        Vector2 uipos;
        uipos.x = (screenpos2.x / Screen.width) * uisize.x;
        uipos.y = (screenpos2.y / Screen.height) * uisize.y;//得到UGUI的anchoredPositio
        return uipos;

    }


    public Vector2 WorldPosToUIPos(Vector3 worldPos, Canvas canvas)
    {

        //是否在前方
        Vector3 forwardVector = came.transform.forward;
        Vector3 directionToPoint = (worldPos - came.transform.position).normalized;
        float dotProduct = Vector3.Dot(forwardVector, directionToPoint);
        if (dotProduct < 0)
            return -Vector2.one;


        //摄像机空间值域[0,1]，z轴值代表深度
        var viewPos = came.WorldToViewportPoint(worldPos);
        //按照值域进行裁剪
        if (viewPos.x >= 0 && viewPos.x <= 1 && viewPos.y >= 0 && viewPos.y <= 1)
        {
            //屏幕空间高度值
            float sheight = viewPos.y * Screen.height;
            //屏幕空间宽度值
            float swidth = viewPos.x * Screen.width;
            //适配转化
            return new Vector2(GetFixed(swidth, canvas), GetFixed(sheight, canvas));
        }
        //返回一个固定值-1代表不在屏幕当中
        return -Vector2.one;
    }

    //Screen坐标值适配Canvas画布
    public static float GetFixed(float value, Canvas canvas)
    {
        var cs = canvas.GetComponent<CanvasScaler>();
        if (cs.matchWidthOrHeight == 0)
            //匹配宽度时仅按照宽度计算
            return value * Screen.width / Screen.width;
        else
            //匹配高度时仅按照高度计算
            return value * Screen.height / Screen.height;
    }

    public void RemoveObject(GameObject obj)
    {
        if (flyTexts.IndexOf(obj) != -1)
            flyTexts.Remove(obj);
    }

}
