using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_RotateEulerAngVec 
{
    public static Vector3 RotateY(Vector3 originVec,float angleDegrees)
    {

        // 假设你有一个欧拉角  
        Vector3 originalEulerAngles = originVec; // 示例欧拉角  

        // 将欧拉角转换为四元数  
        Quaternion originalRotation = Quaternion.Euler(originalEulerAngles);

        // 创建一个表示绕Y轴旋转30度的四元数  
        Quaternion rotationBy30Degrees = Quaternion.Euler(0, angleDegrees, 0); // 注意这里的负号表示逆时针旋转  

        // 将两个四元数相乘（四元数乘法顺序很重要）  
        Quaternion newRotation = originalRotation * rotationBy30Degrees;

        // 将旋转后的四元数转换回欧拉角  
        Vector3 newEulerAngles = newRotation.eulerAngles;

        return newEulerAngles;

    }

    public static Vector3 RotateX(Vector3 originVec, float angleDegrees)
    {

        // 假设你有一个欧拉角  
        Vector3 originalEulerAngles = originVec; // 示例欧拉角  

        // 将欧拉角转换为四元数  
        Quaternion originalRotation = Quaternion.Euler(originalEulerAngles);

        // 创建一个表示绕Y轴旋转30度的四元数  
        Quaternion rotationBy30Degrees = Quaternion.Euler(angleDegrees, 0, 0); // 注意这里的负号表示逆时针旋转  

        // 将两个四元数相乘（四元数乘法顺序很重要）  
        Quaternion newRotation = originalRotation * rotationBy30Degrees;

        // 将旋转后的四元数转换回欧拉角  
        Vector3 newEulerAngles = newRotation.eulerAngles;

        return newEulerAngles;

    }
}
