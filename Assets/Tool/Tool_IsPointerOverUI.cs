using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

/// <summary>
/// 检测指针是否在UI元素上的工具类，适配不同平台
/// </summary>
public static class Tool_IsPointerOverUI
{


    public static bool Check()
    {
        if (EventSystem.current == null)
            return false;

        PointerEventData eventData = new PointerEventData(EventSystem.current);

#if UNITY_EDITOR || UNITY_STANDALONE
        eventData.position = Input.mousePosition;

        var results = new List<RaycastResult>();
        EventSystem.current.RaycastAll(eventData, results);
        return results.Count > 0;

#elif UNITY_ANDROID || UNITY_IOS
    if (Input.touchCount == 0)
        return false;

    for (int i = 0; i < Input.touchCount; i++)
    {
        eventData.position = Input.GetTouch(i).position;
        var results = new List<RaycastResult>();
        EventSystem.current.RaycastAll(eventData, results);
        if (results.Count > 0)
            return true;
    }
    return false;

#else
    eventData.position = Input.mousePosition;
    var results = new List<RaycastResult>();
    EventSystem.current.RaycastAll(eventData, results);
    return results.Count > 0;
#endif
    }

}
