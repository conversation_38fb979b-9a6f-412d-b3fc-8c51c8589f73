using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class ThreeDHPBar : MonoBehaviour
{


    public Camera Cam;

    //private Image imgHPBar;

    public Image[] imgHPBarTeam;

    public Text txtName;

    public Transform followTarget;

    public float Scale;
    public Vector3 Offset;




    public void Init(string name,Transform target,int teamIndex,Camera camera,float scale,Vector3 offset)
    {

        for(var i = 0; i< imgHPBarTeam.Length;i++)
        {
            imgHPBarTeam[i].gameObject.SetActive(false);
            imgHPBarTeam[i].fillAmount = 1;
        }

        imgHPBarTeam[teamIndex].gameObject.SetActive(true);


        Cam = camera;

        txtName.text = name;
        followTarget = target;


        Scale = scale;
        Offset = offset;
        transform.localScale = new Vector3(Scale, Scale, Scale);

    }

    private void Update()
    {
        if(followTarget)
        {
            if (transform.localScale.x != Scale)
                transform.localScale = new Vector3(Scale, Scale, Scale);

            transform.position = new Vector3(followTarget.position.x+ Offset.x, followTarget.position.y + Offset.y, followTarget.position.z+ Offset.z);

            transform.LookAt(new Vector3(Cam.transform.position.x, transform.position.y, Cam.transform.position.z));
        }
    }

    public void UpdateHPBar(float percent)
    {
        for (var i = 0; i < imgHPBarTeam.Length; i++)
            imgHPBarTeam[i].fillAmount = percent;

    }

}
