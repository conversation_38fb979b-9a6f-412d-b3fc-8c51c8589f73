using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_ThreeDHPbar_Manager : MonoBehaviour
{

   static  GameObject Prefab;

    public static ThreeDHPBar CreateHpBar(string name, Transform target, int teamIndex, Camera camera, float scale, Vector3 offset)
    {
        if (Prefab == null)
            Prefab = Resources.Load<GameObject>("3DHPBar");
       var go = GameObject.Instantiate(Prefab);
        var b = go.GetComponent<ThreeDHPBar>();
        b.Init(name, target, teamIndex, camera, scale, offset);
        return b;
    }

}
