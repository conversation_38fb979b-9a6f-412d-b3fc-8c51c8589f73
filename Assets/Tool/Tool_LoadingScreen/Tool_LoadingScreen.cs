using UnityEngine;
using UnityEngine.UI;

public class Tool_LoadingScreen
{
    // ���ؽ���Ԥ�����·��
    private const string LoadingScreenPath = "Tool_LoadingScreen_Canvas";

    // �����Ѽ��ص���Դ
    private static GameObject loadingScreenPrefab;
    private static GameObject loadingScreenInstance;
    private static Canvas loadingScreenCanvas;

    private static Image imgProgress;
    private static Text txtProgress;

    // ��ʾ���ؽ���
    public static void ShowLoadingScreen()
    {
        // ����Ԥ����
        if (loadingScreenPrefab == null)
        {
            loadingScreenPrefab = Resources.Load<GameObject>(LoadingScreenPath);
        }

        // ʵ�������ؽ���
        if (loadingScreenPrefab != null)
        {
            loadingScreenInstance = Object.Instantiate(loadingScreenPrefab);
            loadingScreenCanvas = loadingScreenInstance.GetComponent<Canvas>();
            if (loadingScreenCanvas != null)
            {
                // ȷ�����ؽ���� Canvas ����˳�����
                int maxSortingOrder = GetMaxSortingOrderInScene();
                loadingScreenCanvas.sortingOrder = maxSortingOrder + 1;
            }

            imgProgress = loadingScreenCanvas.transform.Find("imgProgress").GetComponent<Image>();
            txtProgress = loadingScreenCanvas.transform.Find("txtProgress").GetComponent<Text>();

            imgProgress.fillAmount = 0;
            txtProgress.text = "0%";
        }
    }

    public static void UpdateProgress(float per)
    {
        imgProgress.fillAmount = per;
        string percentage = (per * 100).ToString("F0") + "%";
        txtProgress.text = percentage;
    }

    // ������ɺ����ټ��ؽ���
    public static void HideLoadingScreen()
    {
        if (loadingScreenInstance != null)
        {
            Object.Destroy(loadingScreenInstance);
            loadingScreenInstance = null;
        }
    }


    // ��ȡ���������� Canvas ���������˳��
    private static int GetMaxSortingOrderInScene()
    {
        Canvas[] allCanvases = Object.FindObjectsOfType<Canvas>();
        int maxOrder = int.MinValue;
        foreach (Canvas canvas in allCanvases)
        {
            if (canvas.sortingOrder > maxOrder)
            {
                maxOrder = canvas.sortingOrder;
            }
        }
        return maxOrder;
    }
}