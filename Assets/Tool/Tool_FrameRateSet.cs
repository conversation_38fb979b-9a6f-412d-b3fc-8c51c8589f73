using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_FrameRateSet : MonoBehaviour
{

    static bool haveset = false;
    private void Awake()
    {
        if (!haveset)
        {
            QualitySettings.vSyncCount = 0; // 禁用VSync
            Application.targetFrameRate = 60; // 手动设置目标帧率

            haveset = true;
        }


        Destroy(this.gameObject);
    }
}
