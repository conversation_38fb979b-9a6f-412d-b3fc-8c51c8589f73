using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
#if UNITY_EDITOR
public class Tool_EquipTakePhoto : MonoBehaviour
{
    // 截图分辨率
     int resolutionWidth = 512;
     int resolutionHeight = 512;
    // 摄像机对象（可以设置为当前摄像机）
    public Camera captureCamera;

    //// 截图保存路径和文件名
    //public string filePath = "CapturedImage.jpg";

   public GameObject parent;



    // Start is called before the first frame update
    void Start()
    {
        for(var i = 0; i <parent.transform.childCount;i++)
        {
            parent.transform.GetChild(i).gameObject.SetActive(false);
        }

        for (var i = 0; i < parent.transform.childCount; i++)
        {
            if(i != 0 )
                parent.transform.GetChild(i-1).gameObject.SetActive(false);
            parent.transform.GetChild(i).gameObject.SetActive(true);
            CaptureCameraImageAsPNG(parent.transform.GetChild(i).gameObject.name);
        }



        Debug.Log("截图已保存到");

    }

    void CaptureCameraImageAsPNG(string name)
    {
        // 创建一个新的渲染纹理
        RenderTexture renderTexture = new RenderTexture(resolutionWidth, resolutionHeight, 24);

        // 设置摄像机的目标渲染纹理
        captureCamera.targetTexture = renderTexture;

        // 设置摄像机的渲染目标和Clear Flag为Depth Only
        captureCamera.targetTexture = renderTexture;
        captureCamera.clearFlags = CameraClearFlags.Depth;

        // 渲染摄像机视图到渲染纹理
        RenderTexture.active = renderTexture;
        captureCamera.Render();

        // 创建一个新的纹理2D来保存渲染纹理的内容
        Texture2D texture2D = new Texture2D(resolutionWidth, resolutionHeight, TextureFormat.ARGB32, false);
        texture2D.ReadPixels(new Rect(0, 0, resolutionWidth, resolutionHeight), 0, 0);
        texture2D.Apply();

        // 重置摄像机的目标渲染纹理
        captureCamera.targetTexture = null;
        RenderTexture.active = null;

        // 销毁渲染纹理（注意：在Unity中，RenderTexture.Release()通常不是必需的，因为GC会处理它，但在这里我们显式调用它以保持清晰）
        // 然而，重要的是要注意，在调用Release()之后，您不应该再使用RenderTexture，因为它可能已经被销毁
        // 在这个例子中，由于我们立即退出函数，所以调用Release()是安全的，但在实际使用中请小心
        // renderTexture.Release(); // 通常不需要，除非您遇到内存泄漏问题

        // 保存纹理为PNG文件
        byte[] bytes = texture2D.EncodeToPNG();
        string directoryPath = Path.Combine(Application.dataPath+ "/CAPEQUIP", Path.GetDirectoryName(name+".png"));
        Directory.CreateDirectory(directoryPath);
        string fullPath = Path.Combine(directoryPath, Path.GetFileName(name + ".png"));
        File.WriteAllBytes(fullPath, bytes);
    }

}
#endif