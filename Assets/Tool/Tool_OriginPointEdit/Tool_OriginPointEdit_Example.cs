using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Tool_OriginPointEdit 使用示例
/// 演示如何通过代码使用原点调整工具
/// </summary>
public class Tool_OriginPointEdit_Example : MonoBehaviour
{
    [Header("示例设置")]
    [Tooltip("要调整原点的目标模型")]
    public GameObject targetModel;

    [Toolt<PERSON>("预设的偏移量")]
    public Vector3[] presetOffsets = new Vector3[]
    {
        Vector3.zero,
        new Vector3(0, -1, 0),  // 向下偏移1单位
        new Vector3(0, 0, -1),  // 向前偏移1单位
        new Vector3(-1, 0, 0)   // 向左偏移1单位
    };

    [Tooltip("当前预设索引")]
    public int currentPresetIndex = 0;

    private Tool_OriginPointEdit originTool;

    private void Start()
    {
        // 如果没有指定目标模型，使用当前对象
        if (targetModel == null)
        {
            targetModel = gameObject;
        }

        // 获取或添加原点调整工具组件
        originTool = targetModel.GetComponent<Tool_OriginPointEdit>();
        if (originTool == null)
        {
            originTool = targetModel.AddComponent<Tool_OriginPointEdit>();
        }

        // 设置目标模型
        originTool.SetTargetModel(targetModel);

        // 启用调试信息
        originTool.enableDebug = true;

        Debug.Log("Tool_OriginPointEdit_Example 已初始化");
    }

    /// <summary>
    /// 应用当前预设偏移量
    /// </summary>
    public void ApplyCurrentPreset()
    {
        if (originTool != null && presetOffsets.Length > 0)
        {
            Vector3 offset = presetOffsets[currentPresetIndex % presetOffsets.Length];
            originTool.AdjustOrigin(offset);

            Debug.Log($"已应用预设 {currentPresetIndex}: {offset}");
        }
    }

    /// <summary>
    /// 切换到下一个预设
    /// </summary>
    public void NextPreset()
    {
        currentPresetIndex = (currentPresetIndex + 1) % presetOffsets.Length;
        ApplyCurrentPreset();
    }

    /// <summary>
    /// 切换到上一个预设
    /// </summary>
    public void PreviousPreset()
    {
        currentPresetIndex = (currentPresetIndex - 1 + presetOffsets.Length) % presetOffsets.Length;
        ApplyCurrentPreset();
    }

    /// <summary>
    /// 应用自定义偏移量
    /// </summary>
    /// <param name="offset">偏移量</param>
    public void ApplyCustomOffset(Vector3 offset)
    {
        if (originTool != null)
        {
            originTool.AdjustOrigin(offset);
            Debug.Log($"已应用自定义偏移: {offset}");
        }
    }

    /// <summary>
    /// 重置到原始状态
    /// </summary>
    public void ResetOrigin()
    {
        if (originTool != null)
        {
            originTool.ResetToOriginal();
            Debug.Log("已重置到原始状态");
        }
    }

    /// <summary>
    /// 撤销上一次操作
    /// </summary>
    public void UndoLastOperation()
    {
        if (originTool != null)
        {
            originTool.UndoLastOperation();
            Debug.Log("已撤销上一次操作");
        }
    }

    /// <summary>
    /// 设置原点到模型底部中心
    /// </summary>
    public void SetOriginToBottomCenter()
    {
        if (targetModel != null)
        {
            Renderer renderer = targetModel.GetComponent<Renderer>();
            if (renderer != null)
            {
                Bounds bounds = renderer.bounds;
                // 计算底部中心点在世界空间的位置
                Vector3 bottomCenter = bounds.center - new Vector3(0, bounds.size.y * 0.5f, 0);
                // 转换为相对于Transform的本地偏移
                Vector3 currentOrigin = targetModel.transform.position;
                Vector3 localOffset = targetModel.transform.InverseTransformDirection(bottomCenter - currentOrigin);

                ApplyCustomOffset(localOffset);
                Debug.Log($"原点已设置到底部中心: 世界位置={bottomCenter}, 本地偏移={localOffset}");
            }
        }
    }

    /// <summary>
    /// 设置原点到几何中心
    /// </summary>
    public void SetOriginToGeometricCenter()
    {
        if (targetModel != null)
        {
            Renderer renderer = targetModel.GetComponent<Renderer>();
            if (renderer != null)
            {
                Bounds bounds = renderer.bounds;
                // 计算几何中心在世界空间的位置
                Vector3 geometricCenter = bounds.center;
                // 转换为相对于Transform的本地偏移
                Vector3 currentOrigin = targetModel.transform.position;
                Vector3 localOffset = targetModel.transform.InverseTransformDirection(geometricCenter - currentOrigin);

                ApplyCustomOffset(localOffset);
                Debug.Log($"原点已设置到几何中心: 世界位置={geometricCenter}, 本地偏移={localOffset}");
            }
        }
    }

    /// <summary>
    /// 获取历史记录信息
    /// </summary>
    public void PrintHistoryInfo()
    {
        if (originTool != null)
        {
            List<string> historyInfo = originTool.GetHistoryInfo();
            Debug.Log($"历史记录数量: {historyInfo.Count}");

            foreach (string info in historyInfo)
            {
                Debug.Log($"历史记录: {info}");
            }
        }
    }

    /// <summary>
    /// 检查是否可以保存网格
    /// </summary>
    /// <returns>是否可以保存</returns>
    public bool CanSaveMesh()
    {
        return originTool != null && originTool.CanSaveMesh();
    }

    /// <summary>
    /// 获取调整后的网格（用于代码保存）
    /// </summary>
    /// <returns>调整后的网格</returns>
    public Mesh GetAdjustedMesh()
    {
        if (originTool != null)
        {
            return originTool.GetAdjustedMesh();
        }
        return null;
    }

    // 在Inspector中显示操作按钮（仅在编辑器中）
#if UNITY_EDITOR
    [UnityEditor.CustomEditor(typeof(Tool_OriginPointEdit_Example))]
    public class Tool_OriginPointEdit_ExampleEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();

            Tool_OriginPointEdit_Example example = (Tool_OriginPointEdit_Example)target;

            UnityEditor.EditorGUILayout.Space(10);
            UnityEditor.EditorGUILayout.LabelField("操作控制", UnityEditor.EditorStyles.boldLabel);

            UnityEditor.EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("应用当前预设"))
            {
                example.ApplyCurrentPreset();
            }
            if (GUILayout.Button("下一个预设"))
            {
                example.NextPreset();
            }
            if (GUILayout.Button("上一个预设"))
            {
                example.PreviousPreset();
            }
            UnityEditor.EditorGUILayout.EndHorizontal();

            UnityEditor.EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("底部中心"))
            {
                example.SetOriginToBottomCenter();
            }
            if (GUILayout.Button("几何中心"))
            {
                example.SetOriginToGeometricCenter();
            }
            UnityEditor.EditorGUILayout.EndHorizontal();

            UnityEditor.EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("撤销操作"))
            {
                example.UndoLastOperation();
            }
            if (GUILayout.Button("重置原点"))
            {
                example.ResetOrigin();
            }
            UnityEditor.EditorGUILayout.EndHorizontal();

            if (GUILayout.Button("打印历史信息"))
            {
                example.PrintHistoryInfo();
            }

            // 保存网格按钮
            UnityEditor.EditorGUILayout.Space(5);
            GUI.enabled = example.CanSaveMesh();
            if (GUILayout.Button("保存调整后的网格", GUILayout.Height(25)))
            {
                SaveMeshFromExample(example);
            }
            GUI.enabled = true;

            // 显示当前状态信息
            if (example.originTool != null)
            {
                UnityEditor.EditorGUILayout.Space(5);
                UnityEditor.EditorGUILayout.LabelField("当前状态", UnityEditor.EditorStyles.boldLabel);
                UnityEditor.EditorGUILayout.LabelField($"当前偏移: {example.originTool.originOffset}");
                UnityEditor.EditorGUILayout.LabelField($"历史记录数: {example.originTool.GetHistoryCount()}");
                UnityEditor.EditorGUILayout.LabelField($"可保存网格: {(example.CanSaveMesh() ? "是" : "否")}");
            }
        }

        /// <summary>
        /// 从示例脚本保存网格
        /// </summary>
        /// <param name="example">示例脚本实例</param>
        private void SaveMeshFromExample(Tool_OriginPointEdit_Example example)
        {
            if (!example.CanSaveMesh())
            {
                UnityEditor.EditorUtility.DisplayDialog("无法保存", "没有调整后的网格可以保存。", "确定");
                return;
            }

            Mesh adjustedMesh = example.GetAdjustedMesh();
            if (adjustedMesh == null)
            {
                UnityEditor.EditorUtility.DisplayDialog("保存失败", "调整后的网格为空。", "确定");
                return;
            }

            // 获取保存路径
            string defaultName = $"{example.targetModel.name}_OriginAdjusted_Example";
            string savePath = UnityEditor.EditorUtility.SaveFilePanelInProject(
                "保存调整后的网格",
                defaultName,
                "asset",
                "选择保存位置");

            if (string.IsNullOrEmpty(savePath))
            {
                return; // 用户取消了保存
            }

            try
            {
                // 创建网格资源的副本
                Mesh meshToSave = Instantiate(adjustedMesh);
                meshToSave.name = System.IO.Path.GetFileNameWithoutExtension(savePath);

                // 保存到项目中
                UnityEditor.AssetDatabase.CreateAsset(meshToSave, savePath);
                UnityEditor.AssetDatabase.SaveAssets();
                UnityEditor.AssetDatabase.Refresh();

                // 在Project窗口中选中保存的资源
                UnityEditor.Selection.activeObject = meshToSave;
                UnityEditor.EditorGUIUtility.PingObject(meshToSave);

                Debug.Log($"网格已从示例脚本保存到: {savePath}");
                UnityEditor.EditorUtility.DisplayDialog("保存成功", $"调整后的网格已保存到:\n{savePath}", "确定");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"保存网格时发生错误: {e.Message}");
                UnityEditor.EditorUtility.DisplayDialog("保存失败", $"保存网格时发生错误:\n{e.Message}", "确定");
            }
        }
    }
#endif
}