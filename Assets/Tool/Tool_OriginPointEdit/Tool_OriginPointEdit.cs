using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 模型原点调整工具 - 用于调整模型的原点位置和旋转
/// 支持历史记录、撤销重做等功能
/// </summary>
[System.Serializable]
public class Tool_OriginPointEdit : MonoBehaviour
{
    [System.Serializable]
    public class OriginEditRecord
    {
        public Vector3 position;
        public Vector3 scale;
        public Quaternion rotation;
        public Vector3 pivotOffset;
        public Vector3 rotationOffset;
        public string timestamp;

        public OriginEditRecord(Transform transform, Vector3 offset, Vector3 rotOffset)
        {
            position = transform.position;
            scale = transform.localScale;
            rotation = transform.rotation;
            pivotOffset = offset;
            rotationOffset = rotOffset;
            timestamp = System.DateTime.Now.ToString("HH:mm:ss");
        }
    }

    [Header("目标设置")]
    [SerializeField] private GameObject targetModel;
    [Tooltip("是否自动获取当前物体作为目标")]
    [SerializeField] private bool useCurrentObject = true;

    [Header("原点调整设置")]
    [SerializeField] public Vector3 originOffset = Vector3.zero;
    [Tooltip("旋转偏移量 (欧拉角)")]
    [SerializeField] public Vector3 rotationOffset = Vector3.zero;
    [Tooltip("是否实时预览调整效果")]
    [SerializeField] public bool enablePreview = true;
    [Tooltip("预览时的颜色")]
    [SerializeField] public Color previewColor = Color.yellow;
    [Tooltip("旋转预览颜色")]
    [SerializeField] public Color rotationPreviewColor = Color.magenta;

    [Header("历史记录设置")]
    [Tooltip("最大历史记录数量")]
    [SerializeField] public int maxHistoryCount = 20;
    [Tooltip("是否启用调试信息")]
    [SerializeField] public bool enableDebug = false;

    // 私有变量
    private List<OriginEditRecord> historyRecords = new List<OriginEditRecord>();
    private OriginEditRecord originalRecord;
    private bool isInitialized = false;
    private MeshRenderer targetRenderer;
    private Mesh originalMesh;
    private Mesh workingMesh;

    // 事件
    public System.Action<Vector3> OnOriginChanged;
    public System.Action<Vector3> OnRotationChanged;
    public System.Action OnHistoryChanged;

    #region Unity生命周期

    private void Awake()
    {
        InitializeTool();
    }

    private void Start()
    {
        if (useCurrentObject && targetModel == null)
        {
            targetModel = gameObject;
        }

        if (targetModel != null)
        {
            SetTargetModel(targetModel);
        }
    }

    private void OnValidate()
    {
        if (Application.isPlaying && isInitialized && enablePreview)
        {
            PreviewOriginAdjustment();
        }
    }

    #endregion

    #region 公共接口

    /// <summary>
    /// 设置目标模型
    /// </summary>
    /// <param name="model">目标模型</param>
    public void SetTargetModel(GameObject model)
    {
        if (model == null)
        {
            LogDebug("目标模型为空");
            return;
        }

        targetModel = model;
        targetRenderer = model.GetComponent<MeshRenderer>();

        if (targetRenderer == null)
        {
            LogDebug($"目标模型 {model.name} 没有MeshRenderer组件");
            return;
        }

        // 获取原始网格
        MeshFilter meshFilter = model.GetComponent<MeshFilter>();
        if (meshFilter != null && meshFilter.sharedMesh != null)
        {
            originalMesh = meshFilter.sharedMesh;
            workingMesh = Instantiate(originalMesh);
            meshFilter.mesh = workingMesh;
        }

        // 记录原始状态
        SaveOriginalState();
        LogDebug($"已设置目标模型: {model.name}");
    }

    /// <summary>
    /// 调整原点位置
    /// </summary>
    /// <param name="offset">偏移量</param>
    public void AdjustOrigin(Vector3 offset)
    {
        if (!ValidateTarget()) return;

        // 保存当前状态到历史记录
        SaveCurrentState();

        // 应用原点调整
        ApplyOriginAdjustment(offset, rotationOffset);

        originOffset = offset;
        OnOriginChanged?.Invoke(offset);

        LogDebug($"原点已调整: {offset}");
    }

    /// <summary>
    /// 调整旋转偏移
    /// </summary>
    /// <param name="rotOffset">旋转偏移量（欧拉角）</param>
    public void AdjustRotation(Vector3 rotOffset)
    {
        if (!ValidateTarget()) return;

        // 保存当前状态到历史记录
        SaveCurrentState();

        // 应用旋转调整
        ApplyOriginAdjustment(originOffset, rotOffset);

        rotationOffset = rotOffset;
        OnRotationChanged?.Invoke(rotOffset);

        LogDebug($"旋转已调整: {rotOffset}");
    }

    /// <summary>
    /// 同时调整原点位置和旋转
    /// </summary>
    /// <param name="posOffset">位置偏移量</param>
    /// <param name="rotOffset">旋转偏移量（欧拉角）</param>
    public void AdjustOriginAndRotation(Vector3 posOffset, Vector3 rotOffset)
    {
        if (!ValidateTarget()) return;

        // 保存当前状态到历史记录
        SaveCurrentState();

        // 应用调整
        ApplyOriginAdjustment(posOffset, rotOffset);

        originOffset = posOffset;
        rotationOffset = rotOffset;
        OnOriginChanged?.Invoke(posOffset);
        OnRotationChanged?.Invoke(rotOffset);

        LogDebug($"原点和旋转已调整 - 位置: {posOffset}, 旋转: {rotOffset}");
    }

    /// <summary>
    /// 设置原点偏移并应用
    /// </summary>
    /// <param name="x">X轴偏移</param>
    /// <param name="y">Y轴偏移</param>
    /// <param name="z">Z轴偏移</param>
    public void SetOriginOffset(float x, float y, float z)
    {
        AdjustOrigin(new Vector3(x, y, z));
    }

    /// <summary>
    /// 设置旋转偏移并应用
    /// </summary>
    /// <param name="x">X轴旋转</param>
    /// <param name="y">Y轴旋转</param>
    /// <param name="z">Z轴旋转</param>
    public void SetRotationOffset(float x, float y, float z)
    {
        AdjustRotation(new Vector3(x, y, z));
    }

    /// <summary>
    /// 快速旋转调整方法
    /// </summary>
    /// <param name="axis">旋转轴 (0=X, 1=Y, 2=Z)</param>
    /// <param name="angle">角度</param>
    public void RotateAroundAxis(int axis, float angle)
    {
        Vector3 currentRotation = rotationOffset;
        switch (axis)
        {
            case 0: // X轴
                currentRotation.x += angle;
                break;
            case 1: // Y轴
                currentRotation.y += angle;
                break;
            case 2: // Z轴
                currentRotation.z += angle;
                break;
        }
        AdjustRotation(currentRotation);
    }

    /// <summary>
    /// 预览原点调整效果
    /// </summary>
    public void PreviewOriginAdjustment()
    {
        if (!ValidateTarget() || !enablePreview) return;

        // 这里可以添加预览逻辑，比如显示辅助线等
        // 暂时通过Gizmos在Scene视图中显示
    }

    /// <summary>
    /// 应用当前设置的原点调整
    /// </summary>
    public void ApplyCurrentAdjustment()
    {
        AdjustOriginAndRotation(originOffset, rotationOffset);
    }

    /// <summary>
    /// 重置到原始状态
    /// </summary>
    public void ResetToOriginal()
    {
        if (!ValidateTarget() || originalRecord == null) return;

        // 保存当前状态到历史记录
        SaveCurrentState();

        // 恢复到原始状态
        RestoreFromRecord(originalRecord);
        originOffset = Vector3.zero;
        rotationOffset = Vector3.zero;

        LogDebug("已重置到原始状态");
        OnOriginChanged?.Invoke(Vector3.zero);
        OnRotationChanged?.Invoke(Vector3.zero);
    }

    /// <summary>
    /// 撤销上一次操作
    /// </summary>
    public void UndoLastOperation()
    {
        if (historyRecords.Count == 0)
        {
            LogDebug("没有可撤销的操作");
            return;
        }

        // 获取上一个状态
        OriginEditRecord lastRecord = historyRecords[historyRecords.Count - 1];
        historyRecords.RemoveAt(historyRecords.Count - 1);

        // 恢复状态
        RestoreFromRecord(lastRecord);
        originOffset = lastRecord.pivotOffset;
        rotationOffset = lastRecord.rotationOffset;

        LogDebug($"已撤销操作，回到 {lastRecord.timestamp} 的状态");
        OnHistoryChanged?.Invoke();
        OnOriginChanged?.Invoke(originOffset);
        OnRotationChanged?.Invoke(rotationOffset);
    }

    /// <summary>
    /// 清空历史记录
    /// </summary>
    public void ClearHistory()
    {
        historyRecords.Clear();
        OnHistoryChanged?.Invoke();
        LogDebug("历史记录已清空");
    }

    /// <summary>
    /// 获取历史记录数量
    /// </summary>
    /// <returns>历史记录数量</returns>
    public int GetHistoryCount()
    {
        return historyRecords.Count;
    }

    /// <summary>
    /// 获取历史记录信息
    /// </summary>
    /// <returns>历史记录信息列表</returns>
    public List<string> GetHistoryInfo()
    {
        List<string> info = new List<string>();
        for (int i = 0; i < historyRecords.Count; i++)
        {
            var record = historyRecords[i];
            info.Add($"{i + 1}. {record.timestamp} - 位置偏移: {record.pivotOffset}, 旋转偏移: {record.rotationOffset}");
        }
        return info;
    }

    /// <summary>
    /// 获取当前调整后的网格
    /// </summary>
    /// <returns>调整后的网格</returns>
    public Mesh GetAdjustedMesh()
    {
        return workingMesh;
    }

    /// <summary>
    /// 获取原始网格
    /// </summary>
    /// <returns>原始网格</returns>
    public Mesh GetOriginalMesh()
    {
        return originalMesh;
    }

    /// <summary>
    /// 检查是否有调整后的网格可以保存
    /// </summary>
    /// <returns>是否可以保存</returns>
    public bool CanSaveMesh()
    {
        return workingMesh != null && (originOffset != Vector3.zero || rotationOffset != Vector3.zero);
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 初始化工具
    /// </summary>
    private void InitializeTool()
    {
        historyRecords = new List<OriginEditRecord>();
        isInitialized = true;
        LogDebug("原点调整工具已初始化");
    }

    /// <summary>
    /// 验证目标是否有效
    /// </summary>
    /// <returns>是否有效</returns>
    private bool ValidateTarget()
    {
        if (targetModel == null)
        {
            LogDebug("目标模型为空");
            return false;
        }

        if (targetRenderer == null)
        {
            LogDebug("目标模型没有Renderer组件");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 保存原始状态
    /// </summary>
    private void SaveOriginalState()
    {
        if (targetModel != null)
        {
            originalRecord = new OriginEditRecord(targetModel.transform, Vector3.zero, Vector3.zero);
            LogDebug("原始状态已保存");
        }
    }

    /// <summary>
    /// 保存当前状态到历史记录
    /// </summary>
    private void SaveCurrentState()
    {
        if (!ValidateTarget()) return;

        OriginEditRecord currentRecord = new OriginEditRecord(targetModel.transform, originOffset, rotationOffset);
        historyRecords.Add(currentRecord);

        // 限制历史记录数量
        if (historyRecords.Count > maxHistoryCount)
        {
            historyRecords.RemoveAt(0);
        }

        OnHistoryChanged?.Invoke();
        LogDebug($"当前状态已保存到历史记录 ({historyRecords.Count}/{maxHistoryCount})");
    }

    /// <summary>
    /// 应用原点和旋转调整
    /// </summary>
    /// <param name="posOffset">位置偏移量</param>
    /// <param name="rotOffset">旋转偏移量（欧拉角）</param>
    private void ApplyOriginAdjustment(Vector3 posOffset, Vector3 rotOffset)
    {
        if (!ValidateTarget() || originalMesh == null) return;

        // 重新创建工作网格 - 确保保持原始网格的所有属性
        if (workingMesh != null)
        {
            DestroyImmediate(workingMesh);
        }
        workingMesh = Instantiate(originalMesh);

        // 获取网格数据
        Vector3[] vertices = workingMesh.vertices;
        Vector3[] normals = workingMesh.normals;
        Vector4[] tangents = workingMesh.tangents;

        // 创建旋转矩阵
        Quaternion rotation = Quaternion.Euler(rotOffset);
        Matrix4x4 rotationMatrix = Matrix4x4.Rotate(rotation);

        // 应用变换到所有顶点
        for (int i = 0; i < vertices.Length; i++)
        {
            Vector3 vertex = vertices[i];

            // 先应用旋转（绕原点旋转）
            vertex = rotationMatrix.MultiplyPoint3x4(vertex);

            // 再应用位置偏移
            vertex -= posOffset;

            vertices[i] = vertex;
        }

        // 如果存在法线数据，也需要应用旋转变换
        if (normals != null && normals.Length == vertices.Length)
        {
            for (int i = 0; i < normals.Length; i++)
            {
                // 法线只应用旋转，不应用位移
                normals[i] = rotationMatrix.MultiplyVector(normals[i]).normalized;
            }
        }

        // 如果存在切线数据，也需要应用旋转变换
        if (tangents != null && tangents.Length == vertices.Length)
        {
            for (int i = 0; i < tangents.Length; i++)
            {
                Vector3 tangentVec = new Vector3(tangents[i].x, tangents[i].y, tangents[i].z);
                tangentVec = rotationMatrix.MultiplyVector(tangentVec).normalized;
                tangents[i] = new Vector4(tangentVec.x, tangentVec.y, tangentVec.z, tangents[i].w);
            }
        }

        // 更新网格数据 - 保持原有的拓扑结构
        workingMesh.vertices = vertices;

        // 只有当原始网格有法线/切线时才设置，否则保持原有状态
        if (normals != null && normals.Length > 0)
        {
            workingMesh.normals = normals;
        }

        if (tangents != null && tangents.Length > 0)
        {
            workingMesh.tangents = tangents;
        }

        // 只重新计算边界框，避免重新计算法线和切线以保持网格接缝
        workingMesh.RecalculateBounds();

        // 如果原始网格没有法线或切线，才重新计算
        if (originalMesh.normals == null || originalMesh.normals.Length == 0)
        {
            workingMesh.RecalculateNormals();
        }

        if (originalMesh.tangents == null || originalMesh.tangents.Length == 0)
        {
            workingMesh.RecalculateTangents();
        }

        // 应用到MeshFilter
        MeshFilter meshFilter = targetModel.GetComponent<MeshFilter>();
        if (meshFilter != null)
        {
            meshFilter.mesh = workingMesh;
        }

        LogDebug($"网格已调整 - 位置偏移: {-posOffset}, 旋转偏移: {rotOffset}");
    }

    /// <summary>
    /// 从记录恢复状态
    /// </summary>
    /// <param name="record">状态记录</param>
    private void RestoreFromRecord(OriginEditRecord record)
    {
        if (!ValidateTarget() || record == null) return;

        // 只恢复网格，不改变Transform
        if (originalMesh != null)
        {
            if (record.pivotOffset == Vector3.zero && record.rotationOffset == Vector3.zero)
            {
                // 恢复到原始网格
                MeshFilter meshFilter = targetModel.GetComponent<MeshFilter>();
                if (meshFilter != null)
                {
                    if (workingMesh != null)
                    {
                        DestroyImmediate(workingMesh);
                    }
                    workingMesh = Instantiate(originalMesh);
                    meshFilter.mesh = workingMesh;
                }
                LogDebug("已恢复到原始网格");
            }
            else
            {
                // 应用记录中的偏移和旋转
                ApplyOriginAdjustment(record.pivotOffset, record.rotationOffset);
                LogDebug($"已恢复到调整状态 - 位置: {record.pivotOffset}, 旋转: {record.rotationOffset}");
            }
        }
    }

    /// <summary>
    /// 调试日志
    /// </summary>
    /// <param name="message">消息</param>
    private void LogDebug(string message)
    {
        if (enableDebug)
        {
            Debug.Log($"[Tool_OriginPointEdit] {message}");
        }
    }

    #endregion

    #region Gizmos绘制

    private void OnDrawGizmos()
    {
        if (!enablePreview || targetModel == null) return;

        // 当前物体的原点（Transform位置）
        Vector3 currentOrigin = targetModel.transform.position;

        // 绘制当前原点（Transform位置）
        Gizmos.color = previewColor;
        Gizmos.DrawWireSphere(currentOrigin, 0.1f);

        // 如果有位置偏移，显示原来网格中心的位置
        if (originOffset != Vector3.zero)
        {
            // 计算原来网格中心在世界空间的位置
            Vector3 originalMeshCenter = currentOrigin + targetModel.transform.TransformDirection(originOffset);

            // 绘制原来的网格中心位置
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(originalMeshCenter, 0.08f);

            // 绘制连接线
            Gizmos.color = Color.white;
            Gizmos.DrawLine(currentOrigin, originalMeshCenter);
        }

        // 绘制坐标轴（以当前原点为中心）
        float axisLength = 0.5f;
        Transform targetTransform = targetModel.transform;

        // 原始坐标轴
        Gizmos.color = Color.red;
        Gizmos.DrawLine(currentOrigin, currentOrigin + targetTransform.right * axisLength);
        Gizmos.color = Color.green;
        Gizmos.DrawLine(currentOrigin, currentOrigin + targetTransform.up * axisLength);
        Gizmos.color = Color.blue;
        Gizmos.DrawLine(currentOrigin, currentOrigin + targetTransform.forward * axisLength);

        // 如果有旋转偏移，绘制旋转后的坐标轴
        if (rotationOffset != Vector3.zero)
        {
            Quaternion rotationQuat = Quaternion.Euler(rotationOffset);
            Vector3 rotatedRight = targetTransform.TransformDirection(rotationQuat * Vector3.right);
            Vector3 rotatedUp = targetTransform.TransformDirection(rotationQuat * Vector3.up);
            Vector3 rotatedForward = targetTransform.TransformDirection(rotationQuat * Vector3.forward);

            // 绘制旋转后的坐标轴（用不同颜色区分）
            Gizmos.color = rotationPreviewColor;
            Gizmos.DrawLine(currentOrigin, currentOrigin + rotatedRight * axisLength * 0.8f);
            Gizmos.DrawLine(currentOrigin, currentOrigin + rotatedUp * axisLength * 0.8f);
            Gizmos.DrawLine(currentOrigin, currentOrigin + rotatedForward * axisLength * 0.8f);
        }

#if UNITY_EDITOR
        // 添加标签
        UnityEditor.Handles.color = Color.white;
        if (originOffset != Vector3.zero || rotationOffset != Vector3.zero)
        {
            UnityEditor.Handles.Label(currentOrigin + Vector3.up * 0.2f, "新原点");

            if (originOffset != Vector3.zero)
            {
                Vector3 originalMeshCenter = currentOrigin + targetModel.transform.TransformDirection(originOffset);
                UnityEditor.Handles.Label(originalMeshCenter + Vector3.up * 0.2f, "原网格中心");
            }

            if (rotationOffset != Vector3.zero)
            {
                UnityEditor.Handles.Label(currentOrigin + Vector3.up * 0.4f, $"旋转: {rotationOffset}°");
            }
        }
#endif
    }

    #endregion

    #region 清理

    private void OnDestroy()
    {
        // 清理创建的网格
        if (workingMesh != null)
        {
            DestroyImmediate(workingMesh);
        }
    }

    #endregion
}