using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

#if UNITY_EDITOR
/// <summary>
/// 模型原点调整工具 - Editor窗口
/// 提供可视化界面来调整模型原点
/// </summary>
public class Tool_Editor_OriginPointEdit : EditorWindow
{
    [Header("目标设置")]
    private GameObject targetModel;
    private Tool_OriginPointEdit originTool;

    [Header("原点调整")]
    private Vector3 originOffset = Vector3.zero;
    private Vector3 tempOffset = Vector3.zero;

    [Header("旋转调整")]
    private Vector3 rotationOffset = Vector3.zero;
    private Vector3 tempRotationOffset = Vector3.zero;

    [Header("界面设置")]
    private bool showPreview = true;
    private bool showHistory = true;
    private bool enableDebug = false;

    // 界面状态
    private Vector2 scrollPosition;
    private bool isPreviewMode = false;
    private GUIStyle titleStyle;
    private GUIStyle buttonStyle;
    private GUIStyle boxStyle;

    // 颜色设置
    private Color primaryColor = new Color(0.2f, 0.6f, 1f);
    private Color successColor = new Color(0.2f, 0.8f, 0.2f);
    private Color warningColor = new Color(1f, 0.8f, 0.2f);
    private Color dangerColor = new Color(1f, 0.3f, 0.3f);

    // 创建菜单项
    [MenuItem("Tools/模型原点调整工具")]
    public static void ShowWindow()
    {
        Tool_Editor_OriginPointEdit window = GetWindow<Tool_Editor_OriginPointEdit>("模型原点调整工具");
        window.minSize = new Vector2(450, 800);
        window.Show();
    }

    private void OnEnable()
    {
        // 订阅选择变化事件
        Selection.selectionChanged += OnSelectionChanged;

        // 初始化样式
        InitializeStyles();

        // 检查当前选择
        OnSelectionChanged();
    }

    private void OnDisable()
    {
        // 取消订阅事件
        Selection.selectionChanged -= OnSelectionChanged;

        // 退出预览模式
        if (isPreviewMode)
        {
            ExitPreviewMode();
        }
    }

    private void OnGUI()
    {
        // 更新样式
        InitializeStyles();

        // 绘制标题
        DrawTitle();

        // 绘制目标选择区域
        DrawTargetSelection();

        // 如果有目标，绘制控制界面
        if (targetModel != null && originTool != null)
        {
            EditorGUILayout.Space(10);
            DrawOriginControls();

            EditorGUILayout.Space(10);
            DrawActionButtons();

            if (showHistory)
            {
                EditorGUILayout.Space(10);
                DrawHistorySection();
            }
        }
        else
        {
            DrawHelpInfo();
        }

        // 绘制设置区域
        EditorGUILayout.Space(15);
        DrawSettings();
    }

    #region 界面绘制

    /// <summary>
    /// 绘制标题
    /// </summary>
    private void DrawTitle()
    {
        EditorGUILayout.BeginVertical(boxStyle);

        GUILayout.Label("模型原点调整工具", titleStyle);
        GUILayout.Label("Tool_OriginPointEdit", EditorStyles.miniLabel);

        EditorGUILayout.EndVertical();
        EditorGUILayout.Space(5);
    }

    /// <summary>
    /// 绘制目标选择区域
    /// </summary>
    private void DrawTargetSelection()
    {
        EditorGUILayout.BeginVertical(boxStyle);
        EditorGUILayout.LabelField("目标模型设置", EditorStyles.boldLabel);

        EditorGUI.BeginChangeCheck();
        GameObject newTarget = (GameObject)EditorGUILayout.ObjectField(
            "目标模型", targetModel, typeof(GameObject), true);

        if (EditorGUI.EndChangeCheck())
        {
            SetTargetModel(newTarget);
        }

        // 显示当前选择的信息
        if (targetModel != null)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("当前目标:", targetModel.name);

            if (originTool != null)
            {
                GUI.color = successColor;
                GUILayout.Label("✓ 工具已连接", GUILayout.Width(80));
                GUI.color = Color.white;
            }
            else
            {
                GUI.color = warningColor;
                GUILayout.Label("⚠ 未找到工具组件", GUILayout.Width(100));
                GUI.color = Color.white;

                if (GUILayout.Button("添加组件", GUILayout.Width(80)))
                {
                    AddOriginToolComponent();
                }
            }

            EditorGUILayout.EndHorizontal();
        }

        EditorGUILayout.EndVertical();
    }

    /// <summary>
    /// 绘制原点控制区域
    /// </summary>
    private void DrawOriginControls()
    {
        EditorGUILayout.BeginVertical(boxStyle);
        EditorGUILayout.LabelField("原点调整控制", EditorStyles.boldLabel);

        // XYZ输入框
        EditorGUILayout.LabelField("偏移量 (X, Y, Z):");

        EditorGUILayout.BeginHorizontal();
        tempOffset.x = EditorGUILayout.FloatField("X", tempOffset.x);
        tempOffset.y = EditorGUILayout.FloatField("Y", tempOffset.y);
        tempOffset.z = EditorGUILayout.FloatField("Z", tempOffset.z);
        EditorGUILayout.EndHorizontal();

        // 快速设置按钮
        EditorGUILayout.LabelField("快速设置:");
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("重置为0"))
        {
            tempOffset = Vector3.zero;
        }

        if (GUILayout.Button("底部中心"))
        {
            SetToBottomCenter();
        }

        if (GUILayout.Button("几何中心"))
        {
            SetToGeometricCenter();
        }

        EditorGUILayout.EndHorizontal();

        // 预览模式
        EditorGUILayout.BeginHorizontal();
        bool newPreviewMode = EditorGUILayout.Toggle("预览模式", isPreviewMode);
        if (newPreviewMode != isPreviewMode)
        {
            if (newPreviewMode)
            {
                EnterPreviewMode();
            }
            else
            {
                ExitPreviewMode();
            }
        }

        if (isPreviewMode)
        {
            GUI.color = primaryColor;
            GUILayout.Label("预览中...", GUILayout.Width(60));
            GUI.color = Color.white;
        }

        EditorGUILayout.EndHorizontal();

        // 显示当前偏移
        if (originTool != null)
        {
            Vector3 currentOffset = originTool.originOffset;
            EditorGUILayout.LabelField($"当前位置偏移: ({currentOffset.x:F3}, {currentOffset.y:F3}, {currentOffset.z:F3})");

            Vector3 currentRotation = originTool.rotationOffset;
            EditorGUILayout.LabelField($"当前旋转偏移: ({currentRotation.x:F1}°, {currentRotation.y:F1}°, {currentRotation.z:F1}°)");
        }

        EditorGUILayout.EndVertical();

        // 旋转调整区域
        EditorGUILayout.BeginVertical(boxStyle);
        EditorGUILayout.LabelField("旋转调整控制", EditorStyles.boldLabel);

        // 旋转XYZ输入框
        EditorGUILayout.LabelField("旋转偏移 (X, Y, Z) 度数:");

        EditorGUILayout.BeginHorizontal();
        tempRotationOffset.x = EditorGUILayout.FloatField("X", tempRotationOffset.x);
        tempRotationOffset.y = EditorGUILayout.FloatField("Y", tempRotationOffset.y);
        tempRotationOffset.z = EditorGUILayout.FloatField("Z", tempRotationOffset.z);
        EditorGUILayout.EndHorizontal();

        // 快速旋转按钮
        EditorGUILayout.LabelField("快速旋转:");

        // X轴旋转
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("X轴:", GUILayout.Width(30));
        if (GUILayout.Button("-90°", GUILayout.Width(50)))
        {
            tempRotationOffset.x -= 90f;
        }
        if (GUILayout.Button("-45°", GUILayout.Width(50)))
        {
            tempRotationOffset.x -= 45f;
        }
        if (GUILayout.Button("+45°", GUILayout.Width(50)))
        {
            tempRotationOffset.x += 45f;
        }
        if (GUILayout.Button("+90°", GUILayout.Width(50)))
        {
            tempRotationOffset.x += 90f;
        }
        if (GUILayout.Button("重置", GUILayout.Width(50)))
        {
            tempRotationOffset.x = 0f;
        }
        EditorGUILayout.EndHorizontal();

        // Y轴旋转
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Y轴:", GUILayout.Width(30));
        if (GUILayout.Button("-90°", GUILayout.Width(50)))
        {
            tempRotationOffset.y -= 90f;
        }
        if (GUILayout.Button("-45°", GUILayout.Width(50)))
        {
            tempRotationOffset.y -= 45f;
        }
        if (GUILayout.Button("+45°", GUILayout.Width(50)))
        {
            tempRotationOffset.y += 45f;
        }
        if (GUILayout.Button("+90°", GUILayout.Width(50)))
        {
            tempRotationOffset.y += 90f;
        }
        if (GUILayout.Button("重置", GUILayout.Width(50)))
        {
            tempRotationOffset.y = 0f;
        }
        EditorGUILayout.EndHorizontal();

        // Z轴旋转
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Z轴:", GUILayout.Width(30));
        if (GUILayout.Button("-90°", GUILayout.Width(50)))
        {
            tempRotationOffset.z -= 90f;
        }
        if (GUILayout.Button("-45°", GUILayout.Width(50)))
        {
            tempRotationOffset.z -= 45f;
        }
        if (GUILayout.Button("+45°", GUILayout.Width(50)))
        {
            tempRotationOffset.z += 45f;
        }
        if (GUILayout.Button("+90°", GUILayout.Width(50)))
        {
            tempRotationOffset.z += 90f;
        }
        if (GUILayout.Button("重置", GUILayout.Width(50)))
        {
            tempRotationOffset.z = 0f;
        }
        EditorGUILayout.EndHorizontal();

        // 旋转快速设置
        EditorGUILayout.LabelField("快速设置:");
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("重置旋转"))
        {
            tempRotationOffset = Vector3.zero;
        }

        if (GUILayout.Button("翻转X"))
        {
            tempRotationOffset.x = 180f;
        }

        if (GUILayout.Button("翻转Y"))
        {
            tempRotationOffset.y = 180f;
        }

        if (GUILayout.Button("翻转Z"))
        {
            tempRotationOffset.z = 180f;
        }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.EndVertical();
    }

    /// <summary>
    /// 绘制操作按钮
    /// </summary>
    private void DrawActionButtons()
    {
        EditorGUILayout.BeginVertical(boxStyle);
        EditorGUILayout.LabelField("操作控制", EditorStyles.boldLabel);

        EditorGUILayout.BeginHorizontal();

        // 应用调整按钮
        GUI.color = primaryColor;
        if (GUILayout.Button("确定位置调整", GUILayout.Height(30)))
        {
            ApplyOriginAdjustment();
        }

        if (GUILayout.Button("确定旋转调整", GUILayout.Height(30)))
        {
            ApplyRotationAdjustment();
        }

        if (GUILayout.Button("确定全部调整", GUILayout.Height(30)))
        {
            ApplyAllAdjustments();
        }
        GUI.color = Color.white;

        // 撤销按钮
        GUI.enabled = originTool != null && originTool.GetHistoryCount() > 0;
        GUI.color = warningColor;
        if (GUILayout.Button("撤销上一次", GUILayout.Height(30)))
        {
            UndoLastOperation();
        }
        GUI.color = Color.white;
        GUI.enabled = true;

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();

        // 重置到原始状态按钮
        GUI.color = dangerColor;
        if (GUILayout.Button("重置到最开始", GUILayout.Height(30)))
        {
            if (EditorUtility.DisplayDialog("确认重置",
                "确定要重置到最开始的状态吗？这将清除所有调整。", "确定", "取消"))
            {
                ResetToOriginal();
            }
        }
        GUI.color = Color.white;

        // 清空历史记录按钮
        GUI.enabled = originTool != null && originTool.GetHistoryCount() > 0;
        if (GUILayout.Button("清空历史", GUILayout.Height(30)))
        {
            if (EditorUtility.DisplayDialog("确认清空",
                "确定要清空所有历史记录吗？", "确定", "取消"))
            {
                ClearHistory();
            }
        }
        GUI.enabled = true;

        EditorGUILayout.EndHorizontal();

        // 保存网格按钮
        EditorGUILayout.BeginHorizontal();

        GUI.enabled = originTool != null && originTool.CanSaveMesh();
        GUI.color = successColor;
        if (GUILayout.Button("保存调整后的网格", GUILayout.Height(30)))
        {
            SaveAdjustedMesh();
        }
        GUI.color = Color.white;
        GUI.enabled = true;

        EditorGUILayout.EndHorizontal();

        // 保存网格按钮
        EditorGUILayout.BeginHorizontal();

        GUI.enabled = originTool != null && originTool.CanSaveMesh();
        GUI.color = successColor;
        if (GUILayout.Button("保存调整后的网格", GUILayout.Height(30)))
        {
            SaveAdjustedMesh();
        }
        GUI.color = Color.white;
        GUI.enabled = true;

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.EndVertical();
    }

    /// <summary>
    /// 绘制历史记录区域
    /// </summary>
    private void DrawHistorySection()
    {
        EditorGUILayout.BeginVertical(boxStyle);

        EditorGUILayout.BeginHorizontal();
        showHistory = EditorGUILayout.Foldout(showHistory, "历史记录", true);

        if (originTool != null)
        {
            int historyCount = originTool.GetHistoryCount();
            GUILayout.Label($"({historyCount} 条记录)", EditorStyles.miniLabel);
        }

        EditorGUILayout.EndHorizontal();

        if (showHistory && originTool != null)
        {
            List<string> historyInfo = originTool.GetHistoryInfo();

            if (historyInfo.Count > 0)
            {
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(120));

                foreach (string info in historyInfo)
                {
                    EditorGUILayout.LabelField(info, EditorStyles.miniLabel);
                }

                EditorGUILayout.EndScrollView();
            }
            else
            {
                EditorGUILayout.LabelField("暂无历史记录", EditorStyles.centeredGreyMiniLabel);
            }
        }

        EditorGUILayout.EndVertical();
    }

    /// <summary>
    /// 绘制帮助信息
    /// </summary>
    private void DrawHelpInfo()
    {
        EditorGUILayout.BeginVertical(boxStyle);
        EditorGUILayout.LabelField("使用说明", EditorStyles.boldLabel);

        EditorGUILayout.LabelField("1. 在场景中选择要调整原点的模型");
        EditorGUILayout.LabelField("2. 或者直接拖拽模型到上方的目标模型框中");
        EditorGUILayout.LabelField("3. 输入XYZ偏移值或使用快速设置");
        EditorGUILayout.LabelField("4. 设置旋转角度或使用快速旋转按钮");
        EditorGUILayout.LabelField("5. 点击对应的'确定调整'按钮应用更改");
        EditorGUILayout.LabelField("6. 可以使用'撤销'或'重置'功能恢复");

        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("注意:", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("• 工具会自动添加Tool_OriginPointEdit组件");
        EditorGUILayout.LabelField("• 支持预览模式查看调整效果");
        EditorGUILayout.LabelField("• 支持位置和旋转的独立或同时调整");
        EditorGUILayout.LabelField("• 所有操作都会保存到历史记录中");

        EditorGUILayout.EndVertical();
    }

    /// <summary>
    /// 绘制设置区域
    /// </summary>
    private void DrawSettings()
    {
        EditorGUILayout.BeginVertical(boxStyle);
        EditorGUILayout.LabelField("工具设置", EditorStyles.boldLabel);

        showPreview = EditorGUILayout.Toggle("显示预览", showPreview);
        enableDebug = EditorGUILayout.Toggle("启用调试", enableDebug);

        // 应用设置到工具组件
        if (originTool != null)
        {
            originTool.enablePreview = showPreview;
            originTool.enableDebug = enableDebug;
        }

        EditorGUILayout.EndVertical();
    }

    #endregion

    #region 功能实现

    /// <summary>
    /// 设置目标模型
    /// </summary>
    /// <param name="model">目标模型</param>
    private void SetTargetModel(GameObject model)
    {
        // 退出当前预览模式
        if (isPreviewMode)
        {
            ExitPreviewMode();
        }

        targetModel = model;

        if (targetModel != null)
        {
            // 查找或添加工具组件
            originTool = targetModel.GetComponent<Tool_OriginPointEdit>();
            if (originTool == null)
            {
                originTool = targetModel.AddComponent<Tool_OriginPointEdit>();
                Debug.Log($"已为 {targetModel.name} 添加 Tool_OriginPointEdit 组件");
            }

            // 设置工具组件的目标
            originTool.SetTargetModel(targetModel);

            // 同步当前偏移值
            tempOffset = originTool.originOffset;
            tempRotationOffset = originTool.rotationOffset;

            // 应用设置
            originTool.enablePreview = showPreview;
            originTool.enableDebug = enableDebug;
        }
        else
        {
            originTool = null;
        }

        Repaint();
    }

    /// <summary>
    /// 添加工具组件
    /// </summary>
    private void AddOriginToolComponent()
    {
        if (targetModel != null)
        {
            originTool = targetModel.AddComponent<Tool_OriginPointEdit>();
            originTool.SetTargetModel(targetModel);

            Debug.Log($"已为 {targetModel.name} 添加 Tool_OriginPointEdit 组件");
            Repaint();
        }
    }

    /// <summary>
    /// 应用原点调整
    /// </summary>
    private void ApplyOriginAdjustment()
    {
        if (originTool != null)
        {
            // 退出预览模式
            if (isPreviewMode)
            {
                ExitPreviewMode();
            }

            // 应用调整
            originTool.AdjustOrigin(tempOffset);

            Debug.Log($"原点调整已应用: {tempOffset}");

            // 标记场景为已修改
            EditorUtility.SetDirty(targetModel);
            EditorUtility.SetDirty(originTool);
        }
    }

    /// <summary>
    /// 应用旋转调整
    /// </summary>
    private void ApplyRotationAdjustment()
    {
        if (originTool != null)
        {
            // 退出预览模式
            if (isPreviewMode)
            {
                ExitPreviewMode();
            }

            // 应用旋转调整
            originTool.AdjustRotation(tempRotationOffset);

            Debug.Log($"旋转调整已应用: {tempRotationOffset}");

            // 标记场景为已修改
            EditorUtility.SetDirty(targetModel);
            EditorUtility.SetDirty(originTool);
        }
    }

    /// <summary>
    /// 应用全部调整（位置和旋转）
    /// </summary>
    private void ApplyAllAdjustments()
    {
        if (originTool != null)
        {
            // 退出预览模式
            if (isPreviewMode)
            {
                ExitPreviewMode();
            }

            // 同时应用位置和旋转调整
            originTool.AdjustOriginAndRotation(tempOffset, tempRotationOffset);

            Debug.Log($"全部调整已应用 - 位置: {tempOffset}, 旋转: {tempRotationOffset}");

            // 标记场景为已修改
            EditorUtility.SetDirty(targetModel);
            EditorUtility.SetDirty(originTool);
        }
    }

    /// <summary>
    /// 撤销上一次操作
    /// </summary>
    private void UndoLastOperation()
    {
        if (originTool != null)
        {
            originTool.UndoLastOperation();
            tempOffset = originTool.originOffset;
            tempRotationOffset = originTool.rotationOffset;

            Debug.Log("已撤销上一次操作");
            EditorUtility.SetDirty(targetModel);
            EditorUtility.SetDirty(originTool);
        }
    }

    /// <summary>
    /// 重置到原始状态
    /// </summary>
    private void ResetToOriginal()
    {
        if (originTool != null)
        {
            // 退出预览模式
            if (isPreviewMode)
            {
                ExitPreviewMode();
            }

            originTool.ResetToOriginal();
            tempOffset = Vector3.zero;
            tempRotationOffset = Vector3.zero;

            Debug.Log("已重置到原始状态");
            EditorUtility.SetDirty(targetModel);
            EditorUtility.SetDirty(originTool);
        }
    }

    /// <summary>
    /// 清空历史记录
    /// </summary>
    private void ClearHistory()
    {
        if (originTool != null)
        {
            originTool.ClearHistory();
            Debug.Log("历史记录已清空");
        }
    }

    /// <summary>
    /// 保存调整后的网格
    /// </summary>
    private void SaveAdjustedMesh()
    {
        if (originTool == null || !originTool.CanSaveMesh())
        {
            EditorUtility.DisplayDialog("无法保存", "没有调整后的网格可以保存。", "确定");
            return;
        }

        Mesh adjustedMesh = originTool.GetAdjustedMesh();
        if (adjustedMesh == null)
        {
            EditorUtility.DisplayDialog("保存失败", "调整后的网格为空。", "确定");
            return;
        }

        // 获取保存路径
        string defaultName = $"{targetModel.name}_OriginAdjusted";
        string savePath = EditorUtility.SaveFilePanelInProject(
            "保存调整后的网格",
            defaultName,
            "asset",
            "选择保存位置");

        if (string.IsNullOrEmpty(savePath))
        {
            return; // 用户取消了保存
        }

        try
        {
            // 创建网格资源的副本
            Mesh meshToSave = Instantiate(adjustedMesh);
            meshToSave.name = System.IO.Path.GetFileNameWithoutExtension(savePath);

            // 保存到项目中
            UnityEditor.AssetDatabase.CreateAsset(meshToSave, savePath);
            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();

            // 在Project窗口中选中保存的资源
            UnityEditor.Selection.activeObject = meshToSave;
            UnityEditor.EditorGUIUtility.PingObject(meshToSave);

            Debug.Log($"网格已保存到: {savePath}");

            // 显示成功对话框
            if (EditorUtility.DisplayDialog("保存成功",
                $"调整后的网格已保存到:\n{savePath}\n\n是否要将当前模型的网格替换为保存的网格？",
                "是", "否"))
            {
                // 替换当前模型的网格
                ReplaceMeshWithSaved(meshToSave);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"保存网格时发生错误: {e.Message}");
            EditorUtility.DisplayDialog("保存失败", $"保存网格时发生错误:\n{e.Message}", "确定");
        }
    }

    /// <summary>
    /// 用保存的网格替换当前模型的网格
    /// </summary>
    /// <param name="savedMesh">保存的网格</param>
    private void ReplaceMeshWithSaved(Mesh savedMesh)
    {
        if (targetModel != null && savedMesh != null)
        {
            MeshFilter meshFilter = targetModel.GetComponent<MeshFilter>();
            if (meshFilter != null)
            {
                // 记录这个操作到撤销系统
                UnityEditor.Undo.RecordObject(meshFilter, "替换网格");

                meshFilter.sharedMesh = savedMesh;

                // 标记场景为已修改
                UnityEditor.EditorUtility.SetDirty(meshFilter);
                UnityEditor.EditorUtility.SetDirty(targetModel);

                Debug.Log($"已将模型 {targetModel.name} 的网格替换为保存的网格");
            }
        }
    }

    /// <summary>
    /// 设置到底部中心
    /// </summary>
    private void SetToBottomCenter()
    {
        if (targetModel != null)
        {
            Renderer renderer = targetModel.GetComponent<Renderer>();
            if (renderer != null)
            {
                Bounds bounds = renderer.bounds;
                // 计算底部中心点在世界空间的位置
                Vector3 bottomCenter = bounds.center - new Vector3(0, bounds.size.y * 0.5f, 0);
                // 转换为相对于Transform的本地偏移
                Vector3 currentOrigin = targetModel.transform.position;
                tempOffset = targetModel.transform.InverseTransformDirection(bottomCenter - currentOrigin);

                Debug.Log($"底部中心设置: 世界位置={bottomCenter}, 本地偏移={tempOffset}");
            }
        }
    }

    /// <summary>
    /// 设置到几何中心
    /// </summary>
    private void SetToGeometricCenter()
    {
        if (targetModel != null)
        {
            Renderer renderer = targetModel.GetComponent<Renderer>();
            if (renderer != null)
            {
                Bounds bounds = renderer.bounds;
                // 计算几何中心在世界空间的位置
                Vector3 geometricCenter = bounds.center;
                // 转换为相对于Transform的本地偏移
                Vector3 currentOrigin = targetModel.transform.position;
                tempOffset = targetModel.transform.InverseTransformDirection(geometricCenter - currentOrigin);

                Debug.Log($"几何中心设置: 世界位置={geometricCenter}, 本地偏移={tempOffset}");
            }
        }
    }

    /// <summary>
    /// 进入预览模式
    /// </summary>
    private void EnterPreviewMode()
    {
        if (originTool != null)
        {
            isPreviewMode = true;
            // 这里可以添加预览逻辑
            SceneView.RepaintAll();
        }
    }

    /// <summary>
    /// 退出预览模式
    /// </summary>
    private void ExitPreviewMode()
    {
        isPreviewMode = false;
        SceneView.RepaintAll();
    }

    /// <summary>
    /// 选择变化回调
    /// </summary>
    private void OnSelectionChanged()
    {
        if (Selection.activeGameObject != null && Selection.activeGameObject != targetModel)
        {
            // 检查选择的对象是否有MeshRenderer
            if (Selection.activeGameObject.GetComponent<MeshRenderer>() != null)
            {
                SetTargetModel(Selection.activeGameObject);
            }
        }
    }

    /// <summary>
    /// 初始化样式
    /// </summary>
    private void InitializeStyles()
    {
        if (titleStyle == null)
        {
            titleStyle = new GUIStyle(EditorStyles.boldLabel);
            titleStyle.fontSize = 16;
            titleStyle.alignment = TextAnchor.MiddleCenter;
        }

        if (buttonStyle == null)
        {
            buttonStyle = new GUIStyle(GUI.skin.button);
            buttonStyle.fontStyle = FontStyle.Bold;
        }

        if (boxStyle == null)
        {
            boxStyle = new GUIStyle(EditorStyles.helpBox);
            boxStyle.padding = new RectOffset(10, 10, 10, 10);
        }
    }

    #endregion
}
#endif