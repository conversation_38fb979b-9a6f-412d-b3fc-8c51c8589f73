using LitJson;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using UnityEngine;

public abstract class Tool_SaveData_Base<T> 
{

    //获取
    public  T Get
    {
        get
        {
            if (currentData == null)
            {
                var datajson = MXR_BRIGE.Playerprefs_Getstring(typeof(T).ToString(), "");
                if (datajson == "")
                {
                    T data = CreateData();
                    currentData = data;
                    Save();
                }
                else
                {
                    T data = JsonMapper.ToObject<T>(datajson);
                    currentData = data;
                }

            }

            return currentData;
        }
        
    }



    private  T currentData;

    //赋值
    public T Set
    {
        get
        {
            Tool_InvokeAction.Invoker.InvokeAciton(() => { Save();  }, 0.1f);
       
            return currentData;
        }
    }


    protected abstract T CreateData();



    void Save()
    {
        if (currentData == null)
            return;
        string djson = JsonMapper.ToJson(currentData);
        MXR_BRIGE.Playerprefs_SetString(typeof(T).ToString(), djson);
    }

    public void Clear()
    {
        T data = CreateData();
        currentData = data;
        Save();
    }
    

}

//
// // 1. 定义数据结构
// [System.Serializable]
// public class PlayerData
// {
//     public int coins = 0;
//     public int level = 1;
//     public List<string> unlockedItems = new List<string>();
//     public bool hasCompletedTutorial = false;
// }
//
// // 2. 创建继承自Tool_SaveData_Base的数据管理类
// public class PlayerDataManager : Tool_SaveData_Base<PlayerData>
// {
//     // 单例实现
//     private static PlayerDataManager _instance;
//     public static PlayerDataManager Instance
//     {
//         get
//         {
//             if (_instance == null)
//                 _instance = new PlayerDataManager();
//             return _instance;
//         }
//     }
//
//     // 实现CreateData方法，提供默认数据
//     protected override PlayerData CreateData()
//     {
//         return new PlayerData();
//     }
// }
//
// // 3. 在游戏中使用示例
// public class GameManager : MonoBehaviour
// {
//     void Start()
//     {
//         // 读取玩家数据 - 可以使用Get或Set来获取数据
//         Debug.Log($"玩家金币: {PlayerDataManager.Instance.Set.coins}");
//         Debug.Log($"玩家等级: {PlayerDataManager.Instance.Set.level}");
//         
//         // 检查是否完成教程
//         if (!PlayerDataManager.Instance.Set.hasCompletedTutorial)
//         {
//             ShowTutorial();
//         }
//     }
//     
//     void AddCoins(int amount)
//     {
//         // 直接通过Set获取数据并修改（会自动触发保存）
//         PlayerDataManager.Instance.Set.coins += amount;
//         Debug.Log($"添加金币: {amount}, 当前金币: {PlayerDataManager.Instance.Set.coins}");
//     }
//     
//     void SetCoins(int amount)
//     {
//         // 直接设置金币数量
//         PlayerDataManager.Instance.Set.coins = amount;
//         Debug.Log($"设置金币为: {amount}");
//     }
//     
//     void LevelUp()
//     {
//         // 提升等级
//         PlayerDataManager.Instance.Set.level++;
//         Debug.Log($"升级! 当前等级: {PlayerDataManager.Instance.Set.level}");
//     }
//     
//     void UnlockItem(string itemId)
//     {
//         // 解锁物品
//         if (!PlayerDataManager.Instance.Set.unlockedItems.Contains(itemId))
//         {
//             PlayerDataManager.Instance.Set.unlockedItems.Add(itemId);
//             Debug.Log($"解锁物品: {itemId}");
//         }
//     }
//     
//     void CompleteTutorial()
//     {
//         // 标记教程完成
//         PlayerDataManager.Instance.Set.hasCompletedTutorial = true;
//         Debug.Log("教程已完成");
//     }
//     
//     void ResetPlayerData()
//     {
//         // 重置数据
//         PlayerDataManager.Instance.Clear();
//         Debug.Log("玩家数据已重置");
//     }
//     
//     void ShowTutorial()
//     {
//         Debug.Log("显示教程");
//     }
// }