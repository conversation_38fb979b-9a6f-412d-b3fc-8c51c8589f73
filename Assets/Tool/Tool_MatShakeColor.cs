using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_MatShakeColor : MonoBehaviour
{

    public SkinnedMeshRenderer[] SkinMeshRenders;
    public MeshRenderer[] MeshRenders;

    public bool AutoGetSkinMesh;
    public bool AutoGetMesh;

     List<Material> Mat = new List<Material>();

     Color OrginColor = new Color(1,1,1);
     Color ToColor;

    int colorState = 0;
    public float ColorToSpeed = 4.5f;
    public float ColorOriSpeed = 4.5f;

    public string ShaderColorArg = "_EmissionColor";

    void Awake()
    {
        if (AutoGetSkinMesh)
            SkinMeshRenders = GetComponentsInChildren<SkinnedMeshRenderer>();
        if(AutoGetMesh)
            MeshRenders = GetComponentsInChildren<MeshRenderer>();

        for (var i = 0; i < SkinMeshRenders.Length;i++)
        {
            for(var j = 0;j < SkinMeshRenders[i].materials.Length;j++)
            {
                Mat.Add(SkinMeshRenders[i].materials[j]);
            }

        }

        for (var i = 0; i < MeshRenders.Length; i++)
        {
            for (var j = 0; j < MeshRenders[i].materials.Length; j++)
            {
                Mat.Add(MeshRenders[i].materials[j]);
            }

        }

        for (var i = 0; i < Mat.Count; i++)
        {
            OrginColor = Mat[i].GetColor(ShaderColorArg);
            break;
        }

 

    }


    private void FixedUpdate()
    {
      

        if (colorState == 1)
            {
                bool to = true;
                for (var i = 0; i < Mat.Count; i++)
                {
                    var ncolor = Mat[i].GetColor(ShaderColorArg);
                    if (!AreColorsClose(ncolor, ToColor, 0.01f))
                    {

                        to = false;

                        Mat[i].SetColor(ShaderColorArg, Color.Lerp(ncolor, ToColor, ColorToSpeed * Time.fixedDeltaTime));

                    }

                }

                if(to)
                {
                    colorState = 2;
                }

            }

            if(colorState == 2)
            {
                bool ori = true;
                for (var i = 0; i < Mat.Count; i++)
                {
                    var ncolor = Mat[i].GetColor(ShaderColorArg);
                    if (!AreColorsClose(ncolor, OrginColor, 0.01f))
                    {

                        ori = false;

                        Mat[i].SetColor(ShaderColorArg, Color.Lerp(ncolor, OrginColor, ColorOriSpeed * Time.fixedDeltaTime));

                    }

                }

                if (ori)
                {
                    colorState = 0;
                }
            }
         
            
        
           
    }


    public void HighlightColor(float r, float g, float b)
    {
        ToColor = new Color(r, g, b);
        colorState = 1;
    }

    public void SetRender(SkinnedMeshRenderer[] render)
    {
        SkinMeshRenders = render;
        Mat = new List<Material>();

        for (var i = 0; i < SkinMeshRenders.Length; i++)
        {
            for (var j = 0; j < SkinMeshRenders[i].materials.Length; j++)
            {
                Mat.Add(SkinMeshRenders[i].materials[j]);
            }

        }

        for (var i = 0; i < Mat.Count; i++)
        {
            OrginColor = Mat[i].GetColor(ShaderColorArg);
            break;
        }

    }

    public void SetRender(MeshRenderer[] render)
    {
        MeshRenders = render;
        Mat = new List<Material>();

        for (var i = 0; i < MeshRenders.Length; i++)
        {
            for (var j = 0; j < MeshRenders[i].materials.Length; j++)
            {
                Mat.Add(MeshRenders[i].materials[j]);
            }

        }

        for (var i = 0; i < Mat.Count; i++)
        {
            OrginColor = Mat[i].GetColor(ShaderColorArg);
            break;
        }

    }

    bool AreColorsClose(Color color1, Color color2, float threshold)
    {
        float rDiff = Mathf.Abs(color1.r - color2.r);
        float gDiff = Mathf.Abs(color1.g - color2.g);
        float bDiff = Mathf.Abs(color1.b - color2.b);

        // 你可以根据需要调整这个判断条件，例如使用平方和
        float totalDiff = rDiff + gDiff + bDiff;

        return totalDiff <= threshold * 3; // 乘以3是因为有三个通道
    }

}
