using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class Tool_WorldPos_To_UIPos 
{

    //GET rectTransform.position


    public static Vector2 WorldPosToUIPos(Vector3 worldPos, Canvas canvas,Camera came)
    {
        //摄像机空间值域[0,1]，z轴值代表深度
        var viewPos = came.WorldToViewportPoint(worldPos);
        //按照值域进行裁剪
        if (viewPos.x >= 0 && viewPos.x <= 1 && viewPos.y >= 0 && viewPos.y <= 1)
        {
            //屏幕空间高度值
            float sheight = viewPos.y * Screen.height;
            //屏幕空间宽度值
            float swidth = viewPos.x * Screen.width;
            //适配转化
            return new Vector2(GetFixed(swidth, canvas), GetFixed(sheight, canvas));
        }
        //返回一个固定值-1代表不在屏幕当中
        return -Vector2.one;
    }

    //Screen坐标值适配Canvas画布
    static float GetFixed(float value, Canvas canvas)
    {
        var cs = canvas.GetComponent<CanvasScaler>();
        if (cs.matchWidthOrHeight == 0)
            //匹配宽度时仅按照宽度计算
            return value * Screen.width / Screen.width;
        else
            //匹配高度时仅按照高度计算
            return value * Screen.height / Screen.height;
    }

}
