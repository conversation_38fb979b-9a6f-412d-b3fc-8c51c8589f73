using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_FlattenCapCollider 
{
    public static void FlattenCapCollider(CapsuleCollider col)
    {

        var cap_collider = col;
        var raidus = cap_collider.radius;
        var oriheight = cap_collider.height;
        var oriy = cap_collider.center.y;

        var tarheight = raidus * 2;

        var tary = oriy * (tarheight / oriheight);

        cap_collider.height = tarheight;
        cap_collider.center = new Vector3(cap_collider.center.x, tary, cap_collider.center.z);
    }
}
