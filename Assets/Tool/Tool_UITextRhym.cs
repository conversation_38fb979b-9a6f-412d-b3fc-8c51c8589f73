using System;
using System.Collections;
using UnityEngine;
using TMPro;

/// <summary>
/// 文本韵律动画工具 - 使文本字符上下浮动
/// </summary>
public class Tool_UITextRhym : MonoBehaviour
{
    [SerializeField, Tooltip("文本组件")] 
    private TextMeshProUGUI targetText;
    
    [Serial<PERSON><PERSON><PERSON>, Toolt<PERSON>("动画高度")] 
    private float animationHeight = 20f;
    
    [SerializeField, Toolt<PERSON>("动画周期(秒)")] 
    private float animationDuration = 0.8f;
    
    [SerializeField, Tooltip("字符间动画偏移")] 
    private float characterOffset = 0.1f;

    private void Start()
    {
        if (targetText == null)
        {
            targetText = GetComponent<TextMeshProUGUI>();
            if (targetText == null)
            {
                Debug.LogError("Tool_TextRhym: 未找到TextMeshProUGUI组件");
                return;
            }
        }
        
        StartCoroutine(AnimateText());
    }

    /// <summary>
    /// 文本动画协程
    /// </summary>
    private IEnumerator AnimateText()
    {
        if (targetText == null) yield break;

        targetText.ForceMeshUpdate();
        
        TMP_TextInfo textInfo = targetText.textInfo;
        if (textInfo == null || textInfo.meshInfo == null || textInfo.meshInfo.Length == 0) 
        {
            Debug.LogError("TextMeshPro 文本信息获取失败");
            yield break;
        }
        
        Vector3[] originalVertices = new Vector3[textInfo.meshInfo[0].vertices.Length];
        Array.Copy(textInfo.meshInfo[0].vertices, originalVertices, originalVertices.Length);
        
        float totalTime = 0;
        while (true)
        {
            totalTime += Time.deltaTime;
            
            targetText.ForceMeshUpdate();

            
            for (int i = 0; i < textInfo.characterCount; i++)
            {
                TMP_CharacterInfo charInfo = textInfo.characterInfo[i];
                if (!charInfo.isVisible) continue;
                
                float charTime = totalTime - (i * characterOffset);
                
                float yOffset = Mathf.Sin(charTime * Mathf.PI / animationDuration) * animationHeight;
                
                int vertexIndex = charInfo.vertexIndex;
                for (int j = 0; j < 4; j++)
                {
                    Vector3 vertex = originalVertices[vertexIndex + j];
                    textInfo.meshInfo[0].vertices[vertexIndex + j] = vertex + new Vector3(0, yOffset, 0);
                }
            }
            
            for (int i = 0; i < textInfo.meshInfo.Length; i++)
            {
                textInfo.meshInfo[i].mesh.vertices = textInfo.meshInfo[i].vertices;
                targetText.UpdateGeometry(textInfo.meshInfo[i].mesh, i);
            }
            
            yield return null;
        }
    }
}