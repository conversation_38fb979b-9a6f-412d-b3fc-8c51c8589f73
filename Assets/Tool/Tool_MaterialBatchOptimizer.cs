using System.Collections.Generic;
using UnityEngine;
using System.Collections;

/// <summary>
/// 材质批处理优化工具类
/// 负责管理MaterialPropertyBlock，避免材质实例化，提高渲染批次性能
/// 支持多种材质属性的设置和管理，确保不会触发材质实例化
/// </summary>
public static class Tool_MaterialBatchOptimizer
{
    #region 常用Shader属性ID缓存
    // 基础属性
    public static readonly int ShaderID_Color = Shader.PropertyToID("_Color");
    public static readonly int ShaderID_MainTex = Shader.PropertyToID("_MainTex");
    public static readonly int ShaderID_Emission = Shader.PropertyToID("_EmissionColor");
    public static readonly int ShaderID_Metallic = Shader.PropertyToID("_Metallic");
    public static readonly int ShaderID_Smoothness = Shader.PropertyToID("_Glossiness");
    public static readonly int ShaderID_NormalMap = Shader.PropertyToID("_BumpMap");
    public static readonly int ShaderID_Cutoff = Shader.PropertyToID("_Cutoff");
    public static readonly int ShaderID_Alpha = Shader.PropertyToID("_Alpha");

    // 扩展属性
    public static readonly int ShaderID_Tiling = Shader.PropertyToID("_MainTex_ST");
    public static readonly int ShaderID_DetailAlbedo = Shader.PropertyToID("_DetailAlbedoMap");
    public static readonly int ShaderID_DetailNormal = Shader.PropertyToID("_DetailNormalMap");
    public static readonly int ShaderID_SpecColor = Shader.PropertyToID("_SpecColor");

    // 常用自定义属性
    public static readonly int ShaderID_Rim = Shader.PropertyToID("_RimPower");
    public static readonly int ShaderID_RimColor = Shader.PropertyToID("_RimColor");
    public static readonly int ShaderID_Outline = Shader.PropertyToID("_OutlineWidth");
    public static readonly int ShaderID_OutlineColor = Shader.PropertyToID("_OutlineColor");
    public static readonly int ShaderID_FresnelPower = Shader.PropertyToID("_FresnelPower");
    public static readonly int ShaderID_Transparency = Shader.PropertyToID("_Transparency");
    public static readonly int ShaderID_Dissolve = Shader.PropertyToID("_DissolveAmount");
    public static readonly int ShaderID_DissolveColor = Shader.PropertyToID("_DissolveColor");
    public static readonly int ShaderID_Saturation = Shader.PropertyToID("_Saturation");
    public static readonly int ShaderID_Brightness = Shader.PropertyToID("_Brightness");
    public static readonly int ShaderID_Contrast = Shader.PropertyToID("_Contrast");
    #endregion

    /// <summary>
    /// 材质属性数据结构（扩展版）
    /// 用于存储和传递各种材质属性
    /// </summary>
    public struct MaterialPropertyData
    {
        // 基础属性
        public Color? color;
        public Color? emissionColor;
        public float? metallic;
        public float? smoothness;
        public float? cutoff;
        public float? alpha;
        public Texture mainTexture;
        public Texture normalMap;
        public Vector4? tilingOffset;

        // 扩展属性
        public float? rimPower;
        public Color? rimColor;
        public float? outlineWidth;
        public Color? outlineColor;
        public float? fresnelPower;
        public float? transparency;
        public float? dissolveAmount;
        public Color? dissolveColor;
        public float? saturation;
        public float? brightness;
        public float? contrast;

        // 自定义属性字典
        public Dictionary<string, object> customProperties;

        public MaterialPropertyData(Color? color = null)
        {
            this.color = color;
            this.emissionColor = null;
            this.metallic = null;
            this.smoothness = null;
            this.cutoff = null;
            this.alpha = null;
            this.mainTexture = null;
            this.normalMap = null;
            this.tilingOffset = null;
            this.rimPower = null;
            this.rimColor = null;
            this.outlineWidth = null;
            this.outlineColor = null;
            this.fresnelPower = null;
            this.transparency = null;
            this.dissolveAmount = null;
            this.dissolveColor = null;
            this.saturation = null;
            this.brightness = null;
            this.contrast = null;
            this.customProperties = null;
        }
    }

    #region 核心功能方法

    /// <summary>
    /// 安全创建PropertyBlock（不触发材质实例化）
    /// </summary>
    /// <param name="renderer">目标渲染器</param>
    /// <param name="propertyBlock">现有PropertyBlock（可为null）</param>
    /// <returns>PropertyBlock实例</returns>
    public static MaterialPropertyBlock SafeCreatePropertyBlock(Renderer renderer, MaterialPropertyBlock propertyBlock = null)
    {
        if (renderer == null)
        {
            Debug.LogError("[MaterialBatchOptimizer] 渲染器为空，无法创建PropertyBlock");
            return null;
        }

        if (propertyBlock == null)
        {
            propertyBlock = new MaterialPropertyBlock();
        }

        // 获取当前PropertyBlock状态（安全方式）
        renderer.GetPropertyBlock(propertyBlock);
        return propertyBlock;
    }

    /// <summary>
    /// 应用属性数据到PropertyBlock（完整版）
    /// </summary>
    /// <param name="propertyBlock">目标PropertyBlock</param>
    /// <param name="propertyData">属性数据</param>
    public static void ApplyPropertiesToBlock(MaterialPropertyBlock propertyBlock, MaterialPropertyData propertyData)
    {
        if (propertyBlock == null) return;

        // 基础属性
        if (propertyData.color.HasValue)
            propertyBlock.SetColor(ShaderID_Color, propertyData.color.Value);

        if (propertyData.emissionColor.HasValue)
            propertyBlock.SetColor(ShaderID_Emission, propertyData.emissionColor.Value);

        if (propertyData.metallic.HasValue)
            propertyBlock.SetFloat(ShaderID_Metallic, propertyData.metallic.Value);

        if (propertyData.smoothness.HasValue)
            propertyBlock.SetFloat(ShaderID_Smoothness, propertyData.smoothness.Value);

        if (propertyData.cutoff.HasValue)
            propertyBlock.SetFloat(ShaderID_Cutoff, propertyData.cutoff.Value);

        if (propertyData.alpha.HasValue)
            propertyBlock.SetFloat(ShaderID_Alpha, propertyData.alpha.Value);

        if (propertyData.mainTexture != null)
            propertyBlock.SetTexture(ShaderID_MainTex, propertyData.mainTexture);

        if (propertyData.normalMap != null)
            propertyBlock.SetTexture(ShaderID_NormalMap, propertyData.normalMap);

        if (propertyData.tilingOffset.HasValue)
            propertyBlock.SetVector(ShaderID_Tiling, propertyData.tilingOffset.Value);

        // 扩展属性
        if (propertyData.rimPower.HasValue)
            propertyBlock.SetFloat(ShaderID_Rim, propertyData.rimPower.Value);

        if (propertyData.rimColor.HasValue)
            propertyBlock.SetColor(ShaderID_RimColor, propertyData.rimColor.Value);

        if (propertyData.outlineWidth.HasValue)
            propertyBlock.SetFloat(ShaderID_Outline, propertyData.outlineWidth.Value);

        if (propertyData.outlineColor.HasValue)
            propertyBlock.SetColor(ShaderID_OutlineColor, propertyData.outlineColor.Value);

        if (propertyData.fresnelPower.HasValue)
            propertyBlock.SetFloat(ShaderID_FresnelPower, propertyData.fresnelPower.Value);

        if (propertyData.transparency.HasValue)
            propertyBlock.SetFloat(ShaderID_Transparency, propertyData.transparency.Value);

        if (propertyData.dissolveAmount.HasValue)
            propertyBlock.SetFloat(ShaderID_Dissolve, propertyData.dissolveAmount.Value);

        if (propertyData.dissolveColor.HasValue)
            propertyBlock.SetColor(ShaderID_DissolveColor, propertyData.dissolveColor.Value);

        if (propertyData.saturation.HasValue)
            propertyBlock.SetFloat(ShaderID_Saturation, propertyData.saturation.Value);

        if (propertyData.brightness.HasValue)
            propertyBlock.SetFloat(ShaderID_Brightness, propertyData.brightness.Value);

        if (propertyData.contrast.HasValue)
            propertyBlock.SetFloat(ShaderID_Contrast, propertyData.contrast.Value);

        // 自定义属性
        if (propertyData.customProperties != null)
        {
            ApplyCustomProperties(propertyBlock, propertyData.customProperties);
        }
    }

    #endregion

    #region 快捷设置方法

    /// <summary>
    /// 安全设置颜色
    /// </summary>
    public static void SetColor(Renderer renderer, MaterialPropertyBlock propertyBlock, Color color)
    {
        if (renderer == null || propertyBlock == null) return;
        propertyBlock.SetColor(ShaderID_Color, color);
        renderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 设置发光颜色
    /// </summary>
    public static void SetEmissionColor(Renderer renderer, MaterialPropertyBlock propertyBlock, Color emissionColor)
    {
        if (renderer == null || propertyBlock == null) return;
        propertyBlock.SetColor(ShaderID_Emission, emissionColor);
        renderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 设置金属度和光滑度
    /// </summary>
    public static void SetMetallicSmoothness(Renderer renderer, MaterialPropertyBlock propertyBlock, float metallic, float smoothness)
    {
        if (renderer == null || propertyBlock == null) return;
        propertyBlock.SetFloat(ShaderID_Metallic, metallic);
        propertyBlock.SetFloat(ShaderID_Smoothness, smoothness);
        renderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 设置透明度
    /// </summary>
    public static void SetAlpha(Renderer renderer, MaterialPropertyBlock propertyBlock, float alpha)
    {
        if (renderer == null || propertyBlock == null) return;
        propertyBlock.SetFloat(ShaderID_Alpha, alpha);
        renderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 设置边缘光效果
    /// </summary>
    public static void SetRimLight(Renderer renderer, MaterialPropertyBlock propertyBlock, Color rimColor, float rimPower)
    {
        if (renderer == null || propertyBlock == null) return;
        propertyBlock.SetColor(ShaderID_RimColor, rimColor);
        propertyBlock.SetFloat(ShaderID_Rim, rimPower);
        renderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 设置轮廓效果
    /// </summary>
    public static void SetOutline(Renderer renderer, MaterialPropertyBlock propertyBlock, Color outlineColor, float outlineWidth)
    {
        if (renderer == null || propertyBlock == null) return;
        propertyBlock.SetColor(ShaderID_OutlineColor, outlineColor);
        propertyBlock.SetFloat(ShaderID_Outline, outlineWidth);
        renderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 设置溶解效果
    /// </summary>
    public static void SetDissolve(Renderer renderer, MaterialPropertyBlock propertyBlock, float dissolveAmount, Color dissolveColor)
    {
        if (renderer == null || propertyBlock == null) return;
        propertyBlock.SetFloat(ShaderID_Dissolve, dissolveAmount);
        propertyBlock.SetColor(ShaderID_DissolveColor, dissolveColor);
        renderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 设置颜色调整（饱和度、亮度、对比度）
    /// </summary>
    public static void SetColorAdjustment(Renderer renderer, MaterialPropertyBlock propertyBlock, float saturation, float brightness, float contrast)
    {
        if (renderer == null || propertyBlock == null) return;
        propertyBlock.SetFloat(ShaderID_Saturation, saturation);
        propertyBlock.SetFloat(ShaderID_Brightness, brightness);
        propertyBlock.SetFloat(ShaderID_Contrast, contrast);
        renderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 设置主纹理
    /// </summary>
    public static void SetMainTexture(Renderer renderer, MaterialPropertyBlock propertyBlock, Texture texture)
    {
        if (renderer == null || propertyBlock == null) return;
        propertyBlock.SetTexture(ShaderID_MainTex, texture);
        renderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 设置UV平铺和偏移
    /// </summary>
    public static void SetTilingOffset(Renderer renderer, MaterialPropertyBlock propertyBlock, Vector2 tiling, Vector2 offset)
    {
        if (renderer == null || propertyBlock == null) return;
        Vector4 tilingOffset = new Vector4(tiling.x, tiling.y, offset.x, offset.y);
        propertyBlock.SetVector(ShaderID_Tiling, tilingOffset);
        renderer.SetPropertyBlock(propertyBlock);
    }

    /// <summary>
    /// 设置自定义属性（通用方法）
    /// </summary>
    public static void SetCustomProperty(Renderer renderer, MaterialPropertyBlock propertyBlock, string propertyName, object value)
    {
        if (renderer == null || propertyBlock == null || string.IsNullOrEmpty(propertyName)) return;

        int propertyID = Shader.PropertyToID(propertyName);
        SetCustomPropertyByID(propertyBlock, propertyID, value);
        renderer.SetPropertyBlock(propertyBlock);
    }

    #endregion

    #region 便捷方法（自动创建PropertyBlock）

    /// <summary>
    /// 快速设置颜色（自动创建PropertyBlock，效率较低，建议复用PropertyBlock）
    /// </summary>
    public static void QuickSetColor(Renderer renderer, Color color)
    {
        if (renderer == null) return;
        var block = SafeCreatePropertyBlock(renderer);
        SetColor(renderer, block, color);
    }

    /// <summary>
    /// 快速设置发光颜色（自动创建PropertyBlock，效率较低）
    /// </summary>
    public static void QuickSetEmissionColor(Renderer renderer, Color emissionColor)
    {
        if (renderer == null) return;
        var block = SafeCreatePropertyBlock(renderer);
        SetEmissionColor(renderer, block, emissionColor);
    }

    /// <summary>
    /// 快速设置材质属性（自动创建PropertyBlock，效率较低）
    /// </summary>
    public static void QuickSetMaterialProperties(Renderer renderer, MaterialPropertyData propertyData)
    {
        if (renderer == null) return;
        var block = SafeCreatePropertyBlock(renderer);
        ApplyPropertiesToBlock(block, propertyData);
        renderer.SetPropertyBlock(block);
    }

    #endregion

    #region 批量操作方法

    /// <summary>
    /// 批量设置颜色
    /// </summary>
    public static void BatchSetColors(Renderer[] renderers, Color[] colors)
    {
        if (renderers == null || colors == null) return;

        int count = Mathf.Min(renderers.Length, colors.Length);

        for (int i = 0; i < count; i++)
        {
            if (renderers[i] == null) continue;

            var block = SafeCreatePropertyBlock(renderers[i]);
            SetColor(renderers[i], block, colors[i]);
        }
    }

    /// <summary>
    /// 批量应用属性数据
    /// </summary>
    public static void BatchApplyProperties(Renderer[] renderers, MaterialPropertyData[] propertyDataArray)
    {
        if (renderers == null || propertyDataArray == null) return;

        int count = Mathf.Min(renderers.Length, propertyDataArray.Length);

        for (int i = 0; i < count; i++)
        {
            if (renderers[i] == null) continue;

            var block = SafeCreatePropertyBlock(renderers[i]);
            ApplyPropertiesToBlock(block, propertyDataArray[i]);
            renderers[i].SetPropertyBlock(block);
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 应用自定义属性字典
    /// </summary>
    private static void ApplyCustomProperties(MaterialPropertyBlock propertyBlock, Dictionary<string, object> customProperties)
    {
        foreach (var kvp in customProperties)
        {
            int propertyID = Shader.PropertyToID(kvp.Key);
            SetCustomPropertyByID(propertyBlock, propertyID, kvp.Value);
        }
    }

    /// <summary>
    /// 根据属性ID设置自定义属性
    /// </summary>
    private static void SetCustomPropertyByID(MaterialPropertyBlock propertyBlock, int propertyID, object value)
    {
        switch (value)
        {
            case float floatValue:
                propertyBlock.SetFloat(propertyID, floatValue);
                break;
            case int intValue:
                propertyBlock.SetInt(propertyID, intValue);
                break;
            case Color colorValue:
                propertyBlock.SetColor(propertyID, colorValue);
                break;
            case Vector4 vectorValue:
                propertyBlock.SetVector(propertyID, vectorValue);
                break;
            case Vector3 vector3Value:
                propertyBlock.SetVector(propertyID, vector3Value);
                break;
            case Vector2 vector2Value:
                propertyBlock.SetVector(propertyID, vector2Value);
                break;
            case Texture textureValue:
                propertyBlock.SetTexture(propertyID, textureValue);
                break;
            case Matrix4x4 matrixValue:
                propertyBlock.SetMatrix(propertyID, matrixValue);
                break;
            default:
                Debug.LogWarning($"[MaterialBatchOptimizer] 不支持的属性类型: {value.GetType()}");
                break;
        }
    }

    /// <summary>
    /// 安全检查渲染器是否有材质实例化问题（已废弃，避免触发实例化）
    /// </summary>
    [System.Obsolete("此方法已废弃，因为检测本身会导致材质实例化")]
    public static bool HasMaterialInstancing(Renderer renderer)
    {
        return false; // 始终返回false，避免检测导致的实例化
    }

    /// <summary>
    /// 获取全局状态报告
    /// </summary>
    public static string GetOptimizationReport()
    {
        return "材质批处理优化工具状态:\n" +
               "- 使用安全的PropertyBlock机制\n" +
               "- 已避免所有材质实例化风险\n" +
               "- 支持多种shader属性设置";
    }

    #endregion
}