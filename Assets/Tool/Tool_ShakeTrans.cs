using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_ShakeTrans 
{
    public static void Shake(Tool_ShakeTrans_Data data,float shakeTime)
    {
        data.shakeTimer = shakeTime;
    }

    public static void UpdateShake(Tool_ShakeTrans_Data data,Transform trans,float maxZ,float speed)
    {
        if (data.shakeTimer > 0)
        {
            data.shakeTimer -= Time.deltaTime;
            if (data.shakeTop)
            {

                trans.Rotate(new Vector3(0, 0, speed), Space.World);
                data.rotz += speed;
                data.SHAKE_OriginAngle += speed;

                if (data.rotz > maxZ)
                {
                    data.rotz = 0;
                    data.shakeTop = false;
                }

            }
            if (!data.shakeTop)
            {
                trans.Rotate(new Vector3(0, 0, -speed), Space.World);
                data.rotz += speed;
                data.SHAKE_OriginAngle += -speed;

                if (data.rotz > maxZ)
                {
                    data.rotz = 0;
                    data.shakeTop = true;
                }
            }


        }
        else
        {

            if (data.SHAKE_OriginAngle != 0)
            {
                if (data.SHAKE_OriginAngle > 0)
                {
                    trans.Rotate(new Vector3(0, 0, -speed), Space.World);
                    data.SHAKE_OriginAngle--;
                }
                else if (data.SHAKE_OriginAngle < 0)
                {
                    trans.Rotate(new Vector3(0, 0, speed), Space.World);
                    data.SHAKE_OriginAngle++;
                }
            }

        }
    }

    public  class Tool_ShakeTrans_Data
    {
       public float shakeTimer = 0;
        public bool shakeTop = false;
        public float SHAKE_OriginAngle = 0;
        public float rotz = 0;
    }
    
}
