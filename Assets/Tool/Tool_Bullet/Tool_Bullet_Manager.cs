using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_Bullet_Manager 
{
    //设置碰撞层 自身为 BulletOrGrenade 忽略物体为 BulletOrGrenade_PASSOBJ

    public static GameObject[] Resources_BulletGroup
    {
        get
        {
            if (resources_BulletGroup == null)
            {
                var gs = Resources.LoadAll<Tool_Bullet_Group>("BulletPrefab/");
                resources_BulletGroup = new GameObject[gs.Length];
                for (var i = 0; i < gs.Length; i++)
                    resources_BulletGroup[i] = gs[i].gameObject;
            }
            return resources_BulletGroup;
        }
    }
    static GameObject[] resources_BulletGroup;

    public static GameObject[] Resources_Bullet
    {
        get
        {
            if (resources_Bullet == null)
            {
                var gs = Resources.LoadAll<Tool_Bullet_Object>("BulletPrefab/");
                resources_Bullet = new GameObject[gs.Length];
                for (var i = 0; i < gs.Length; i++)
                    resources_Bullet[i] = gs[i].gameObject;
            }
            return resources_Bullet;
        }
    }
    static GameObject[] resources_Bullet;

    public static Action<GameObject[]> OnBulletCol_Event;

    public static void ShotBullet(int bulletGroupType,GameObject shooter,bool passSelf, Vector3 pos, Vector3 ang)
    {

        GameObject bg = GameObject.Instantiate(Resources_BulletGroup[bulletGroupType]);
        bg.transform.position = pos;
        bg.transform.eulerAngles = ang;

        Tool_Bullet_Group b = bg.GetComponent<Tool_Bullet_Group>();
 
        List<GameObject> passlist = new List<GameObject>();
        passlist.Add(shooter.gameObject);
        for (var i = 0; i < shooter.transform.childCount; i++)
        {
            passlist.Add(shooter.transform.GetChild(i).gameObject);
        }

        b.Init(shooter, passlist,pos,ang, bulletGroupType);

    }




}
