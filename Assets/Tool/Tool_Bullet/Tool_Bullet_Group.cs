using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_Bullet_Group : MonoBehaviour
{
    public Tool_Bullet_Motion_Type MotionType;

    [HideInInspector]
    public int BulletGroupType;
    [HideInInspector]
    public GameObject Shooter;

    public float Speed = 5;
    public float DestroyTime = 3;
    public int BulletIndex;
    public int Boom_Effect_Index;
    public float ColEffect_Time = 0.8f;
    public List<string> PASS_NAME = new List<string>() { "Tool_CheckTrigger" };

    List<GameObject> PassSelfObj = new List<GameObject>();

    float DestroyTimer = 0;

    List<Tool_Bullet_Object> bullets = new List<Tool_Bullet_Object>();

    [Header("Continuous")]
    public int Continuous_Count;
    public float Continuous_DelayTime;
    int Continuous_Current_Count;
    float Continuous_Current_DelayTime;
    Vector3 Continuous_RemberPos;
    Vector3 Continuous_RemberAng;

    [Header("Diffuse")]
    public int Diffuse_Count;
    public float Diffuse_HorAng;
    public float Diffuse_VecAng;



    public void Init(GameObject shooter, List<GameObject> passSelfObj, Vector3 pos, Vector3 ang, int type)
    {
        Shooter = shooter;
        PassSelfObj = passSelfObj;
        DestroyTimer = DestroyTime;


        switch (MotionType)
        {
            case Tool_Bullet_Motion_Type.Single:
                CreateBullet(pos, ang);
                break;
            case Tool_Bullet_Motion_Type.Continuous:
                Continuous_Current_Count = Continuous_Count;
                Continuous_Current_DelayTime = 0;
                Continuous_RemberPos = pos;
                Continuous_RemberAng = ang;
                break;
            case Tool_Bullet_Motion_Type.Diffuse:
                float rycount = 0;
                bool left = true;
                int up = 0;

                for (var i = 0; i < Diffuse_Count; i++)
                {
                    Vector3 eang = Tool_RotateEulerAngVec.RotateY(ang, rycount);
                    Vector3 sang = eang;

                    switch (up)
                    {
                        case 0:
                            sang = eang;

                            break;
                        case 1:
                            sang = Tool_RotateEulerAngVec.RotateX(eang, Diffuse_VecAng);
                            break;
                        case 2:
                            sang = Tool_RotateEulerAngVec.RotateX(eang, -Diffuse_VecAng);
                            break;
                    }
                    up++;
                    if (up > 2)
                        up = 0;

                    CreateBullet(pos, sang);


                    if (left)
                    {
                        rycount = Mathf.Abs(rycount);
                        rycount += Diffuse_HorAng;
                        left = false;
                    }
                    else
                    {
                        rycount = -rycount;
                        left = true;
                    }
                }
                break;

        }
    }


    void Update()
    {
        if (MotionType == Tool_Bullet_Motion_Type.Continuous)
        {
            if (Continuous_Current_Count > 0)
            {
                Continuous_Current_DelayTime -= Time.deltaTime;
                if (Continuous_Current_DelayTime <= 0)
                {
                    CreateBullet(Continuous_RemberPos, Continuous_RemberAng);
                    Continuous_Current_Count--;
                    Continuous_Current_DelayTime = Continuous_DelayTime;
                }
            }
        }

        if (DestroyTimer > 0)
        {

            DestroyTimer -= Time.deltaTime;
            if (DestroyTimer <= 0)
                GameObject.Destroy(this.gameObject);
        }

        for (var i = 0; i < bullets.Count; i++)
            if (!bullets[i].IsCol)
                bullets[i].transform.position += bullets[i].transform.forward * Speed * Time.deltaTime;

    }

    public bool OnCol(Tool_Bullet_Object bullet, Collider other)
    {
        for (var i = 0; i < PassSelfObj.Count; i++)
        {
            if (other.gameObject == PassSelfObj[i])
                return false;
        }
        Tool_Boom_Effect.CreateBoom(Boom_Effect_Index, new List<GameObject> { Shooter, Tool_Bullet_Manager.Resources_BulletGroup[BulletGroupType] }, bullet.transform.position, bullet.transform.eulerAngles);


        Tool_Bullet_Manager.OnBulletCol_Event?.Invoke(new GameObject[] { Shooter, Tool_Bullet_Manager.Resources_BulletGroup[BulletIndex], other.gameObject });
        
        bullet.gameObject.SetActive(false);

        bool isAllCol = true;
        for (var i = 0; i < bullets.Count; i++)
        {
            if (!bullets[i].IsCol)
            {
                isAllCol = false;
                break;
            }
        }

        if (MotionType == Tool_Bullet_Motion_Type.Continuous)
        {
            if (Continuous_Current_Count <= 0 && isAllCol)
                GameObject.Destroy(this.gameObject);
        }
        else
        {
            if (isAllCol)
                GameObject.Destroy(this.gameObject);
        }



        return true;
    }

    public GameObject CreateBullet(Vector3 pos, Vector3 ang)
    {

        GameObject bg = GameObject.Instantiate(Tool_Bullet_Manager.Resources_Bullet[BulletIndex]);
        bg.transform.position = pos;
        bg.transform.eulerAngles = ang;
        bg.transform.SetParent(transform);

        Tool_Bullet_Object b = bg.GetComponent<Tool_Bullet_Object>();
        b.group = this;

        bullets.Add(b);

        return bg;
    }


}

public enum Tool_Bullet_Motion_Type
{
    Single,
    Continuous,
    Diffuse
}
