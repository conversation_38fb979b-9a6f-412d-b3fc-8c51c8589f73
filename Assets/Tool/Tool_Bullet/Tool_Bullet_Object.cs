using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_Bullet_Object : MonoBehaviour
{
    //设置碰撞层 自身为 BulletOrGrenade 忽略物体为 BulletOrGrenade_PASSOBJ

    [HideInInspector]
    public Tool_Bullet_Group group;

    [HideInInspector]
    public bool IsCol = false;


    void OnTriggerEnter(Collider other)
    {
        if (group.PASS_NAME.IndexOf(other.name) != -1)
            return;
        if (IsCol)
            return;
        //if (other.GetComponent<Tool_Boom_Effect>() != null || other.GetComponent<Tool_Bullet_Object>() != null || other.GetComponent<Tool_Grenade_Object>() != null || other.GetComponent<Tool_CheckTrigger>() != null)
        //    return;

        if (group.OnCol(this, other))
            IsCol = true;




    }

}

