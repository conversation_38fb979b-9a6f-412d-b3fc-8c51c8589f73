using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 3D模型透明淡化扩散效果工具类
/// 可以在目标位置生成一个与原模型相同的透明淡化物体，向外扩散消失
/// </summary>
public static class Tool_DissolveEffect
{
    // 默认效果参数
    private static float defaultDuration = 0.4f;        // 默认持续时间
    private static float defaultExpandScale = 2f;       // 默认扩散比例
    private static Color defaultColor = new Color(1f, 1f, 1f, 0.1f); // 默认颜色，半透明白色
    private static float defaultAlpha = 0.5f;           // 默认透明度
    private static float defaultAcceleration = 2.0f;    // 默认加速度值，控制动画曲线
    
    // 缓存shader引用，避免重复加载
    private static Shader cachedDissolveShader;

    /// <summary>
    /// 获取溶解效果Shader，使用缓存避免重复加载
    /// </summary>
    /// <returns>溶解效果Shader</returns>
    public static Shader GetDissolveShader()
    {
        if (cachedDissolveShader == null)
        {
            // 从Resources加载shader
            cachedDissolveShader = Resources.Load<Shader>("Tool_DissolveEffect/DissolveEffect");
            
            if (cachedDissolveShader == null)
            {
                Debug.LogError("找不到溶解效果Shader，请确保已将DissolveEffect.shader放入Resources/Tool_DissolveEffect文件夹中");
            }
        }
        
        return cachedDissolveShader;
    }

    /// <summary>
    /// 根据加速度生成扩散动画曲线
    /// </summary>
    /// <param name="acceleration">加速度值</param>
    /// <returns>动画曲线</returns>
    public static AnimationCurve GetScaleCurve(float acceleration)
    {
        if (acceleration == 1.0f)
        {
            // 线性变化
            return new AnimationCurve(
                new Keyframe(0f, 0f, 1f, 1f),
                new Keyframe(1f, 1f, 1f, 1f)
            );
        }
        else if (acceleration > 1.0f)
        {
            // 加速效果
            return new AnimationCurve(
                new Keyframe(0f, 0f, 0f, acceleration),
                new Keyframe(1f, 1f, acceleration, 0f)
            );
        }
        else
        {
            // 减速效果
            return new AnimationCurve(
                new Keyframe(0f, 0f, 1/acceleration, 0f),
                new Keyframe(1f, 1f, 0f, 1/acceleration)
            );
        }
    }

    /// <summary>
    /// 获取物体的世界缩放
    /// </summary>
    /// <param name="transform">物体的Transform组件</param>
    /// <returns>世界缩放</returns>
    private static Vector3 GetWorldScale(Transform transform)
    {
        Vector3 worldScale = transform.lossyScale;
        return worldScale;
    }

    /// <summary>
    /// 创建3D模型扩散效果
    /// </summary>
    /// <param name="sourceObject">源游戏对象（必须有MeshRenderer和MeshFilter组件）</param>
    /// <param name="duration">效果持续时间</param>
    /// <param name="expandScale">扩散比例</param>
    /// <param name="alpha">透明度</param>
    /// <param name="acceleration">加速度</param>
    /// <param name="effectColor">效果颜色</param>
    /// <returns>生成的效果对象</returns>
    public static GameObject CreateDissolveEffect(
        GameObject sourceObject, 
        float duration = -1f, 
        float expandScale = -1f, 
        float alpha = -1f, 
        float acceleration = -1f, 
        Color? effectColor = null)
    {
        // 使用默认值替换未指定的参数
        duration = duration <= 0 ? defaultDuration : duration;
        expandScale = expandScale <= 0 ? defaultExpandScale : expandScale;
        alpha = alpha < 0 ? defaultAlpha : alpha;
        acceleration = acceleration <= 0 ? defaultAcceleration : acceleration;
        Color color = effectColor ?? defaultColor;

        MeshRenderer sourceMeshRenderer = sourceObject.GetComponent<MeshRenderer>();
        MeshFilter sourceMeshFilter = sourceObject.GetComponent<MeshFilter>();

        if (sourceMeshRenderer == null || sourceMeshFilter == null)
        {
            Debug.LogError("源对象必须包含MeshRenderer和MeshFilter组件");
            return null;
        }

        // 创建新的游戏对象，放在场景根级别以避免继承父物体的缩放
        GameObject effectObject = new GameObject(sourceObject.name + "_DissolveEffect");
        effectObject.transform.position = sourceObject.transform.position;
        effectObject.transform.rotation = sourceObject.transform.rotation;
        
        // 获取源物体的世界缩放
        Vector3 sourceWorldScale = GetWorldScale(sourceObject.transform);
        
        // 设置局部缩放为源物体的世界缩放，因为效果物体在根级别
        effectObject.transform.localScale = sourceWorldScale;

        // 添加必要的组件
        MeshFilter meshFilter = effectObject.AddComponent<MeshFilter>();
        MeshRenderer meshRenderer = effectObject.AddComponent<MeshRenderer>();

        // 复制网格
        meshFilter.mesh = sourceMeshFilter.mesh;

        // 获取缓存的shader
        Shader dissolveShader = GetDissolveShader();
        
        if (dissolveShader == null)
        {
            GameObject.Destroy(effectObject);
            return null;
        }

        // 创建材质实例
        Material dissolveMaterial = new Material(dissolveShader);
        if (dissolveMaterial == null)
        {
            Debug.LogError("无法创建溶解效果材质");
            GameObject.Destroy(effectObject);
            return null;
        }

        // 设置材质属性
        // 使用单独的Alpha参数设置颜色的透明度
        Color colorWithAlpha = new Color(color.r, color.g, color.b, alpha);
        dissolveMaterial.SetColor("_Color", colorWithAlpha);
        dissolveMaterial.SetFloat("_DissolveEdgeWidth", 0.1f); // 边缘宽度
        dissolveMaterial.SetColor("_DissolveEdgeColor", new Color(1f, 1f, 1f, alpha * 1.5f)); // 边缘颜色透明度也基于Alpha
        
        // 如果源对象有主纹理，复制主纹理
        if (sourceMeshRenderer.material.mainTexture != null)
        {
            dissolveMaterial.SetTexture("_MainTex", sourceMeshRenderer.material.mainTexture);
        }

        // 应用材质
        meshRenderer.material = dissolveMaterial;
        
        // 设置渲染队列，确保在原物体之上显示
        meshRenderer.material.renderQueue = 3100;

        // 添加控制器组件来处理动画效果
        DissolveEffectController controller = effectObject.AddComponent<DissolveEffectController>();
        
        // 生成动画曲线
        AnimationCurve scaleCurve = GetScaleCurve(acceleration);
        
        // 初始化控制器，传入源物体以便在Update中获取最新的世界缩放
        controller.Initialize(duration, expandScale, alpha, sourceObject.transform, scaleCurve, scaleCurve);

        return effectObject;
    }
}

/// <summary>
/// 3D模型扩散效果控制器
/// 控制单个扩散效果对象的生命周期和动画
/// </summary>
public class DissolveEffectController : MonoBehaviour
{
    private float duration;
    private float expandScale;
    private float startTime;
    private Vector3 originalScale;
    private Material material;
    private float startAlpha; // 初始透明度
    private AnimationCurve scaleCurve; // 缩放动画曲线
    private AnimationCurve alphaCurve; // 透明度动画曲线
    private Transform sourceTransform; // 源物体的Transform，用于获取最新的世界缩放

    /// <summary>
    /// 初始化控制器
    /// </summary>
    /// <param name="duration">效果持续时间</param>
    /// <param name="expandScale">最终扩散比例</param>
    /// <param name="alpha">初始透明度</param>
    /// <param name="sourceTransform">源物体的Transform</param>
    /// <param name="scaleCurve">缩放动画曲线</param>
    /// <param name="alphaCurve">透明度动画曲线</param>
    public void Initialize(float duration, float expandScale, float alpha = 0.5f, 
                          Transform sourceTransform = null,
                          AnimationCurve scaleCurve = null, AnimationCurve alphaCurve = null)
    {
        this.duration = duration;
        this.expandScale = expandScale;
        this.startTime = Time.time;
        this.originalScale = transform.localScale;
        this.material = GetComponent<MeshRenderer>().material;
        this.startAlpha = alpha; // 保存初始透明度
        this.sourceTransform = sourceTransform;
        
        // 如果没有提供曲线，使用默认曲线
        this.scaleCurve = scaleCurve ?? Tool_DissolveEffect.GetScaleCurve(1.0f);
        this.alphaCurve = alphaCurve ?? this.scaleCurve;
    }

    private void Update()
    {
        // 计算当前进度 (0-1)
        float progress = (Time.time - startTime) / duration;
        
        if (progress >= 1f)
        {
            // 效果结束，销毁对象
            Destroy(gameObject);
            return;
        }

        // 使用动画曲线计算缩放值
        float scaleProgress = scaleCurve.Evaluate(progress);
        
        // 如果有源物体，使用其最新的世界缩放作为基础
        if (sourceTransform != null && sourceTransform.gameObject.activeInHierarchy)
        {
            Vector3 currentSourceWorldScale = sourceTransform.lossyScale;
            transform.localScale = Vector3.Lerp(currentSourceWorldScale, currentSourceWorldScale * expandScale, scaleProgress);
        }
        else
        {
            // 源物体不存在或已禁用，使用原始缩放
            transform.localScale = Vector3.Lerp(originalScale, originalScale * expandScale, scaleProgress);
        }
        
        // 使用动画曲线计算透明度值
        float alphaProgress = alphaCurve.Evaluate(progress);
        Color currentColor = material.GetColor("_Color");
        // 注意：对于透明度，我们需要从初始值到0，所以用1-alphaProgress
        float alpha = Mathf.Lerp(startAlpha, 0f, alphaProgress);
        material.SetColor("_Color", new Color(currentColor.r, currentColor.g, currentColor.b, alpha));
        
        // 更新溶解值 - 也使用动画曲线
        material.SetFloat("_DissolveAmount", scaleProgress);
    }
}
