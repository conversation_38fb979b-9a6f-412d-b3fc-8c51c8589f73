Shader "Custom/DissolveEffect"
{
    Properties
    {
        _MainTex ("主纹理", 2D) = "white" {}
        _Color ("颜色", Color) = (1,1,1,0.5)
        _DissolveAmount ("溶解程度", Range(0, 1)) = 0
        _DissolveEdgeWidth ("边缘宽度", Range(0.01, 0.1)) = 0.03
        _DissolveEdgeColor ("边缘颜色", Color) = (1,1,1,0.7)
        _NoiseMap ("噪声纹理", 2D) = "white" {}
        _NoiseStrength ("噪声强度", Range(0, 1)) = 0.1
        _EmissionStrength ("发光强度", Range(0, 5)) = 1.0
        _SmoothRadius ("平滑半径", Range(0, 1)) = 0.7
    }
    
    SubShader
    {
        Tags { "Queue" = "Transparent+100" "RenderType" = "Transparent" "IgnoreProjector" = "True" }
        Blend SrcAlpha OneMinusSrcAlpha
        Cull Off
        ZWrite Off
        
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"
            
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normal : NORMAL;
            };
            
            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float3 worldPos : TEXCOORD1;
                float3 normal : NORMAL;
            };
            
            sampler2D _MainTex;
            float4 _MainTex_ST;
            float4 _Color;
            float _DissolveAmount;
            float _DissolveEdgeWidth;
            float4 _DissolveEdgeColor;
            sampler2D _NoiseMap;
            float _NoiseStrength;
            float _EmissionStrength;
            float _SmoothRadius;
            
            // 平滑噪声函数，避免三角波纹
            float smoothNoise(float2 uv) {
                // 使用距离场而不是UV乘积
                float dist = length(uv - 0.5) * 2.0; // 距离中心的距离
                dist = 1.0 - smoothstep(0.0, _SmoothRadius, dist); // 平滑过渡
                return dist * _NoiseStrength;
            }
            
            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                o.normal = UnityObjectToWorldNormal(v.normal);
                return o;
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                // 使用半透明白色
                fixed4 col = _Color;
                
                // 使用平滑噪声
                float noise = smoothNoise(i.uv);
                
                // 计算溶解值
                float dissolveValue = noise + (1 - _DissolveAmount);
                
                // 计算边缘效果，使用平滑过渡
                float edge = 1 - smoothstep(0, _DissolveEdgeWidth, dissolveValue);
                
                // 如果溶解值小于0，完全透明
                if (dissolveValue < 0)
                {
                    discard;
                }
                
                // 应用边缘效果，使用柔和的发光效果
                col.rgb = lerp(col.rgb, _DissolveEdgeColor.rgb * _EmissionStrength, edge * _DissolveEdgeColor.a);
                
                // 应用溶解效果到透明度，使用平滑过渡
                col.a *= smoothstep(0, 0.5, dissolveValue) * _Color.a;
                
                return col;
            }
            ENDCG
        }
    }
    
    Fallback "Transparent/VertexLit"
}
