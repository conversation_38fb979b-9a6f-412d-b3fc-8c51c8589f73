Shader "Custom/DissolveEffect2D"
{
    Properties
    {
        _MainTex ("主纹理", 2D) = "white" {}
        _Color ("颜色", Color) = (1,1,1,0.5)
        _DissolveAmount ("溶解程度", Range(0, 1)) = 0
        _DissolveEdgeWidth ("边缘宽度", Range(0.01, 0.1)) = 0.03
        _DissolveEdgeColor ("边缘颜色", Color) = (1,1,1,0.7)
        _NoiseStrength ("噪声强度", Range(0, 1)) = 0.1
        _EmissionStrength ("发光强度", Range(0, 5)) = 1.0
        _SmoothRadius ("平滑半径", Range(0, 1)) = 0.7
        _UseOriginalTexture ("使用原始纹理", Float) = 0 // 0表示使用纯色，1表示使用原始纹理
        _OutlineStrength ("轮廓强度", Range(0, 1)) = 0.3 // 控制原始纹理轮廓的保留程度
    }
    
    SubShader
    {
        Tags { 
            "Queue" = "Transparent" 
            "RenderType" = "Transparent" 
            "IgnoreProjector" = "True" 
            "PreviewType" = "Plane"
            "CanUseSpriteAtlas" = "True"
        }
        
        Blend SrcAlpha OneMinusSrcAlpha
        Cull Off
        ZWrite Off
        
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"
            
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float4 color : COLOR;
            };
            
            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float4 color : COLOR;
            };
            
            sampler2D _MainTex;
            float4 _MainTex_ST;
            float4 _Color;
            float _DissolveAmount;
            float _DissolveEdgeWidth;
            float4 _DissolveEdgeColor;
            float _NoiseStrength;
            float _EmissionStrength;
            float _SmoothRadius;
            float _UseOriginalTexture;
            float _OutlineStrength;
            
            // 为2D精灵生成噪声
            float noise2D(float2 uv) {
                // 使用UV坐标生成简单的噪声
                float noise = frac(sin(dot(uv, float2(12.9898, 78.233))) * 43758.5453);
                
                // 平滑处理
                float dist = length(uv - 0.5) * 2.0; // 距离中心的距离
                dist = 1.0 - smoothstep(0.0, _SmoothRadius, dist); // 平滑过渡
                
                return lerp(noise, dist, 0.7) * _NoiseStrength;
            }
            
            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.color = v.color;
                return o;
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                // 采样精灵纹理
                fixed4 texColor = tex2D(_MainTex, i.uv);
                
                // 计算亮度，用于提取轮廓
                float luminance = dot(texColor.rgb, float3(0.299, 0.587, 0.114));
                
                // 计算轮廓效果
                float outline = smoothstep(0.1, 0.3, luminance) * (1.0 - smoothstep(0.6, 0.9, luminance));
                
                // 根据_UseOriginalTexture决定是否使用原始纹理颜色
                fixed4 col;
                if (_UseOriginalTexture > 0.5) {
                    // 使用原始纹理颜色
                    col = texColor * _Color * i.color;
                } else {
                    // 使用纯色，但保留纹理的轮廓和透明通道
                    col = _Color;
                    // 混合原始纹理的轮廓
                    col.rgb = lerp(col.rgb, texColor.rgb, outline * _OutlineStrength);
                    col.a *= texColor.a * i.color.a;
                }
                
                // 生成噪声
                float noise = noise2D(i.uv);
                
                // 计算溶解值
                float dissolveValue = noise + (1 - _DissolveAmount);
                
                // 计算边缘效果，使用平滑过渡
                float edge = 1 - smoothstep(0, _DissolveEdgeWidth, dissolveValue);
                
                // 如果溶解值小于0，完全透明
                if (dissolveValue < 0)
                {
                    discard;
                }
                
                // 应用边缘效果，使用柔和的发光效果
                col.rgb = lerp(col.rgb, _DissolveEdgeColor.rgb * _EmissionStrength, edge * _DissolveEdgeColor.a);
                
                // 应用溶解效果到透明度，使用平滑过渡
                col.a *= smoothstep(0, 0.5, dissolveValue) * _Color.a;
                
                return col;
            }
            ENDCG
        }
    }
    
    Fallback "Sprites/Default"
}
