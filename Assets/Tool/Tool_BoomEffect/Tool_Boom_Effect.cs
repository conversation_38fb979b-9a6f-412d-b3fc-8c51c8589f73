using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;


public class Tool_Boom_Effect : MonoBehaviour
{
    public static GameObject[] Resources_BoomEffect
    {
        get
        {
            if (resources_BoomEffect == null)
            {
                var gs = Resources.LoadAll<Tool_Boom_Effect>("BoomPrefab/");
                resources_BoomEffect = new GameObject[gs.Length];
                for (var i = 0; i < gs.Length; i++)
                    resources_BoomEffect[i] = gs[i].gameObject;
            }
            return resources_BoomEffect;
        }
    }
    static GameObject[] resources_BoomEffect;



    public List<GameObject> ArgObjs;

    public float TriTime;
     float TriTimer;
    public float DesTime;
     float DesTimer;
     List<Collider> triSameObj = new List<Collider>();

   public bool isTriOver = false;
  //  public int Index;

    public static Action<List<GameObject>,GameObject> OnCol_Event;

   

    public void Init(List<GameObject> argObjs)
    {
        ArgObjs = argObjs;
    }

    private void Update()
    {

        TriTimer += Time.deltaTime;
        if (TriTimer > TriTime)
            isTriOver = true;


        DesTimer += Time.deltaTime;
        if (DesTimer > DesTime)
            GameObject.Destroy(this.gameObject);
    }

    private void OnTriggerEnter(Collider other)
    {
        if (isTriOver)
            return;

        if (triSameObj.Contains(other))
            return;
        triSameObj.Add(other);

        OnCol_Event?.Invoke(ArgObjs,other.gameObject);

    }

    public static Tool_Boom_Effect CreateBoom(int boomIndex, List<GameObject> argobjs, Vector3 pos, Vector3 ang)
    {

        GameObject bg = GameObject.Instantiate(Resources_BoomEffect[boomIndex]);
        bg.transform.position = pos;
        bg.transform.eulerAngles = ang;


        Tool_Boom_Effect b = bg.GetComponent<Tool_Boom_Effect>();

        //List<GameObject> passlist = new List<GameObject>();
        //passlist.Add(shooter.gameObject);
        //for (var i = 0; i < shooter.transform.childCount; i++)
        //{
        //    passlist.Add(shooter.transform.GetChild(i).gameObject);
        //}
        argobjs.Add(bg);

        b.Init(argobjs);

        return b;


    }

}
