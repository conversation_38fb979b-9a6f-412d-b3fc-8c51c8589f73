using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_ColCheck 
{
    public static bool DebugDrawLine = false;
 

    public static bool CheckIsCol(Transform self, Transform target, float distance, float angle, bool passY = false)
    {
        if (Vector3.Distance(self.position, target.GetComponent<Collider>().bounds.center) > distance * 4)
            return false;

        var p = COL_GetObjBoundsPoints(target.gameObject);

        for (var i = 0; i < p.Count; i++)
        {

            if (CheckColPos(self, p[i], distance, angle, passY))
                return true;
        }

        return false;
    }

    static bool CheckColPos(Transform self, Vector3 targetpos,float distance, float angle, bool passY = false)
    {
        Vector3 selfpos = self.position;
        Vector3 tarpos = Vector3.zero;
        if (passY)
            tarpos = new Vector3(targetpos.x, selfpos.y, targetpos.z);
        else
            tarpos = targetpos;
        Vector3 secpos = self.position + self.forward * distance;
        float targetDis = Vector3.Distance(selfpos, tarpos);



        Vector3 forwardDir = secpos - selfpos;

        Vector3 targetDir = new Vector3(tarpos.x, selfpos.y,tarpos.z) - selfpos;

        float targetAng = Vector3.Angle(forwardDir.normalized, targetDir.normalized);

        if (DebugDrawLine)
        {
            //COLTEST
            CreateColTESTBox(self, secpos, angle);
            CreateColTESTBox(self, secpos, 0);
            CreateColTESTBox(self, secpos, -angle);
        }

      //  Debug.LogError(targetDis + "  " + targetAng);

        if (targetDis < distance && targetAng < angle)
        {
            return true;
        }
                

        return false;
    }

    static void CreateColTESTBox(Transform self, Vector3 tarpos, float angle)
    {
  
        var pos1 = new Vector3(self.position.x, self.GetComponent<Collider>().bounds.center.y, self.position.z);


        Quaternion rotation = Quaternion.AngleAxis(angle, self.up);
        Vector3 v = rotation * (tarpos - pos1) + pos1;



        Tool_TestLineRen.DrawLine(pos1, v);


    }

    static List<Vector3> COL_GetObjBoundsPoints(GameObject target)
    {

        List<Vector3> poses = new List<Vector3>();

        var bounds = target.GetComponent<Collider>().bounds;

        var center = bounds.center;
        var ext = bounds.extents;

        float deltaX = Mathf.Abs(ext.x) * 0.9f;
        float deltaY = Mathf.Abs(ext.y) * 0.9f;
        float deltaZ = Mathf.Abs(ext.z) * 0.9f;

        poses.Add(center);
        poses.Add(center + new Vector3(-deltaX, deltaY, -deltaZ));        // 上前左（相对于中心点）
        poses.Add(center + new Vector3(deltaX, deltaY, -deltaZ));         // 上前右
        poses.Add(center + new Vector3(deltaX, deltaY, deltaZ));          // 上后右
        poses.Add(center + new Vector3(-deltaX, deltaY, deltaZ));         // 上后左

        poses.Add(center + new Vector3(-deltaX, -deltaY, -deltaZ));       // 下前左
        poses.Add(center + new Vector3(deltaX, -deltaY, -deltaZ));        // 下前右 
        poses.Add(center + new Vector3(deltaX, -deltaY, deltaZ));         // 下后右
        poses.Add(center + new Vector3(-deltaX, -deltaY, deltaZ));        // 下后左

        poses.Add(center + new Vector3(-deltaX, 0, -deltaZ));
        poses.Add(center + new Vector3(deltaX, 0, -deltaZ));
        poses.Add(center + new Vector3(deltaX, 0, deltaZ));
        poses.Add(center + new Vector3(-deltaX, 0, deltaZ));

        poses.Add(center + new Vector3(0, deltaY, deltaZ));
        poses.Add(center + new Vector3(0, -deltaY, -deltaZ));
        poses.Add(center + new Vector3(0, deltaY, -deltaZ));
        poses.Add(center + new Vector3(0, -deltaY, deltaZ));


        poses.Add(center + new Vector3(deltaX, deltaY, 0));
        poses.Add(center + new Vector3(-deltaX, -deltaY, 0));
        poses.Add(center + new Vector3(-deltaX, deltaY, 0));
        poses.Add(center + new Vector3(deltaX, -deltaY, 0));

        poses.Add(center + new Vector3(deltaX, 0, 0));
        poses.Add(center + new Vector3(-deltaX, 0, 0));

        poses.Add(center + new Vector3(0, deltaY, 0));
        poses.Add(center + new Vector3(0, -deltaY, 0));

        poses.Add(center + new Vector3(0, 0, -deltaZ));
        poses.Add(center + new Vector3(0, 0, deltaZ));


        return poses;
    }

}
