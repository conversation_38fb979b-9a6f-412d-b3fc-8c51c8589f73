using UnityEngine;
using System.Collections.Generic;

public class Tool_ElasticScaler : MonoBehaviour
{
    // ����ʵ��
    private static Tool_ElasticScaler instance;
    public static Tool_ElasticScaler Instance
    {
        get
        {
            if (instance == null)
            {
                GameObject go = new GameObject("Tool_ElasticScaler");
                instance = go.AddComponent<Tool_ElasticScaler>();
                DontDestroyOnLoad(go);
            }
            return instance;
        }
    }

    // �洢��Ҫ���ŵĶ�����Ϣ
    private Dictionary<Transform, ScaleInfo> scalingObjects = new Dictionary<Transform, ScaleInfo>();

    // �洢������Ϣ�Ľṹ��
    private struct ScaleInfo
    {
        public Vector3 targetScale;
        public float scaleSpeed;
        public float elasticity;
        public bool[] overshoot;
        public bool[] isScalingComplete; // ���ÿ�������������Ƿ����
    }

    private void Update()
    {

        // ���ڴ洢��Ҫ�Ƴ��Ķ���
        List<Transform> toRemove = new List<Transform>();
        // ���ڴ洢��Ҫ���µĶ�����Ϣ
        Dictionary<Transform, ScaleInfo> toUpdate = new Dictionary<Transform, ScaleInfo>();

        foreach (var kvp in scalingObjects)
        {
            Transform target = kvp.Key;
            ScaleInfo info = kvp.Value;

            // �������Ƿ�Ϊ null
            if (target == null)
            {
                toRemove.Add(target);
                continue;
            }

            Vector3 currentScale = target.localScale;
            float scaleDelta = info.scaleSpeed * Time.deltaTime;

            bool allComplete = true;

            for (int i = 0; i < 3; i++)
            {
                if (!info.isScalingComplete[i])
                {
                    allComplete = false;

                    if (info.elasticity == 0)
                    {
                        // û�е��ԣ�ֱ�����ŵ�Ŀ��ֵ
                        currentScale[i] = Mathf.MoveTowards(currentScale[i], info.targetScale[i], scaleDelta);
                        if (Mathf.Abs(currentScale[i] - info.targetScale[i]) < 0.001f)
                        {
                            info.isScalingComplete[i] = true;
                        }
                    }
                    else
                    {
                        if (!info.overshoot[i])
                        {
                            // δ����Ŀ��ֵ����������
                            if (info.elasticity > 0)
                            {
                                currentScale[i] += scaleDelta;
                                if (currentScale[i] >= info.targetScale[i] + info.elasticity)
                                {
                                    info.overshoot[i] = true;
                                }
                            }
                            else
                            {
                                currentScale[i] -= scaleDelta;
                                if (currentScale[i] <= info.targetScale[i] + info.elasticity)
                                {
                                    info.overshoot[i] = true;
                                }
                            }
                        }
                        else
                        {
                            // ����Ŀ��ֵ���ص�
                            if (info.elasticity > 0)
                            {
                                currentScale[i] -= scaleDelta;
                                if (Mathf.Abs(currentScale[i] - info.targetScale[i]) < 0.001f || currentScale[i] <= info.targetScale[i])
                                {
                                    currentScale[i] = info.targetScale[i];
                                    info.isScalingComplete[i] = true;
                                }
                            }
                            else
                            {
                                currentScale[i] += scaleDelta;
                                if (Mathf.Abs(currentScale[i] - info.targetScale[i]) < 0.001f || currentScale[i] >= info.targetScale[i])
                                {
                                    currentScale[i] = info.targetScale[i];
                                    info.isScalingComplete[i] = true;
                                }
                            }
                        }
                    }
                }
            }

            target.localScale = currentScale;

            if (allComplete)
            {
                toRemove.Add(target);
            }
            else
            {
                // ��¼��Ҫ���µ���Ϣ
                toUpdate[target] = info;
            }
        }

        // �Ƴ�������ŵĶ���
        foreach (Transform target in toRemove)
        {
            if (scalingObjects.ContainsKey(target))
            {
                scalingObjects.Remove(target);
            }
        }

        // �����ֵ��е���Ϣ
        foreach (var kvp in toUpdate)
        {
            scalingObjects[kvp.Key] = kvp.Value;
        }
    }

    // ��ʼ���Ŷ���ķ���
    public void Scale(Transform target, float targetScaleFactor, float scaleSpeed, float elasticity)
    {
        Vector3 targetScale = target.localScale * targetScaleFactor;

        ScaleInfo info = new ScaleInfo
        {
            targetScale = targetScale,
            scaleSpeed = scaleSpeed,
            elasticity = elasticity,
            overshoot = new bool[3] { false, false, false },
            isScalingComplete = new bool[3] { false, false, false }
        };

        scalingObjects[target] = info;
    }
}