using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

#if UNITY_EDITOR
/// <summary>
/// 独立控制台应用程序，用于从C#代码文件中提取中文文本
/// </summary>
public class ChineseTextExtractorConsole
{
    // 主程序入口点
    public static void Main(string[] args)
    {
        Console.OutputEncoding = Encoding.UTF8;
        Console.WriteLine("中文文本提取工具 - 控制台版");
        Console.WriteLine("============================");

        string rootDirectory = "";
        string outputFilePath = "";

        // 处理命令行参数
        if (args.Length >= 2)
        {
            rootDirectory = args[0];
            outputFilePath = args[1];
        }
        else
        {
            // 交互式获取参数
            Console.Write("请输入要扫描的目录路径: ");
            rootDirectory = Console.ReadLine().Trim();

            Console.Write("请输入输出文件路径: ");
            outputFilePath = Console.ReadLine().Trim();
        }

        // 验证参数
        if (string.IsNullOrEmpty(rootDirectory) || !Directory.Exists(rootDirectory))
        {
            Console.WriteLine("错误: 指定的目录不存在!");
            return;
        }

        if (string.IsNullOrEmpty(outputFilePath))
        {
            outputFilePath = Path.Combine(rootDirectory, "ChineseTexts.txt");
        }

        Console.WriteLine($"开始从 {rootDirectory} 提取中文文本...");

        try
        {
            // 获取所有C#文件
            string[] files = Directory.GetFiles(rootDirectory, "*.cs", SearchOption.AllDirectories);
            Console.WriteLine($"找到 {files.Length} 个C#文件");

            HashSet<string> uniqueTexts = new HashSet<string>();
            int processedFiles = 0;

            foreach (string file in files)
            {
                string content = File.ReadAllText(file, Encoding.UTF8);
                List<string> chineseTexts = ExtractChineseFromFile(content);

                foreach (string text in chineseTexts)
                {
                    if (!string.IsNullOrWhiteSpace(text) && !uniqueTexts.Contains(text))
                    {
                        uniqueTexts.Add(text);
                    }
                }

                processedFiles++;
                if (processedFiles % 100 == 0 || processedFiles == files.Length)
                {
                    Console.WriteLine($"已处理 {processedFiles}/{files.Length} 个文件...");
                }
            }

            // 将结果写入文件
            List<string> sortedTexts = uniqueTexts.OrderBy(t => t).ToList();
            File.WriteAllLines(outputFilePath, sortedTexts, Encoding.UTF8);

            Console.WriteLine($"提取完成! 共找到 {sortedTexts.Count} 条中文文本，已保存到: {outputFilePath}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"提取中文文本时出错: {ex.Message}");
        }

        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }

    private static List<string> ExtractChineseFromFile(string content)
    {
        List<string> results = new List<string>();

        // 中文字符的Unicode范围
        string chinesePattern = @"[\u4e00-\u9fa5]+";

        // 提取注释中的中文
        // 单行注释
        string singleLineCommentPattern = @"//.*?(?=\r|\n|$)";
        MatchCollection singleLineComments = Regex.Matches(content, singleLineCommentPattern);
        foreach (Match comment in singleLineComments)
        {
            ExtractChineseFromText(comment.Value, results);
        }

        // 多行注释
        string multiLineCommentPattern = @"/\*[\s\S]*?\*/";
        MatchCollection multiLineComments = Regex.Matches(content, multiLineCommentPattern);
        foreach (Match comment in multiLineComments)
        {
            ExtractChineseFromText(comment.Value, results);
        }

        // 提取字符串中的中文
        string stringPattern = @"""(?:[^""\\]|\\.)*""";
        MatchCollection strings = Regex.Matches(content, stringPattern);
        foreach (Match str in strings)
        {
            string value = str.Value.Trim('"');
            ExtractChineseFromText(value, results);
        }

        return results;
    }

    private static void ExtractChineseFromText(string text, List<string> results)
    {
        string chinesePattern = @"[\u4e00-\u9fa5]+";
        MatchCollection matches = Regex.Matches(text, chinesePattern);

        foreach (Match match in matches)
        {
            // 获取包含中文的完整短语
            string chineseText = GetCompletePhraseContainingChinese(text, match.Index, match.Length);
            if (!string.IsNullOrWhiteSpace(chineseText))
            {
                results.Add(chineseText);
            }
        }
    }

    private static string GetCompletePhraseContainingChinese(string text, int startIndex, int length)
    {
        // 向前查找短语的开始（空格、标点或行首）
        int phraseStart = startIndex;
        while (phraseStart > 0)
        {
            char c = text[phraseStart - 1];
            if (char.IsWhiteSpace(c) || char.IsPunctuation(c))
            {
                break;
            }
            phraseStart--;
        }

        // 向后查找短语的结束（空格、标点或行尾）
        int phraseEnd = startIndex + length;
        while (phraseEnd < text.Length)
        {
            char c = text[phraseEnd];
            if (char.IsWhiteSpace(c) || char.IsPunctuation(c))
            {
                break;
            }
            phraseEnd++;
        }

        // 提取完整短语
        return text.Substring(phraseStart, phraseEnd - phraseStart).Trim();
    }
}
#endif