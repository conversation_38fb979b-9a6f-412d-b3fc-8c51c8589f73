using UnityEngine;
using System.Reflection;

public static class Tool_MeshOutline
{
    public static void ShowOutline(GameObject parentObj, string colorPropertyName, Color color, string widthPropertyName, float width)
    {
        Renderer[] allRenderers = parentObj.GetComponentsInChildren<Renderer>(true);
        foreach (Renderer renderer in allRenderers)
        {
            Material[] materials = renderer.materials;
            foreach (Material material in materials)
            {
                // ��ȡ������Ϣ
                PropertyInfo outlineColorProperty = material.GetType().GetProperty(colorPropertyName);
                PropertyInfo outlineWidthProperty = material.GetType().GetProperty(widthPropertyName);

                if (outlineColorProperty == null)
                {
                    Debug.LogError($"The material on {renderer.gameObject.name} does not have a property named {colorPropertyName}.");
                    continue;
                }

                if (outlineWidthProperty == null)
                {
                    Debug.LogError($"The material on {renderer.gameObject.name} does not have a property named {widthPropertyName}.");
                    continue;
                }

                // ������ɫ�Ϳ��
                outlineColorProperty.SetValue(material, color, null);
                outlineWidthProperty.SetValue(material, width, null);
            }
        }
    }

    public static void HideOutline(GameObject parentObj, string colorPropertyName, string widthPropertyName)
    {
        Renderer[] allRenderers = parentObj.GetComponentsInChildren<Renderer>(true);
        foreach (Renderer renderer in allRenderers)
        {
            Material[] materials = renderer.materials;
            foreach (Material material in materials)
            {
                // ��ȡ������Ϣ
                PropertyInfo outlineColorProperty = material.GetType().GetProperty(colorPropertyName);
                PropertyInfo outlineWidthProperty = material.GetType().GetProperty(widthPropertyName);

                if (outlineColorProperty != null)
                {
                    outlineColorProperty.SetValue(material, Color.clear, null);
                }

                if (outlineWidthProperty != null)
                {
                    outlineWidthProperty.SetValue(material, 0f, null);
                }
            }
        }
    }
}