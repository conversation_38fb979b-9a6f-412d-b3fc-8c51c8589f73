using System.Collections.Generic;
using UnityEngine;

public static class Tool_LanguageManager
{
    private static Dictionary<string, string> languageDict = new Dictionary<string, string>();
    private static bool isInitialized = false;

    private static void LoadLanguage()
    {
        if (isInitialized) return;
        
        languageDict.Clear();
        string filePath = "Tool_LanguageManager/LocLanguage/" + MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.CurrentLanguage;

        TextAsset textAsset = Resources.Load<TextAsset>(filePath);
        if (textAsset != null)
        {
            string[] lines = textAsset.text.Split('\n');
            foreach (string line in lines)
            {
                if (string.IsNullOrEmpty(line)) continue;

                string[] parts = line.Split('#');
                if (parts.Length == 2)
                {
                    string key = parts[0].Trim();
                    string value = parts[1].Trim();
                    languageDict[key] = value;
                }
            }
            isInitialized = true;
            Debug.Log($"Language {MXR_BRIGE.Cos_GameSetting.MatchUI_Const_Data.CurrentLanguage} loaded successfully");
        }
        else
        {
            Debug.LogError($"Language file not found: {filePath}");
        }
    }

    public static string GetText(string key)
    {
        if (!isInitialized)
        {
            LoadLanguage();
        }

        if (languageDict.TryGetValue(key, out string value))
        {
            return value;
        }
        
        Debug.LogWarning($"Translation not found for key: {key}");
        return key;
    }
}