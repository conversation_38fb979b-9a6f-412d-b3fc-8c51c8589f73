using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_Effect_Controller : MonoBehaviour
{

    private static Tool_Effect_Controller instance = null;
    public static Tool_Effect_Controller Instance
    {
        get
        {
            if (instance == null)
            {
                GameObject eff = new GameObject();
                eff.name = "Tool_Effect_Controller";
                DontDestroyOnLoad(eff);
                instance = eff.AddComponent<Tool_Effect_Controller>();
            }
               
            return instance;
        }
    }


    private List<ShowHideEffectData> HidingEffectDatas = new List<ShowHideEffectData>();

    private static Dictionary<string, GameObject> _effectCache = new Dictionary<string, GameObject>(); // 资源缓存字典
    
    private void Update()
    {
        for (var i = 0; i < HidingEffectDatas.Count; i++)
        {
            var data = HidingEffectDatas[i];

            if(data.effectObj == null)
            {
                DeleteHideEffect(data);
                continue;
            }
           

            if (data.hideTimer > 0)
            {
                data.hideTimer -= Time.deltaTime;

                if(!data.follow)
                {
                    data.effectObj.transform.position = data.showingPos;
                    data.effectObj.transform.eulerAngles = data.showingAng;
                }
 
                if (data.hideTimer < 0)
                {
                    data.effectObj.SetActive(false);
                    data.effectObj.transform.localPosition = data.originPos;
                    data.effectObj.transform.localEulerAngles = data.originAng;
                }
            }
        }
    }


    public void ShowHideEffect(Tool_Effect_SelfEffectData edata, float hideTimer,bool follow = false)
    {

        for(var i = 0; i < HidingEffectDatas.Count;i++)
        {
            if(HidingEffectDatas[i].effectObj == edata.effectObj)
            {
                HidingEffectDatas[i].effectObj.gameObject.SetActive(false);
                HidingEffectDatas[i].effectObj.gameObject.SetActive(true);
                HidingEffectDatas[i].hideTimer = hideTimer;
                HidingEffectDatas[i].showingPos = edata.effectObj.transform.position;
                HidingEffectDatas[i].showingAng = edata.effectObj.transform.eulerAngles;
            }
        }

        ShowHideEffectData data = new ShowHideEffectData();
        data.effectObj = edata.effectObj;
        data.hideTimer = hideTimer;
        data.originAng = edata.originAng;
        data.originPos = edata.originPos;
        data.showingPos = edata.effectObj.transform.position;
        data.showingAng = edata.effectObj.transform.eulerAngles;
        data.follow = follow;
        data.effectObj.SetActive(true);

        HidingEffectDatas.Add(data);
    }

    private void DeleteHideEffect(ShowHideEffectData data)
    {
        HidingEffectDatas.Remove(data);
    }


    public static Tool_Effect_SelfEffectData CreateSelfEffectData(Transform parent,string effectName)
    {
        var obj = parent.Find(effectName).gameObject;
        Tool_Effect_SelfEffectData sdata = new Tool_Effect_SelfEffectData(obj, obj.transform.localPosition, obj.transform.localEulerAngles);
        return sdata;
    }

    public static Tool_Effect_SelfEffectData CreateSelfEffectData(Transform parent, GameObject effect)
    {
        var obj = effect;
        Tool_Effect_SelfEffectData sdata = new Tool_Effect_SelfEffectData(obj, obj.transform.localPosition, obj.transform.localEulerAngles);
        effect.gameObject.SetActive(false);
        return sdata;
    }

    public static void CreateEffect(GameObject prefab,Vector3 pos ,Vector3 eulerAng, float timer)
    {
        GameObject effect = GameObject.Instantiate(prefab);
        effect.transform.position = pos;
        effect.transform.eulerAngles = eulerAng;
        effect.AddComponent<Tool_DestroySelfByTime>().Set(timer);
    }

    /// <summary>
    /// 通过资源路径创建特效（带缓存功能）
    /// </summary>
    public static void CreateEffect(string resourcePath, Vector3 pos, Vector3 eulerAng, float timer)
    {
        // 检查缓存中是否存在
        if (!_effectCache.TryGetValue(resourcePath, out GameObject prefab))
        {
            // 加载资源并缓存
            prefab = Resources.Load<GameObject>(resourcePath);
            if (prefab == null)
            {
                Debug.LogError($"Effect prefab not found at path: {resourcePath}");
                return;
            }
            _effectCache.Add(resourcePath, prefab);
        }

        // 创建特效实例
        GameObject effectInstance = Instantiate(prefab);
        effectInstance.transform.position = pos;
        effectInstance.transform.eulerAngles = eulerAng;
        effectInstance.AddComponent<Tool_DestroySelfByTime>().Set(timer);
    }

}

public class Tool_Effect_SelfEffectData
{
    public Tool_Effect_SelfEffectData()
    {

    }

    public Tool_Effect_SelfEffectData(GameObject ebj,Vector3 pos,Vector3 ang)
    {
        effectObj = ebj;
        originPos = pos;
        originAng = ang;
    }

    public GameObject effectObj;
    public Vector3 originPos;
    public Vector3 originAng;
}

public class ShowHideEffectData
{
    public GameObject effectObj;
    public float hideTimer;
    public Vector3 originPos;
    public Vector3 originAng;
    public Vector3 showingPos;
    public Vector3 showingAng;
    public bool follow = false;
}
