using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using UnityEngine;
using TMPro;
#if UNITY_EDITOR
using UnityEditor;

/// <summary>
/// 中文文本查找工具
/// 功能1：提取C#脚本中引号内的中文字符
/// 功能2：提取TextMeshPro组件中的中文文本
/// </summary>
public class Tool_Editor_GetChineseText : EditorWindow
{
    private static readonly string OutputFilePrefix = "Tool_Editor_GetChineseText_";
    private static HashSet<string> uniqueChineseTexts = new HashSet<string>();
    
    // 添加右键菜单项 - 查找文件夹中的中文文本
    [MenuItem("Assets/Tool_Editor_GetChineseText/提取中文", false, 20)]
    private static void ExtractChineseFromScripts()
    {
        // 获取当前选中的文件夹
        UnityEngine.Object[] selectedObjects = Selection.GetFiltered<UnityEngine.Object>(SelectionMode.Assets);
        if (selectedObjects.Length == 0)
        {
            EditorUtility.DisplayDialog("错误", "请先选择一个文件夹", "确定");
            return;
        }

        uniqueChineseTexts.Clear();
        int processedFiles = 0;
        int processedPrefabs = 0;
        int processedJsonFiles = 0;
        int foundTexts = 0;

        // 处理每个选中的对象
        foreach (UnityEngine.Object obj in selectedObjects)
        {
            string path = AssetDatabase.GetAssetPath(obj);
            if (Directory.Exists(path))
            {
                // 如果是文件夹，获取所有C#脚本
                string[] files = Directory.GetFiles(path, "*.cs", SearchOption.AllDirectories);
                EditorUtility.DisplayProgressBar("处理中", "正在扫描C#脚本...", 0f);

                for (int i = 0; i < files.Length; i++)
                {
                    string file = files[i];
                    string content = File.ReadAllText(file, Encoding.UTF8);
                    List<string> chineseTexts = ExtractChineseFromContent(content);
                    
                    foreach (string text in chineseTexts)
                    {
                        if (!string.IsNullOrWhiteSpace(text) && !uniqueChineseTexts.Contains(text))
                        {
                            uniqueChineseTexts.Add(text);
                            foundTexts++;
                        }
                    }

                    processedFiles++;
                    EditorUtility.DisplayProgressBar("处理中", $"已处理 {processedFiles}/{files.Length} 个C#脚本...", (float)i / files.Length);
                }
                
                // 获取文件夹中的所有JSON文件
                string[] jsonFiles = Directory.GetFiles(path, "*.json", SearchOption.AllDirectories);
                EditorUtility.DisplayProgressBar("处理中", "正在扫描JSON文件...", 0f);
                
                for (int i = 0; i < jsonFiles.Length; i++)
                {
                    string jsonFile = jsonFiles[i];
                    string content = File.ReadAllText(jsonFile, Encoding.UTF8);
                    List<string> chineseTexts = ExtractChineseFromContent(content);
                    
                    foreach (string text in chineseTexts)
                    {
                        if (!string.IsNullOrWhiteSpace(text) && !uniqueChineseTexts.Contains(text))
                        {
                            uniqueChineseTexts.Add(text);
                            foundTexts++;
                        }
                    }
                    
                    processedJsonFiles++;
                    EditorUtility.DisplayProgressBar("处理中", $"已处理 {processedJsonFiles}/{jsonFiles.Length} 个JSON文件...", (float)i / jsonFiles.Length);
                }
                
                // 获取文件夹中的所有预制体
                string[] prefabs = Directory.GetFiles(path, "*.prefab", SearchOption.AllDirectories);
                EditorUtility.DisplayProgressBar("处理中", "正在扫描预制体...", 0f);
                
                for (int i = 0; i < prefabs.Length; i++)
                {
                    string prefabPath = prefabs[i];
                    GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
                    
                    if (prefab != null)
                    {
                        // 提取预制体中的TextMeshPro组件文本
                        ExtractTextFromPrefab(prefab, ref foundTexts);
                        processedPrefabs++;
                    }
                    
                    EditorUtility.DisplayProgressBar("处理中", $"已处理 {processedPrefabs}/{prefabs.Length} 个预制体...", (float)i / prefabs.Length);
                }
            }
        }

        EditorUtility.ClearProgressBar();
        
        // 保存结果
        if (uniqueChineseTexts.Count > 0)
        {
            SaveExtractedTexts();
            EditorUtility.DisplayDialog("完成", $"已处理 {processedFiles} 个C#脚本、{processedJsonFiles} 个JSON文件和 {processedPrefabs} 个预制体，找到 {foundTexts} 条中文文本", "确定");
        }
        else
        {
            EditorUtility.DisplayDialog("完成", "未找到任何中文文本", "确定");
        }
    }
    
    // 从预制体中提取TextMeshPro文本
    private static void ExtractTextFromPrefab(GameObject prefab, ref int foundTexts)
    {
        // 获取所有TextMeshPro组件
        TextMeshProUGUI[] tmpComponents = prefab.GetComponentsInChildren<TextMeshProUGUI>(true);
        TextMeshPro[] tmp3DComponents = prefab.GetComponentsInChildren<TextMeshPro>(true);
        
        // 处理UI版本的TextMeshPro
        foreach (TextMeshProUGUI tmp in tmpComponents)
        {
            if (tmp != null && !string.IsNullOrEmpty(tmp.text))
            {
                string text = tmp.text;
                if (ContainsChinese(text) && !uniqueChineseTexts.Contains(text))
                {
                    uniqueChineseTexts.Add(text);
                    foundTexts++;
                }
            }
        }

        // 处理3D版本的TextMeshPro
        foreach (TextMeshPro tmp in tmp3DComponents)
        {
            if (tmp != null && !string.IsNullOrEmpty(tmp.text))
            {
                string text = tmp.text;
                if (ContainsChinese(text) && !uniqueChineseTexts.Contains(text))
                {
                    uniqueChineseTexts.Add(text);
                    foundTexts++;
                }
            }
        }
    }

    // 添加右键菜单项 - 查找预制体中的中文文本
    [MenuItem("GameObject/Tool_Editor_GetChineseText/提取中文", false, 20)]
    private static void ExtractChineseFromTextMeshPro()
    {
        // 获取当前选中的游戏对象
        GameObject[] selectedObjects = Selection.gameObjects;
        if (selectedObjects.Length == 0)
        {
            EditorUtility.DisplayDialog("错误", "请先选择一个游戏对象", "确定");
            return;
        }

        uniqueChineseTexts.Clear();
        int processedComponents = 0;
        int foundTexts = 0;

        // 处理每个选中的对象
        foreach (GameObject obj in selectedObjects)
        {
            // 获取所有TextMeshPro组件
            TextMeshProUGUI[] tmpComponents = obj.GetComponentsInChildren<TextMeshProUGUI>(true);
            TextMeshPro[] tmp3DComponents = obj.GetComponentsInChildren<TextMeshPro>(true);
            
            EditorUtility.DisplayProgressBar("处理中", "正在扫描TextMeshPro组件...", 0f);

            // 处理UI版本的TextMeshPro
            for (int i = 0; i < tmpComponents.Length; i++)
            {
                TextMeshProUGUI tmp = tmpComponents[i];
                if (tmp != null && !string.IsNullOrEmpty(tmp.text))
                {
                    string text = tmp.text;
                    if (ContainsChinese(text) && !uniqueChineseTexts.Contains(text))
                    {
                        uniqueChineseTexts.Add(text);
                        foundTexts++;
                    }
                }
                processedComponents++;
                EditorUtility.DisplayProgressBar("处理中", $"已处理 {processedComponents} 个组件...", (float)i / (tmpComponents.Length + tmp3DComponents.Length));
            }

            // 处理3D版本的TextMeshPro
            for (int i = 0; i < tmp3DComponents.Length; i++)
            {
                TextMeshPro tmp = tmp3DComponents[i];
                if (tmp != null && !string.IsNullOrEmpty(tmp.text))
                {
                    string text = tmp.text;
                    if (ContainsChinese(text) && !uniqueChineseTexts.Contains(text))
                    {
                        uniqueChineseTexts.Add(text);
                        foundTexts++;
                    }
                }
                processedComponents++;
                EditorUtility.DisplayProgressBar("处理中", $"已处理 {processedComponents} 个组件...", (float)(i + tmpComponents.Length) / (tmpComponents.Length + tmp3DComponents.Length));
            }
        }

        EditorUtility.ClearProgressBar();
        
        // 保存结果
        if (uniqueChineseTexts.Count > 0)
        {
            SaveExtractedTexts();
            EditorUtility.DisplayDialog("完成", $"已处理 {processedComponents} 个组件，找到 {foundTexts} 条中文文本", "确定");
        }
        else
        {
            EditorUtility.DisplayDialog("完成", "未找到任何中文文本", "确定");
        }
    }

    // 从内容中提取中文文本
    private static List<string> ExtractChineseFromContent(string content)
    {
        List<string> results = new List<string>();

        try
        {
            // 首先移除注释内容，避免提取注释中的中文
            string codeWithoutComments = Regex.Replace(content, @"//.*?$|/\*[\s\S]*?\*/", "", RegexOptions.Multiline);
            
            // 匹配双引号内包含中文字符的完整字符串
            // 这个正则表达式会匹配双引号内的内容，并确保内容中包含中文字符
            string pattern = @"""([^""]*[\u4e00-\u9fa5]+[^""]*)""";
            MatchCollection matches = Regex.Matches(codeWithoutComments, pattern);

            foreach (Match match in matches)
            {
                if (match.Groups.Count > 1)
                {
                    string chineseText = match.Groups[1].Value;
                    if (!string.IsNullOrWhiteSpace(chineseText) && ContainsChinese(chineseText))
                    {
                        // 过滤掉可能的代码片段
                        if (!chineseText.Contains("==") && !chineseText.Contains("+=") && 
                            !chineseText.Contains("-=") && !chineseText.Contains("*=") && 
                            !chineseText.Contains("/=") && !chineseText.Contains("="))
                        {
                            results.Add(chineseText);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"提取中文文本时出错: {ex.Message}");
        }

        return results;
    }

    // 检查字符串是否包含中文
    private static bool ContainsChinese(string text)
    {
        return Regex.IsMatch(text, @"[\u4e00-\u9fa5]+");
    }

    // 保存提取的文本到文件
    private static void SaveExtractedTexts()
    {
        string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
        string outputFileName = $"{OutputFilePrefix}{timestamp}.txt";
        string outputPath = Path.Combine(Application.dataPath, outputFileName);

        try
        {
            // 将每个中文文本转换为"文本:"的格式
            List<string> formattedTexts = new List<string>();
            foreach (string text in uniqueChineseTexts)
            {
                formattedTexts.Add($"{text}#{text}");
            }

            // 按照示例格式排序输出
            formattedTexts.Sort();
            
            File.WriteAllLines(outputPath, formattedTexts.ToArray(), Encoding.UTF8);
            AssetDatabase.Refresh();
            Debug.Log($"中文文本已保存到: {outputPath}");
        }
        catch (Exception ex)
        {
            Debug.LogError($"保存文件时出错: {ex.Message}");
            EditorUtility.DisplayDialog("错误", $"保存文件时出错: {ex.Message}", "确定");
        }
    }
}
#endif
