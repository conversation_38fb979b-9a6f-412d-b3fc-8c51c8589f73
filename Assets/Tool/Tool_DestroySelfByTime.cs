using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_DestroySelfByTime : MonoBehaviour
{
    
    public float timer = 1;

    public void Set(float Time)
    {
        timer = Time;
    }

    private void Update()
    {
        if(timer > 0)
        {
            timer -= Time.deltaTime;
        }
        else
        {
            timer = 1;
            GameObject.Destroy(this.gameObject);
        }
    }

}
