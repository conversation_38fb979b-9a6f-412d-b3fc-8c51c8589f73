using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_CameraFollow_ThirdRole : MonoBehaviour
{
    private static Tool_CameraFollow_ThirdRole instance = null;
    public static Tool_CameraFollow_ThirdRole Instance
    {
        get
        {

            if (instance == null)
                instance = GameObject.FindObjectOfType<Tool_CameraFollow_ThirdRole>();

            return instance;
        }
    }

    public static Transform FollowTarget;    //相机追随目标
    public float Seting_RotateXSpeed = 160;  //X轴方向拖动速度
    public float Seting_RotateYSpeed = 160;  //Y轴方向拖动速度

    public float Setting_RotateYMinLimit = -5; //在Y轴最小移动范围
    public float Seting_BeginRotateY = 10;
    public float Setting_RotateYMaxLimit = 10; //在Y轴最大移动范围
    public float Setting_Distance = 3.5f;  //相机视角距离

    public float Setting_FieldViewFadeSpeed = 7f;

    public Vector3 Setting_TargetPosOffSet = new Vector3(0, 1.25f, 0);

    private Vector2 rotateValue;

    private static Transform currentTarPar;
    //public float parLerpSpeed;

    Camera cam;

    static float originFieldView;
    static float targetFieldView;

    [Header("X轴比例插值距离功能")]
    public bool Setting_OpenRotateXLerpZValue = false;
    public Vector2 Setting_RotateXLerpZValue;


    void Awake()
    {
        rotateValue.x = transform.eulerAngles.y;
        rotateValue.y = transform.eulerAngles.x;

        rotateValue.y = Seting_BeginRotateY;

        cam = GetComponent<Camera>();

        originFieldView = cam.fieldOfView;
        targetFieldView = originFieldView;
    }

    public void SetSetting(Tool_CameraFollow_ThirdRole_Setting setting)
    {
        Seting_RotateXSpeed = setting.Seting_RotateXSpeed;
        Seting_RotateYSpeed = setting.Seting_RotateYSpeed;
        Setting_RotateYMinLimit = setting.Setting_RotateYMinLimit;
        Seting_BeginRotateY = setting.Seting_BeginRotateY;
        Setting_RotateYMaxLimit = setting.Setting_RotateYMaxLimit;
        Setting_Distance = setting.Setting_Distance;
        Setting_FieldViewFadeSpeed = setting.Setting_FieldViewFadeSpeed;
        Setting_TargetPosOffSet = new Vector3(setting.Setting_TargetPosOffSet[0], setting.Setting_TargetPosOffSet[1], setting.Setting_TargetPosOffSet[2]);
        Setting_OpenRotateXLerpZValue = setting.Setting_OpenRotateXLerpZValue;
        Setting_RotateXLerpZValue = new Vector2(setting.Setting_RotateXLerpZValue[0], setting.Setting_RotateXLerpZValue[1]);

        cam.fieldOfView = setting.Setting_BeginFieldView;
        originFieldView = cam.fieldOfView;
        targetFieldView = originFieldView;
    }

    public static void SetTarget(Transform target)
    {
        currentTarPar = target;
        FollowTarget = target;

    }

    public static void SetTargetFieldView(float target)
    {
        targetFieldView = target;
    }

    public static void ResetFieldView()
    {
        targetFieldView = originFieldView;
    }


    void LateUpdate()
    {
        if (!FollowTarget)
            return;


#if UNITY_EDITOR
        if (Input.GetMouseButton(0))
        {
            SetCameraRotateValue(new Vector2(Input.GetAxis("Mouse X"), Input.GetAxis("Mouse Y")));
        }

        //if (Input.GetMouseButton(1))
        //{
        //    SetCameraRotateValue(new Vector2(Input.GetAxis("Mouse X"), Input.GetAxis("Mouse Y")));
        //}

        // SetCameraRotateValue(new Vector2(0.5f, 0));
#endif



        Vector3 targetPos = FollowTarget.position;

        Quaternion rotation = Quaternion.Euler(rotateValue.y, rotateValue.x, 0.0f);
        Vector3 forward = rotation * Vector3.forward;
        Vector3 right = rotation * Vector3.right;

        // 创建一个与摄像机前向向量垂直的偏移向量，并调整其长度  
        Vector3 offset = rotation * Setting_TargetPosOffSet;

        // 计算摄像机位置  
        Vector3 position = targetPos + offset - forward * Setting_Distance;

        transform.rotation = rotation;
        transform.position = position;

        if (Setting_OpenRotateXLerpZValue)
        {
            float per = (rotateValue.y - Setting_RotateYMinLimit) / (Setting_RotateYMaxLimit - Setting_RotateYMinLimit);
            float Setting_TargetPosOffSetZ = Setting_RotateXLerpZValue.x + (Setting_RotateXLerpZValue.y - Setting_RotateXLerpZValue.x) * per;
            Setting_TargetPosOffSet = new Vector3(Setting_TargetPosOffSet.x, Setting_TargetPosOffSet.y, Setting_TargetPosOffSetZ);

        }

        //fv
        cam.fieldOfView = Mathf.Lerp(cam.fieldOfView, targetFieldView, Setting_FieldViewFadeSpeed * Time.deltaTime);
    }


    public void SetCameraRotateValue(Vector2 vec)
    {
        rotateValue.x += vec.x * Seting_RotateXSpeed * Time.deltaTime;

        rotateValue.y -= vec.y * Seting_RotateYSpeed * Time.deltaTime;
        rotateValue.y = Mathf.Clamp(rotateValue.y, Setting_RotateYMinLimit, Setting_RotateYMaxLimit);


    }

    public static Vector3 InputDirToCameraInputDir(Camera c, Vector3 dir)
    {
        var targetDirection = dir;
        float y = c.transform.rotation.eulerAngles.y;
        targetDirection = Quaternion.Euler(0, y, 0) * targetDirection;
        return targetDirection;
    }


    public void SetToRearBack()
    {
        for(var i= 0; i < 10; i++)
        {
            // 假设角色有一个 Transform 组件，名为 characterTransform
            Transform characterTransform = FollowTarget; // 获取角色的 Transform

            // 获取角色当前的前向向量
            Vector3 characterForward = characterTransform.forward;

            // 获取相机当前的前向向量（基于 rotateValue.y 的旋转）
            Quaternion cameraRotation = Quaternion.Euler(rotateValue.y, rotateValue.x, 0);
            Vector3 cameraForward = cameraRotation * Vector3.forward;

            // 计算两个前向向量之间的角度差（在 XY 平面上）
            float angleDifference = Vector3.Angle(cameraForward, characterForward);
            Vector3 crossProduct = Vector3.Cross(cameraForward, characterForward);
            //bool isClockwise = Vector3.Dot(crossProduct, Vector3.up) < 0;

            //// 根据是顺时针还是逆时针旋转，调整角度差的方向
            //if (!isClockwise)
            //{
            //    angleDifference = -angleDifference;
            //}

            // 应用角度差到 rotateValue.x 上（可能需要平滑过渡）
            float targetRotateX = rotateValue.x + angleDifference;
            // 这里可以添加平滑过渡的逻辑，例如使用 Lerp
            // rotateValue.x = Mathf.Lerp(rotateValue.x, targetRotateX, smoothTime * Time.deltaTime);
            rotateValue.x = targetRotateX; // 简化版，没有平滑过渡
        }


    }

}


public class Tool_CameraFollow_ThirdRole_Setting
{
    public float Seting_RotateXSpeed;
    public float Seting_RotateYSpeed;

    public float Setting_RotateYMinLimit;
    public float Seting_BeginRotateY;
    public float Setting_RotateYMaxLimit;
    public float Setting_Distance;

    public int Setting_BeginFieldView;
    public float Setting_FieldViewFadeSpeed;

    public float[] Setting_TargetPosOffSet;

    public bool Setting_OpenRotateXLerpZValue;
    public float[] Setting_RotateXLerpZValue;
}