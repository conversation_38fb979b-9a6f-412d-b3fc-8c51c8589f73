using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public abstract class Tool_BSAI_Base 
{

    // 当前行为状态
    public ITool_BSAI_State currentState;

    // 可用行为状态列表
    protected Dictionary<string, ITool_BSAI_State> states = new Dictionary<string, ITool_BSAI_State>();

    // 初始化
    protected virtual void Init(GameObject AIObj)
    {
        // 初始化状态
        InitializeStates();

        // 设置初始状态
        ChangeState(GetDefaultState());
    }


    // 切换状态
    public void ChangeState(string stateName)
    {
        if (states.ContainsKey(stateName))
        {
            currentState?.OnExit(this);
            currentState = states[stateName];
            currentState.OnEnter(this);
        }
        else
        {
            Debug.LogError("State " + stateName + " not found!");
        }
    }

    // 初始化状态
    protected abstract void InitializeStates();

    // 获取默认状态
    protected abstract string GetDefaultState();



}

public interface ITool_BSAI_State
{
    void OnEnter(Tool_BSAI_Base ai);
    void Execute(Tool_BSAI_Base ai);
    void OnExit(Tool_BSAI_Base ai);
}