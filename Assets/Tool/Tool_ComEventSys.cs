using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class Tool_ComEventSys 
{

    // 定义委托：事件名称 + 可变参数数组
    public  delegate void EventHandler(string eventName, params object[] args);
    private static Dictionary<string, EventHandler> _eventListeners = new Dictionary<string, EventHandler>();

    // 注册事件
    public static void Subscribe(string eventName, EventHandler handler)
    {
        if (_eventListeners.ContainsKey(eventName))
            _eventListeners[eventName] += handler;
        else
            _eventListeners.Add(eventName, handler);
    }

    // 触发事件
    public static void Publish(string eventName, params object[] args)
    {
        if (_eventListeners.TryGetValue(eventName, out var handler))
            handler?.Invoke(eventName, args);
    }

    // 取消注册
    public static void Unsubscribe(string eventName, EventHandler handler)
    {
        if (_eventListeners.ContainsKey(eventName))
            _eventListeners[eventName] -= handler;
    }
}



//Example
//// 发送事件（支持任意参数）
//Tool_ComEventSys.Instance.Publish("PlayerDamaged", 100, "Fire", transform.position);

//// 接收事件
//void OnEnable() => Tool_ComEventSys.Instance.Subscribe("PlayerDamaged", OnPlayerDamaged);
//void OnDisable() => Tool_ComEventSys.Instance.Unsubscribe("PlayerDamaged", OnPlayerDamaged);

//private void OnPlayerDamaged(string eventName, params object[] args)
//{
//    int damage = (int)args[0];
//    string damageType = (string)args[1];
//    Vector3 position = (Vector3)args[2];
//    // 处理逻辑...
//}