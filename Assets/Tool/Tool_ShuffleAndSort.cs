using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_ShuffleAndSort 
{

    //Sort by 
   // rank.OrderBy(a => a.div).ToList();
    // rank.OrderByDescending(a => a.div).ToList();


    public static List<T> Shuffle<T>(List<T> original)
    {
        System.Random randomNum = new System.Random();
        int index = 0;
        T temp;
        for (int i = 0; i < original.Count; i++)
        {
            index = randomNum.Next(0, original.Count - 1);
            if (index != i)
            {
                temp = original[i];
                original[i] = original[index];
                original[index] = temp;
            }
        }
        return original;
    }



    /// <summary>
    /// Knuth-Durstenfeld Shuffle打乱算法
    /// </summary>
    public static void KnuthD<PERSON>tenfeld<T>(List<T> targetList)
    {
        for (int i = targetList.Count - 1; i > 0; i--)
        {
            int exchange = UnityEngine.Random.Range(0, i + 1);
            T temp = targetList[i];
            targetList[i] = targetList[exchange];
            targetList[exchange] = temp;
        }
    }

    /// <summary>
    /// Knuth-<PERSON> Shuffle打乱算法
    /// </summary>
    public static void Knuth<PERSON><PERSON><PERSON>feld<T>(T[] targetList)
    {
        for (int i = targetList.Length - 1; i > 0; i--)
        {
            int exchange = UnityEngine.Random.Range(0, i + 1);
            T temp = targetList[i];
            targetList[i] = targetList[exchange];
            targetList[exchange] = temp;
        }
    }

}
