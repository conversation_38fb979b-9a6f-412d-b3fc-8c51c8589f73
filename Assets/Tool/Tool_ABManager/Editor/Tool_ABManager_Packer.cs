using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class Tool_ABManager_Packer
{
#if UNITY_EDITOR
	//����������Ʋ�������Դ����һ�£�����ص���Դ

	static	BuildTarget BuildTarget = BuildTarget.WebGL;

	/// <summary>
	/// ��ѡ�е�Ԥ�Ʒֱ���
	/// </summary>
	[MenuItem("Tool_ABManager/Pack ABs (Each)")]
    [System.Obsolete]
    static void CreateAssetBundleThemelves()
	{
		//��ȡҪ����Ķ�����Project��ͼ�У�
		Object[] selects = Selection.GetFiltered(typeof(Object), SelectionMode.DeepAssets);
		//����ѡ�еĶ���
		foreach (Object obj in selects)
		{
			// ��ȡ��ǰʱ�䲢��ʽ��Ϊ�ַ���
			string timeStamp = System.DateTime.Now.ToString("yyyyMMddHHmmss");

			// ��ȡ�ļ�������չ��
			string fileNameWithoutExtension = obj.name;
			string fileExtension = ".assetbundle";

			// ����µ��ļ���������ʱ���ʶ
			string newFileName = fileNameWithoutExtension + "_" + timeStamp + fileExtension;

			// �����µ�����·��
			string targetPath = Application.dataPath + "/" + newFileName;

			if (BuildPipeline.BuildAssetBundle(obj, null, targetPath, BuildAssetBundleOptions.CollectDependencies, BuildTarget))
			{

				Debug.Log(obj.name + " is packed successfully!");
			}
			else
			{
				Debug.Log(obj.name + " is packed failly!");
			}
		}
		//ˢ�±༭������д�Ļ�Ҫ�ֶ�ˢ��,����������Դ���ܼ�ʱ��Project��ͼ����ʾ��
		AssetDatabase.Refresh();
	}

	/// <summary>
	/// ��ѡ�е�Ԥ�ƴ����һ��
	/// </summary>
	[MenuItem("Tool_ABManager/Pack ABs (In_One)")]
	[System.Obsolete]
    static void CreateAssetBundleTogether()
	{
		//Ҫ����Ķ���
		Object[] selects = Selection.GetFiltered(typeof(Object), SelectionMode.DeepAssets);


		// ��ȡ��ǰʱ�䲢��ʽ��Ϊ�ַ���
		string timeStamp = System.DateTime.Now.ToString("yyyyMMddHHmmss");

		// ԭʼ��·�����ļ���
		string originalFileName = "Tool_ABManager_Packer_In_One.assetbundle";

		// ��ȡ�ļ�������չ��
		int dotIndex = originalFileName.LastIndexOf('.');
		string fileNameWithoutExtension = originalFileName.Substring(0, dotIndex);
		string fileExtension = originalFileName.Substring(dotIndex);

		// ����µ��ļ���������ʱ���ʶ
		string newFileName = fileNameWithoutExtension + "_" + timeStamp + fileExtension;

		// �����µ�����·��
		string targetPath = Application.dataPath + "/" + newFileName;

		//Ҫ�������·��
		//string targetPath = Application.dataPath + "/" + "Tool_ABManager_Packer_In_One_temp.assetbundle";//�ļ��ĺ�׺����assetbundle��unity������

		if (BuildPipeline.BuildAssetBundle(null, selects, targetPath, BuildAssetBundleOptions.CollectDependencies, BuildTarget))
		{
			
				Debug.Log("Packed successfully!");

		}
		else
		{
			Debug.Log("Packed failly!");
		}
		//ˢ�±༭������д�Ļ�Ҫ�ֶ�ˢ�£�
		AssetDatabase.Refresh();
	}
#endif
}
