using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class Tool_CueText : MonoBehaviour
{
    public float Cue_ScaleBegin = 0;
    public float Cue_ScaleTarget = 1;
    public float Cue_ShowScaleSpeed = 20;
    public float Cue_HideScaleSpeed = 30;
    public float Cue_ShowTime = 1.5f; 

    public RectTransform parent;
    public Text txt_Cue;

     float CurrentShowTimer;
     float CurrentPerTimer;
     float CurrentShowCount = -1;


    void Update()
    {
        switch (CurrentShowCount)
        {
            case 0:
                var target = new Vector3(Cue_ScaleTarget, Cue_ScaleTarget, Cue_ScaleTarget);
                parent.localScale = Vector3.Lerp(parent.localScale, target, Cue_ShowScaleSpeed * Time.deltaTime);

                if(Vector3.Distance(parent.localScale, target) < 0.02f)
                    CurrentShowCount = 1;
                break;
            case 1:
                CurrentShowTimer -= Time.deltaTime;
                if (CurrentShowTimer <= 0)
                    CurrentShowCount = 2;
                break;
            case 2:
                parent.localScale = Vector3.Lerp(parent.localScale, Vector3.zero, Cue_HideScaleSpeed * Time.deltaTime);
                if (Vector3.Distance(parent.localScale, Vector3.zero) < 0.02f)
                {
                    CurrentShowCount = -1;
                    parent.gameObject.SetActive(false);
                }
              
                break;
        }

    }

    public void ShowCue(string text)
    {
        parent.gameObject.SetActive(true);
        txt_Cue.text = text;

        CurrentShowCount = 0;
        CurrentShowTimer = Cue_ShowTime;
        parent.localScale = new Vector3(Cue_ScaleBegin,Cue_ScaleBegin, Cue_ScaleBegin);
        
    }

}

