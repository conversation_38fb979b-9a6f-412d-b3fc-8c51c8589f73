using UnityEngine;

/// <summary>
/// Tool_OutlineExample - 描边效果使用示例
/// 展示如何使用Tool_OutlineController控制描边效果
/// 可复制到其他项目作为参考
/// </summary>
public class Tool_OutlineExample : MonoBehaviour
{
    [Header("描边控制器引用")]
    [Tooltip("要控制的描边控制器")]
    public Tool_OutlineController outlineController;

    [<PERSON>er("动画演示设置")]
    [Tooltip("启用自动演示")]
    public bool enableDemo = false;

    [Tooltip("演示速度")]
    public float demoSpeed = 1.0f;

    [Tooltip("颜色变化速度")]
    public float colorChangeSpeed = 2.0f;

    [Header("预设配置")]
    [Tooltip("预设的描边配置")]
    public OutlinePreset[] outlinePresets = new OutlinePreset[]
    {
        new OutlinePreset("黑色描边", true, Color.black, 0.001f, 1.0f),
        new OutlinePreset("红色描边", true, Color.red, 0.002f, 0.8f),
        new OutlinePreset("蓝色半透明", true, Color.blue, 0.0015f, 0.5f),
        new OutlinePreset("白色细线", true, Color.white, 0.0008f, 1.0f),
        new OutlinePreset("绿色描边", true, Color.green, 0.001f, 1.0f)
    };

    // 演示用变量
    private float demoTime = 0f;
    private int currentPresetIndex = 0;
    private float lastPresetChangeTime = 0f;

    void Start()
    {
        // 如果没有指定描边控制器，尝试自动获取
        if (outlineController == null)
        {
            outlineController = GetComponent<Tool_OutlineController>();
        }

        if (outlineController == null)
        {
            Debug.LogError("[Tool_OutlineExample] 找不到Tool_OutlineController组件！");
            return;
        }

        // 应用第一个预设
        if (outlinePresets.Length > 0)
        {
            ApplyPreset(0);
        }
    }

    void Update()
    {
        if (enableDemo && outlineController != null)
        {
            RunDemo();
        }
    }

    /// <summary>
    /// 运行自动演示
    /// </summary>
    private void RunDemo()
    {
        demoTime += Time.deltaTime * demoSpeed;

        // 每3秒切换一个预设
        if (Time.time - lastPresetChangeTime > 3f)
        {
            currentPresetIndex = (currentPresetIndex + 1) % outlinePresets.Length;
            ApplyPreset(currentPresetIndex);
            lastPresetChangeTime = Time.time;
        }

        // 如果当前预设启用了描边，添加颜色动画
        if (outlinePresets[currentPresetIndex].enableOutline)
        {
            // 使用HSV颜色空间创建彩虹效果
            Color baseColor = outlinePresets[currentPresetIndex].outlineColor;
            Color.RGBToHSV(baseColor, out float h, out float s, out float v);

            // 调制色相以创建颜色变化
            h = (h + Time.time * colorChangeSpeed * 0.1f) % 1f;
            Color animatedColor = Color.HSVToRGB(h, s, v);

            outlineController.SetOutlineColor(animatedColor);
        }
    }

    /// <summary>
    /// 应用预设配置
    /// </summary>
    /// <param name="presetIndex">预设索引</param>
    public void ApplyPreset(int presetIndex)
    {
        if (outlineController == null || presetIndex < 0 || presetIndex >= outlinePresets.Length)
            return;

        OutlinePreset preset = outlinePresets[presetIndex];
        outlineController.SetOutlineSettings(
            preset.enableOutline,
            preset.outlineColor,
            preset.outlineWidth,
            preset.outlineAlpha
        );

        Debug.Log($"[Tool_OutlineExample] 应用预设: {preset.name}");
    }

    /// <summary>
    /// 切换到下一个预设
    /// </summary>
    public void NextPreset()
    {
        currentPresetIndex = (currentPresetIndex + 1) % outlinePresets.Length;
        ApplyPreset(currentPresetIndex);
    }

    /// <summary>
    /// 切换到上一个预设
    /// </summary>
    public void PreviousPreset()
    {
        currentPresetIndex = (currentPresetIndex - 1 + outlinePresets.Length) % outlinePresets.Length;
        ApplyPreset(currentPresetIndex);
    }

    /// <summary>
    /// 随机应用一个预设
    /// </summary>
    public void RandomPreset()
    {
        int randomIndex = Random.Range(0, outlinePresets.Length);
        currentPresetIndex = randomIndex;
        ApplyPreset(currentPresetIndex);
    }

    /// <summary>
    /// 启用/禁用描边
    /// </summary>
    public void ToggleOutline()
    {
        if (outlineController != null)
        {
            OutlineSettings current = outlineController.GetOutlineSettings();
            if (current.enableOutline)
            {
                outlineController.DisableOutline();
            }
            else
            {
                outlineController.EnableOutline();
            }
        }
    }

    /// <summary>
    /// 设置随机颜色
    /// </summary>
    public void SetRandomColor()
    {
        if (outlineController != null)
        {
            Color randomColor = new Color(
                Random.Range(0f, 1f),
                Random.Range(0f, 1f),
                Random.Range(0f, 1f),
                1f
            );
            outlineController.SetOutlineColor(randomColor);
        }
    }

    /// <summary>
    /// 设置随机宽度
    /// </summary>
    public void SetRandomWidth()
    {
        if (outlineController != null)
        {
            float randomWidth = Random.Range(0.0005f, 0.003f);
            outlineController.SetOutlineWidth(randomWidth);
        }
    }

    /// <summary>
    /// 设置随机透明度
    /// </summary>
    public void SetRandomAlpha()
    {
        if (outlineController != null)
        {
            float randomAlpha = Random.Range(0.3f, 1f);
            outlineController.SetOutlineAlpha(randomAlpha);
        }
    }

    // GUI显示控制按钮（仅在编辑器或开发版本中显示）
    void OnGUI()
    {
        if (!Debug.isDebugBuild) return;

        GUILayout.BeginArea(new Rect(10, 10, 200, 300));
        GUILayout.Label("描边控制示例", GUI.skin.box);

        if (GUILayout.Button("切换描边"))
        {
            ToggleOutline();
        }

        if (GUILayout.Button("下一个预设"))
        {
            NextPreset();
        }

        if (GUILayout.Button("上一个预设"))
        {
            PreviousPreset();
        }

        if (GUILayout.Button("随机预设"))
        {
            RandomPreset();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("随机颜色"))
        {
            SetRandomColor();
        }

        if (GUILayout.Button("随机宽度"))
        {
            SetRandomWidth();
        }

        if (GUILayout.Button("随机透明度"))
        {
            SetRandomAlpha();
        }

        GUILayout.Space(10);

        enableDemo = GUILayout.Toggle(enableDemo, "自动演示");

        if (outlineController != null)
        {
            OutlineSettings current = outlineController.GetOutlineSettings();
            GUILayout.Label($"当前预设: {outlinePresets[currentPresetIndex].name}");
            GUILayout.Label($"描边状态: {(current.enableOutline ? "启用" : "禁用")}");
        }

        GUILayout.EndArea();
    }
}

/// <summary>
/// 描边预设配置
/// </summary>
[System.Serializable]
public class OutlinePreset
{
    [Tooltip("预设名称")]
    public string name;

    [Tooltip("启用描边")]
    public bool enableOutline;

    [Tooltip("描边颜色")]
    public Color outlineColor;

    [Tooltip("描边宽度")]
    [Range(0f, 0.005f)]
    public float outlineWidth;

    [Tooltip("描边透明度")]
    [Range(0f, 1f)]
    public float outlineAlpha;

    public OutlinePreset(string name, bool enable, Color color, float width, float alpha)
    {
        this.name = name;
        this.enableOutline = enable;
        this.outlineColor = color;
        this.outlineWidth = width;
        this.outlineAlpha = alpha;
    }
}