using System.Collections.Generic;
using UnityEngine;
using System.Collections;

namespace JRJelly
{
    /// <summary>
    /// 果冻效果主控制器
    /// 负责管理和计算所有果冻效果
    /// </summary>
    [RequireComponent(typeof(Renderer))]
    public class Tool_JRJELLY_Controller : MonoBehaviour
    {
        [Header("=== 果冻设置 ===")]
        [SerializeField]
        private Tool_JRJELLY_Settings settings = Tool_JRJELLY_Settings.Default;

        [Header("=== 调试设置 ===")]
        [SerializeField]
        private bool showDebugGizmos = true;

        [SerializeField]
        private bool enableDebugLog = false;

        // === 私有变量 ===
        private Renderer targetRenderer;
        private MaterialPropertyBlock materialPropertyBlock;
        private List<JellyEffectData> activeEffects = new List<JellyEffectData>();

        // Shader属性ID缓存
        private static readonly int ShaderID_JellyEnabled = Shader.PropertyToID("_JellyEnabled");
        private static readonly int ShaderID_JellyOffset = Shader.PropertyToID("_JellyOffset");
        private static readonly int ShaderID_JellyScale = Shader.PropertyToID("_JellyScale");
        private static readonly int ShaderID_JellyRotation = Shader.PropertyToID("_JellyRotation");
        private static readonly int ShaderID_JellyIntensity = Shader.PropertyToID("_JellyIntensity");
        private static readonly int ShaderID_JellyDirection = Shader.PropertyToID("_JellyDirection");
        private static readonly int ShaderID_JellySquash = Shader.PropertyToID("_JellySquash");

        // 当前效果状态
        private Vector3 currentOffset = Vector3.zero;
        private Vector3 currentScale = Vector3.one;
        private Vector3 currentRotation = Vector3.zero;
        private float currentIntensity = 0f;
        private Vector3 currentDirection = Vector3.zero;
        private float currentSquash = 1f;

        // 性能优化相关
        private bool isSleeping = true;
        private float lastUpdateTime = 0f;
        private float updateTimer = 0f;

        // 原始Transform信息
        private Vector3 originalPosition;
        private Vector3 originalScale;
        private Quaternion originalRotation;

        #region Unity生命周期

        void Awake()
        {
            targetRenderer = GetComponent<Renderer>();
            materialPropertyBlock = new MaterialPropertyBlock();

            // 记录原始变换信息
            originalPosition = transform.position;
            originalScale = transform.localScale;
            originalRotation = transform.rotation;
        }

        void Start()
        {
            // 初始化Shader属性
            UpdateShaderProperties();
        }

        void Update()
        {
            // 性能优化：按设定频率更新
            updateTimer += Time.deltaTime * 1000f;
            if (updateTimer < settings.updateInterval)
                return;

            updateTimer = 0f;

            if (!isSleeping)
            {
                UpdateJellyEffects();
                UpdateShaderProperties();

                // 检查是否可以进入休眠
                CheckSleepCondition();
            }
        }

        #endregion

        #region 公共接口

        /// <summary>
        /// 触发基于目标点的甩动效果
        /// </summary>
        /// <param name="targetPosition">目标世界坐标位置</param>
        /// <param name="intensity">效果强度 (0-1)</param>
        /// <param name="duration">持续时间</param>
        public void TriggerTargetSwing(Vector3 targetPosition, float intensity = 1f, float duration = 1f)
        {
            if (enableDebugLog)
                Debug.Log($"[JRJELLY] 触发目标甩动: 目标={targetPosition}, 强度={intensity}");

            var effectData = new JellyEffectData(JellyEffectType.TargetSwing, targetPosition, intensity, duration);

            // 计算甩动方向（从目标点指向物体原点）
            Vector3 toTarget = targetPosition - originalPosition;
            effectData.direction = -toTarget.normalized; // 甩动方向与目标方向相反

            AddEffect(effectData);
        }

        /// <summary>
        /// 触发刹车回弹效果
        /// </summary>
        /// <param name="direction">回弹方向</param>
        /// <param name="intensity">回弹强度</param>
        /// <param name="duration">持续时间</param>
        public void TriggerBrakeRebound(Vector3 direction, float intensity = 0.8f, float duration = 0.8f)
        {
            if (enableDebugLog)
                Debug.Log($"[JRJELLY] 触发刹车回弹: 方向={direction}, 强度={intensity}");

            var effectData = new JellyEffectData(JellyEffectType.BrakeRebound, Vector3.zero, intensity, duration);
            effectData.direction = direction.normalized;

            AddEffect(effectData);
        }

        /// <summary>
        /// 触发扩散甩动效果
        /// </summary>
        /// <param name="centerPoint">扩散中心点</param>
        /// <param name="intensity">扩散强度</param>
        /// <param name="duration">持续时间</param>
        public void TriggerSpreadSwing(Vector3 centerPoint, float intensity = 1.2f, float duration = 1.2f)
        {
            if (enableDebugLog)
                Debug.Log($"[JRJELLY] 触发扩散甩动: 中心点={centerPoint}, 强度={intensity}");

            var effectData = new JellyEffectData(JellyEffectType.SpreadSwing, centerPoint, intensity, duration);

            // 计算扩散方向（从中心点指向物体）
            Vector3 spreadDirection = (originalPosition - centerPoint).normalized;
            effectData.direction = spreadDirection;

            AddEffect(effectData);
        }

        /// <summary>
        /// 停止所有效果
        /// </summary>
        public void StopAllEffects()
        {
            activeEffects.Clear();
            ResetToOriginalState();
            isSleeping = true;

            if (enableDebugLog)
                Debug.Log("[JRJELLY] 停止所有效果");
        }

        /// <summary>
        /// 设置果冻配置
        /// </summary>
        public void SetSettings(Tool_JRJELLY_Settings newSettings)
        {
            settings = newSettings;

            if (enableDebugLog)
                Debug.Log("[JRJELLY] 更新配置设置");
        }

        /// <summary>
        /// 获取当前配置
        /// </summary>
        public Tool_JRJELLY_Settings GetSettings()
        {
            return settings;
        }

        /// <summary>
        /// 是否有活跃效果
        /// </summary>
        public bool HasActiveEffects()
        {
            return activeEffects.Count > 0;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 添加新效果
        /// </summary>
        private void AddEffect(JellyEffectData effectData)
        {
            activeEffects.Add(effectData);
            WakeUp();
        }

        /// <summary>
        /// 唤醒效果更新
        /// </summary>
        private void WakeUp()
        {
            if (isSleeping)
            {
                isSleeping = false;
                lastUpdateTime = Time.time;

                if (enableDebugLog)
                    Debug.Log("[JRJELLY] 唤醒效果更新");
            }
        }

        /// <summary>
        /// 检查休眠条件
        /// </summary>
        private void CheckSleepCondition()
        {
            if (!settings.enableSleep || activeEffects.Count > 0)
                return;

            // 检查当前效果强度是否低于休眠阈值
            bool canSleep = currentIntensity < settings.sleepThreshold &&
                           currentOffset.magnitude < settings.sleepThreshold &&
                           Mathf.Abs(currentSquash - 1f) < settings.sleepThreshold;

            if (canSleep)
            {
                isSleeping = true;
                ResetToOriginalState();

                if (enableDebugLog)
                    Debug.Log("[JRJELLY] 进入休眠状态");
            }
        }

        /// <summary>
        /// 重置到原始状态
        /// </summary>
        private void ResetToOriginalState()
        {
            currentOffset = Vector3.zero;
            currentScale = Vector3.one;
            currentRotation = Vector3.zero;
            currentIntensity = 0f;
            currentDirection = Vector3.zero;
            currentSquash = 1f;

            // 重置Transform
            transform.position = originalPosition;
            transform.localScale = originalScale;
            transform.rotation = originalRotation;
        }

        /// <summary>
        /// 更新所有果冻效果
        /// </summary>
        private void UpdateJellyEffects()
        {
            // 清理已完成的效果
            for (int i = activeEffects.Count - 1; i >= 0; i--)
            {
                if (activeEffects[i].IsFinished())
                {
                    activeEffects.RemoveAt(i);
                }
            }

            // 重置累计值
            Vector3 totalOffset = Vector3.zero;
            Vector3 totalRotation = Vector3.zero;
            Vector3 totalScale = Vector3.one;
            float totalIntensity = 0f;
            Vector3 totalDirection = Vector3.zero;
            float totalSquash = 1f;

            // 计算所有活跃效果
            foreach (var effect in activeEffects)
            {
                CalculateEffectContribution(effect, ref totalOffset, ref totalRotation,
                    ref totalScale, ref totalIntensity, ref totalDirection, ref totalSquash);
            }

            // 应用计算结果
            ApplyEffectResult(totalOffset, totalRotation, totalScale, totalIntensity, totalDirection, totalSquash);
        }

        /// <summary>
        /// 计算单个效果的贡献
        /// </summary>
        private void CalculateEffectContribution(JellyEffectData effect, ref Vector3 totalOffset,
            ref Vector3 totalRotation, ref Vector3 totalScale, ref float totalIntensity,
            ref Vector3 totalDirection, ref float totalSquash)
        {
            float progress = effect.GetProgress();

            // 基础衰减曲线（弹性衰减）
            float dampedProgress = Mathf.Exp(-settings.damping * progress);
            float effectIntensity = effect.intensity * dampedProgress;

            switch (effect.effectType)
            {
                case JellyEffectType.TargetSwing:
                    CalculateTargetSwingEffect(effect, progress, effectIntensity,
                        ref totalOffset, ref totalRotation, ref totalIntensity, ref totalDirection);
                    break;

                case JellyEffectType.BrakeRebound:
                    CalculateBrakeReboundEffect(effect, progress, effectIntensity,
                        ref totalOffset, ref totalRotation);
                    break;

                case JellyEffectType.SpreadSwing:
                    CalculateSpreadSwingEffect(effect, progress, effectIntensity,
                        ref totalOffset, ref totalRotation);
                    break;
            }
        }

        /// <summary>
        /// 计算目标甩动效果
        /// </summary>
        private void CalculateTargetSwingEffect(JellyEffectData effect, float progress, float effectIntensity,
            ref Vector3 totalOffset, ref Vector3 totalRotation, ref float totalIntensity, ref Vector3 totalDirection)
        {
            // 振荡效果 - 先甩出去再回弹
            float oscillation = Mathf.Sin(progress * Mathf.PI * settings.elasticity) * effectIntensity;

            // 位移效果
            Vector3 swingOffset = effect.direction * oscillation * settings.swingMaxIntensity;
            totalOffset += swingOffset;

            // 旋转效果（基于甩动方向）
            Vector3 rotationAxis = Vector3.Cross(Vector3.up, effect.direction);
            float rotationAmount = oscillation * 15f; // 最大15度旋转
            totalRotation += rotationAxis * rotationAmount;

            // 更新总体强度和方向
            totalIntensity += effectIntensity;
            totalDirection += effect.direction * effectIntensity;
        }

        /// <summary>
        /// 计算刹车回弹效果
        /// </summary>
        private void CalculateBrakeReboundEffect(JellyEffectData effect, float progress, float effectIntensity,
            ref Vector3 totalOffset, ref Vector3 totalRotation)
        {
            // 刹车回弹曲线：快速前冲，然后急刹
            float brakeCurve = Mathf.Sin(progress * Mathf.PI) * (1f - progress * 0.8f);
            Vector3 brakeOffset = effect.direction * brakeCurve * effectIntensity * settings.brakeReboundIntensity;
            totalOffset += brakeOffset;

            // 轻微的震颤旋转
            Vector3 shakeRotation = UnityEngine.Random.insideUnitSphere * brakeCurve * effectIntensity * 5f;
            totalRotation += shakeRotation;
        }

        /// <summary>
        /// 计算扩散甩动效果
        /// </summary>
        private void CalculateSpreadSwingEffect(JellyEffectData effect, float progress, float effectIntensity,
            ref Vector3 totalOffset, ref Vector3 totalRotation)
        {
            // 扩散波浪效果
            float waveValue = Mathf.Sin(progress * Mathf.PI * 2f) * Mathf.Exp(-progress * 2f);
            Vector3 spreadOffset = effect.direction * waveValue * effectIntensity * settings.spreadSwingIntensity;
            totalOffset += spreadOffset;

            // 扩散旋转
            Vector3 spreadRotation = Vector3.Cross(effect.direction, Vector3.up) * waveValue * effectIntensity * 10f;
            totalRotation += spreadRotation;
        }

        /// <summary>
        /// 应用计算结果到Transform和Shader
        /// </summary>
        private void ApplyEffectResult(Vector3 offset, Vector3 rotation, Vector3 scale,
            float intensity, Vector3 direction, float squash)
        {
            // 应用刚性限制
            offset *= (1f / settings.rigidity);
            rotation *= (1f / settings.rigidity);

            // 更新当前状态
            currentOffset = offset;
            currentScale = new Vector3(scale.x, scale.y * squash, scale.z);
            currentRotation = rotation;
            currentIntensity = intensity;
            currentDirection = direction.normalized;
            currentSquash = squash;

            // 应用到Transform
            transform.position = originalPosition + offset;
            transform.localScale = Vector3.Scale(originalScale, currentScale);
            transform.rotation = originalRotation * Quaternion.Euler(rotation);
        }

        /// <summary>
        /// 更新Shader属性
        /// </summary>
        private void UpdateShaderProperties()
        {
            if (targetRenderer == null || materialPropertyBlock == null)
                return;

            targetRenderer.GetPropertyBlock(materialPropertyBlock);

            // 设置果冻效果是否启用
            materialPropertyBlock.SetFloat(ShaderID_JellyEnabled, isSleeping ? 0f : 1f);

            // 设置效果参数
            materialPropertyBlock.SetVector(ShaderID_JellyOffset, currentOffset);
            materialPropertyBlock.SetVector(ShaderID_JellyScale, currentScale);
            materialPropertyBlock.SetVector(ShaderID_JellyRotation, currentRotation);
            materialPropertyBlock.SetFloat(ShaderID_JellyIntensity, currentIntensity);
            materialPropertyBlock.SetVector(ShaderID_JellyDirection, currentDirection);
            materialPropertyBlock.SetFloat(ShaderID_JellySquash, currentSquash);

            targetRenderer.SetPropertyBlock(materialPropertyBlock);
        }

        #endregion

    }
}