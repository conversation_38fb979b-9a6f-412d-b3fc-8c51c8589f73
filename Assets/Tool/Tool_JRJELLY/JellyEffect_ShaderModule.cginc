// ================================================================
// Tool_JRJELLY 通用果冻效果模块
// 可以轻松复制到其他Shader项目中
// 使用方法：#include "JellyEffect_ShaderModule.cginc"
// ================================================================

#ifndef JELLY_EFFECT_INCLUDED
#define JELLY_EFFECT_INCLUDED

// === 果冻效果属性声明 ===
// 在你的Shader Properties中添加以下属性：
/*
[Header(果冻效果 - JELLY EFFECT)]
[HideInInspector] _JellyEnabled ("Jelly Enabled", Float) = 0
[HideInInspector] _JellyIntensity ("Jelly Intensity", Float) = 0
[HideInInspector] _JellyDirection ("Jelly Direction", Vector) = (0,0,0,0)
[HideInInspector] _JellySquash ("Jelly Squash", Float) = 1
[HideInInspector] _JellyOffset ("Jelly Offset", Vector) = (0,0,0,0)
*/

// === 果冻效果变量声明 ===
float _JellyEnabled;
float _JellyIntensity;
float4 _JellyDirection;
float _JellySquash;
float4 _JellyOffset;

// === 果冻效果函数 ===

/// <summary>
/// 应用果冻顶点变形效果
/// 在vertex函数中调用此函数
/// </summary>
/// <param name="vertex">顶点位置（模型空间）</param>
/// <param name="normal">顶点法线</param>
void ApplyJellyVertexEffect(inout float4 vertex, inout float3 normal)
{
    // 如果果冻效果未启用，直接返回
    if (_JellyEnabled < 0.5)
        return;
    
    // === 基础参数计算 ===
    
    // 标准化高度因子，更温和的影响
    float heightFactor = saturate(vertex.y); // 根据模型调整这个系数
    
    // 计算弯曲强度（底部弱，顶部强），使用线性曲线保持刚性
    float bendStrength = heightFactor * 0.8; // 线性曲线，减少夸张效果
    
    // === 压扁效果 ===
    // Y轴缩放，模拟压扁
    vertex.y *= _JellySquash;
    
    // === 甩动弯曲效果 ===
    if (_JellyIntensity > 0.01)
    {
        // 计算弯曲位移 - 支持三轴方向，Y轴使用较小的强度
        float3 bendOffset = float3(
            _JellyDirection.x * _JellyIntensity * bendStrength * 0.15,     // X轴弯曲
            _JellyDirection.y * _JellyIntensity * bendStrength * 0.075,    // Y轴弯曲（减半强度）
            _JellyDirection.z * _JellyIntensity * bendStrength * 0.15      // Z轴弯曲
        );
        
        // 添加细微的扭曲效果（可选）
        if (_JellyIntensity > 0.1)
        {
            float twistAmount = _JellyIntensity * heightFactor * 0.1;
            float cosA = cos(twistAmount);
            float sinA = sin(twistAmount);
            
            float newX = vertex.x * cosA - vertex.z * sinA;
            float newZ = vertex.x * sinA + vertex.z * cosA;
            
            vertex.x = newX;
            vertex.z = newZ;
        }
        
        // 应用弯曲位移
        vertex.xyz += bendOffset;
        
        // 调整法线以保持正确的光照 - 支持三轴方向，Y轴使用较小的强度
        float3 normalAdjust = float3(
            _JellyDirection.x * _JellyIntensity * bendStrength * 0.05,     // X轴法线调整
            _JellyDirection.y * _JellyIntensity * bendStrength * 0.025,    // Y轴法线调整（减半）
            _JellyDirection.z * _JellyIntensity * bendStrength * 0.05      // Z轴法线调整
        );
        normal = normalize(normal + normalAdjust);
    }
    
    // === 额外的整体偏移 ===
    // 如果需要整体移动效果，取消下面的注释
    // vertex.xyz += _JellyOffset.xyz;
}

/// <summary>
/// 简化版果冻效果（适用于简单需求）
/// </summary>
/// <param name="vertex">顶点位置</param>
void ApplySimpleJellyEffect(inout float4 vertex)
{
    if (_JellyEnabled < 0.5)
        return;
    
    float heightFactor = saturate(vertex.y);
    
    // 压扁
    vertex.y *= _JellySquash;
    
    // 简单弯曲
    if (_JellyIntensity > 0.01)
    {
        float3 bend = _JellyDirection.xyz * _JellyIntensity * heightFactor * heightFactor * 0.3;
        vertex.xyz += bend;
    }
}

/// <summary>
/// 高级果冻效果（更真实的物理模拟）
/// </summary>
/// <param name="vertex">顶点位置</param>
/// <param name="normal">顶点法线</param>
/// <param name="uv">UV坐标（可选，用于更复杂的变形）</param>
void ApplyAdvancedJellyEffect(inout float4 vertex, inout float3 normal, float2 uv)
{
    if (_JellyEnabled < 0.5)
        return;
    
    // 高度因子（使用更复杂的曲线）
    float heightFactor = vertex.y;
    float heightCurve = 1.0 - exp(-heightFactor * 2.0); // 指数曲线
    
    // 径向距离因子（从中心向外的影响）
    float radialDist = length(vertex.xz);
    float radialFactor = saturate(1.0 - radialDist * 0.5);
    
    // 组合影响因子
    float totalInfluence = heightCurve * radialFactor;
    
    // === 压扁效果 ===
    vertex.y *= _JellySquash;
    
    // === 高级弯曲效果 ===
    if (_JellyIntensity > 0.01)
    {
        // 主弯曲
        float3 primaryBend = _JellyDirection.xyz * _JellyIntensity * totalInfluence * 0.4;
        
        // 次级波动（添加更自然的变形）
        float wavePhase = uv.x * 3.14159 + uv.y * 1.57;
        float wave = sin(wavePhase) * _JellyIntensity * 0.1;
        float3 secondaryBend = cross(_JellyDirection.xyz, float3(0, 1, 0)) * wave * heightFactor;
        
        // 扭曲效果
        float twistAngle = _JellyIntensity * heightFactor * 0.2;
        float cosT = cos(twistAngle);
        float sinT = sin(twistAngle);
        
        float2 twisted = float2(
            vertex.x * cosT - vertex.z * sinT,
            vertex.x * sinT + vertex.z * cosT
        );
        vertex.xz = lerp(vertex.xz, twisted, totalInfluence);
        
        // 应用所有弯曲效果
        vertex.xyz += primaryBend + secondaryBend;
        
        // 高级法线调整
        float3 tangent = normalize(cross(normal, _JellyDirection.xyz));
        float3 bitangent = normalize(cross(normal, tangent));
        
        float normalBendAmount = _JellyIntensity * totalInfluence * 0.3;
        normal = normalize(normal + tangent * normalBendAmount);
    }
}

/// <summary>
/// 果冻效果工具函数 - 获取弯曲强度
/// </summary>
float GetJellyBendStrength(float height, float maxHeight)
{
    float normalizedHeight = height / maxHeight;
    return normalizedHeight * normalizedHeight; // 二次曲线
}

/// <summary>
/// 果冻效果工具函数 - 应用扭曲
/// </summary>
float3 ApplyJellyTwist(float3 position, float intensity, float heightFactor)
{
    float angle = intensity * heightFactor * 0.15;
    float cosA = cos(angle);
    float sinA = sin(angle);
    
    return float3(
        position.x * cosA - position.z * sinA,
        position.y,
        position.x * sinA + position.z * cosA
    );
}

// === 使用示例 ===
/*
// 在你的vertex函数中：
void vert(inout appdata_full v, out Input o)
{
    UNITY_INITIALIZE_OUTPUT(Input, o);
    
    // 应用果冻效果
    ApplyJellyVertexEffect(v.vertex, v.normal);
    
    // 其他顶点处理...
}

// 或者使用简化版本：
void vert_simple(inout appdata_full v, out Input o)
{
    UNITY_INITIALIZE_OUTPUT(Input, o);
    
    // 简单果冻效果
    ApplySimpleJellyEffect(v.vertex);
}
*/

#endif // JELLY_EFFECT_INCLUDED 