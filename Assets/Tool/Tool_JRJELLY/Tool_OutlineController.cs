using UnityEngine;

/// <summary>
/// Tool_OutlineController - 果冻效果描边控制器
/// 用于运行时控制Tool_JRJELLY_Shader的描边效果
/// 可复制到其他项目使用
/// </summary>
[System.Serializable]
public class OutlineSettings
{
    [Header("描边设置 - Outline Settings")]
    [Tooltip("启用描边效果")]
    public bool enableOutline = true;

    [Toolt<PERSON>("描边颜色")]
    public Color outlineColor = Color.black;

    [Tooltip("描边宽度")]
    [Range(0f, 0.005f)]
    public float outlineWidth = 0.001f;

    [Tooltip("描边透明度")]
    [Range(0f, 1f)]
    public float outlineAlpha = 1.0f;
}

public class Tool_OutlineController : MonoBehaviour
{
    [Header("目标渲染器 - Target Renderer")]
    [Tooltip("要应用描边效果的渲染器，如果为空则自动获取")]
    public Renderer targetRenderer;

    [Header("描边配置 - Outline Configuration")]
    public OutlineSettings outlineSettings = new OutlineSettings();

    [Header("运行时控制 - Runtime Control")]
    [Tooltip("在Update中实时应用设置")]
    public bool applyInUpdate = true;

    // 材质实例
    private Material materialInstance;

    // Shader属性ID缓存 - 提高性能
    private static readonly int OutlineEnabledID = Shader.PropertyToID("_OutlineEnabled");
    private static readonly int OutlineColorID = Shader.PropertyToID("_OutlineColor");
    private static readonly int OutlineWidthID = Shader.PropertyToID("_OutlineWidth");
    private static readonly int OutlineAlphaID = Shader.PropertyToID("_OutlineAlpha");

    void Start()
    {
        InitializeController();
        ApplyOutlineSettings();
    }

    void Update()
    {
        if (applyInUpdate)
        {
            ApplyOutlineSettings();
        }
    }

    /// <summary>
    /// 初始化控制器
    /// </summary>
    private void InitializeController()
    {
        // 如果没有指定目标渲染器，自动获取
        if (targetRenderer == null)
        {
            targetRenderer = GetComponent<Renderer>();
        }

        if (targetRenderer == null)
        {
            Debug.LogError($"[Tool_OutlineController] 在 {gameObject.name} 上找不到Renderer组件！");
            return;
        }

        // 创建材质实例以避免影响其他对象
        if (materialInstance == null && targetRenderer.material != null)
        {
            materialInstance = new Material(targetRenderer.material);
            targetRenderer.material = materialInstance;
        }

        // 验证Shader是否支持描边
        if (materialInstance != null && !materialInstance.HasProperty(OutlineEnabledID))
        {
            Debug.LogWarning($"[Tool_OutlineController] 材质 {materialInstance.name} 不支持描边效果！请使用Tool_JRJELLY/Vertex Colors Specular Jelly Shader。");
        }
    }

    /// <summary>
    /// 应用描边设置到材质
    /// </summary>
    public void ApplyOutlineSettings()
    {
        if (materialInstance == null) return;

        // 应用描边设置
        materialInstance.SetFloat(OutlineEnabledID, outlineSettings.enableOutline ? 1.0f : 0.0f);
        materialInstance.SetColor(OutlineColorID, outlineSettings.outlineColor);
        materialInstance.SetFloat(OutlineWidthID, outlineSettings.outlineWidth);
        materialInstance.SetFloat(OutlineAlphaID, outlineSettings.outlineAlpha);
    }

    /// <summary>
    /// 启用描边
    /// </summary>
    public void EnableOutline()
    {
        outlineSettings.enableOutline = true;
        ApplyOutlineSettings();
    }

    /// <summary>
    /// 禁用描边
    /// </summary>
    public void DisableOutline()
    {
        outlineSettings.enableOutline = false;
        ApplyOutlineSettings();
    }

    /// <summary>
    /// 设置描边颜色
    /// </summary>
    /// <param name="color">描边颜色</param>
    public void SetOutlineColor(Color color)
    {
        outlineSettings.outlineColor = color;
        ApplyOutlineSettings();
    }

    /// <summary>
    /// 设置描边宽度
    /// </summary>
    /// <param name="width">描边宽度 (0-0.005)</param>
    public void SetOutlineWidth(float width)
    {
        outlineSettings.outlineWidth = Mathf.Clamp(width, 0f, 0.005f);
        ApplyOutlineSettings();
    }

    /// <summary>
    /// 设置描边透明度
    /// </summary>
    /// <param name="alpha">透明度 (0-1)</param>
    public void SetOutlineAlpha(float alpha)
    {
        outlineSettings.outlineAlpha = Mathf.Clamp01(alpha);
        ApplyOutlineSettings();
    }

    /// <summary>
    /// 批量设置描边参数
    /// </summary>
    /// <param name="enable">是否启用</param>
    /// <param name="color">颜色</param>
    /// <param name="width">宽度</param>
    /// <param name="alpha">透明度</param>
    public void SetOutlineSettings(bool enable, Color color, float width, float alpha)
    {
        outlineSettings.enableOutline = enable;
        outlineSettings.outlineColor = color;
        outlineSettings.outlineWidth = Mathf.Clamp(width, 0f, 0.005f);
        outlineSettings.outlineAlpha = Mathf.Clamp01(alpha);
        ApplyOutlineSettings();
    }

    /// <summary>
    /// 获取当前描边设置
    /// </summary>
    /// <returns>描边设置的副本</returns>
    public OutlineSettings GetOutlineSettings()
    {
        return new OutlineSettings
        {
            enableOutline = outlineSettings.enableOutline,
            outlineColor = outlineSettings.outlineColor,
            outlineWidth = outlineSettings.outlineWidth,
            outlineAlpha = outlineSettings.outlineAlpha
        };
    }

    void OnDestroy()
    {
        // 清理材质实例
        if (materialInstance != null)
        {
            if (Application.isPlaying)
            {
                Destroy(materialInstance);
            }
            else
            {
                DestroyImmediate(materialInstance);
            }
        }
    }

    void OnValidate()
    {
        // 在编辑器中修改参数时自动应用
        if (Application.isPlaying && materialInstance != null)
        {
            ApplyOutlineSettings();
        }
    }
}