// Tool_JRJELLY 果冻效果修复版Shader - 支持GPU Instancing
// 修复语法错误，正确使用表面着色器
// 可复制到其他项目使用的高性能果冻效果模块

Shader "Tool_JRJELLY/Basic"
{
	Properties
	{
	[TCP2HeaderHelp(SWITCHES, Function Switches)]
		//主要功能开关
		[Toggle(ENABLE_JELLY)] _EnableJelly ("启用果冻效果 (Enable Jelly Effect)", Float) = 1
		[Toggle(ENABLE_OUTLINE)] _EnableOutline ("启用描边效果 (Enable Outline)", Float) = 1
		[Toggle(ENABLE_COLOR_OVERLAY)] _EnableColorOverlay ("启用颜色膜层 (Enable Color Overlay)", Float) = 0
	[TCP2Separator]

	[TCP2HeaderHelp(BASE, Base Properties)]
		//TOONY COLORS
		_Color ("Color", Color) = (1,1,1,1)
		_HColor ("Highlight Color", Color) = (0.785,0.785,0.785,1.0)
		_SColor ("Shadow Color", Color) = (0.195,0.195,0.195,1.0)

		//DIFFUSE
		_MainTex ("Main Texture", 2D) = "white" {}
	[TCP2Separator]

		//TOONY COLORS RAMP
		[TCP2Header(RAMP SETTINGS)]
		_RampThreshold ("Ramp Threshold", Range(0,1)) = 0.5
		_RampSmooth ("Ramp Smoothing", Range(0.001,1)) = 0.1
	[TCP2Separator]

	[TCP2HeaderHelp(EMISSION, Emission)]
		[HDR] _EmissionColor ("Emission Color", Color) = (1,1,1,1.0)
	[TCP2Separator]

	[TCP2HeaderHelp(SPECULAR, Specular)]
		//SPECULAR
		_SpecColor ("Specular Color", Color) = (0.5, 0.5, 0.5, 1)
		_Smoothness ("Roughness", Range(0,1)) = 0.5
	[TCP2Separator]

	[TCP2HeaderHelp(RIM, Rim)]
		//RIM LIGHT
		_RimColor ("Rim Color", Color) = (0.8,0.8,0.8,0.6)
		_RimMin ("Rim Min", Range(0,2)) = 0.5
		_RimMax ("Rim Max", Range(0,2)) = 1.0
	[TCP2Separator]

	[TCP2HeaderHelp(OUTLINE, Outline Effect Settings)]
		//OUTLINE EFFECT - 纯色描边设置
		_OutlineColor ("Outline Color", Color) = (0,0,0,1)
		_OutlineWidth ("Outline Width", Range(0,0.3)) = 0.001
		_OutlineAlpha ("Outline Alpha", Range(0,1)) = 1.0
	[TCP2Separator]

	[TCP2HeaderHelp(COLOR_OVERLAY, Color Overlay Settings)]
		//COLOR OVERLAY - 颜色膜层设置
		_OverlayColor ("覆盖颜色 (Overlay Color)", Color) = (1,0,0,0.5)
		_OverlayAlpha ("覆盖透明度 (Overlay Alpha)", Range(0,1)) = 0.5
		_OverlayWidthScale ("膜层宽度比例 (Overlay Width Scale)", Range(0,1)) = 1.0
	[TCP2Separator]

		//Avoid compile error if the properties are ending with a drawer
		[HideInInspector] __dummy__ ("unused", Float) = 0
	}

	SubShader
	{
		Tags { "RenderType"="Opaque" "Queue"="Geometry" }

		// ================================================================
		// 描边Pass - 重新设计：使用深度缓冲区解决多物体重叠问题
		// ================================================================
		Pass
		{
			Name "OUTLINE"
			Tags { "LightMode" = "ForwardBase" }
			Blend SrcAlpha OneMinusSrcAlpha  // 启用透明混合
			ZWrite On  // 开启深度写入，让描边参与深度测试
			ZTest LEqual  // 深度测试：小于等于时通过
			Cull Front  // 只渲染背面，这样前面的物体会遮挡描边
			Offset 0, 0  // 不使用深度偏移

			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma shader_feature ENABLE_JELLY
			#pragma shader_feature ENABLE_OUTLINE
			
			// 启用GPU Instancing
			#pragma multi_compile_instancing
			#pragma instancing_options assumeuniformscaling
			
			#include "UnityCG.cginc"

			// 描边属性
			fixed4 _OutlineColor;
			float _OutlineWidth;
			float _OutlineAlpha;

			// GPU Instancing 果冻效果数据
			UNITY_INSTANCING_BUFFER_START(JellyProps)
				UNITY_DEFINE_INSTANCED_PROP(float, _JellyEnabled)
				UNITY_DEFINE_INSTANCED_PROP(float4, _JellyOffset)
				UNITY_DEFINE_INSTANCED_PROP(float4, _JellyScale)
				UNITY_DEFINE_INSTANCED_PROP(float, _JellyIntensity)
				UNITY_DEFINE_INSTANCED_PROP(float4, _JellyDirection)
				UNITY_DEFINE_INSTANCED_PROP(float, _JellySquash)
				UNITY_DEFINE_INSTANCED_PROP(float, _JellyBendStrength)
			UNITY_INSTANCING_BUFFER_END(JellyProps)

			struct appdata
			{
				float4 vertex : POSITION;
				float3 normal : NORMAL;
				float2 uv : TEXCOORD0;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct v2f
			{
				float4 pos : SV_POSITION;
				float2 uv : TEXCOORD0;
				fixed4 color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			// 应用果冻效果的函数 - 与主体Pass完全一致，支持三轴变形
			float4 ApplyJellyEffect(float4 vertex, float3 normal, out float3 modifiedNormal)
			{
				modifiedNormal = normal; // 默认保持原法线
				
				#ifndef ENABLE_JELLY
					return vertex;
				#endif
				
				// 检查果冻效果脚本控制开关
				float jellyEnabled = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyEnabled);
				if (jellyEnabled < 0.5) return vertex;
				
				// 获取实例化属性
				float jellyIntensity = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyIntensity);
				float4 jellyDirection = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyDirection);
				float jellySquash = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellySquash);
				float jellyBendStrength = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyBendStrength);
				
				// === 果冻效果计算 - 与主体Pass完全一致 ===
				
				// 1. 基础变形：基于顶点高度的倾斜效果
				float minY = -1.0;
				float maxY = 1.0;
				float heightFactor = saturate((vertex.y - minY) / (maxY - minY));
				float bendAmount = jellyIntensity * jellyBendStrength * 6.0;
				
				// 2. 压扁效果：Y轴缩放
				vertex.y *= jellySquash;
				
				// 3. 甩动效果：从底部到顶部的线性倾斜（支持三轴）
				if (jellyIntensity > 0.001)
				{
					float tiltAmount = heightFactor;
					// 支持完整的三轴变形，包括Y轴
					float3 tiltOffset = float3(
						jellyDirection.x * bendAmount * tiltAmount,           // X轴：线性倾斜
						jellyDirection.y * bendAmount * tiltAmount * 0.5,     // Y轴：减半强度，避免过度拉伸
						jellyDirection.z * bendAmount * tiltAmount            // Z轴：线性倾斜
					);
					vertex.xyz += tiltOffset;
					
					if (heightFactor < 0.1)
					{
						vertex.xyz += float3(
							jellyDirection.x * bendAmount * 0.05,     // X轴最小偏移
							jellyDirection.y * bendAmount * 0.025,    // Y轴最小偏移（减半）
							jellyDirection.z * bendAmount * 0.05      // Z轴最小偏移
						);
					}
				}
				
				// 4. 法线调整（支持三轴）
				if (jellyIntensity > 0.01)
				{
					// 法线调整适应三轴变形，Y轴使用较小的影响系数
					float3 normalAdjust = float3(
						jellyDirection.x * jellyIntensity * jellyBendStrength * 0.1,     // X轴法线调整
						jellyDirection.y * jellyIntensity * jellyBendStrength * 0.05,    // Y轴法线调整（减半）
						jellyDirection.z * jellyIntensity * jellyBendStrength * 0.1      // Z轴法线调整
					);
					modifiedNormal = normalize(normal + normalAdjust);
				}
				
				return vertex;
			}

			// 描边顶点函数 - 简单可靠的法线外推描边
			v2f vert(appdata v)
			{
				v2f o;
				
				// 设置GPU Instancing
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				// 应用果冻效果，同时获取修改后的法线
				float3 modifiedNormal;
				v.vertex = ApplyJellyEffect(v.vertex, v.normal, modifiedNormal);

				// === 简单可靠的描边算法：沿修改后的法线外推顶点 ===
				// 在对象空间中沿修改后的法线外推顶点
				float3 norm = normalize(modifiedNormal);
				float3 outlineVertex = v.vertex.xyz + norm * _OutlineWidth;
				
				o.pos = UnityObjectToClipPos(float4(outlineVertex, 1.0));
				o.uv = v.uv;
				
				// 设置描边颜色
				o.color = _OutlineColor;
				
				return o;
			}

			// 描边片段函数 - 简化透明度处理
			fixed4 frag(v2f i) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				
				#ifndef ENABLE_OUTLINE
					discard; // 如果描边未启用，丢弃所有像素
				#endif
				
				// 直接返回颜色，透明度由_OutlineAlpha控制
				fixed4 finalColor = i.color;
				finalColor.a *= _OutlineAlpha;  // 应用透明度参数
				
				// 如果透明度太低，丢弃像素
				if (finalColor.a < 0.01)
					discard;
				
				return finalColor;
			}
			ENDCG
		}

		// ================================================================
		// 主渲染Pass - 表面着色器，正常渲染物体
		// ================================================================
		CGPROGRAM
		#pragma surface surf ToonyColorsCustom fullforwardshadows addshadow exclude_path:deferred exclude_path:prepass vertex:vert
		#pragma target 3.0
		
		// 启用GPU Instancing - 关键优化点
		#pragma multi_compile_instancing
		#pragma instancing_options assumeuniformscaling
		
		// 功能开关
		#pragma shader_feature ENABLE_JELLY
		#pragma shader_feature ENABLE_OUTLINE
		#pragma shader_feature ENABLE_COLOR_OVERLAY

		//================================================================
		// GPU Instancing 果冻效果数据
		
		UNITY_INSTANCING_BUFFER_START(JellyProps)
			UNITY_DEFINE_INSTANCED_PROP(float, _JellyEnabled)
			UNITY_DEFINE_INSTANCED_PROP(float4, _JellyOffset)
			UNITY_DEFINE_INSTANCED_PROP(float4, _JellyScale)
			UNITY_DEFINE_INSTANCED_PROP(float, _JellyIntensity)
			UNITY_DEFINE_INSTANCED_PROP(float4, _JellyDirection)
			UNITY_DEFINE_INSTANCED_PROP(float, _JellySquash)
			UNITY_DEFINE_INSTANCED_PROP(float, _JellyBendStrength)
		UNITY_INSTANCING_BUFFER_END(JellyProps)

		//================================================================
		// 共享材质属性

		fixed4 _Color;
		sampler2D _MainTex;
		half4 _EmissionColor;
		fixed _Smoothness;
		fixed4 _RimColor;
		fixed _RimMin;
		fixed _RimMax;
		float4 _RimDir;

		// 描边属性
		fixed4 _OutlineColor;
		float _OutlineWidth;
		float _OutlineAlpha;

		// 膜层属性
		fixed4 _OverlayColor;
		float _OverlayAlpha;
		float _OverlayWidthScale;

		#define UV_MAINTEX uv_MainTex

		struct Input
		{
			half2 uv_MainTex;
			float3 viewDir;
			float4 color : COLOR;
			float3 worldPos;
			UNITY_VERTEX_INPUT_INSTANCE_ID  // 添加GPU Instancing支持
		};

		//================================================================
		// 优化的果冻效果顶点函数
		
		void vert(inout appdata_full v, out Input o)
		{
			UNITY_INITIALIZE_OUTPUT(Input, o);
			
			// 设置GPU Instancing
			UNITY_SETUP_INSTANCE_ID(v);
			UNITY_TRANSFER_INSTANCE_ID(v, o);
			
			#ifndef ENABLE_JELLY
				return;
			#endif
			
			// 检查当前实例的果冻效果开关
			float jellyEnabled = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyEnabled);
			if (jellyEnabled < 0.5)
				return;
			
			// 获取实例化属性
			float jellyIntensity = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyIntensity);
			float4 jellyDirection = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyDirection);
			float jellySquash = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellySquash);
			float jellyBendStrength = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyBendStrength);
			
			// === 果冻效果计算 ===
			
			// 1. 基础变形：基于顶点高度的倾斜效果
			float minY = -1.0;
			float maxY = 1.0;
			float heightFactor = saturate((v.vertex.y - minY) / (maxY - minY));
			float bendAmount = jellyIntensity * jellyBendStrength * 6.0;
			
			// 2. 压扁效果：Y轴缩放
			v.vertex.y *= jellySquash;
			
			// 3. 甩动效果：从底部到顶部的线性倾斜（支持三轴）
			if (jellyIntensity > 0.001)
			{
				float tiltAmount = heightFactor;
				// 支持完整的三轴变形，包括Y轴
				float3 tiltOffset = float3(
					jellyDirection.x * bendAmount * tiltAmount,           // X轴：线性倾斜
					jellyDirection.y * bendAmount * tiltAmount * 0.5,     // Y轴：减半强度，避免过度拉伸
					jellyDirection.z * bendAmount * tiltAmount            // Z轴：线性倾斜
				);
				v.vertex.xyz += tiltOffset;
				
				if (heightFactor < 0.1)
				{
					v.vertex.xyz += float3(
						jellyDirection.x * bendAmount * 0.05,     // X轴最小偏移
						jellyDirection.y * bendAmount * 0.025,    // Y轴最小偏移（减半）
						jellyDirection.z * bendAmount * 0.05      // Z轴最小偏移
					);
				}
			}
			
			// 4. 法线调整（支持三轴）
			if (jellyIntensity > 0.01)
			{
				// 法线调整适应三轴变形，Y轴使用较小的影响系数
				float3 normalAdjust = float3(
					jellyDirection.x * jellyIntensity * jellyBendStrength * 0.1,     // X轴法线调整
					jellyDirection.y * jellyIntensity * jellyBendStrength * 0.05,    // Y轴法线调整（减半）
					jellyDirection.z * jellyIntensity * jellyBendStrength * 0.1      // Z轴法线调整
				);
				v.normal = normalize(v.normal + normalAdjust);
			}
		}

		//================================================================
		// CUSTOM LIGHTING

		//Lighting-related variables
		fixed4 _HColor;
		fixed4 _SColor;
		half _RampThreshold;
		half _RampSmooth;

		//Specular help functions
		inline half3 SafeNormalize(half3 inVec)
		{
			half dp3 = max(0.001f, dot(inVec, inVec));
			return inVec * rsqrt(dp3);
		}

		inline half PercRoughnessToSpecPower(half roughness)
		{
			half sq = max(1e-4f, roughness*roughness);
			half n = (2.0 / sq) - 2.0;
			n = max(n, 1e-4f);
			return n;
		}
		
		inline half NDFBlinnPhong(half NdotH, half n)
		{
			half normTerm = (n + 2.0) * (0.5/UNITY_PI);
			half specTerm = pow (NdotH, n);
			return specTerm * normTerm;
		}

		//Custom SurfaceOutput
		struct SurfaceOutputCustom
		{
			half atten;
			fixed3 Albedo;
			fixed3 Normal;
			fixed3 Emission;
			half Specular;
			fixed Gloss;
			fixed Alpha;
			fixed Rim;
		};

		inline half4 LightingToonyColorsCustom (inout SurfaceOutputCustom s, half3 viewDir, UnityGI gi)
		{
		#define IN_NORMAL s.Normal
	
			half3 lightDir = gi.light.dir;
		#if defined(UNITY_PASS_FORWARDBASE)
			half3 lightColor = _LightColor0.rgb;
			half atten = s.atten;
		#else
			half3 lightColor = gi.light.color.rgb;
			half atten = 1;
		#endif

			IN_NORMAL = normalize(IN_NORMAL);
			fixed ndl = max(0, dot(IN_NORMAL, lightDir));
			#define NDL ndl

			#define		RAMP_THRESHOLD	_RampThreshold
			#define		RAMP_SMOOTH		_RampSmooth

			fixed3 ramp = smoothstep(RAMP_THRESHOLD - RAMP_SMOOTH*0.5, RAMP_THRESHOLD + RAMP_SMOOTH*0.5, NDL);
		#if !(POINT) && !(SPOT)
			ramp *= atten;
		#endif
		#if !defined(UNITY_PASS_FORWARDBASE)
			_SColor = fixed4(0,0,0,1);
		#endif
			_SColor = lerp(_HColor, _SColor, _SColor.a);
			ramp = lerp(_SColor.rgb, _HColor.rgb, ramp);
			
			//Specular: PBR Blinn-Phong
			half3 halfDir = SafeNormalize(lightDir + viewDir);
			half roughness = s.Specular*s.Specular;
			half nh = saturate(dot(IN_NORMAL, halfDir));
			half spec = NDFBlinnPhong(nh, PercRoughnessToSpecPower(roughness)) * s.Gloss;
			spec *= atten;
			
			fixed4 c;
			c.rgb = s.Albedo * lightColor.rgb * ramp;
		#if (POINT || SPOT)
			c.rgb *= atten;
		#endif

			#define SPEC_COLOR	_SpecColor.rgb
			c.rgb += lightColor.rgb * SPEC_COLOR * spec;
			c.a = s.Alpha;

		#ifdef UNITY_LIGHT_FUNCTION_APPLY_INDIRECT
			c.rgb += s.Albedo * gi.indirect.diffuse;
		#endif

			//Rim light mask
			c.rgb += ndl * lightColor.rgb * atten * s.Rim * _RimColor.rgb * _RimColor.a;

			return c;
		}

		void LightingToonyColorsCustom_GI(inout SurfaceOutputCustom s, UnityGIInput data, inout UnityGI gi)
		{
			gi = UnityGlobalIllumination(data, 1.0, s.Normal);
			s.atten = data.atten;
			gi.light.color = _LightColor0.rgb;
		}

		//================================================================
		// SURFACE FUNCTION

	#define vcolors IN.color

		void surf(Input IN, inout SurfaceOutputCustom o)
		{
			// 设置GPU Instancing
			UNITY_SETUP_INSTANCE_ID(IN);
			
			fixed4 mainTex = tex2D(_MainTex, IN.UV_MAINTEX);

			//Vertex Colors
			float4 vertexColors = IN.color;
		#if UNITY_VERSION >= 550
		  #ifndef UNITY_COLORSPACE_GAMMA
			vertexColors.rgb = GammaToLinearSpace(vertexColors.rgb);
		  #endif
		#else
			vertexColors.rgb = IsGammaSpace() ? vertexColors.rgb : GammaToLinearSpace(vertexColors.rgb);
		#endif
			mainTex *= vertexColors;
			o.Albedo = mainTex.rgb * _Color.rgb;
			o.Alpha = mainTex.a * _Color.a;

			//Specular
			_Smoothness *= mainTex.a;
			_Smoothness = 1 - _Smoothness;
			o.Gloss = 1;
			o.Specular = _Smoothness;

			//Rim
			float3 viewDir = normalize(IN.viewDir);
			half rim = 1.0f - saturate( dot(viewDir, o.Normal) );
			rim = smoothstep(_RimMin, _RimMax, rim);
			o.Rim = rim;

			//Emission
			half3 emissiveColor = half3(1,1,1);
			emissiveColor *= mainTex.rgb * vcolors.a;
			emissiveColor *= _EmissionColor.rgb * _EmissionColor.a;
			o.Emission += emissiveColor;
		}

		ENDCG

		// ================================================================
		// 颜色膜层Pass - COLOR OVERLAY PASS (显示在所有Pass之上)
		// ================================================================
		Pass
		{
			Name "COLOR_OVERLAY"
			Tags { "LightMode" = "Always" }
			Blend SrcAlpha OneMinusSrcAlpha
			ZWrite Off
			ZTest LEqual
			Cull Off
			Offset -1, -1  // 添加深度偏移，避免Z-fighting

			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma shader_feature ENABLE_COLOR_OVERLAY
			#pragma shader_feature ENABLE_JELLY
			
			// 启用GPU Instancing
			#pragma multi_compile_instancing
			#pragma instancing_options assumeuniformscaling
			
			#include "UnityCG.cginc"

			// 颜色膜层属性 - Color Overlay Properties
			fixed4 _OverlayColor;
			float _OverlayAlpha;
			float _OverlayWidthScale;

			// 描边属性 - 需要与描边保持一致的大小
			float _OutlineWidth;

			// GPU Instancing 果冻效果数据
			UNITY_INSTANCING_BUFFER_START(JellyProps)
				UNITY_DEFINE_INSTANCED_PROP(float, _JellyEnabled)
				UNITY_DEFINE_INSTANCED_PROP(float4, _JellyOffset)
				UNITY_DEFINE_INSTANCED_PROP(float4, _JellyScale)
				UNITY_DEFINE_INSTANCED_PROP(float, _JellyIntensity)
				UNITY_DEFINE_INSTANCED_PROP(float4, _JellyDirection)
				UNITY_DEFINE_INSTANCED_PROP(float, _JellySquash)
				UNITY_DEFINE_INSTANCED_PROP(float, _JellyBendStrength)
			UNITY_INSTANCING_BUFFER_END(JellyProps)

			struct appdata
			{
				float4 vertex : POSITION;
				float3 normal : NORMAL;
				float2 uv : TEXCOORD0;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct v2f
			{
				float4 pos : SV_POSITION;
				float2 uv : TEXCOORD0;
				fixed4 color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			// 应用果冻效果的函数 - 与主体Pass完全一致，支持三轴变形
			float4 ApplyJellyEffect(float4 vertex, float3 normal, out float3 modifiedNormal)
			{
				modifiedNormal = normal; // 默认保持原法线
				
				#ifndef ENABLE_JELLY
					return vertex;
				#endif
				
				// 检查果冻效果脚本控制开关
				float jellyEnabled = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyEnabled);
				if (jellyEnabled < 0.5) return vertex;
				
				// 获取实例化属性
				float jellyIntensity = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyIntensity);
				float4 jellyDirection = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyDirection);
				float jellySquash = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellySquash);
				float jellyBendStrength = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyBendStrength);
				
				// === 果冻效果计算 - 与主体Pass完全一致 ===
				
				// 1. 基础变形：基于顶点高度的倾斜效果
				float minY = -1.0;
				float maxY = 1.0;
				float heightFactor = saturate((vertex.y - minY) / (maxY - minY));
				float bendAmount = jellyIntensity * jellyBendStrength * 6.0;
				
				// 2. 压扁效果：Y轴缩放
				vertex.y *= jellySquash;
				
				// 3. 甩动效果：从底部到顶部的线性倾斜（支持三轴）
				if (jellyIntensity > 0.001)
				{
					float tiltAmount = heightFactor;
					// 支持完整的三轴变形，包括Y轴
					float3 tiltOffset = float3(
						jellyDirection.x * bendAmount * tiltAmount,           // X轴：线性倾斜
						jellyDirection.y * bendAmount * tiltAmount * 0.5,     // Y轴：减半强度，避免过度拉伸
						jellyDirection.z * bendAmount * tiltAmount            // Z轴：线性倾斜
					);
					vertex.xyz += tiltOffset;
					
					if (heightFactor < 0.1)
					{
						vertex.xyz += float3(
							jellyDirection.x * bendAmount * 0.05,     // X轴最小偏移
							jellyDirection.y * bendAmount * 0.025,    // Y轴最小偏移（减半）
							jellyDirection.z * bendAmount * 0.05      // Z轴最小偏移
						);
					}
				}
				
				// 4. 法线调整（支持三轴）
				if (jellyIntensity > 0.01)
				{
					// 法线调整适应三轴变形，Y轴使用较小的影响系数
					float3 normalAdjust = float3(
						jellyDirection.x * jellyIntensity * jellyBendStrength * 0.1,     // X轴法线调整
						jellyDirection.y * jellyIntensity * jellyBendStrength * 0.05,    // Y轴法线调整（减半）
						jellyDirection.z * jellyIntensity * jellyBendStrength * 0.1      // Z轴法线调整
					);
					modifiedNormal = normalize(normal + normalAdjust);
				}
				
				return vertex;
			}

			// 颜色膜层顶点函数 - Color Overlay Vertex Function
			v2f vert(appdata v)
			{
				v2f o;
				
				// 设置GPU Instancing
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				// 应用果冻效果，同时获取修改后的法线
				float3 modifiedNormal;
				v.vertex = ApplyJellyEffect(v.vertex, v.normal, modifiedNormal);

				// === 与描边Pass完全一致的膜层算法：确保厚度精确匹配 ===
				// 使用与描边相同的对象空间法线外推方法
				float3 norm = normalize(modifiedNormal);
				float overlayThickness = _OutlineWidth * _OverlayWidthScale;
				
				// 添加微小的额外厚度，确保完全覆盖描边（解决浮点精度问题）
				float extraThickness = overlayThickness * 0.01; // 增加1%的厚度
				float totalThickness = overlayThickness + extraThickness;
				
				// 在对象空间中沿法线外推，与描边Pass完全一致的计算方式
				float3 overlayVertex = v.vertex.xyz + norm * totalThickness;
				
				o.pos = UnityObjectToClipPos(float4(overlayVertex, 1.0));
				o.uv = v.uv;
				
				// 设置膜层颜色和透明度
				o.color = _OverlayColor;
				o.color.a *= _OverlayAlpha;
				
				return o;
			}

			// 颜色膜层片段函数 - Color Overlay Fragment Function
			fixed4 frag(v2f i) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				
				#ifndef ENABLE_COLOR_OVERLAY
					discard; // 如果膜层未启用，丢弃所有像素
				#endif
				
				return i.color;
			}
			ENDCG
		}
	}

	Fallback "Diffuse"
	CustomEditor "TCP2_MaterialInspector_SG"
} 