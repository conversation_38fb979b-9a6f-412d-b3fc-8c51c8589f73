using UnityEngine;

/// <summary>
/// Tool_OutlineDebug - 描边效果调试工具
/// 用于快速测试和调试描边效果
/// </summary>
public class Tool_OutlineDebug : MonoBehaviour
{
    [Header("调试设置")]
    [Tooltip("目标渲染器")]
    public Renderer targetRenderer;

    [Header("描边测试参数")]
    [Tooltip("启用描边")]
    public bool enableOutline = true;

    [Toolt<PERSON>("描边颜色")]
    public Color outlineColor = Color.red;

    [Tooltip("描边宽度")]
    [Range(0f, 0.005f)]
    public float outlineWidth = 0.001f;

    [Tooltip("描边透明度")]
    [Range(0f, 1f)]
    public float outlineAlpha = 1.0f;

    [Header("实时调试")]
    [Tooltip("实时应用设置")]
    public bool applyRealtime = true;

    // 材质实例
    private Material materialInstance;

    // Shader属性ID
    private static readonly int OutlineEnabledID = Shader.PropertyToID("_OutlineEnabled");
    private static readonly int OutlineColorID = Shader.PropertyToID("_OutlineColor");
    private static readonly int OutlineWidthID = Shader.PropertyToID("_OutlineWidth");
    private static readonly int OutlineAlphaID = Shader.PropertyToID("_OutlineAlpha");

    void Start()
    {
        // 获取渲染器
        if (targetRenderer == null)
        {
            targetRenderer = GetComponent<Renderer>();
        }

        if (targetRenderer == null)
        {
            Debug.LogError("[Tool_OutlineDebug] 找不到Renderer组件！");
            return;
        }

        // 创建材质实例
        if (targetRenderer.material != null)
        {
            materialInstance = new Material(targetRenderer.material);
            targetRenderer.material = materialInstance;

            Debug.Log($"[Tool_OutlineDebug] 材质实例已创建: {materialInstance.name}");
            Debug.Log($"[Tool_OutlineDebug] 使用Shader: {materialInstance.shader.name}");

            // 检查Shader属性
            CheckShaderProperties();
        }

        // 应用初始设置
        ApplyOutlineSettings();
    }

    void Update()
    {
        if (applyRealtime)
        {
            ApplyOutlineSettings();
        }
    }

    /// <summary>
    /// 检查Shader是否支持描边属性
    /// </summary>
    void CheckShaderProperties()
    {
        if (materialInstance == null) return;

        bool hasOutlineEnabled = materialInstance.HasProperty(OutlineEnabledID);
        bool hasOutlineColor = materialInstance.HasProperty(OutlineColorID);
        bool hasOutlineWidth = materialInstance.HasProperty(OutlineWidthID);
        bool hasOutlineAlpha = materialInstance.HasProperty(OutlineAlphaID);

        Debug.Log($"[Tool_OutlineDebug] Shader属性检查:");
        Debug.Log($"  _OutlineEnabled: {hasOutlineEnabled}");
        Debug.Log($"  _OutlineColor: {hasOutlineColor}");
        Debug.Log($"  _OutlineWidth: {hasOutlineWidth}");
        Debug.Log($"  _OutlineAlpha: {hasOutlineAlpha}");

        if (!hasOutlineEnabled || !hasOutlineColor || !hasOutlineWidth || !hasOutlineAlpha)
        {
            Debug.LogWarning("[Tool_OutlineDebug] 材质不支持描边效果！请确保使用Tool_JRJELLY/Vertex Colors Specular Jelly Shader。");
        }
        else
        {
            Debug.Log("[Tool_OutlineDebug] 材质支持描边效果！");
        }
    }

    /// <summary>
    /// 应用描边设置
    /// </summary>
    void ApplyOutlineSettings()
    {
        if (materialInstance == null) return;

        // 设置描边属性
        materialInstance.SetFloat(OutlineEnabledID, enableOutline ? 1.0f : 0.0f);
        materialInstance.SetColor(OutlineColorID, outlineColor);
        materialInstance.SetFloat(OutlineWidthID, outlineWidth);
        materialInstance.SetFloat(OutlineAlphaID, outlineAlpha);

        // 输出当前设置（仅在值改变时）
        if (Time.frameCount % 60 == 0) // 每秒输出一次
        {
            Debug.Log($"[Tool_OutlineDebug] 当前设置: 启用={enableOutline}, 颜色={outlineColor}, 宽度={outlineWidth:F3}, 透明度={outlineAlpha:F2}");
        }
    }

    /// <summary>
    /// 强制刷新描边设置
    /// </summary>
    [ContextMenu("强制刷新描边")]
    public void ForceRefresh()
    {
        ApplyOutlineSettings();
        Debug.Log("[Tool_OutlineDebug] 强制刷新描边设置完成！");
    }

    /// <summary>
    /// 测试不同的描边宽度
    /// </summary>
    [ContextMenu("测试描边宽度")]
    public void TestOutlineWidths()
    {
        StartCoroutine(TestWidthCoroutine());
    }

    System.Collections.IEnumerator TestWidthCoroutine()
    {
        float[] testWidths = { 0.0005f, 0.001f, 0.0015f, 0.002f, 0.003f };

        foreach (float width in testWidths)
        {
            outlineWidth = width;
            ApplyOutlineSettings();
            Debug.Log($"[Tool_OutlineDebug] 测试宽度: {width:F3}");
            yield return new WaitForSeconds(1f);
        }

        Debug.Log("[Tool_OutlineDebug] 描边宽度测试完成！");
    }

    /// <summary>
    /// 测试不同的描边颜色
    /// </summary>
    [ContextMenu("测试描边颜色")]
    public void TestOutlineColors()
    {
        StartCoroutine(TestColorCoroutine());
    }

    System.Collections.IEnumerator TestColorCoroutine()
    {
        Color[] testColors = { Color.red, Color.green, Color.blue, Color.yellow, Color.magenta, Color.cyan, Color.white, Color.black };

        foreach (Color color in testColors)
        {
            outlineColor = color;
            ApplyOutlineSettings();
            Debug.Log($"[Tool_OutlineDebug] 测试颜色: {color}");
            yield return new WaitForSeconds(0.8f);
        }

        Debug.Log("[Tool_OutlineDebug] 描边颜色测试完成！");
    }

    void OnDestroy()
    {
        // 清理材质实例
        if (materialInstance != null)
        {
            if (Application.isPlaying)
            {
                Destroy(materialInstance);
            }
            else
            {
                DestroyImmediate(materialInstance);
            }
        }
    }

    void OnValidate()
    {
        // 在编辑器中修改参数时自动应用
        if (Application.isPlaying && materialInstance != null)
        {
            ApplyOutlineSettings();
        }
    }

    // GUI调试界面
    void OnGUI()
    {
        if (!Debug.isDebugBuild) return;

        GUILayout.BeginArea(new Rect(Screen.width - 220, 10, 200, 200));
        GUILayout.Label("描边调试工具", GUI.skin.box);

        if (GUILayout.Button("强制刷新"))
        {
            ForceRefresh();
        }

        if (GUILayout.Button("测试宽度"))
        {
            TestOutlineWidths();
        }

        if (GUILayout.Button("测试颜色"))
        {
            TestOutlineColors();
        }

        if (GUILayout.Button("切换启用状态"))
        {
            enableOutline = !enableOutline;
            ApplyOutlineSettings();
        }

        GUILayout.Label($"当前状态: {(enableOutline ? "启用" : "禁用")}");
        GUILayout.Label($"宽度: {outlineWidth:F3}");
        GUILayout.Label($"颜色: {outlineColor}");

        GUILayout.EndArea();
    }
}