using UnityEngine;

namespace JRJelly
{
    /// <summary>
    /// 果冻效果修复测试脚本
    /// 专门测试回弹跳帧和弯曲效果问题
    /// </summary>
    public class Tool_JRJELLY_TestFix : MonoBehaviour
    {
        [Header("=== 测试目标 ===")]
        public Tool_JRJELLY_Simple jellyController;

        [Header("=== 回弹测试 ===")]
        [Tooltip("回弹强度")]
        [Range(0.1f, 2f)]
        public float reboundIntensity = 0.8f;

        [Tooltip("回弹持续时间")]
        [Range(0.2f, 2f)]
        public float reboundDuration = 0.6f;

        [Header("=== 弯曲测试 ===")]
        [Tooltip("甩动强度")]
        [Range(0.05f, 1f)]
        public float swingIntensity = 0.2f;

        [Tooltip("弯曲系数")]
        [Range(0.5f, 3f)]
        public float bendMultiplier = 1f;

        [Header("=== 自动测试 ===")]
        public bool autoTest = true;
        public float testInterval = 2f;

        private float nextTestTime = 0f;
        private int testStep = 0;
        private Vector3 lastPosition;
        private bool isTracking = false;

        void Start()
        {
            if (jellyController == null)
            {
                jellyController = FindObjectOfType<Tool_JRJELLY_Simple>();
            }

            if (jellyController == null)
            {
                Debug.LogError("[JELLY_TEST] 找不到果冻控制器！");
                enabled = false;
                return;
            }

            // 设置测试参数
            jellyController.bendIntensityMultiplier = bendMultiplier;
        }

        void Update()
        {
            // 更新弯曲系数
            if (jellyController != null)
            {
                jellyController.bendIntensityMultiplier = bendMultiplier;
            }

            // 自动测试
            if (autoTest && Time.time >= nextTestTime)
            {
                RunTest();
                nextTestTime = Time.time + testInterval;
            }

            // 手动测试
            HandleInput();

            // 跟踪位置变化
            TrackPosition();
        }

        void RunTest()
        {
            if (jellyController == null) return;

            switch (testStep % 3)
            {
                case 0:
                    // 测试回弹效果
                    TestRebound();
                    break;

                case 1:
                    // 测试低强度弯曲
                    TestLowIntensityBend();
                    break;

                case 2:
                    // 测试Z轴甩动
                    TestZAxisSwing();
                    break;
            }

            testStep++;
        }

        void TestRebound()
        {
            Vector3 direction = new Vector3(Random.Range(-1f, 1f), 0, Random.Range(-1f, 1f)).normalized;
            jellyController.TriggerRebound(direction, reboundDuration);

            StartTracking("回弹测试");
            Debug.Log($"[TEST] 回弹测试: 方向={direction}, 强度={reboundIntensity}, 时长={reboundDuration}");
        }

        void TestLowIntensityBend()
        {
            Vector3 targetPos = jellyController.transform.position +
                new Vector3(Random.Range(-1f, 1f), 0, Random.Range(-1f, 1f));
            jellyController.TriggerSwing(targetPos, swingIntensity, 1);

            StartTracking("低强度弯曲测试");
            Debug.Log($"[TEST] 低强度弯曲测试: 强度={swingIntensity}, 弯曲系数={bendMultiplier}");
        }

        void TestZAxisSwing()
        {
            // 专门测试Z轴方向的甩动
            Vector3 targetPos = jellyController.transform.position + new Vector3(0, 0, 2f);
            jellyController.TriggerSwing(targetPos, swingIntensity * 2, 1);

            StartTracking("Z轴甩动测试");
            Debug.Log($"[TEST] Z轴甩动测试: 目标Z={targetPos.z}");
        }

        void StartTracking(string testName)
        {
            isTracking = true;
            lastPosition = jellyController.transform.position;
            Debug.Log($"[TEST] 开始跟踪 - {testName}");
        }

        void TrackPosition()
        {
            if (!isTracking || jellyController == null) return;

            Vector3 currentPos = jellyController.transform.position;
            float deltaDistance = Vector3.Distance(currentPos, lastPosition);

            // 检测跳帧（位置突然大幅变化）
            if (deltaDistance > 0.1f)
            {
                Debug.LogWarning($"[TEST] 检测到可能的跳帧! 位置变化: {deltaDistance:F3}, 从 {lastPosition} 到 {currentPos}");
            }

            // 检测是否回到原始位置
            Vector3 originalPos = jellyController.transform.position; // 这里应该是原始位置，但我们用当前逻辑
            if (!jellyController.HasActiveEffects)
            {
                isTracking = false;
                Debug.Log($"[TEST] 效果结束，最终位置: {currentPos}");
            }

            lastPosition = currentPos;
        }

        void HandleInput()
        {
            if (jellyController == null) return;

            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                TestRebound();
            }

            if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                TestLowIntensityBend();
            }

            if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                TestZAxisSwing();
            }

            if (Input.GetKeyDown(KeyCode.Space))
            {
                jellyController.StopAll();
                isTracking = false;
                Debug.Log("[TEST] 停止所有效果");
            }
        }

        void OnGUI()
        {
            if (!enabled) return;

            GUILayout.BeginArea(new Rect(Screen.width - 250, 10, 240, 300));
            GUILayout.Label("=== 果冻修复测试 ===", GUI.skin.box);

            GUILayout.Label("按键测试:");
            GUILayout.Label("1 - 回弹测试");
            GUILayout.Label("2 - 低强度弯曲测试");
            GUILayout.Label("3 - Z轴甩动测试");
            GUILayout.Label("空格 - 停止所有效果");

            GUILayout.Space(10);

            if (jellyController != null)
            {
                GUILayout.Label($"活跃效果: {(jellyController.HasActiveEffects ? "是" : "否")}");
                GUILayout.Label($"当前位置: {jellyController.transform.position:F2}");
                GUILayout.Label($"弯曲系数: {bendMultiplier:F1}");
            }

            GUILayout.Space(10);

            autoTest = GUILayout.Toggle(autoTest, "自动测试");

            if (GUILayout.Button("手动回弹测试"))
            {
                TestRebound();
            }

            if (GUILayout.Button("手动弯曲测试"))
            {
                TestLowIntensityBend();
            }

            if (GUILayout.Button("手动Z轴测试"))
            {
                TestZAxisSwing();
            }

            GUILayout.EndArea();
        }
    }
}