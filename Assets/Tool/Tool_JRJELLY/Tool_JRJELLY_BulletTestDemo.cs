using UnityEngine;

namespace JRJelly
{
    /// <summary>
    /// 果冻子弹测试演示场景
    /// 快速创建测试环境，包含射击器和多个果冻目标
    /// </summary>
    public class Tool_JRJELLY_BulletTestDemo : MonoBehaviour
    {
        [Header("🎪 演示场景设置")]
        [Header("是否自动创建演示场景")]
        public bool autoCreateDemoScene = true;

        [Header("果冻目标数量")]
        [Range(1, 10)]
        public int jellyTargetCount = 5;

        [Header("目标分布半径")]
        [Range(3f, 20f)]
        public float targetSpreadRadius = 8f;

        [Header("目标高度范围")]
        public Vector2 targetHeightRange = new Vector2(0f, 3f);

        [Header("🔫 射击器设置")]
        [Header("射击器位置")]
        public Vector3 shooterPosition = new Vector3(0, 1, -5);

        [Header("射击器朝向目标中心")]
        public bool shooterLookAtCenter = true;

        [Header("🎯 果冻物体设置")]
        [Header("果冻物体大小范围")]
        public Vector2 jellySizeRange = new Vector2(0.8f, 1.5f);

        [Header("使用的果冻材质")]
        public Material jellyMaterial;

        [Header("🐛 调试设置")]
        public bool showDebugInfo = true;

        // === 私有变量 ===
        private Tool_JRJELLY_BulletTest bulletTester;
        private GameObject[] jellyTargets;

        #region Unity生命周期

        void Start()
        {
            if (autoCreateDemoScene)
            {
                CreateDemoScene();
            }
        }

        void Update()
        {
            // 演示控制
            HandleDemoInput();
        }

        // void OnDrawGizmos()
        // {
        //     if (!showDebugInfo) return;
        //
        //     // 绘制目标分布区域
        //     Gizmos.color = Color.green;
        //     Gizmos.DrawWireSphere(transform.position, targetSpreadRadius);
        //
        //     // 绘制射击器位置
        //     Gizmos.color = Color.red;
        //     Gizmos.DrawWireCube(shooterPosition, Vector3.one * 0.5f);
        //
        //     // 绘制目标位置预览
        //     if (jellyTargets != null)
        //     {
        //         Gizmos.color = Color.yellow;
        //         foreach (var target in jellyTargets)
        //         {
        //             if (target != null)
        //                 Gizmos.DrawWireSphere(target.transform.position, 0.3f);
        //         }
        //     }
        // }

        #endregion

        #region 公共接口

        /// <summary>
        /// 创建演示场景
        /// </summary>
        [ContextMenu("创建演示场景")]
        public void CreateDemoScene()
        {
            // 清理现有场景
            ClearDemoScene();

            // 创建射击器
            CreateShooter();

            // 创建果冻目标
            CreateJellyTargets();

            if (showDebugInfo)
                Debug.Log("[演示场景] 创建完成！使用鼠标左键射击，右键选择目标");
        }

        /// <summary>
        /// 清理演示场景
        /// </summary>
        [ContextMenu("清理演示场景")]
        public void ClearDemoScene()
        {
            // 清理现有目标
            if (jellyTargets != null)
            {
                foreach (var target in jellyTargets)
                {
                    if (target != null)
                        DestroyImmediate(target);
                }
            }

            // 清理射击器
            if (bulletTester != null)
            {
                bulletTester.ClearAllBullets();
            }

            if (showDebugInfo)
                Debug.Log("[演示场景] 清理完成");
        }

        /// <summary>
        /// 重新创建场景
        /// </summary>
        [ContextMenu("重新创建场景")]
        public void RecreateScene()
        {
            CreateDemoScene();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建射击器
        /// </summary>
        private void CreateShooter()
        {
            // 创建射击器对象
            GameObject shooterObj = new GameObject("BulletShooter");
            shooterObj.transform.position = shooterPosition;

            // 添加子弹测试组件
            bulletTester = shooterObj.AddComponent<Tool_JRJELLY_BulletTest>();

            // 配置射击器
            bulletTester.bulletSpeed = 15f;
            bulletTester.bulletLifetime = 8f;
            bulletTester.maxSwingDistance = targetSpreadRadius * 1.5f;
            bulletTester.baseSwingIntensity = 0.8f;
            bulletTester.baseSwingDuration = 0.8f;
            bulletTester.showDebugInfo = showDebugInfo;
            bulletTester.showBulletTrail = true;

            // 朝向目标中心
            if (shooterLookAtCenter)
            {
                Vector3 centerDirection = (transform.position - shooterPosition).normalized;
                shooterObj.transform.rotation = Quaternion.LookRotation(centerDirection);
            }

            if (showDebugInfo)
                Debug.Log("[演示场景] 射击器创建完成");
        }

        /// <summary>
        /// 创建果冻目标
        /// </summary>
        private void CreateJellyTargets()
        {
            jellyTargets = new GameObject[jellyTargetCount];

            for (int i = 0; i < jellyTargetCount; i++)
            {
                // 计算随机位置
                Vector3 randomPos = GetRandomTargetPosition();

                // 创建果冻目标
                GameObject jellyObj = CreateSingleJellyTarget(randomPos, i);
                jellyTargets[i] = jellyObj;
            }

            // 刷新射击器的目标列表
            if (bulletTester != null)
            {
                bulletTester.RefreshJellyTargets();
            }

            if (showDebugInfo)
                Debug.Log($"[演示场景] 创建了 {jellyTargetCount} 个果冻目标");
        }

        /// <summary>
        /// 获取随机目标位置
        /// </summary>
        /// <returns>随机位置</returns>
        private Vector3 GetRandomTargetPosition()
        {
            // 在圆形区域内随机分布
            Vector2 randomCircle = Random.insideUnitCircle * targetSpreadRadius;
            float randomHeight = Random.Range(targetHeightRange.x, targetHeightRange.y);

            return transform.position + new Vector3(randomCircle.x, randomHeight, randomCircle.y);
        }

        /// <summary>
        /// 创建单个果冻目标
        /// </summary>
        /// <param name="position">位置</param>
        /// <param name="index">索引</param>
        /// <returns>创建的游戏对象</returns>
        private GameObject CreateSingleJellyTarget(Vector3 position, int index)
        {
            // 创建基础对象
            GameObject jellyObj = GameObject.CreatePrimitive(PrimitiveType.Cube);
            jellyObj.name = $"JellyTarget_{index}";
            jellyObj.transform.position = position;

            // 随机大小
            float randomSize = Random.Range(jellySizeRange.x, jellySizeRange.y);
            jellyObj.transform.localScale = Vector3.one * randomSize;

            // 随机旋转
            jellyObj.transform.rotation = Random.rotation;

            // 设置材质
            var renderer = jellyObj.GetComponent<Renderer>();
            if (renderer != null)
            {
                if (jellyMaterial != null)
                {
                    renderer.material = jellyMaterial;
                }
                else
                {
                    // 创建随机颜色材质
                    renderer.material = new Material(Shader.Find("Standard"));
                    renderer.material.color = GetRandomJellyColor();
                    renderer.material.SetFloat("_Metallic", 0f);
                    renderer.material.SetFloat("_Smoothness", 0.8f);
                }
            }

            // 添加果冻组件
            var jellyComponent = jellyObj.AddComponent<Tool_JRJELLY_Simple>();

            // 配置果冻参数
            jellyComponent.rigidity = Random.Range(0.8f, 1.5f);
            jellyComponent.elasticity = Random.Range(1f, 2f);
            jellyComponent.damping = Random.Range(0.8f, 1.2f);
            jellyComponent.swingPositionIntensity = Random.Range(0.2f, 0.4f);
            jellyComponent.swingBendIntensity = Random.Range(0.8f, 1.5f);
            jellyComponent.showDebugInfo = showDebugInfo;

            return jellyObj;
        }

        /// <summary>
        /// 获取随机果冻颜色
        /// </summary>
        /// <returns>随机颜色</returns>
        private Color GetRandomJellyColor()
        {
            // 生成鲜艳的果冻颜色
            Color[] jellyColors = {
                new Color(1f, 0.3f, 0.3f),     // 红色
                new Color(0.3f, 1f, 0.3f),     // 绿色
                new Color(0.3f, 0.3f, 1f),     // 蓝色
                new Color(1f, 1f, 0.3f),       // 黄色
                new Color(1f, 0.3f, 1f),       // 品红
                new Color(0.3f, 1f, 1f),       // 青色
                new Color(1f, 0.6f, 0.3f),     // 橙色
                new Color(0.6f, 0.3f, 1f),     // 紫色
            };

            return jellyColors[Random.Range(0, jellyColors.Length)];
        }

        /// <summary>
        /// 处理演示输入
        /// </summary>
        private void HandleDemoInput()
        {
            // 快捷键控制
            if (Input.GetKeyDown(KeyCode.R))
            {
                RecreateScene();
            }

            if (Input.GetKeyDown(KeyCode.C))
            {
                ClearDemoScene();
            }

            if (Input.GetKeyDown(KeyCode.T) && bulletTester != null)
            {
                // 随机选择一个目标
                if (jellyTargets != null && jellyTargets.Length > 0)
                {
                    var randomTarget = jellyTargets[Random.Range(0, jellyTargets.Length)];
                    if (randomTarget != null)
                    {
                        bulletTester.SetTarget(randomTarget.transform);
                    }
                }
            }

            // 显示帮助信息
            if (Input.GetKeyDown(KeyCode.H))
            {
                ShowHelpInfo();
            }
        }

        /// <summary>
        /// 显示帮助信息
        /// </summary>
        private void ShowHelpInfo()
        {
            string helpText = @"
=== 果冻子弹测试演示 - 控制说明 ===
鼠标左键: 射击（如果有选中目标则射击目标，否则射击鼠标位置）
鼠标右键: 选择目标
R键: 重新创建场景
C键: 清理场景
T键: 随机选择目标
H键: 显示此帮助信息

=== 射击规则 ===
- 击中指定目标时，子弹会被销毁
- 击中其他果冻物体时，子弹继续飞行但会触发甩动效果
- 甩动强度和时间根据子弹飞行距离计算
- 距离越近，甩动效果越强
";
            Debug.Log(helpText);
        }

        #endregion

        #region 编辑器辅助

        void OnValidate()
        {
            // 确保参数合理
            jellyTargetCount = Mathf.Max(1, jellyTargetCount);
            targetSpreadRadius = Mathf.Max(1f, targetSpreadRadius);
            jellySizeRange.x = Mathf.Max(0.1f, jellySizeRange.x);
            jellySizeRange.y = Mathf.Max(jellySizeRange.x, jellySizeRange.y);
        }

        #endregion
    }
}