using System.Collections.Generic;
using UnityEngine;

namespace JRJelly
{
    /// <summary>
    /// 简化版果冻效果控制器
    /// 提供基本的果冻甩动效果
    /// </summary>
    public class Tool_JRJELLY_Simple : MonoBehaviour
    {
        [Header("🧊 果冻物理基础")]
        [Header("果冻刚性 - 越大越硬")]
        [Range(0.1f, 3f)]
        public float rigidity = 1f;

        [Header("弹性强度 - 影响回弹")]
        [Range(0.5f, 3f)]
        public float elasticity = 1f;

        [Header("阻尼 - 影响衰减")]
        [Range(0.1f, 2f)]
        public float damping = 1f;

        [Header("🌀 甩动效果")]
        [Header("甩动位移强度 - 控制物体位移幅度")]
        [Range(0.01f, 1f)]
        public float swingPositionIntensity = 0.3f;

        [Header("甩动弯曲强度 - 控制Shader顶点变形幅度")]
        [Range(0.01f, 5f)]
        public float swingBendIntensity = 1f;

        [Header("🎯 弯曲变形")]
        [Header("弯曲强度系数 - 控制顶点变形幅度")]
        [Range(0.1f, 8f)]
        public float bendIntensityMultiplier = 0.6f;

        [Header("高度影响曲线 - 控制从底部到顶部的影响强度")]
        [Range(0.5f, 3f)]
        public float heightInfluenceCurve = 1.5f;

        [Header("🛑 刹车效果")]
        [Header("刹车位移强度 - 控制往前超出的距离")]
        [Range(0.1f, 2f)]
        public float brakePositionIntensity = 0.8f;

        [Header("刹车前冲时间占比 - 控制前冲阶段的时间")]
        [Range(0.2f, 0.6f)]
        public float brakeForwardRatio = 0.3f;

        [Header("刹车弯曲强度 - 控制甩动变形")]
        [Range(0.5f, 5f)]
        public float brakeBendStrength = 2.5f;

        [Header("🫧 呼吸效果")]
        [Header("呼吸上下浮动强度")]
        [Range(0.003f, 1f)]
        public float breatheFloatIntensity = 0.15f;

        [Header("呼吸前后位移强度")]
        [Range(0.003f, 1f)]
        public float breatheMovementIntensity = 0.2f;

        [Header("呼吸周期(秒)")]
        [Range(1f, 10f)]
        public float breatheCycleDuration = 3f;

        [Header("呼吸变形强度")]
        [Range(0.1f, 3f)]
        public float breatheBendIntensity = 1.4f;

        [Header("呼吸平滑度 - 控制中间位置的平缓程度")]
        [Range(0.3f, 1.5f)]
        public float breatheSmoothness = 1f;

        [Header("🌍 落地缓震效果")]
        [Header("缓震弹性次数 - 控制弹跳次数")]
        [Range(1f, 4f)]
        public float landingCushionBounces = 2f;

        [Header("⚡ 性能优化")]
        [Header("是否启用休眠")]
        public bool enableSleep = true;

        [Header("休眠阈值")]
        [Range(0.001f, 0.1f)]
        public float sleepThreshold = 0.01f;

        [Header("🐛 调试信息")]
        public bool showDebugInfo = true;

        [Header("👶 子物体跟随")]
        [Header("是否启用子物体跟随果冻效果")]
        public bool enableChildrenFollow = false;

        [Header("子物体跟随强度 - 控制位置跟随和果冻效果的程度")]
        [Range(0f, 10f)]
        public float childrenJellyStrength = 0.6f;

        [Header("是否包含所有子代（否则只包含直接子物体）")]
        public bool includeAllDescendants = true;

        // === 私有变量 ===
        private Renderer targetRenderer;
        private MaterialPropertyBlock propertyBlock;
        private List<JellyEffect> activeEffects = new List<JellyEffect>();

        // 子物体跟随相关
        private List<ChildFollowData> childrenData = new List<ChildFollowData>();

        // Shader属性ID
        private int shaderID_JellyEnabled;
        private int shaderID_JellyIntensity;
        private int shaderID_JellyDirection;
        private int shaderID_JellySquash;
        private int shaderID_JellyBendStrength;

        // 状态
        private bool isSleeping = true;
        // 【修复】使用本地坐标记录原始位置，避免父物体移动时的位置跳跃问题
        // 原因：之前使用世界坐标，当父物体移动后，果冻效果会强制回到最初的世界位置
        // 解决：改用本地坐标，这样果冻效果会相对于父物体进行，支持父物体的移动
        private Vector3 originalLocalPosition;
        private Vector3 originalScale;
        private Quaternion originalRotation; // 【修复】本地旋转，支持父物体旋转

        // 当前效果
        private float currentIntensity = 0f;
        private Vector3 currentDirection = Vector3.zero;
        private float currentSquash = 1f;

        // 呼吸状态跟踪 - 新增
        private bool isBreathing = false; // 是否正在呼吸
        private float breatheStartTime = 0f; // 呼吸开始时间
        private float breatheFadeInDuration = 0.5f; // 淡入时间
        private float breatheFadeOutDuration = 0.8f; // 淡出时间
        private Vector3 lastBreatheOffset = Vector3.zero; // 上一帧的呼吸偏移
        private bool isBreatheFadingOut = false; // 是否正在淡出

        /// <summary>
        /// 内部果冻效果数据
        /// </summary>
        private class JellyEffect
        {
            public enum EffectType { Swing, Rebound, Breathe, BreatheTransition, LandingCushion }

            public EffectType type;
            public Vector3 direction;
            public float intensity;     // 位移强度
            public float bendIntensity; // 弯曲强度（仅用于甩动效果）
            public float duration;
            public float startTime;

            // 呼吸效果专用参数 - 新增
            public float fadeInDuration = 0.5f; // 淡入时间
            public float fadeOutDuration = 0.8f; // 淡出时间
            public bool isFadingOut = false; // 是否正在淡出
            public Vector3 lastOffset = Vector3.zero; // 上一帧的偏移量，用于平滑过渡

            // 刹车效果参数 - 从外部类复制过来，避免静态访问问题
            public float brakePositionIntensity = 0.8f;
            public float brakeForwardRatio = 0.3f;
            public float brakeBendStrength = 2.5f;

            public float progress => Mathf.Clamp01((Time.time - startTime) / duration);

            // 新的完成判断：基于实际效果值而非时间
            public bool isFinished
            {
                get
                {
                    // 呼吸效果的特殊判断
                    if (type == EffectType.Breathe)
                    {
                        // 如果正在淡出且淡出完成，则认为结束
                        if (isFadingOut)
                        {
                            float fadeOutProgress = (Time.time - startTime) / fadeOutDuration;
                            return fadeOutProgress >= 1f;
                        }
                        // 否则呼吸效果不会自动结束
                        return false;
                    }

                    // 呼吸过渡效果的判断
                    if (type == EffectType.BreatheTransition)
                    {
                        return progress >= 1f;
                    }

                    // 落地缓震效果的判断
                    if (type == EffectType.LandingCushion)
                    {
                        return progress >= 1f;
                    }

                    // 首先检查时间是否超出预期
                    if (progress < 1f) return false;

                    // 时间到了之后，检查实际效果值是否足够小
                    return GetCurrentEffectValue() < 0.005f; // 更严格的阈值
                }
            }

            /// <summary>
            /// 获取当前效果的实际值（用于判断是否真正结束）
            /// </summary>
            public float GetCurrentEffectValue()
            {
                float prog = progress;

                switch (type)
                {
                    case EffectType.Rebound:
                        if (prog >= 1f) return 0f;

                        // 简化的刹车曲线：前冲后回弹
                        float curveValue = 0f;
                        if (prog <= brakeForwardRatio)
                        {
                            // 前冲阶段：0到1的平滑上升
                            float forwardProg = prog / brakeForwardRatio;
                            curveValue = Mathf.Sin(forwardProg * Mathf.PI * 0.5f); // 0到1
                        }
                        else
                        {
                            // 回弹阶段：1到0的平滑下降
                            float backwardProg = (prog - brakeForwardRatio) / (1f - brakeForwardRatio);
                            curveValue = Mathf.Cos(backwardProg * Mathf.PI * 0.5f); // 1到0
                        }

                        // 返回实际位移强度的绝对值
                        return Mathf.Abs(curveValue * brakePositionIntensity * intensity);

                    case EffectType.Swing:
                        if (prog >= 1f) return 0f;
                        float swingValue = Mathf.Sin(prog * Mathf.PI) * (1f - prog);
                        // 使用更大的强度值来判断是否结束
                        return Mathf.Abs(swingValue * Mathf.Max(intensity, bendIntensity));

                    case EffectType.Breathe:
                        // 呼吸效果是持续的，返回一个固定值使其不会自动结束
                        return 0.1f;

                    case EffectType.BreatheTransition:
                        // 呼吸过渡效果 - 平滑回到原位
                        if (prog >= 1f) return 0f;
                        return Mathf.Abs((1f - prog) * intensity);

                    case EffectType.LandingCushion:
                        // 落地缓震效果 - 快速衰减的震荡
                        if (prog >= 1f) return 0f;
                        float cushionValue = Mathf.Exp(-prog * 3f) * intensity;
                        return Mathf.Abs(cushionValue);

                    default:
                        return 0f;
                }
            }
        }

        /// <summary>
        /// 子物体跟随数据 - 简化版本
        /// </summary>
        private class ChildFollowData
        {
            public Transform childTransform;
            public Vector3 originalLocalPosition;
            public Vector3 originalLocalScale;
            public Quaternion originalLocalRotation;
            public float heightFactor; // 子物体在父物体中的相对高度（0-1），用于果冻效果强度计算
            public bool hasJellyShader; // 是否有果冻Shader支持
            public Renderer childRenderer; // 缓存渲染器组件

            public ChildFollowData(Transform child, Transform parent)
            {
                childTransform = child;
                originalLocalPosition = child.localPosition;
                originalLocalScale = child.localScale;
                originalLocalRotation = child.localRotation;

                // 简化高度因子计算：基于Y坐标的相对位置
                // 假设父物体的子物体分布在-1到1的范围内
                float relativeY = child.localPosition.y;
                heightFactor = Mathf.Clamp01((relativeY + 1f) / 2f); // 将-1到1映射到0到1

                // 检查是否有渲染器和果冻Shader支持
                childRenderer = child.GetComponent<Renderer>();
                hasJellyShader = CheckJellyShaderSupport(childRenderer);
            }

            /// <summary>
            /// 检查渲染器是否支持果冻Shader
            /// </summary>
            private bool CheckJellyShaderSupport(Renderer renderer)
            {
                if (renderer == null || renderer.material == null)
                    return false;

                // 检查材质是否有果冻相关的Shader属性
                Material mat = renderer.material;
                return mat.HasProperty("_JellyEnabled") &&
                       mat.HasProperty("_JellyIntensity") &&
                       mat.HasProperty("_JellyDirection");
            }
        }

        #region Unity生命周期

        void Awake()
        {
            targetRenderer = GetComponent<Renderer>();
            if (targetRenderer == null)
            {
                Debug.LogError("[JRJELLY] 没有找到Renderer组件！", this);
                enabled = false;
                return;
            }

            propertyBlock = new MaterialPropertyBlock();

            // 缓存Shader属性ID
            shaderID_JellyEnabled = Shader.PropertyToID("_JellyEnabled");
            shaderID_JellyIntensity = Shader.PropertyToID("_JellyIntensity");
            shaderID_JellyDirection = Shader.PropertyToID("_JellyDirection");
            shaderID_JellySquash = Shader.PropertyToID("_JellySquash");
            shaderID_JellyBendStrength = Shader.PropertyToID("_JellyBendStrength");

            // 记录原始变换
            originalLocalPosition = transform.localPosition;
            originalScale = transform.localScale;
            originalRotation = transform.localRotation;

            // 初始化子物体数据
            InitializeChildrenData();
        }

        void Update()
        {
            if (isSleeping) return;

            UpdateEffects();
            UpdateShader();
            CheckSleep();
        }

        #endregion

        #region 公共接口

        /// <summary>
        /// 触发目标甩动效果
        /// </summary>
        /// <param name="targetWorldPos">目标世界坐标</param>
        /// <param name="intensity">强度(0-1)</param>
        /// <param name="duration">持续时间</param>
        public bool TriggerSwing(Vector3 targetWorldPos, float intensity, float duration)
        {
            // 计算甩动方向（从目标指向自身）
            // 将本地坐标转换为世界坐标进行计算
            Vector3 originalWorldPos = transform.TransformPoint(originalLocalPosition);
            Vector3 toSelf = (targetWorldPos - originalWorldPos).normalized;

            // 直接使用传入的强度，不再进行内部距离计算
            float finalPositionIntensity = intensity * swingPositionIntensity;
            float finalBendIntensity = intensity * swingBendIntensity;

            var effect = new JellyEffect
            {
                type = JellyEffect.EffectType.Swing,
                direction = toSelf,
                intensity = finalPositionIntensity, // 用于位移
                bendIntensity = finalBendIntensity, // 用于弯曲变形
                duration = duration,
                startTime = Time.time
            };

            bool success = AddEffect(effect);

            if (showDebugInfo && success)
                Debug.Log($"[JRJELLY] 触发甩动: 位移强度={finalPositionIntensity:F2}, 弯曲强度={finalBendIntensity:F2}");

            return success;
        }

        /// <summary>
        /// 触发刹车效果 - 往前超出移动一点然后回到原位，配合往前甩往后回弹
        /// </summary>
        /// <param name="direction">刹车方向</param>
        /// <param name="intensity">强度</param>
        /// <param name="duration">持续时间</param>
        public bool TriggerRebound(Vector3 direction, float intensity = 1f, float duration = 0.8f)
        {
            var effect = new JellyEffect
            {
                type = JellyEffect.EffectType.Rebound,
                direction = direction.normalized,
                intensity = intensity,
                duration = duration,
                startTime = Time.time,
                // 复制刹车参数到效果实例
                brakePositionIntensity = this.brakePositionIntensity,
                brakeForwardRatio = this.brakeForwardRatio,
                brakeBendStrength = this.brakeBendStrength
            };

            bool success = AddEffect(effect);

            if (showDebugInfo && success)
                Debug.Log($"[JRJELLY] 触发刹车: 方向={direction}, 位移强度={brakePositionIntensity}");

            return success;
        }

        /// <summary>
        /// 触发呼吸效果 - 果冻上下浮动并轻微前后位移
        /// </summary>
        /// <param name="direction">呼吸前后位移的方向</param>
        /// <param name="duration">持续时间，设为0表示持续到手动停止</param>
        /// <returns>是否成功触发</returns>
        public bool TriggerBreathe(Vector3 direction, float duration = 0f)
        {
            // 先移除已有的呼吸效果
            for (int i = activeEffects.Count - 1; i >= 0; i--)
            {
                if (activeEffects[i].type == JellyEffect.EffectType.Breathe)
                {
                    activeEffects.RemoveAt(i);
                    if (showDebugInfo)
                        Debug.Log("[JRJELLY] 移除已有呼吸效果");
                    break;
                }
            }

            // 创建新的呼吸效果
            var effect = new JellyEffect
            {
                type = JellyEffect.EffectType.Breathe,
                direction = direction.normalized,
                intensity = 1f, // 强度由参数控制
                duration = duration <= 0 ? float.MaxValue : duration, // 如果为0则持续到手动停止
                startTime = Time.time,
                fadeInDuration = this.breatheFadeInDuration,
                fadeOutDuration = this.breatheFadeOutDuration,
                isFadingOut = false,
                lastOffset = Vector3.zero
            };

            // 直接添加到活跃效果列表，允许与其他效果共存
            activeEffects.Add(effect);
            WakeUp();

            // 更新状态跟踪
            isBreathing = true;
            breatheStartTime = Time.time;
            isBreatheFadingOut = false;
            lastBreatheOffset = Vector3.zero;

            if (showDebugInfo)
                Debug.Log($"[JRJELLY] 触发呼吸效果: 方向={direction}, 持续时间={duration}, 平滑度={breatheSmoothness}");

            return true;
        }

        /// <summary>
        /// 停止呼吸效果
        /// </summary>
        /// <param name="immediateStop">是否马上停止，不进行淡出过渡</param>
        public void StopBreathe(bool immediateStop = false)
        {
            for (int i = activeEffects.Count - 1; i >= 0; i--)
            {
                if (activeEffects[i].type == JellyEffect.EffectType.Breathe)
                {
                    if (immediateStop)
                    {
                        // 马上停止，直接移除效果
                        activeEffects.RemoveAt(i);
                        isBreathing = false;
                        isBreatheFadingOut = false;
                        lastBreatheOffset = Vector3.zero;

                        // 【修复】立即重置Shader属性，确保果冻效果完全停止
                        ResetBreatheShaderProperties();

                        if (showDebugInfo)
                            Debug.Log("[JRJELLY] 马上停止呼吸效果");

                        return;
                    }

                    // 获取当前呼吸效果
                    var breatheEffect = activeEffects[i];

                    // 如果已经在淡出，直接返回
                    if (breatheEffect.isFadingOut)
                    {
                        if (showDebugInfo)
                            Debug.Log("[JRJELLY] 呼吸效果已在淡出中");
                        return;
                    }

                    // 开始淡出过程
                    breatheEffect.isFadingOut = true;
                    breatheEffect.startTime = Time.time; // 重置开始时间为淡出开始时间
                    breatheEffect.duration = breatheEffect.fadeOutDuration;

                    // 更新状态跟踪
                    isBreatheFadingOut = true;

                    if (showDebugInfo)
                        Debug.Log("[JRJELLY] 开始呼吸效果淡出");

                    return;
                }
            }

            // 如果没有找到呼吸效果，更新状态
            isBreathing = false;
            isBreatheFadingOut = false;
            lastBreatheOffset = Vector3.zero;

            // 【修复】确保Shader属性被重置
            ResetBreatheShaderProperties();
        }

        /// <summary>
        /// 停止所有效果
        /// </summary>
        public void StopAll()
        {
            activeEffects.Clear();
            ResetState();
            ResetAllChildren(); // 同时重置所有子物体

            if (showDebugInfo)
                Debug.Log("[JRJELLY] 停止所有效果（包括子物体）");
        }

        /// <summary>
        /// 检查是否有活跃效果
        /// </summary>
        public bool HasActiveEffects => activeEffects.Count > 0;

        /// <summary>
        /// 是否正在呼吸
        /// </summary>
        public bool IsBreathing => isBreathing;

        /// <summary>
        /// 是否正在呼吸淡出
        /// </summary>
        public bool IsBreatheFadingOut => isBreatheFadingOut;

        /// <summary>
        /// 触发落地缓震效果 - 快速的果冻震荡，不产生位移
        /// </summary>
        /// <param name="intensity">缓震强度倍数(0-1)，会与配置的landingCushionIntensity相乘</param>
        /// <param name="duration">持续时间，默认使用配置的landingCushionDuration</param>
        /// <returns>是否成功触发</returns>
        public bool TriggerLandingCushion(float intensity = 1f, float duration = 0f)
        {
            // 使用配置的持续时间，如果没有指定的话
            float actualDuration = duration;

            var effect = new JellyEffect
            {
                type = JellyEffect.EffectType.LandingCushion,
                direction = Vector3.zero, // 不需要方向，因为不产生位移
                intensity = intensity,
                duration = actualDuration,
                startTime = Time.time
            };

            bool success = AddEffect(effect);

            if (showDebugInfo && success)
                Debug.Log($"[JRJELLY] 触发落地缓震: 强度={intensity}, 持续时间={actualDuration}");

            return success;
        }

        /// <summary>
        /// 重新记录原始位置（用于运行时位置发生变化的情况）
        /// 注意：现在使用本地坐标系统，支持父物体移动
        /// </summary>
        public void RecordOriginalPosition()
        {
            originalLocalPosition = transform.localPosition;
            originalScale = transform.localScale;
            originalRotation = transform.localRotation;

            // 重新初始化子物体数据
            InitializeChildrenData();

            if (showDebugInfo)
                Debug.Log($"[JRJELLY] 重新记录原始本地位置: {originalLocalPosition}");
        }

        /// <summary>
        /// 设置子物体跟随参数（运行时调用）
        /// </summary>
        /// <param name="jellyStrength">跟随强度 (0-2)，同时控制位置跟随和果冻效果</param>
        public void SetChildrenFollowParams(float jellyStrength)
        {
            childrenJellyStrength = Mathf.Clamp(jellyStrength, 0f, 2f);

            if (showDebugInfo)
                Debug.Log($"[JRJELLY] 设置子物体跟随强度: {childrenJellyStrength:F2}");
        }

        #endregion

        #region 子物体跟随方法

        /// <summary>
        /// 初始化子物体跟随数据 - 简化版本
        /// </summary>
        private void InitializeChildrenData()
        {
            childrenData.Clear();

            if (!enableChildrenFollow)
                return;

            // 收集子物体
            Transform[] children;
            if (includeAllDescendants)
            {
                children = GetComponentsInChildren<Transform>();
            }
            else
            {
                children = new Transform[transform.childCount];
                for (int i = 0; i < transform.childCount; i++)
                {
                    children[i] = transform.GetChild(i);
                }
            }

            foreach (var child in children)
            {
                // 跳过自身
                if (child == transform)
                    continue;

                // 创建简化的子物体数据
                var childData = new ChildFollowData(child, transform);
                childrenData.Add(childData);
            }

            if (showDebugInfo && childrenData.Count > 0)
            {
                int jellyShaderCount = 0;
                int transformOnlyCount = 0;

                foreach (var childData in childrenData)
                {
                    if (childData.hasJellyShader)
                        jellyShaderCount++;
                    else
                        transformOnlyCount++;
                }

                Debug.Log($"[JRJELLY] 初始化了 {childrenData.Count} 个子物体的跟随数据：" +
                         $"{jellyShaderCount} 个支持果冻Shader，{transformOnlyCount} 个仅位置跟随");
            }
        }

        /// <summary>
        /// 更新子物体跟随效果 - 完全跟随版本（无延迟、无超调）
        /// </summary>
        private void UpdateChildrenFollow()
        {
            if (!enableChildrenFollow || childrenData.Count == 0)
                return;

            foreach (var childData in childrenData)
            {
                if (childData.childTransform == null)
                    continue;

                UpdateChildTransform(childData);
            }
        }

        /// <summary>
        /// 更新单个子物体的变换 - 统一使用childrenJellyStrength控制跟随
        /// </summary>
        private void UpdateChildTransform(ChildFollowData childData)
        {
            // 计算父物体当前相对于原始位置的偏移
            Vector3 parentCurrentOffset = transform.localPosition - originalLocalPosition;

            // === 1. 位置跟随（所有子物体都支持） ===
            // 使用childrenJellyStrength作为跟随强度，但限制在0-1范围内用于位置跟随
            float positionFollowStrength = Mathf.Clamp01(childrenJellyStrength);
            Vector3 targetPosition = childData.originalLocalPosition + parentCurrentOffset * positionFollowStrength;

            // 设置位置
            childData.childTransform.localPosition = targetPosition;

            // === 2. 果冻效果（仅支持果冻Shader的子物体） ===
            if (childData.hasJellyShader && childData.childRenderer != null && childrenJellyStrength > 0f)
            {
                // 计算子物体受到的果冻效果强度（基于高度因子）
                float jellyInfluence = childData.heightFactor * childrenJellyStrength;

                // 创建子物体的MaterialPropertyBlock
                MaterialPropertyBlock childPropertyBlock = new MaterialPropertyBlock();
                childData.childRenderer.GetPropertyBlock(childPropertyBlock);

                // 应用与父物体一致的果冻效果，但强度不同
                if (currentIntensity > 0.001f || currentDirection.magnitude > 0.001f || currentSquash != 1f)
                {
                    // 启用果冻效果
                    childPropertyBlock.SetFloat(shaderID_JellyEnabled, 1f);

                    // 应用强度（基于子物体的影响因子）
                    childPropertyBlock.SetFloat(shaderID_JellyIntensity, currentIntensity * jellyInfluence);

                    // 应用方向（与父物体保持一致）
                    childPropertyBlock.SetVector(shaderID_JellyDirection, currentDirection * jellyInfluence);

                    // 应用压扁效果（基于子物体的影响因子）
                    float childSquash = Mathf.Lerp(1f, currentSquash, jellyInfluence);
                    childPropertyBlock.SetFloat(shaderID_JellySquash, childSquash);

                    // 应用弯曲强度
                    childPropertyBlock.SetFloat(shaderID_JellyBendStrength,
                        currentDirection.magnitude * bendIntensityMultiplier * jellyInfluence);
                }
                else
                {
                    // 禁用果冻效果
                    childPropertyBlock.SetFloat(shaderID_JellyEnabled, 0f);
                }

                // 应用属性块到子物体渲染器
                childData.childRenderer.SetPropertyBlock(childPropertyBlock);
            }
            // === 3. 非果冻Shader子物体的Transform模拟效果（可选） ===
            else if (!childData.hasJellyShader && childrenJellyStrength > 0f)
            {
                // 对于没有果冻Shader的子物体，可以通过Transform来模拟简单的果冻效果
                ApplyTransformJellyEffect(childData, parentCurrentOffset);
            }
        }

        /// <summary>
        /// 对没有果冻Shader的子物体应用Transform模拟果冻效果
        /// </summary>
        private void ApplyTransformJellyEffect(ChildFollowData childData, Vector3 parentPositionOffset)
        {
            // 计算子物体受到的果冻效果强度（基于高度因子）
            float jellyInfluence = childData.heightFactor * childrenJellyStrength * 0.5f; // 降低强度避免过度

            // === 缩放模拟压扁效果 ===
            Vector3 newScale = childData.originalLocalScale;
            if (currentSquash != 1f)
            {
                float childSquash = Mathf.Lerp(1f, currentSquash, jellyInfluence);
                newScale.y *= childSquash;

                // 轻微的X、Z轴补偿，模拟体积保持
                float compensation = Mathf.Sqrt(1f / childSquash);
                newScale.x *= Mathf.Lerp(1f, compensation, 0.2f);
                newScale.z *= Mathf.Lerp(1f, compensation, 0.2f);
            }
            childData.childTransform.localScale = newScale;

            // === 轻微的额外位移模拟弯曲效果 ===
            Vector3 additionalOffset = Vector3.zero;
            if (currentDirection.magnitude > 0.01f)
            {
                // 基于当前果冻方向添加轻微的额外位移
                additionalOffset = currentDirection * jellyInfluence * 0.1f;
            }

            // 注意：位置已经在UpdateChildTransform中设置了，这里只添加额外的果冻效果位移
            float positionFollowStrength = Mathf.Clamp01(childrenJellyStrength);
            Vector3 basePosition = childData.originalLocalPosition + parentPositionOffset * positionFollowStrength;
            Vector3 finalPosition = basePosition + additionalOffset;
            childData.childTransform.localPosition = finalPosition;
        }

        /// <summary>
        /// 刷新子物体跟随设置（运行时调用）
        /// </summary>
        public void RefreshChildrenFollow()
        {
            InitializeChildrenData();

            if (showDebugInfo)
                Debug.Log("[JRJELLY] 已刷新子物体跟随设置");
        }

        /// <summary>
        /// 重置所有子物体到原始状态 - 根据类型分别处理
        /// </summary>
        public void ResetAllChildren()
        {
            foreach (var childData in childrenData)
            {
                if (childData.childTransform == null)
                    continue;

                // 重置Transform（所有子物体都需要）
                childData.childTransform.localPosition = childData.originalLocalPosition;
                childData.childTransform.localScale = childData.originalLocalScale;
                childData.childTransform.localRotation = childData.originalLocalRotation;

                // 重置Shader属性（仅有果冻Shader的子物体）
                if (childData.hasJellyShader && childData.childRenderer != null)
                {
                    MaterialPropertyBlock childPropertyBlock = new MaterialPropertyBlock();
                    childPropertyBlock.SetFloat(shaderID_JellyEnabled, 0f);
                    childData.childRenderer.SetPropertyBlock(childPropertyBlock);
                }
            }

            if (showDebugInfo)
                Debug.Log("[JRJELLY] 已重置所有子物体到原始状态（区分Shader类型）");
        }

        #endregion

        #region 私有方法

        private bool AddEffect(JellyEffect effect)
        {
            // 如果有活跃效果，拒绝新的效果请求
            if (activeEffects.Count > 0)
            {
                if (showDebugInfo)
                    Debug.Log($"[JRJELLY] 拒绝新效果 {effect.type}，当前有活跃效果: {activeEffects[0].type}");
                return false;
            }

            activeEffects.Add(effect);
            WakeUp();

            if (showDebugInfo)
                Debug.Log($"[JRJELLY] 启动效果: {effect.type}");
            return true;
        }

        private void WakeUp()
        {
            if (isSleeping)
            {
                isSleeping = false;
                if (showDebugInfo)
                    Debug.Log("[JRJELLY] 唤醒");
            }
        }

        private void UpdateEffects()
        {
            // 清理真正完成的效果
            for (int i = activeEffects.Count - 1; i >= 0; i--)
            {
                var effect = activeEffects[i];

                // 呼吸效果特殊处理：只有在淡出完成时才移除
                if (effect.type == JellyEffect.EffectType.Breathe)
                {
                    if (effect.isFadingOut && effect.isFinished)
                    {
                        // 淡出完成，移除效果并更新状态
                        activeEffects.RemoveAt(i);
                        isBreathing = false;
                        isBreatheFadingOut = false;
                        lastBreatheOffset = Vector3.zero;

                        // 【修复】淡出完成时立即重置Shader属性
                        ResetBreatheShaderProperties();

                        if (showDebugInfo)
                            Debug.Log("[JRJELLY] 呼吸效果淡出完成，已移除");
                    }
                    // 否则不移除呼吸效果
                    continue;
                }

                // 其他效果正常移除
                if (effect.isFinished)
                {
                    if (showDebugInfo)
                        Debug.Log($"[JRJELLY] 移除已完成效果: {effect.type}");

                    activeEffects.RemoveAt(i);
                }
            }

            // 重置当前效果值
            currentIntensity = 0f;
            currentDirection = Vector3.zero;
            currentSquash = 1f;

            Vector3 totalOffset = Vector3.zero;
            bool hasSwingEffect = false; // 标记是否有甩动效果（产生位移）

            // 计算所有效果的叠加
            foreach (var effect in activeEffects)
            {
                float progress = effect.progress;

                switch (effect.type)
                {
                    case JellyEffect.EffectType.Swing:
                        // 甩动效果 - 简单可靠的实现
                        hasSwingEffect = true; // 标记有位移效果
                        float swingValue = 0f;

                        if (progress < 1f)
                        {
                            if (elasticity > 1f)
                            {
                                // 多次振荡
                                swingValue = Mathf.Sin(progress * Mathf.PI * elasticity) * (1f - progress);
                            }
                            else
                            {
                                // 单次摆动
                                swingValue = Mathf.Sin(progress * Mathf.PI) * (1f - progress);
                            }
                        }
                        // progress >= 1f 时，swingValue = 0，自然结束

                        // 位移计算 - 使用 effect.intensity
                        float smoothSwingPosition = swingValue * effect.intensity;

                        // 弯曲计算 - 使用 effect.bendIntensity
                        float smoothSwingBend = swingValue * effect.bendIntensity * bendIntensityMultiplier;

                        // 保持XZ平面的甩动，包括Z轴方向
                        Vector3 horizontalDirection = new Vector3(effect.direction.x, 0, effect.direction.z).normalized;
                        totalOffset += horizontalDirection * smoothSwingPosition;
                        currentIntensity += Mathf.Abs(smoothSwingPosition);
                        // 传递弯曲方向给Shader用于弯曲计算
                        currentDirection += horizontalDirection * smoothSwingBend;
                        break;

                    case JellyEffect.EffectType.Rebound:
                        // 刹车效果 - 往前超出移动一点然后回到原位，配合往前甩往后回弹
                        if (progress < 1f)
                        {
                            hasSwingEffect = true; // 标记有位移效果

                            // 简化的刹车曲线：前冲后回弹
                            float curveValue = 0f;
                            if (progress <= effect.brakeForwardRatio)
                            {
                                // 前冲阶段：0到1的平滑上升
                                float forwardProg = progress / effect.brakeForwardRatio;
                                curveValue = Mathf.Sin(forwardProg * Mathf.PI * 0.5f); // 0到1
                            }
                            else
                            {
                                // 回弹阶段：1到0的平滑下降
                                float backwardProg = (progress - effect.brakeForwardRatio) / (1f - effect.brakeForwardRatio);
                                curveValue = Mathf.Cos(backwardProg * Mathf.PI * 0.5f); // 1到0
                            }

                            // 计算位移强度和方向
                            Vector3 directionVector = new Vector3(effect.direction.x, 0, effect.direction.z).normalized;
                            float positionIntensity = curveValue * effect.brakePositionIntensity * effect.intensity;

                            // 应用实际位移 - 前冲时向前，回弹时自然回到原位
                            Vector3 brakeOffset = directionVector * positionIntensity;
                            totalOffset += brakeOffset;

                            // 用于休眠判断
                            currentIntensity += Mathf.Abs(positionIntensity);

                            // 应用Shader变形效果，增强视觉效果
                            float bendStrength2 = curveValue * bendIntensityMultiplier * effect.brakeBendStrength;
                            Vector3 bendEffect = directionVector * bendStrength2;
                            currentDirection += bendEffect;
                        }
                        break;

                    case JellyEffect.EffectType.Breathe:
                        // 【重新设计】呼吸效果 - 上下浮动和轻微前后位移，带平滑淡入淡出
                        hasSwingEffect = true;

                        float timeSinceStart = Time.time - effect.startTime;
                        float fadeMultiplier = 1f;
                        float breatheValue = 0f;

                        if (effect.isFadingOut)
                        {
                            // 淡出阶段：保持当前呼吸位置，平滑过渡到原位
                            float fadeOutProgress = timeSinceStart / effect.fadeOutDuration;
                            fadeMultiplier = Mathf.Clamp01(1f - fadeOutProgress);

                            // 【修复】使用更严格的阈值，确保完全淡出
                            if (fadeOutProgress >= 0.95f)
                            {
                                // 接近淡出完成时，强制置零所有效果
                                breatheValue = 0f;
                                fadeMultiplier = 0f;
                                isBreathing = false;
                                isBreatheFadingOut = false;
                            }
                            else
                            {
                                // 使用上一帧的偏移值，通过fadeMultiplier平滑过渡到0
                                Vector3 lastOffsetNormalized = effect.lastOffset.magnitude > 0.001f ?
                                    effect.lastOffset / effect.lastOffset.magnitude : Vector3.zero;
                                float lastIntensity = effect.lastOffset.magnitude /
                                    Mathf.Max(breatheFloatIntensity, breatheMovementIntensity);

                                breatheValue = lastIntensity * fadeMultiplier;
                            }
                        }
                        else
                        {
                            // 正常呼吸阶段
                            // 【终极修复】让呼吸从中性位置（0）开始，避免拉伸效果
                            float breathePhase = (timeSinceStart % breatheCycleDuration) / breatheCycleDuration;

                            // 使用正弦波，但从中性位置（0）开始，而不是从最低点开始
                            float rawValue = Mathf.Sin(breathePhase * Mathf.PI * 2f);

                            // 应用平滑度调整
                            if (breatheSmoothness != 1f && rawValue != 0f)
                            {
                                float absValue = Mathf.Abs(rawValue);
                                float sign = Mathf.Sign(rawValue);
                                rawValue = sign * Mathf.Pow(absValue, breatheSmoothness);
                            }

                            // 淡入处理：在开始阶段平滑增强幅度
                            if (timeSinceStart < effect.fadeInDuration)
                            {
                                float fadeInProgress = timeSinceStart / effect.fadeInDuration;
                                fadeMultiplier = Mathf.SmoothStep(0f, 1f, fadeInProgress);
                                breatheValue = rawValue * fadeMultiplier;
                            }
                            else
                            {
                                // 淡入完成后，使用完整强度
                                breatheValue = rawValue;
                            }
                        }

                        // 【修复】检查是否需要应用效果，避免微小残留值
                        if (Mathf.Abs(breatheValue) > 0.001f)
                        {
                            // 计算位移
                            Vector3 floatOffset = Vector3.up * breatheValue * breatheFloatIntensity;
                            Vector3 moveDirection = new Vector3(effect.direction.x, 0, effect.direction.z).normalized;
                            Vector3 moveOffset = moveDirection * breatheValue * breatheMovementIntensity;
                            Vector3 currentBreatheOffset = floatOffset + moveOffset;

                            // 应用位移
                            totalOffset += currentBreatheOffset;

                            // 更新记录的偏移值
                            effect.lastOffset = currentBreatheOffset;
                            lastBreatheOffset = currentBreatheOffset;

                            // 应用变形效果
                            float bendStrength = breatheBendIntensity * breatheValue;
                            Vector3 bendDirection = new Vector3(
                                moveDirection.x * 0.3f,
                                breatheValue,
                                moveDirection.z * 0.3f
                            ).normalized;
                            currentDirection += bendDirection * bendStrength * 1.5f;

                            // 更新当前强度
                            currentIntensity += Mathf.Abs(breatheValue) * 0.1f;
                        }
                        else
                        {
                            // 【修复】呼吸值太小时，清零所有相关状态
                            effect.lastOffset = Vector3.zero;
                            lastBreatheOffset = Vector3.zero;
                        }
                        break;

                    case JellyEffect.EffectType.LandingCushion:
                        // 落地缓震效果 - 快速震荡但不产生位移，只通过Shader变形实现
                        if (progress < 1f)
                        {
                            // 使用衰减的正弦波创建弹跳效果
                            float bounceFreq = landingCushionBounces * Mathf.PI * 2; // 弹跳频率
                            float dampingFactor = Mathf.Exp(-progress * 3f); // 指数衰减
                            float cushionValue = Mathf.Sin(progress * bounceFreq) * dampingFactor;

                            // 只影响Shader变形，不改变Transform位置
                            // 主要在Y轴方向产生变形，模拟落地时的压缩和回弹
                            // 修复参数重叠问题：使用传入的intensity参数，再乘以配置的基础强度
                            float finalIntensity = cushionValue * effect.intensity;
                            Vector3 cushionBend = new Vector3(0f, finalIntensity, 0f) * bendIntensityMultiplier;
                            currentDirection += cushionBend;

                            // 更新当前强度，用于休眠判断
                            currentIntensity += Mathf.Abs(finalIntensity) * 0.3f;
                        }
                        break;
                }
            }

            // 应用刚性限制
            totalOffset /= rigidity;

            // 位置应用 - 使用本地坐标系统，支持父物体移动
            if (hasSwingEffect)
            {
                // 有甩动效果时，应用位移（相对于父物体的本地坐标）
                transform.localPosition = originalLocalPosition + totalOffset;
            }
            else
            {
                // 没有甩动效果时，回到原始本地位置（相对于父物体）
                transform.localPosition = originalLocalPosition;
            }

            transform.localScale = new Vector3(originalScale.x, originalScale.y * currentSquash, originalScale.z);

            // 更新子物体跟随效果
            UpdateChildrenFollow();

            // 删除与顶点位置计算相关的注释
        }

        private void UpdateShader()
        {
            if (targetRenderer == null) return;

            targetRenderer.GetPropertyBlock(propertyBlock);

            propertyBlock.SetFloat(shaderID_JellyEnabled, isSleeping ? 0f : 1f);
            propertyBlock.SetFloat(shaderID_JellyIntensity, currentIntensity);
            propertyBlock.SetVector(shaderID_JellyDirection, currentDirection);
            propertyBlock.SetFloat(shaderID_JellySquash, currentSquash);
            propertyBlock.SetFloat(shaderID_JellyBendStrength, bendIntensityMultiplier);

            targetRenderer.SetPropertyBlock(propertyBlock);
        }

        private void CheckSleep()
        {
            if (!enableSleep || activeEffects.Count > 0)
                return;

            if (currentIntensity < sleepThreshold)
            {
                isSleeping = true;
                ResetState();

                if (showDebugInfo)
                    Debug.Log("[JRJELLY] 进入休眠");
            }
        }

        /// <summary>
        /// 重置呼吸相关的Shader属性，确保果冻效果完全停止
        /// </summary>
        private void ResetBreatheShaderProperties()
        {
            if (targetRenderer == null) return;

            // 立即清零与呼吸相关的Shader属性
            targetRenderer.GetPropertyBlock(propertyBlock);

            // 如果没有其他活跃效果，完全禁用果冻效果
            bool hasOtherEffects = false;
            foreach (var effect in activeEffects)
            {
                if (effect.type != JellyEffect.EffectType.Breathe)
                {
                    hasOtherEffects = true;
                    break;
                }
            }

            if (!hasOtherEffects)
            {
                // 没有其他效果时，完全重置所有Shader属性
                propertyBlock.SetFloat(shaderID_JellyEnabled, 0f);
                propertyBlock.SetFloat(shaderID_JellyIntensity, 0f);
                propertyBlock.SetVector(shaderID_JellyDirection, Vector3.zero);
                propertyBlock.SetFloat(shaderID_JellySquash, 1f);
                propertyBlock.SetFloat(shaderID_JellyBendStrength, 0f);

                if (showDebugInfo)
                    Debug.Log("[JRJELLY] 重置呼吸Shader属性：完全禁用果冻效果");
            }
            else
            {
                // 有其他效果时，只重置可能受呼吸影响的属性
                // 让其他效果继续更新这些属性
                if (showDebugInfo)
                    Debug.Log("[JRJELLY] 重置呼吸Shader属性：保留其他效果");
            }

            targetRenderer.SetPropertyBlock(propertyBlock);
        }

        private void ResetState()
        {
            currentIntensity = 0f;
            currentDirection = Vector3.zero;
            currentSquash = 1f;

            // 重置呼吸状态
            isBreathing = false;
            isBreatheFadingOut = false;
            lastBreatheOffset = Vector3.zero;

            // 重置子物体到原始状态
            ResetAllChildren();

            UpdateShader();

            if (showDebugInfo)
                Debug.Log("[JRJELLY] 执行状态重置（位置将平滑回归）");
        }

        #endregion
    }
}