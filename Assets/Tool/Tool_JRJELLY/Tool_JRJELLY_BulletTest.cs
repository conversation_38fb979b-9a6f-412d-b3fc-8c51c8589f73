using System.Collections.Generic;
using UnityEngine;

namespace JRJelly
{
    /// <summary>
    /// 果冻子弹射击测试工具
    /// 模拟子弹射击果冻物体，实现碰撞检测和甩动效果
    /// </summary>
    public class Tool_JRJELLY_BulletTest : MonoBehaviour
    {
        [Header("🔫 子弹设置")]
        [Header("子弹预制体 - 留空将自动创建")]
        public GameObject bulletPrefab;

        [Header("子弹速度")]
        [Range(5f, 50f)]
        public float bulletSpeed = 20f;

        [Header("子弹生存时间(秒)")]
        [Range(1f, 10f)]
        public float bulletLifetime = 5f;

        [Header("子弹大小")]
        [Range(0.1f, 2f)]
        public float bulletSize = 0.2f;

        [Header("🎯 射击设置")]
        [Header("射击点偏移")]
        public Vector3 shootOffset = Vector3.forward;

        [Header("自动射击间隔(秒)")]
        [Range(0.1f, 3f)]
        public float autoShootInterval = 1f;

        [Header("是否启用自动射击")]
        public bool enableAutoShoot = false;

        [Header("🌀 甩动效果设置")]
        [Header("最大甩动距离 - 用于计算强度")]
        [Range(1f, 20f)]
        public float maxSwingDistance = 10f;

        [Header("基础甩动强度")]
        [Range(0.1f, 2f)]
        public float baseSwingIntensity = 0.8f;

        [Header("基础甩动时间")]
        [Range(0.3f, 2f)]
        public float baseSwingDuration = 0.6f;

        [Header("距离强度曲线 - 控制距离对强度的影响")]
        [Range(0.5f, 3f)]
        public float distanceIntensityCurve = 1.5f;

        [Header("🎮 控制设置")]
        [Header("鼠标射击按键")]
        public KeyCode shootKey = KeyCode.Mouse0;

        [Header("目标选择按键")]
        public KeyCode targetSelectKey = KeyCode.Mouse1;

        [Header("🐛 调试设置")]
        public bool showDebugInfo = true;
        public bool showBulletTrail = true;

        // === 私有变量 ===
        private List<BulletData> activeBullets = new List<BulletData>();
        private List<Tool_JRJELLY_Simple> jellyTargets = new List<Tool_JRJELLY_Simple>();
        private Transform currentTarget;
        private float lastAutoShootTime;
        private Camera playerCamera;

        /// <summary>
        /// 子弹数据类
        /// </summary>
        private class BulletData
        {
            public GameObject bulletObject;     // 子弹游戏对象
            public Vector3 startPosition;      // 发射起始位置
            public Vector3 direction;          // 飞行方向
            public float speed;                // 飞行速度
            public float startTime;            // 发射时间
            public float lifetime;             // 生存时间
            public Transform targetTransform;  // 目标Transform（用于销毁判断）
            public bool hasHitTarget;          // 是否已击中目标
            public LineRenderer trailRenderer; // 轨迹渲染器

            public bool IsExpired => Time.time - startTime > lifetime;
        }

        #region Unity生命周期

        void Awake()
        {
            // 获取摄像机
            playerCamera = Camera.main;
            if (playerCamera == null)
                playerCamera = FindObjectOfType<Camera>();

            if (playerCamera == null)
            {
                Debug.LogError("[子弹测试] 未找到摄像机！", this);
                enabled = false;
                return;
            }

            // 查找所有果冻目标
            RefreshJellyTargets();

            // 创建默认子弹预制体（仅在需要时）
            if (bulletPrefab == null)
            {
                CreateDefaultBulletPrefab();
            }

            if (showDebugInfo)
                Debug.Log("[子弹测试] 初始化完成，从当前物体位置射击");
        }

        void Update()
        {
            HandleInput();
            UpdateBullets();
            HandleAutoShoot();
        }

        // void OnDrawGizmos()
        // {
        //     if (!showDebugInfo) return;
        //
        //     // 绘制射击点
        //     Vector3 shootPos = transform.position + transform.TransformDirection(shootOffset);
        //     Gizmos.color = Color.green;
        //     Gizmos.DrawWireSphere(shootPos, 0.1f);
        //
        //     // 绘制到目标的连线
        //     if (currentTarget != null)
        //     {
        //         Gizmos.color = Color.red;
        //         Gizmos.DrawLine(shootPos, currentTarget.position);
        //     }
        //
        //     // 绘制子弹轨迹
        //     if (showBulletTrail)
        //     {
        //         Gizmos.color = Color.yellow;
        //         foreach (var bullet in activeBullets)
        //         {
        //             if (bullet.bulletObject != null)
        //             {
        //                 Gizmos.DrawLine(bullet.startPosition, bullet.bulletObject.transform.position);
        //             }
        //         }
        //     }
        // }

        #endregion

        #region 公共接口

        /// <summary>
        /// 手动射击到指定位置
        /// </summary>
        /// <param name="targetPosition">目标位置</param>
        /// <param name="targetTransform">目标Transform（用于销毁判断）</param>
        public void ShootAt(Vector3 targetPosition, Transform targetTransform = null)
        {
            Vector3 shootPos = transform.position + transform.TransformDirection(shootOffset);
            Vector3 direction = (targetPosition - shootPos).normalized;

            CreateBullet(shootPos, direction, targetTransform);

            if (showDebugInfo)
                Debug.Log($"[子弹测试] 射击目标: {targetPosition}, 距离: {Vector3.Distance(shootPos, targetPosition):F2}");
        }

        /// <summary>
        /// 射击当前选中的目标
        /// </summary>
        public void ShootCurrentTarget()
        {
            if (currentTarget == null)
            {
                if (showDebugInfo)
                    Debug.Log("[子弹测试] 没有选中目标");
                return;
            }

            ShootAt(currentTarget.position, currentTarget);
        }

        /// <summary>
        /// 设置当前目标
        /// </summary>
        /// <param name="target">目标Transform</param>
        public void SetTarget(Transform target)
        {
            currentTarget = target;
            if (showDebugInfo && target != null)
                Debug.Log($"[子弹测试] 选中目标: {target.name}");
        }

        /// <summary>
        /// 刷新果冻目标列表
        /// </summary>
        public void RefreshJellyTargets()
        {
            jellyTargets.Clear();
            jellyTargets.AddRange(FindObjectsOfType<Tool_JRJELLY_Simple>());

            if (showDebugInfo)
            {
                Debug.Log($"[子弹测试] 找到 {jellyTargets.Count} 个果冻目标");
                CheckJellyTargetsColliders();
            }
        }

        /// <summary>
        /// 检查果冻目标的碰撞器设置
        /// </summary>
        public void CheckJellyTargetsColliders()
        {
            int missingColliders = 0;
            foreach (var jelly in jellyTargets)
            {
                if (jelly != null)
                {
                    var collider = jelly.GetComponent<Collider>();
                    if (collider == null)
                    {
                        missingColliders++;
                        if (showDebugInfo)
                            Debug.LogWarning($"[子弹测试] 果冻物体 {jelly.name} 缺少碰撞器，无法被子弹击中！建议添加碰撞器组件。");
                    }
                }
            }

            if (missingColliders > 0)
            {
                Debug.LogWarning($"[子弹测试] 发现 {missingColliders} 个果冻物体缺少碰撞器。" +
                               "右键选择目标和子弹碰撞检测都需要碰撞器组件！");
            }
            else if (jellyTargets.Count > 0)
            {
                Debug.Log("[子弹测试] 所有果冻目标都已正确配置碰撞器");
            }
        }

        /// <summary>
        /// 清除所有子弹
        /// </summary>
        public void ClearAllBullets()
        {
            foreach (var bullet in activeBullets)
            {
                if (bullet.bulletObject != null)
                    DestroyImmediate(bullet.bulletObject);
            }
            activeBullets.Clear();

            if (showDebugInfo)
                Debug.Log("[子弹测试] 清除所有子弹");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 处理输入
        /// </summary>
        private void HandleInput()
        {
            // 射击输入
            if (Input.GetKeyDown(shootKey))
            {
                if (currentTarget != null)
                {
                    ShootCurrentTarget();
                }
                else
                {
                    // 鼠标射击
                    ShootFromMouse();
                }
            }

            // 目标选择输入
            if (Input.GetKeyDown(targetSelectKey))
            {
                SelectTargetFromMouse();
            }
        }

        /// <summary>
        /// 从鼠标位置射击
        /// </summary>
        private void ShootFromMouse()
        {
            Ray ray = playerCamera.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, 100f))
            {
                ShootAt(hit.point, hit.transform);
            }
            else
            {
                // 射击到射线远处
                Vector3 targetPos = ray.origin + ray.direction * 50f;
                ShootAt(targetPos);
            }
        }

        /// <summary>
        /// 从鼠标选择目标
        /// </summary>
        private void SelectTargetFromMouse()
        {
            Ray ray = playerCamera.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, 100f))
            {
                // 检查是否是果冻物体
                var jellyComponent = hit.transform.GetComponent<Tool_JRJELLY_Simple>();
                if (jellyComponent != null)
                {
                    SetTarget(hit.transform);
                    if (showDebugInfo)
                        Debug.Log($"[子弹测试] 选中果冻目标: {hit.transform.name}");
                }
                else
                {
                    // 如果不是果冻物体，检查是否需要添加碰撞器
                    var collider = hit.transform.GetComponent<Collider>();
                    if (collider == null)
                    {
                        if (showDebugInfo)
                            Debug.LogWarning($"[子弹测试] 物体 {hit.transform.name} 没有果冻组件，无法选为目标");
                    }
                    else
                    {
                        if (showDebugInfo)
                            Debug.LogWarning($"[子弹测试] 物体 {hit.transform.name} 有碰撞器但没有果冻组件");
                    }
                }
            }
        }

        /// <summary>
        /// 处理自动射击
        /// </summary>
        private void HandleAutoShoot()
        {
            if (!enableAutoShoot || currentTarget == null) return;

            if (Time.time - lastAutoShootTime >= autoShootInterval)
            {
                ShootCurrentTarget();
                lastAutoShootTime = Time.time;
            }
        }

        /// <summary>
        /// 创建子弹
        /// </summary>
        /// <param name="startPos">起始位置</param>
        /// <param name="direction">飞行方向</param>
        /// <param name="target">目标Transform</param>
        private void CreateBullet(Vector3 startPos, Vector3 direction, Transform target = null)
        {
            // 创建子弹对象
            GameObject bullet = Instantiate(bulletPrefab, startPos, Quaternion.LookRotation(direction));
            bullet.name = $"Bullet_{activeBullets.Count}";

            // 创建子弹数据
            var bulletData = new BulletData
            {
                bulletObject = bullet,
                startPosition = startPos,
                direction = direction,
                speed = bulletSpeed,
                startTime = Time.time,
                lifetime = bulletLifetime,
                targetTransform = target,
                hasHitTarget = false
            };

            // 添加轨迹渲染器
            if (showBulletTrail)
            {
                bulletData.trailRenderer = bullet.GetComponent<LineRenderer>();
                if (bulletData.trailRenderer == null)
                {
                    bulletData.trailRenderer = bullet.AddComponent<LineRenderer>();
                    SetupTrailRenderer(bulletData.trailRenderer);
                }
            }

            activeBullets.Add(bulletData);
        }

        /// <summary>
        /// 设置轨迹渲染器
        /// </summary>
        /// <param name="lineRenderer">线条渲染器</param>
        private void SetupTrailRenderer(LineRenderer lineRenderer)
        {
            // 创建材质并设置颜色
            var material = new Material(Shader.Find("Sprites/Default"));
            material.color = Color.yellow;
            lineRenderer.material = material;

            // 设置线条属性
            lineRenderer.startWidth = 0.02f;
            lineRenderer.endWidth = 0.02f;
            lineRenderer.positionCount = 2;
            lineRenderer.useWorldSpace = true;

            // 使用顶点颜色（备用方案）
            lineRenderer.startColor = Color.yellow;
            lineRenderer.endColor = Color.yellow;
        }

        /// <summary>
        /// 更新所有子弹
        /// </summary>
        private void UpdateBullets()
        {
            for (int i = activeBullets.Count - 1; i >= 0; i--)
            {
                var bullet = activeBullets[i];

                // 检查是否过期
                if (bullet.IsExpired || bullet.bulletObject == null)
                {
                    DestroyBullet(i);
                    continue;
                }

                // 更新子弹位置
                Vector3 newPos = bullet.bulletObject.transform.position + bullet.direction * bullet.speed * Time.deltaTime;
                bullet.bulletObject.transform.position = newPos;

                // 更新轨迹
                if (bullet.trailRenderer != null)
                {
                    bullet.trailRenderer.SetPosition(0, bullet.startPosition);
                    bullet.trailRenderer.SetPosition(1, newPos);
                }

                // 检查碰撞
                CheckBulletCollision(bullet, i);
            }
        }

        /// <summary>
        /// 检查子弹碰撞
        /// </summary>
        /// <param name="bullet">子弹数据</param>
        /// <param name="bulletIndex">子弹索引</param>
        private void CheckBulletCollision(BulletData bullet, int bulletIndex)
        {
            Vector3 bulletPos = bullet.bulletObject.transform.position;

            // 使用球形检测
            Collider[] hitColliders = Physics.OverlapSphere(bulletPos, bulletSize * 0.5f);

            foreach (var hitCollider in hitColliders)
            {
                // 跳过自己和子弹本身
                if (hitCollider.transform == transform || hitCollider.transform == bullet.bulletObject.transform)
                    continue;

                // 检查是否是果冻物体（必须有碰撞器才能被检测到）
                var jellyComponent = hitCollider.GetComponent<Tool_JRJELLY_Simple>();
                if (jellyComponent != null)
                {
                    // 计算撞击效果
                    ProcessJellyHit(jellyComponent, bullet, bulletPos);

                    // 检查是否是目标（需要销毁子弹）
                    if (bullet.targetTransform == hitCollider.transform && !bullet.hasHitTarget)
                    {
                        bullet.hasHitTarget = true;
                        if (showDebugInfo)
                            Debug.Log($"[子弹测试] 击中目标: {hitCollider.name}");

                        // 销毁子弹
                        DestroyBullet(bulletIndex);
                        return;
                    }
                    else if (!bullet.hasHitTarget)
                    {
                        // 击中其他果冻物体，但不销毁子弹
                        if (showDebugInfo)
                            Debug.Log($"[子弹测试] 击中果冻物体: {hitCollider.name}");
                    }
                }
                else
                {
                    // 击中了有碰撞器但没有果冻组件的物体
                    if (showDebugInfo)
                        Debug.Log($"[子弹测试] 子弹击中非果冻物体: {hitCollider.name}（有碰撞器但无果冻组件）");
                }
            }
        }

        /// <summary>
        /// 处理果冻物体被击中
        /// </summary>
        /// <param name="jellyComponent">果冻组件</param>
        /// <param name="bullet">子弹数据</param>
        /// <param name="hitPosition">撞击位置</param>
        private void ProcessJellyHit(Tool_JRJELLY_Simple jellyComponent, BulletData bullet, Vector3 hitPosition)
        {
            // 计算撞击距离（从发射点到撞击点）
            float hitDistance = Vector3.Distance(bullet.startPosition, hitPosition);

            // 计算甩动强度和时间（基于距离）
            float distanceRatio = Mathf.Clamp01(hitDistance / maxSwingDistance);

            // 使用曲线调整距离影响
            float intensityMultiplier = Mathf.Pow(1f - distanceRatio, distanceIntensityCurve);

            float swingIntensity = baseSwingIntensity * intensityMultiplier;
            float swingDuration = baseSwingDuration * (0.5f + intensityMultiplier * 0.5f);

            // 确保最小值
            swingIntensity = Mathf.Max(swingIntensity, 0.1f);
            swingDuration = Mathf.Max(swingDuration, 0.2f);

            // 触发甩动效果
            bool success = jellyComponent.TriggerSwing(hitPosition, swingIntensity, swingDuration);

            if (showDebugInfo)
            {
                Debug.Log($"[子弹测试] 果冻甩动 - 物体: {jellyComponent.name}, " +
                         $"距离: {hitDistance:F2}, 强度: {swingIntensity:F2}, 时长: {swingDuration:F2}, " +
                         $"成功: {success}");
            }
        }

        /// <summary>
        /// 销毁子弹
        /// </summary>
        /// <param name="index">子弹索引</param>
        private void DestroyBullet(int index)
        {
            if (index < 0 || index >= activeBullets.Count) return;

            var bullet = activeBullets[index];
            if (bullet.bulletObject != null)
                Destroy(bullet.bulletObject);

            activeBullets.RemoveAt(index);
        }

        /// <summary>
        /// 创建默认子弹预制体
        /// </summary>
        private void CreateDefaultBulletPrefab()
        {
            // 创建子弹对象
            GameObject bullet = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            bullet.name = "DefaultBullet";
            bullet.transform.localScale = Vector3.one * bulletSize;

            // 设置材质
            var renderer = bullet.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material = new Material(Shader.Find("Standard"));
                renderer.material.color = Color.yellow;
                renderer.material.SetFloat("_Metallic", 0f);
                renderer.material.SetFloat("_Smoothness", 0.8f);
            }

            // 移除碰撞器（我们用代码检测碰撞）
            var collider = bullet.GetComponent<Collider>();
            if (collider != null)
                DestroyImmediate(collider);

            // 设置为预制体
            bulletPrefab = bullet;
            bullet.SetActive(false);

            if (showDebugInfo)
                Debug.Log("[子弹测试] 创建默认子弹预制体");
        }

        #endregion

        #region 编辑器辅助方法

        [ContextMenu("刷新果冻目标")]
        public void RefreshTargetsFromMenu()
        {
            RefreshJellyTargets();
        }

        [ContextMenu("清除所有子弹")]
        public void ClearBulletsFromMenu()
        {
            ClearAllBullets();
        }

        [ContextMenu("射击当前目标")]
        public void ShootTargetFromMenu()
        {
            ShootCurrentTarget();
        }

        [ContextMenu("检查果冻物体碰撞器")]
        public void CheckCollidersFromMenu()
        {
            RefreshJellyTargets();
        }

        #endregion
    }
}