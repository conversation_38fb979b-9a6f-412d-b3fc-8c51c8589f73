Shader "Tool_JRJELLY/BLT_Cube" {
    Properties {
        // 基础材质属性
        _MainTex ("主纹理", 2D) = "white" {}
        _BumpMap ("法线贴图", 2D) = "bump" {}
        _Color ("颜色", Color) = (1,1,1,1)
        
        // 塑料材质参数
        _Roughness ("粗糙度", Range (0.1, 1)) = 0.6
        _SpecularIntensity ("高光强度", Range (0, 0.4)) = 0.15
        _SpecularSize ("高光尺寸", Range (0.01, 1)) = 0.3
        _SpecularTint ("高光色调", Color) = (1,1,1,1)
        
        // 环境光控制
        _AmbientIntensity ("环境光强度", Range (0, 3)) = 1.0
        
        // 果冻效果开关
        [Toggle(ENABLE_JELLY)] _EnableJelly ("启用果冻效果 (Enable Jelly Effect)", Float) = 0
        [Toggle(ENABLE_OUTLINE)] _EnableOutline ("启用描边效果 (Enable Outline)", Float) = 0
        [Toggle(ENABLE_COLOR_OVERLAY)] _EnableColorOverlay ("启用颜色膜层 (Enable Color Overlay)", Float) = 0
        
        // 描边效果设置
        _OutlineColor ("描边颜色 (Outline Color)", Color) = (0,0,0,1)
        _OutlineWidth ("描边宽度 (Outline Width)", Range(0,0.3)) = 0.001
        _OutlineAlpha ("描边透明度 (Outline Alpha)", Range(0,1)) = 1.0
        
        // 颜色膜层设置
        _OverlayColor ("覆盖颜色 (Overlay Color)", Color) = (1,0,0,0.5)
        _OverlayAlpha ("覆盖透明度 (Overlay Alpha)", Range(0,1)) = 0.5
        _OverlayWidthScale ("膜层宽度比例 (Overlay Width Scale)", Range(0,1)) = 1.0
    }
    
    SubShader {
        Tags { "RenderType"="Opaque" "Queue"="Geometry" }
        LOD 250
        
        // ================================================================
        // 描边Pass - 沿法线外推的描边渲染
        // ================================================================
        Pass
        {
            Name "OUTLINE"
            Tags { "LightMode" = "ForwardBase" }
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite On
            ZTest LEqual
            Cull Front
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma shader_feature ENABLE_JELLY
            #pragma shader_feature ENABLE_OUTLINE
            
            // 启用GPU Instancing
            #pragma multi_compile_instancing
            #pragma instancing_options assumeuniformscaling
            
            #include "UnityCG.cginc"

            // 描边属性
            fixed4 _OutlineColor;
            float _OutlineWidth;
            float _OutlineAlpha;

            // GPU Instancing 果冻效果数据
            UNITY_INSTANCING_BUFFER_START(JellyProps)
                UNITY_DEFINE_INSTANCED_PROP(float, _JellyEnabled)
                UNITY_DEFINE_INSTANCED_PROP(float4, _JellyOffset)
                UNITY_DEFINE_INSTANCED_PROP(float4, _JellyScale)
                UNITY_DEFINE_INSTANCED_PROP(float, _JellyIntensity)
                UNITY_DEFINE_INSTANCED_PROP(float4, _JellyDirection)
                UNITY_DEFINE_INSTANCED_PROP(float, _JellySquash)
                UNITY_DEFINE_INSTANCED_PROP(float, _JellyBendStrength)
            UNITY_INSTANCING_BUFFER_END(JellyProps)

            struct appdata
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 uv : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
                fixed4 color : COLOR;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            // 应用果冻效果的函数 - 支持三轴变形
            float4 ApplyJellyEffect(float4 vertex, float3 normal, out float3 modifiedNormal)
            {
                modifiedNormal = normal; // 默认保持原法线
                
                #ifndef ENABLE_JELLY
                    return vertex;
                #endif
                
                // 检查果冻效果脚本控制开关
                float jellyEnabled = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyEnabled);
                if (jellyEnabled < 0.5) return vertex;
                
                // 获取实例化属性
                float jellyIntensity = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyIntensity);
                float4 jellyDirection = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyDirection);
                float jellySquash = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellySquash);
                float jellyBendStrength = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyBendStrength);
                
                // === 果冻效果计算 ===
                
                // 1. 基础变形：基于顶点高度的倾斜效果
                float minY = -1.0;
                float maxY = 1.0;
                float heightFactor = saturate((vertex.y - minY) / (maxY - minY));
                float bendAmount = jellyIntensity * jellyBendStrength * 6.0;
                
                // 2. 压扁效果：Y轴缩放
                vertex.y *= jellySquash;
                
                // 3. 甩动效果：从底部到顶部的线性倾斜（支持三轴）
                if (jellyIntensity > 0.001)
                {
                    float tiltAmount = heightFactor;
                    // 支持完整的三轴变形，包括Y轴
                    float3 tiltOffset = float3(
                        jellyDirection.x * bendAmount * tiltAmount,           // X轴：线性倾斜
                        jellyDirection.y * bendAmount * tiltAmount * 0.5,     // Y轴：减半强度，避免过度拉伸
                        jellyDirection.z * bendAmount * tiltAmount            // Z轴：线性倾斜
                    );
                    vertex.xyz += tiltOffset;
                    
                    if (heightFactor < 0.1)
                    {
                        vertex.xyz += float3(
                            jellyDirection.x * bendAmount * 0.05,     // X轴最小偏移
                            jellyDirection.y * bendAmount * 0.025,    // Y轴最小偏移（减半）
                            jellyDirection.z * bendAmount * 0.05      // Z轴最小偏移
                        );
                    }
                }
                
                // 4. 法线调整（支持三轴）
                if (jellyIntensity > 0.01)
                {
                    // 法线调整适应三轴变形，Y轴使用较小的影响系数
                    float3 normalAdjust = float3(
                        jellyDirection.x * jellyIntensity * jellyBendStrength * 0.1,     // X轴法线调整
                        jellyDirection.y * jellyIntensity * jellyBendStrength * 0.05,    // Y轴法线调整（减半）
                        jellyDirection.z * jellyIntensity * jellyBendStrength * 0.1      // Z轴法线调整
                    );
                    modifiedNormal = normalize(normal + normalAdjust);
                }
                
                return vertex;
            }

            // 描边顶点函数 - 沿法线外推描边
            v2f vert(appdata v)
            {
                v2f o;
                
                // 设置GPU Instancing
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_TRANSFER_INSTANCE_ID(v, o);

                // 应用果冻效果，同时获取修改后的法线
                float3 modifiedNormal;
                v.vertex = ApplyJellyEffect(v.vertex, v.normal, modifiedNormal);

                // === 沿修改后的法线外推顶点创建描边 ===
                // 在对象空间中沿修改后的法线外推顶点
                float3 norm = normalize(modifiedNormal);
                float3 outlineVertex = v.vertex.xyz + norm * _OutlineWidth;
                
                o.pos = UnityObjectToClipPos(float4(outlineVertex, 1.0));
                o.uv = v.uv;
                
                // 设置描边颜色
                o.color = _OutlineColor;
                
                return o;
            }

            // 描边片段函数
            fixed4 frag(v2f i) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(i);
                
                #ifndef ENABLE_OUTLINE
                    discard; // 如果描边未启用，丢弃所有像素
                #endif
                
                // 直接返回颜色，透明度由_OutlineAlpha控制
                fixed4 finalColor = i.color;
                finalColor.a *= _OutlineAlpha;
                
                // 如果透明度太低，丢弃像素
                if (finalColor.a < 0.01)
                    discard;
                
                return finalColor;
            }
            ENDCG
        }
        
        // ================================================================
        // 主渲染Pass - 表面着色器，保持原有的塑料材质效果
        // ================================================================
        CGPROGRAM
        #pragma surface surf CustomLighting fullforwardshadows vertex:vert
        #pragma target 3.0
        
        // 启用GPU Instancing
        #pragma multi_compile_instancing
        #pragma instancing_options assumeuniformscaling
        
        // 功能开关
        #pragma shader_feature ENABLE_JELLY
        #pragma shader_feature ENABLE_OUTLINE
        #pragma shader_feature ENABLE_COLOR_OVERLAY
        
        #include "UnityCG.cginc"
        #include "Lighting.cginc"
        #include "AutoLight.cginc"
        
        // GPU Instancing 果冻效果数据
        UNITY_INSTANCING_BUFFER_START(JellyProps)
            UNITY_DEFINE_INSTANCED_PROP(float, _JellyEnabled)
            UNITY_DEFINE_INSTANCED_PROP(float4, _JellyOffset)
            UNITY_DEFINE_INSTANCED_PROP(float4, _JellyScale)
            UNITY_DEFINE_INSTANCED_PROP(float, _JellyIntensity)
            UNITY_DEFINE_INSTANCED_PROP(float4, _JellyDirection)
            UNITY_DEFINE_INSTANCED_PROP(float, _JellySquash)
            UNITY_DEFINE_INSTANCED_PROP(float, _JellyBendStrength)
        UNITY_INSTANCING_BUFFER_END(JellyProps)
        
        sampler2D _MainTex;
        sampler2D _BumpMap;
        fixed4 _Color;
        
        // 塑料质感控制
        fixed _Roughness;
        fixed _SpecularIntensity;
        fixed _SpecularSize;
        fixed4 _SpecularTint;
        
        // 环境光控制
        fixed _AmbientIntensity;

        struct Input {
            float2 uv_MainTex;
            float2 uv_BumpMap;
            float3 viewDir;
            UNITY_VERTEX_INPUT_INSTANCE_ID  // 添加GPU Instancing支持
        };

        // 果冻效果顶点函数
        void vert(inout appdata_full v, out Input o)
        {
            UNITY_INITIALIZE_OUTPUT(Input, o);
            
            // 设置GPU Instancing
            UNITY_SETUP_INSTANCE_ID(v);
            UNITY_TRANSFER_INSTANCE_ID(v, o);
            
            #ifndef ENABLE_JELLY
                return;
            #endif
            
            // 检查当前实例的果冻效果开关
            float jellyEnabled = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyEnabled);
            if (jellyEnabled < 0.5)
                return;
            
            // 获取实例化属性
            float jellyIntensity = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyIntensity);
            float4 jellyDirection = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyDirection);
            float jellySquash = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellySquash);
            float jellyBendStrength = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyBendStrength);
            
            // === 果冻效果计算 ===
            
            // 1. 基础变形：基于顶点高度的倾斜效果
            float minY = -1.0;
            float maxY = 1.0;
            float heightFactor = saturate((v.vertex.y - minY) / (maxY - minY));
            float bendAmount = jellyIntensity * jellyBendStrength * 6.0;
            
            // 2. 压扁效果：Y轴缩放
            v.vertex.y *= jellySquash;
            
            // 3. 甩动效果：从底部到顶部的线性倾斜（支持三轴）
            if (jellyIntensity > 0.001)
            {
                float tiltAmount = heightFactor;
                // 支持完整的三轴变形，包括Y轴
                float3 tiltOffset = float3(
                    jellyDirection.x * bendAmount * tiltAmount,           // X轴：线性倾斜
                    jellyDirection.y * bendAmount * tiltAmount * 0.5,     // Y轴：减半强度，避免过度拉伸
                    jellyDirection.z * bendAmount * tiltAmount            // Z轴：线性倾斜
                );
                v.vertex.xyz += tiltOffset;
                
                if (heightFactor < 0.1)
                {
                    v.vertex.xyz += float3(
                        jellyDirection.x * bendAmount * 0.05,     // X轴最小偏移
                        jellyDirection.y * bendAmount * 0.025,    // Y轴最小偏移（减半）
                        jellyDirection.z * bendAmount * 0.05      // Z轴最小偏移
                    );
                }
            }
            
            // 4. 法线调整（支持三轴）
            if (jellyIntensity > 0.01)
            {
                // 法线调整适应三轴变形，Y轴使用较小的影响系数
                float3 normalAdjust = float3(
                    jellyDirection.x * jellyIntensity * jellyBendStrength * 0.1,     // X轴法线调整
                    jellyDirection.y * jellyIntensity * jellyBendStrength * 0.05,    // Y轴法线调整（减半）
                    jellyDirection.z * jellyIntensity * jellyBendStrength * 0.1      // Z轴法线调整
                );
                v.normal = normalize(v.normal + normalAdjust);
            }
        }

        // 简化的光照模型 - 移除暗部调节功能，但保留粗糙度控制
        inline fixed4 LightingCustomLighting (SurfaceOutput s, fixed3 lightDir, fixed3 viewDir, fixed atten)
        {
            // 基础漫反射计算
            fixed NdotL = max(0, dot(s.Normal, lightDir));
            fixed lightStrength = NdotL * atten;
            
            // 塑料高光计算 - 使用粗糙度控制高光范围
            float3 halfVector = normalize(lightDir + viewDir);
            float NdotH = saturate(dot(s.Normal, halfVector));
            
            // 粗糙度影响高光：粗糙度越高，高光越分散（指数越小）
            float roughnessExponent = lerp(256, 8, _Roughness); // 粗糙度0.1->256, 1.0->8
            float specularTerm = pow(NdotH, roughnessExponent * _SpecularSize) * _SpecularIntensity;
            
            // 粗糙度也影响漫反射：粗糙度越高，漫反射越强
            float diffuseBoost = lerp(1.0, 1.5, _Roughness);
            
            // 应用高光色调
            float3 specular = specularTerm * _SpecularTint.rgb * _LightColor0.rgb;
            
            // 标准漫反射光照 - 应用粗糙度增强
            fixed3 diffuse = s.Albedo * _LightColor0.rgb * lightStrength * diffuseBoost;
            
            // 基础环境光 - 应用环境光强度调节
            fixed3 ambient = s.Albedo * UNITY_LIGHTMODEL_AMBIENT.rgb * _AmbientIntensity;
            
            // 组合最终颜色：标准光照 + 高光 + 环境光
            fixed3 finalColor = diffuse + specular + ambient;
            
            fixed4 c;
            c.rgb = finalColor;
            c.a = s.Alpha;
            return c;
        }

        void surf (Input IN, inout SurfaceOutput o) {
            // 设置GPU Instancing
            UNITY_SETUP_INSTANCE_ID(IN);
            
            fixed4 tex = tex2D(_MainTex, IN.uv_MainTex) * _Color;
            o.Albedo = tex.rgb;
            o.Normal = UnpackNormal(tex2D(_BumpMap, IN.uv_BumpMap));
            o.Alpha = tex.a;
        }
        
        ENDCG
        
        // ================================================================
        // 颜色膜层Pass - 显示在描边之上的颜色覆盖层
        // ================================================================
        Pass
        {
            Name "COLOR_OVERLAY"
            Tags { "LightMode" = "Always" }
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite Off
            ZTest LEqual
            Cull Off
            Offset -1, -1  // 添加深度偏移，避免Z-fighting

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma shader_feature ENABLE_COLOR_OVERLAY
            #pragma shader_feature ENABLE_JELLY
            
            // 启用GPU Instancing
            #pragma multi_compile_instancing
            #pragma instancing_options assumeuniformscaling
            
            #include "UnityCG.cginc"

            // 颜色膜层属性
            fixed4 _OverlayColor;
            float _OverlayAlpha;
            float _OverlayWidthScale;

            // 描边属性 - 需要与描边保持一致的大小
            float _OutlineWidth;

            // GPU Instancing 果冻效果数据
            UNITY_INSTANCING_BUFFER_START(JellyProps)
                UNITY_DEFINE_INSTANCED_PROP(float, _JellyEnabled)
                UNITY_DEFINE_INSTANCED_PROP(float4, _JellyOffset)
                UNITY_DEFINE_INSTANCED_PROP(float4, _JellyScale)
                UNITY_DEFINE_INSTANCED_PROP(float, _JellyIntensity)
                UNITY_DEFINE_INSTANCED_PROP(float4, _JellyDirection)
                UNITY_DEFINE_INSTANCED_PROP(float, _JellySquash)
                UNITY_DEFINE_INSTANCED_PROP(float, _JellyBendStrength)
            UNITY_INSTANCING_BUFFER_END(JellyProps)

            struct appdata
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 uv : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
                fixed4 color : COLOR;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            // 应用果冻效果的函数 - 与描边Pass完全一致
            float4 ApplyJellyEffect(float4 vertex, float3 normal, out float3 modifiedNormal)
            {
                modifiedNormal = normal; // 默认保持原法线
                
                #ifndef ENABLE_JELLY
                    return vertex;
                #endif
                
                // 检查果冻效果脚本控制开关
                float jellyEnabled = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyEnabled);
                if (jellyEnabled < 0.5) return vertex;
                
                // 获取实例化属性
                float jellyIntensity = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyIntensity);
                float4 jellyDirection = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyDirection);
                float jellySquash = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellySquash);
                float jellyBendStrength = UNITY_ACCESS_INSTANCED_PROP(JellyProps, _JellyBendStrength);
                
                // === 果冻效果计算 - 与主体Pass完全一致 ===
                
                // 1. 基础变形：基于顶点高度的倾斜效果
                float minY = -1.0;
                float maxY = 1.0;
                float heightFactor = saturate((vertex.y - minY) / (maxY - minY));
                float bendAmount = jellyIntensity * jellyBendStrength * 6.0;
                
                // 2. 压扁效果：Y轴缩放
                vertex.y *= jellySquash;
                
                // 3. 甩动效果：从底部到顶部的线性倾斜（支持三轴）
                if (jellyIntensity > 0.001)
                {
                    float tiltAmount = heightFactor;
                    // 支持完整的三轴变形，包括Y轴
                    float3 tiltOffset = float3(
                        jellyDirection.x * bendAmount * tiltAmount,           // X轴：线性倾斜
                        jellyDirection.y * bendAmount * tiltAmount * 0.5,     // Y轴：减半强度，避免过度拉伸
                        jellyDirection.z * bendAmount * tiltAmount            // Z轴：线性倾斜
                    );
                    vertex.xyz += tiltOffset;
                    
                    if (heightFactor < 0.1)
                    {
                        vertex.xyz += float3(
                            jellyDirection.x * bendAmount * 0.05,     // X轴最小偏移
                            jellyDirection.y * bendAmount * 0.025,    // Y轴最小偏移（减半）
                            jellyDirection.z * bendAmount * 0.05      // Z轴最小偏移
                        );
                    }
                }
                
                // 4. 法线调整（支持三轴）
                if (jellyIntensity > 0.01)
                {
                    // 法线调整适应三轴变形，Y轴使用较小的影响系数
                    float3 normalAdjust = float3(
                        jellyDirection.x * jellyIntensity * jellyBendStrength * 0.1,     // X轴法线调整
                        jellyDirection.y * jellyIntensity * jellyBendStrength * 0.05,    // Y轴法线调整（减半）
                        jellyDirection.z * jellyIntensity * jellyBendStrength * 0.1      // Z轴法线调整
                    );
                    modifiedNormal = normalize(normal + normalAdjust);
                }
                
                return vertex;
            }

            // 颜色膜层顶点函数
            v2f vert(appdata v)
            {
                v2f o;
                
                // 设置GPU Instancing
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_TRANSFER_INSTANCE_ID(v, o);

                // 应用果冻效果，同时获取修改后的法线
                float3 modifiedNormal;
                v.vertex = ApplyJellyEffect(v.vertex, v.normal, modifiedNormal);

                // === 与描边Pass完全一致的膜层算法：确保厚度精确匹配 ===
                // 使用与描边相同的对象空间法线外推方法
                float3 norm = normalize(modifiedNormal);
                float overlayThickness = _OutlineWidth * _OverlayWidthScale;
                
                // 添加微小的额外厚度，确保完全覆盖描边
                float extraThickness = overlayThickness * 0.01; // 增加1%的厚度
                float totalThickness = overlayThickness + extraThickness;
                
                // 在对象空间中沿法线外推，与描边Pass完全一致的计算方式
                float3 overlayVertex = v.vertex.xyz + norm * totalThickness;
                
                o.pos = UnityObjectToClipPos(float4(overlayVertex, 1.0));
                o.uv = v.uv;
                
                // 设置膜层颜色和透明度
                o.color = _OverlayColor;
                o.color.a *= _OverlayAlpha;
                
                return o;
            }

            // 颜色膜层片段函数
            fixed4 frag(v2f i) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(i);
                
                #ifndef ENABLE_COLOR_OVERLAY
                    discard; // 如果膜层未启用，丢弃所有像素
                #endif
                
                return i.color;
            }
            ENDCG
        }
    }
    
    FallBack "Mobile/VertexLit"
}