using System;
using UnityEngine;

namespace JRJelly
{
    /// <summary>
    /// 果冻效果配置设置类
    /// 统一管理所有果冻效果的参数配置
    /// </summary>
    [System.Serializable]
    public class Tool_JRJELLY_Settings
    {
        [Header("=== 基础设置 ===")]
        [Tooltip("果冻刚性强度 - 影响形变的抵抗力")]
        [Range(0.1f, 5f)]
        public float rigidity = 1f;

        [Tooltip("回弹强度 - 影响回弹的速度")]
        [Range(0.1f, 3f)]
        public float elasticity = 1.5f;

        [Tooltip("阻尼系数 - 影响振动衰减")]
        [Range(0.1f, 2f)]
        public float damping = 0.8f;

        [Tooltip("是否在无效果时休眠")]
        public bool enableSleep = true;

        [Tooltip("休眠强度阈值")]
        [Range(0.001f, 0.1f)]
        public float sleepThreshold = 0.01f;

        [Header("=== 甩动效果设置 ===")]
        [Tooltip("甩动最大强度")]
        [Range(0.1f, 2f)]
        public float swingMaxIntensity = 1f;

        [Tooltip("甩动影响范围")]
        [Range(0.1f, 5f)]
        public float swingRange = 2f;

        [Tooltip("甩动衰减速度")]
        [Range(0.5f, 3f)]
        public float swingDecay = 1.2f;

        [Header("=== 刹车回弹设置 ===")]
        [Tooltip("刹车回弹强度")]
        [Range(0.1f, 1.5f)]
        public float brakeReboundIntensity = 0.8f;

        [Header("=== 扩散甩动设置 ===")]
        [Tooltip("扩散甩动强度")]
        [Range(0.5f, 2f)]
        public float spreadSwingIntensity = 1.2f;

        [Tooltip("扩散影响半径")]
        [Range(0.5f, 4f)]
        public float spreadRadius = 2f;

        [Header("=== 性能优化 ===")]
        [Tooltip("更新间隔(毫秒)")]
        [Range(10f, 100f)]
        public float updateInterval = 16f; // 约60FPS

        /// <summary>
        /// 默认设置
        /// </summary>
        public static Tool_JRJELLY_Settings Default
        {
            get
            {
                return new Tool_JRJELLY_Settings
                {
                    rigidity = 1f,
                    elasticity = 1.5f,
                    damping = 0.8f,
                    enableSleep = true,
                    sleepThreshold = 0.01f,
                    swingMaxIntensity = 1f,
                    swingRange = 2f,
                    swingDecay = 1.2f,
                    brakeReboundIntensity = 0.8f,
                    spreadSwingIntensity = 1.2f,
                    spreadRadius = 2f,
                    updateInterval = 16f
                };
            }
        }

        /// <summary>
        /// 验证设置参数的有效性
        /// </summary>
        public void ValidateSettings()
        {
            rigidity = Mathf.Clamp(rigidity, 0.1f, 5f);
            elasticity = Mathf.Clamp(elasticity, 0.1f, 3f);
            damping = Mathf.Clamp(damping, 0.1f, 2f);
            sleepThreshold = Mathf.Clamp(sleepThreshold, 0.001f, 0.1f);
            swingMaxIntensity = Mathf.Clamp(swingMaxIntensity, 0.1f, 2f);
            swingRange = Mathf.Clamp(swingRange, 0.1f, 5f);
            swingDecay = Mathf.Clamp(swingDecay, 0.5f, 3f);
            brakeReboundIntensity = Mathf.Clamp(brakeReboundIntensity, 0.1f, 1.5f);
            spreadSwingIntensity = Mathf.Clamp(spreadSwingIntensity, 0.5f, 2f);
            spreadRadius = Mathf.Clamp(spreadRadius, 0.5f, 4f);
            updateInterval = Mathf.Clamp(updateInterval, 10f, 100f);
        }

        /// <summary>
        /// 创建设置的深拷贝
        /// </summary>
        public Tool_JRJELLY_Settings Clone()
        {
            return new Tool_JRJELLY_Settings
            {
                rigidity = this.rigidity,
                elasticity = this.elasticity,
                damping = this.damping,
                enableSleep = this.enableSleep,
                sleepThreshold = this.sleepThreshold,
                swingMaxIntensity = this.swingMaxIntensity,
                swingRange = this.swingRange,
                swingDecay = this.swingDecay,
                brakeReboundIntensity = this.brakeReboundIntensity,
                spreadSwingIntensity = this.spreadSwingIntensity,
                spreadRadius = this.spreadRadius,
                updateInterval = this.updateInterval
            };
        }
    }

    /// <summary>
    /// 果冻效果类型枚举
    /// </summary>
    public enum JellyEffectType
    {
        /// <summary>基于目标点的甩动</summary>
        TargetSwing,
        /// <summary>刹车回弹</summary>
        BrakeRebound,
        /// <summary>扩散甩动</summary>
        SpreadSwing
    }

    /// <summary>
    /// 果冻效果数据结构
    /// </summary>
    [System.Serializable]
    public struct JellyEffectData
    {
        /// <summary>效果类型</summary>
        public JellyEffectType effectType;
        /// <summary>目标位置</summary>
        public Vector3 targetPosition;
        /// <summary>效果强度</summary>
        public float intensity;
        /// <summary>效果方向</summary>
        public Vector3 direction;
        /// <summary>持续时间</summary>
        public float duration;
        /// <summary>开始时间</summary>
        public float startTime;

        public JellyEffectData(JellyEffectType type, Vector3 target, float force, float time = 1f)
        {
            effectType = type;
            targetPosition = target;
            intensity = force;
            direction = Vector3.zero;
            duration = time;
            startTime = Time.time;
        }

        /// <summary>
        /// 获取效果进度 (0-1)
        /// </summary>
        public float GetProgress()
        {
            if (duration <= 0f) return 1f;
            return Mathf.Clamp01((Time.time - startTime) / duration);
        }

        /// <summary>
        /// 效果是否已完成
        /// </summary>
        public bool IsFinished()
        {
            return GetProgress() >= 1f;
        }
    }
}