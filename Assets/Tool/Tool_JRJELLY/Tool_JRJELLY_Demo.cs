using UnityEngine;

namespace JRJelly
{
    /// <summary>
    /// 果冻工具演示脚本
    /// 展示如何使用果冻效果
    /// </summary>
    public class Tool_JRJELLY_Demo : MonoBehaviour
    {
        [Header("=== 演示设置 ===")]
        [Tooltip("果冻对象")]
        public Tool_JRJELLY_Simple jellyController;

        [Tooltip("是否自动演示")]
        public bool autoDemo = true;

        [Toolt<PERSON>("演示间隔")]
        public float demoInterval = 3f;

        [Header("=== 手动触发测试 ===")]
        [Tooltip("目标点位置")]
        public Transform targetPoint;

        [Space]
        [Tooltip("甩动强度")]
        [Range(0.1f, 2f)]
        public float swingIntensity = 0.8f;

        [Tooltip("回弹强度")]
        [Range(0.1f, 1f)]
        public float reboundIntensity = 0.6f;

        [Tooltip("呼吸效果方向")]
        public Transform breatheDirectionTarget;

        [Tooltip("呼吸效果持续时间(0表示持续到手动停止)")]
        public float breatheDuration = 0f;

        private float nextDemoTime = 0f;
        private int demoStep = 0;
        private bool isBreathingActive = false; // 记录呼吸效果是否激活

        void Start()
        {
            if (jellyController == null)
            {
                jellyController = FindObjectOfType<Tool_JRJELLY_Simple>();
            }

            if (jellyController == null)
            {
                Debug.LogError("[JRJELLY_DEMO] 找不到果冻控制器！");
                enabled = false;
            }
        }

        void Update()
        {
            if (autoDemo && Time.time >= nextDemoTime)
            {
                RunAutoDemo();
                nextDemoTime = Time.time + demoInterval;
            }

            HandleInput();
        }

        /// <summary>
        /// 自动演示
        /// </summary>
        private void RunAutoDemo()
        {
            if (jellyController == null) return;

            switch (demoStep % 3) // 改为3个演示步骤
            {
                case 0:
                    // 甩动演示
                    Vector3 randomTarget = jellyController.transform.position +
                        new Vector3(Random.Range(-2f, 2f), 0, Random.Range(-2f, 2f));
                    jellyController.TriggerSwing(randomTarget, swingIntensity, 0.5f);
                    Debug.Log($"[DEMO] 触发甩动效果 - 强度:{swingIntensity:F2}");
                    break;

                case 1:
                    // 刹车演示
                    Vector3 randomDir = new Vector3(Random.Range(-1f, 1f), 0, Random.Range(-1f, 1f)).normalized;
                    jellyController.TriggerRebound(randomDir, reboundIntensity);
                    Debug.Log("[DEMO] 触发刹车抖动效果");
                    break;

                case 2:
                    // 呼吸演示
                    Vector3 breatheDir = breatheDirectionTarget != null
                        ? (breatheDirectionTarget.position - jellyController.transform.position).normalized
                        : Vector3.forward;
                    jellyController.TriggerBreathe(breatheDir, 3f); // 短时间演示
                    Debug.Log("[DEMO] 触发呼吸效果");
                    break;
            }

            demoStep++;
        }

        /// <summary>
        /// 处理输入
        /// </summary>
        private void HandleInput()
        {
            if (jellyController == null) return;

            // Q键 - 甩动效果
            if (Input.GetKeyDown(KeyCode.Q))
            {
                Vector3 targetPos = targetPoint != null
                    ? targetPoint.position
                    : jellyController.transform.position + Vector3.forward * 2f;
                jellyController.TriggerSwing(targetPos, swingIntensity, 0.6f);
                Debug.Log($"[DEMO] 手动触发甩动效果: 强度={swingIntensity}");
            }

            // E键 - 刹车回弹效果
            if (Input.GetKeyDown(KeyCode.E))
            {
                Vector3 direction = Vector3.forward;
                if (targetPoint != null)
                    direction = (targetPoint.position - jellyController.transform.position).normalized;

                jellyController.TriggerRebound(direction, reboundIntensity);
                Debug.Log($"[DEMO] 手动触发刹车回弹效果: 强度={reboundIntensity}");
            }

            // R键 - 呼吸效果开关
            if (Input.GetKeyDown(KeyCode.R))
            {
                if (!isBreathingActive)
                {
                    Vector3 breatheDirection = breatheDirectionTarget != null
                        ? (breatheDirectionTarget.position - jellyController.transform.position).normalized
                        : Vector3.forward;

                    jellyController.TriggerBreathe(breatheDirection, breatheDuration);
                    isBreathingActive = true;
                    Debug.Log("[DEMO] 手动启动呼吸效果");
                }
                else
                {
                    jellyController.StopBreathe();
                    isBreathingActive = false;
                    Debug.Log("[DEMO] 手动停止呼吸效果");
                }
            }

            // T键 - 停止所有效果
            if (Input.GetKeyDown(KeyCode.T))
            {
                jellyController.StopAll();
                isBreathingActive = false;
                Debug.Log("[DEMO] 停止所有效果");
            }

            // 更新呼吸状态
            if (isBreathingActive && !jellyController.IsBreathing)
            {
                isBreathingActive = false;
            }
        }

        void OnGUI()
        {
            if (jellyController == null) return;

            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            GUILayout.Label("=== 果冻效果演示控制 ===", GUI.skin.box);

            GUILayout.Space(10);
            GUILayout.Label("键盘控制:");
            GUILayout.Label("Q - 甩动效果");
            GUILayout.Label("E - 刹车回弹效果");
            GUILayout.Label("R - 呼吸效果 开/关");
            GUILayout.Label("T - 停止所有效果");

            GUILayout.Space(10);
            GUILayout.Label($"当前状态:");
            GUILayout.Label($"活跃效果: {jellyController.HasActiveEffects}");
            GUILayout.Label($"呼吸状态: {(isBreathingActive ? "激活" : "停止")}");

            GUILayout.Space(10);
            autoDemo = GUILayout.Toggle(autoDemo, "自动演示");

            if (autoDemo)
            {
                demoInterval = GUILayout.HorizontalSlider(demoInterval, 1f, 10f);
                GUILayout.Label($"演示间隔: {demoInterval:F1}s");
            }

            GUILayout.EndArea();
        }
    }
}