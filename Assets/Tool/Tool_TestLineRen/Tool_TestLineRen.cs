using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tool_TestLineRen 
{
    static GameObject ColCheckLinePrefab;

    public static void DrawLine(Vector3 pos, Vector3 topos)
    {
#if UNITY_EDITOR
        if (ColCheckLinePrefab == null)
            ColCheckLinePrefab = Resources.Load<GameObject>("ColCheckLine");
        var line = GameObject.Instantiate(ColCheckLinePrefab).GetComponent<LineRenderer>();
        line.SetPosition(0, pos);
        line.SetPosition(1, topos);
#endif
    }

}
