using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Runtime.InteropServices;
using System;
using WeChatWASM;
using LitJson;

namespace QGMiniGame
{
    public class QGMiniGameManager : MonoBehaviour
    {
        #region Instance

        private static QGMiniGameManager instance = null;


        public static QGMiniGameManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new GameObject(typeof(QGMiniGameManager).Name).AddComponent<QGMiniGameManager>();
                    DontDestroyOnLoad(instance.gameObject);
                }
                return instance;
            }
        }

        #endregion

        public static int PlatFormVersion = -1;

        public static Action<OnTouchStartCallbackResult> onTouchStartAction = null;
        public static Action<OnTouchStartCallbackResult> onTouchEndAction = null;
        public static Action<OnTouchStartCallbackResult> onTouchCancelAction = null;
        public static Action<OnTouchStartCallbackResult> onTouchMoveAction = null;

        public void Init()
        {

#if UNITY_EDITOR

#else

#if Wechat
           
#endif
#if OPPO
                 InitOPPO(60);
                         QGRegisterTouch();
#endif
#if VIVO
                     InitVIVO(60);
                             QGRegisterTouch();
#endif

#endif


        }

        public void ShowADisReading()
        {
            #if !UNITY_ANDROID
            ShowADReading();
#endif
        }

        public void SetToLoadingProgress(int progress)
        {
            #if !UNITY_ANDROID
            SetLoadingProgress(progress);
#endif
        }

        public void VibShort()
        {
            #if !UNITY_ANDROID
            QGVibShort();
#endif
        }



        public void ExitApplication()
        {
            #if !UNITY_ANDROID
            QGExitApplication();
#endif
        }

#region 登录

        public void Login(Action<QGCommonResponse<QGLoginBean>> successCallback = null, Action<QGCommonResponse<QGLoginBean>> failCallback = null)
        {
#if !UNITY_ANDROID
            QGLogin(QGCallBackManager.Add(successCallback), QGCallBackManager.Add(failCallback));
#endif
        }

#endregion

#region 用户信息

        public void GetUserInfo(Action<QGCommonResponse<QGUserInfoBean>> successCallback = null, Action<QGCommonResponse<QGUserInfoBean>> failCallback = null)
        {
            #if !UNITY_ANDROID
            QGGetUserInfo(QGCallBackManager.Add(successCallback), QGCallBackManager.Add(failCallback));
#endif
        }

#endregion

#region 获取桌面图标是否创建

        public void HasShortcutInstalled(Action<QGCommonResponse<QGShortcutBean>> successCallback = null, Action<QGCommonResponse<QGShortcutBean>> failCallback = null)
        {
#if !UNITY_ANDROID
            QGHasShortcutInstalled(QGCallBackManager.Add(successCallback), QGCallBackManager.Add(failCallback));
#endif
            }

#endregion

            #region 创建桌面图标

        public void InstallShortcut(string message, Action<QGBaseResponse> successCallback = null, Action<QGBaseResponse> failCallback = null)
        {
#if !UNITY_ANDROID
            QGInstallShortcut(message, QGCallBackManager.Add(successCallback), QGCallBackManager.Add(failCallback));
#endif
            }

#endregion

#region 创建Banner广告

        public QGBannerAd CreateBannerAd(QGCreateBannerAdParam param)
        {
            var adId = QGCallBackManager.getKey();
            QGBannerAd ad = new QGBannerAd(adId);
            #if !UNITY_ANDROID
            QGCreateBannerAd(adId, param.posId, JsonUtility.ToJson(param.style), param.adIntervals);
#endif
            return ad;
        }

#endregion

#region 创建插屏广告

        public QGInterstitialAd CreateInterstitialAd(QGCommonAdParam param)
        {
            var adId = QGCallBackManager.getKey();
            QGInterstitialAd ad = new QGInterstitialAd(adId);
            #if !UNITY_ANDROID
            QGCreateInterstitialAd(adId, param.posId);
#endif
            return ad;
        }

#endregion

#region 创建激励视频广告

        public QGRewardedVideoAd CreateRewardedVideoAd(QGCommonAdParam param)
        {
            var adId = QGCallBackManager.getKey();
            QGRewardedVideoAd ad = new QGRewardedVideoAd(adId);
            #if !UNITY_ANDROID
            QGCreateRewardedVideoAd(adId, param.posId);
#endif
            return ad;
        }

#endregion

#region 创建原生广告

        public QGNativeAd CreateNativeAd(QGCommonAdParam param)
        {
            var adId = QGCallBackManager.getKey();
            QGNativeAd ad = new QGNativeAd(adId);
            #if !UNITY_ANDROID
            QGCreateNativeAd(adId, param.posId);
#endif
            return ad;
        }

#endregion

#region 创建模板广告

        public QGCustomAd CreateCustomAd(QGCreateCustomAdParam param)
        {
            var adId = QGCallBackManager.getKey();
            QGCustomAd ad = new QGCustomAd(adId);
            #if !UNITY_ANDROID
            QGCreateCustomAd(adId, param.posId, JsonUtility.ToJson(param.style));
#endif
            return ad;
        }

        public void CreateAndShowCustomAd(string adid, int leftPer, int topPer, int widthPer)
        {

#if !UNITY_ANDROID
            CreateAndShowCustomAD(adid, leftPer,topPer,widthPer);
#endif

        }

        public bool IsShow(string adId)
        {
#if !UNITY_ANDROID
            return QGIsShow(adId);
#endif
            return false;
        }

#endregion

#region 创建横幅广告

        public QGBoxBannerAd CreateBoxBannerAd(QGCommonAdParam param)
        {
            var adId = QGCallBackManager.getKey();
            QGBoxBannerAd ad = new QGBoxBannerAd(adId);
            #if !UNITY_ANDROID
            QGCreateBoxBannerAd(adId, param.posId);
#endif
            return ad;
        }

#endregion

#region 创建九宫格广告

        public QGBoxPortalAd CreateBoxPortalAd(QGCreateBoxPortalAdParam param)
        {
            var adId = QGCallBackManager.getKey();
            QGBoxPortalAd ad = new QGBoxPortalAd(adId);
            #if !UNITY_ANDROID
            QGCreateBoxPortalAd(adId, param.posId, param.image, param.marginTop);
#endif
            return ad;
        }

#endregion

#region 原生广告曝光

        public void ReportAdShow(string adId, QGNativeReportParam param)
        {
            #if !UNITY_ANDROID
            QGReportAdShow(adId, param.adId);
#endif
        }

        public void ReportAdClick(string adId, QGNativeReportParam param)
        {
            #if !UNITY_ANDROID
            QGReportAdClick(adId, param.adId);
#endif
        }

#endregion

#region 广告通用逻辑
        public void ShowAd(string adId, Action<QGBaseResponse> successCallback = null, Action<QGBaseResponse> failCallback = null)
        {
#if !UNITY_ANDROID
            QGShowAd(adId, QGCallBackManager.Add(successCallback), QGCallBackManager.Add(failCallback));
#endif
            }

        public void HideAd(string adId, Action<QGBaseResponse> successCallback = null, Action<QGBaseResponse> failCallback = null)
        {
#if !UNITY_ANDROID
            QGHideAd(adId, QGCallBackManager.Add(successCallback), QGCallBackManager.Add(failCallback));
#endif
        }

        public void LoadAd(string adId, Action<QGBaseResponse> successCallback = null, Action<QGBaseResponse> failCallback = null)
        {
#if !UNITY_ANDROID
            QGLoadAd(adId, QGCallBackManager.Add(successCallback), QGCallBackManager.Add(failCallback));
#endif
        }

        public void DestroyAd(string adId)
        {
#if !UNITY_ANDROID
            QGDestroyAd(adId);
#endif
        }

        #endregion

        #region unity的PlayerPrefs

        public void StorageSetIntSync(string key, int value)
        {
#if !UNITY_ANDROID
            QGStorageSetIntSync(key, value);
#endif
        }

        public int StorageGetIntSync(string key, int defaultValue)
        {
#if !UNITY_ANDROID
            return QGStorageGetIntSync(key, defaultValue);
#endif
            return 0;
        }

        public void StorageSetStringSync(string key, string value)
        {
#if !UNITY_ANDROID
            QGStorageSetStringSync(key, value);
#endif
        }

        public string StorageGetStringSync(string key, string defaultValue)
        {
#if !UNITY_ANDROID
            return QGStorageGetStringSync(key, defaultValue);
#endif
            return "";
        }

        public void StorageSetFloatSync(string key, float value)
        {
#if !UNITY_ANDROID
            QGStorageSetFloatSync(key, value);
#endif
        }

        public float StorageGetFloatSync(string key, float defaultValue)
        {
#if !UNITY_ANDROID
            return QGStorageGetFloatSync(key, defaultValue);
#endif
            return 0;
        }

        public void StorageDeleteAllSync()
        {
#if !UNITY_ANDROID
            QGStorageDeleteAllSync();
#endif
        }

        public void StorageDeleteKeySync(string key)
        {
#if !UNITY_ANDROID
            QGStorageDeleteKeySync(key);
#endif
        }

        public bool StorageHasKeySync(string key)
        {
#if !UNITY_ANDROID
            return QGStorageHasKeySync(key);
#endif
            return false;
        }

        #endregion

        #region 支付
        public void Pay(PayParam param, Action<QGCommonResponse<QGPayBean>> successCallback = null, Action<QGCommonResponse<QGPayBean>> failCallback = null, Action<QGCommonResponse<QGPayBean>> cancelCallback = null, Action<QGCommonResponse<QGPayBean>> completeCallback = null)
        {
            #if !UNITY_ANDROID
            QGPay(JsonUtility.ToJson(param), QGCallBackManager.Add(successCallback), QGCallBackManager.Add(failCallback), QGCallBackManager.Add(cancelCallback), QGCallBackManager.Add(completeCallback));
#endif
            }
#endregion

#region 判断文件是否存在
        public string AccessFile(string uri)
        {
#if !UNITY_ANDROID
            return QGAccessFile(uri);
#endif
            return "";
        }
#endregion

#region 读取文件
        public void ReadFile(QGFileParam param, Action<QGFileResponse> successCallback = null, Action<QGFileResponse> failCallback = null)
        {
            #if !UNITY_ANDROID
            QGReadFile(param.uri, param.encoding, param.position, param.length, QGCallBackManager.Add(successCallback), QGCallBackManager.Add(failCallback));
#endif
            }
#endregion

#region 写入文件
        public void WriteFile(QGFileParam param, Action<QGFileResponse> successCallback = null, Action<QGFileResponse> failCallback = null)
        {
            #if !UNITY_ANDROID
            QGWriteFile(param.uri, param.encoding, param.position, param.textStr, param.textData, param.textData == null ? 0 : param.textData.Length, QGCallBackManager.Add(successCallback), QGCallBackManager.Add(failCallback));
#endif
            }
#endregion

#region JS回调

        public void GetTouchStart(string datajson)
        {
            OnTouchStartCallbackResult touchr = JsonMapper.ToObject<OnTouchStartCallbackResult>(datajson);
            if (onTouchStartAction != null)
                onTouchStartAction(touchr);
        }

        public void GetTouchEnd(string datajson)
        {
            OnTouchStartCallbackResult touchr = JsonMapper.ToObject<OnTouchStartCallbackResult>(datajson);

            if (onTouchEndAction != null)
                onTouchEndAction(touchr);
        }
        public void GetTouchCancel(string datajson)
        {
            OnTouchStartCallbackResult touchr = JsonMapper.ToObject<OnTouchStartCallbackResult>(datajson);

            if (onTouchCancelAction != null)
                onTouchCancelAction(touchr);
        }
        public void GetTouchMove(string datajson)
        {
            OnTouchStartCallbackResult touchr = JsonMapper.ToObject<OnTouchStartCallbackResult>(datajson);

            if (onTouchMoveAction != null)
                onTouchMoveAction(touchr);
        }


        public void GetPlatformVersion(string version)
        {

            var res = JsonUtility.FromJson<GetPlatVersionData>(version);
             PlatFormVersion = int.Parse(res.version);
        }

        public void LoginResponseCallback(string msg)
        {
            QGCallBackManager.InvokeResponseCallback<QGCommonResponse<QGLoginBean>>(msg);
        }

        public void GetUserInfoResponseCallback(string msg)
        {
            QGCallBackManager.InvokeResponseCallback<QGCommonResponse<QGUserInfoBean>>(msg);
        }

        public void ShortcutResponseCallback(string msg)
        {
            QGCallBackManager.InvokeResponseCallback<QGCommonResponse<QGShortcutBean>>(msg);
        }

        public void DefaultResponseCallback(string msg)
        {
            QGCallBackManager.InvokeResponseCallback<QGBaseResponse>(msg);
        }

        public void PayResponseCallback(string msg)
        {
            QGCallBackManager.InvokeResponseCallback<QGCommonResponse<QGPayBean>>(msg);
        }

        public void ReadFileResponseCallback(string msg)
        {
            if (msg.Contains("utf8"))
            {
                QGCallBackManager.InvokeResponseCallback<QGFileResponse>(msg);
            }
            else
            {
                QGFileResponse response = JsonUtility.FromJson<QGFileResponse>(msg);
                var fileBuffer = new byte[response.byteLength];
                #if !UNITY_ANDROID
                QGGetFileBuffer(fileBuffer, response.callbackId);
#endif
                response.textData = fileBuffer;
                var callback = (Action<QGFileResponse>)QGCallBackManager.responseCallBacks[response.callbackId];
                callback(response);
                QGCallBackManager.responseCallBacks.Remove(response.callbackId);
            }

        }

        public void WriteFileResponseCallback(string msg)
        {
            QGCallBackManager.InvokeResponseCallback<QGFileResponse>(msg);
        }

        // 广告通用回调 
        public void AdOnErrorCallBack(string msg)
        {
            var res = JsonUtility.FromJson<QGBaseResponse>(msg);
            var ad = QGBaseAd.QGAds[res.callbackId];
            if (ad != null)
            {
                ad.onErrorAction?.Invoke(res);
            }
        }

        public void AdOnLoadCallBack(string msg)
        {
            var res = JsonUtility.FromJson<QGBaseResponse>(msg);
            var ad = QGBaseAd.QGAds[res.callbackId];
            if (ad != null)
            {
                ad.onLoadAction?.Invoke();
            }
        }

        public void AdOnCloseCallBack(string msg)
        {
            var res = JsonUtility.FromJson<QGBaseResponse>(msg);
            var ad = QGBaseAd.QGAds[res.callbackId];
            if (ad != null)
            {
                ad.onCloseAction?.Invoke();
            }
        }

        public void AdOnHideCallBack(string msg)
        {
            var res = JsonUtility.FromJson<QGBaseResponse>(msg);
            var ad = QGBaseAd.QGAds[res.callbackId];
            if (ad != null)
            {
                ad.onHideAction?.Invoke();
            }
        }

        public void AdOnShowCallBack(string msg)
        {
            var res = JsonUtility.FromJson<QGBaseResponse>(msg);
            var ad = QGBaseAd.QGAds[res.callbackId];
            if (ad != null && ad is QGBoxPortalAd)
            {
                ((QGBoxPortalAd)ad).onShowAction?.Invoke();
            }
        }

        public void NativeAdOnLoadCallBack(string msg)
        {
            var res = JsonUtility.FromJson<QGNativeResponse>(msg);
            var ad = QGBaseAd.QGAds[res.callbackId];
            if (ad != null && ad is QGNativeAd)
            {
                ((QGNativeAd)ad).onLoadNativeAction?.Invoke(res);
            }
        }

        public void RewardedVideoAdOnCloseCallBack(string msg)
        {
            var res = JsonUtility.FromJson<QGRewardedVideoResponse>(msg);
            var ad = QGBaseAd.QGAds[res.callbackId];
            if (ad != null && ad is QGRewardedVideoAd)
            {
                ((QGRewardedVideoAd)ad).onCloseRewardedVideoAction?.Invoke(res);
            }
        }

#endregion


#if !UNITY_ANDROID
        [DllImport("__Internal")]
        private static extern void QGRegisterTouch();

        [DllImport("__Internal")]
        private static extern void InitOPPO(int framemax);

        [DllImport("__Internal")]
        private static extern void InitVIVO(int framemax);

        [DllImport("__Internal")]
        private static extern void SetLoadingProgress(int progress);

        [DllImport("__Internal")]
        private static extern void ShowADReading();

        [DllImport("__Internal")]
        private static extern void QGExitApplication();

        [DllImport("__Internal")]
        private static extern void QGVibShort();

        [DllImport("__Internal")]
        private static extern void QGLogin(string s, string f);

        [DllImport("__Internal")]
        private static extern void QGGetUserInfo(string s, string f);

        [DllImport("__Internal")]
        private static extern void QGHasShortcutInstalled(string s, string f);

        [DllImport("__Internal")]
        private static extern void QGInstallShortcut(string m, string s, string f);

        [DllImport("__Internal")]
        private static extern void QGCreateBannerAd(string a, string p, string s, int i);

        [DllImport("__Internal")]
        private static extern void QGCreateInterstitialAd(string a, string p);

        [DllImport("__Internal")]
        private static extern void QGCreateRewardedVideoAd(string a, string p);

        [DllImport("__Internal")]
        private static extern void QGCreateNativeAd(string a, string p);

        [DllImport("__Internal")]
        private static extern void QGCreateCustomAd(string a, string p, string s);


        [DllImport("__Internal")]
        private static extern void CreateAndShowCustomAD(string adid, int leftPer,int topPer,int widthPer);


        [DllImport("__Internal")]
        private static extern void QGCreateBoxBannerAd(string a, string p);

        [DllImport("__Internal")]
        private static extern void QGCreateBoxPortalAd(string a, string p, string i, int m);

        [DllImport("__Internal")]
        private static extern void QGShowAd(string a, string s, string f);

        [DllImport("__Internal")]
        private static extern void QGHideAd(string a, string s, string f);

        [DllImport("__Internal")]
        private static extern void QGLoadAd(string a, string s, string f);

        [DllImport("__Internal")]
        private static extern void QGDestroyAd(string a);

        [DllImport("__Internal")]
        private static extern bool QGIsShow(string a);

        [DllImport("__Internal")]
        private static extern void QGReportAdShow(string a, string p);

        [DllImport("__Internal")]
        private static extern void QGReportAdClick(string a, string p);

        [DllImport("__Internal")]
        private static extern void QGStorageSetIntSync(string k, int v);

        [DllImport("__Internal")]
        private static extern int QGStorageGetIntSync(string k, int d);

        [DllImport("__Internal")]
        private static extern void QGStorageSetStringSync(string k, string v);

        [DllImport("__Internal")]
        private static extern string QGStorageGetStringSync(string k, string d);

        [DllImport("__Internal")]
        private static extern void QGStorageSetFloatSync(string k, float v);

        [DllImport("__Internal")]
        private static extern float QGStorageGetFloatSync(string k, float d);

        [DllImport("__Internal")]
        private static extern void QGStorageDeleteAllSync();

        [DllImport("__Internal")]
        private static extern void QGStorageDeleteKeySync(string k);

        [DllImport("__Internal")]
        private static extern bool QGStorageHasKeySync(string k);

        [DllImport("__Internal")]
        private static extern void QGPay(string p, string s, string f, string c, string o);

        [DllImport("__Internal")]
        private static extern string QGAccessFile(string u);

        [DllImport("__Internal")]
        private static extern void QGReadFile(string u, string e, int p, int l, string s, string f);

        [DllImport("__Internal")]
        private static extern void QGGetFileBuffer(byte[] d, string c);

        [DllImport("__Internal")]
        private static extern void QGWriteFile(string u, string e, int p, string t, byte[] d, int l, string c, string f);

  
#endif
    }
}
