using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class UIbtnVib : MonoBehaviour
{

    private void Awake()
    {
        SetState();
    }

    public void OnClick()
    {
        int isOpenVib = SDK_Common_Playerprefs.GetInt("isOpenVib",1);
        isOpenVib = isOpenVib == 1 ? 0 : 1;
        SDK_Common_Playerprefs.SetInt("isOpenVib", isOpenVib);
        SetState();
    }

    private void SetState()
    {
        int isOpenVib = SDK_Common_Playerprefs.GetInt("isOpenVib",1);
        if (isOpenVib == -99)
        {
            isOpenVib = 1;
            SDK_Common_Playerprefs.SetInt("isOpenVib", 1);
        }
       

        if(isOpenVib == 1)
            this.GetComponent<Image>().color = Color.white;
            else
            this.GetComponent<Image>().color = Color.gray;
    }

}
