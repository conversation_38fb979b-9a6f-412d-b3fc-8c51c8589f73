using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class UIPrivatePanel : MonoBehaviour
{

    public Text txtTitle1;
    public Text txtTitle2;
    public Toggle togQue;
    public Button btnAgree;
    public Button btnDisAgree;
    public Button btnOpenPrivateText;

    private void Awake()
    {
        string gamename = "";
#if UNITY_EDITOR
        gamename = "XXXX";
#else
        gamename = SDK_Common_COSData.Instance.GetADIDData("Name").ToString();
#endif

        txtTitle1.text = "欢迎使用"+gamename+"！"+gamename+"非常重视您的隐私和个人信息保护。";
        txtTitle2.text = "您同意并接受全部条款后方可使用" + gamename;

    }

    public void OnAgreeClick()
    {
        int isNoMorePrivateQue = togQue.isOn ? 1 : 0;

        SDK_Common_Playerprefs.SetInt("isNoMorePrivateQue", isNoMorePrivateQue);

        gameObject.SetActive(false);
    }

    public void OnDisAgreeClick()
    {
        OVManager.ExitApplication();
    }

    public void OnOpenPrivateTextClick()
    {
        OVManager.CreatePrivateTextPanel();
    }



}
