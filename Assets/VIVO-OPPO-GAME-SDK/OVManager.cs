using QGMiniGame;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using UnityEngine;
using UnityEngine.UI;

public class OVManager : MonoBehaviour
{
    private static OVManager instance = null;

    public static OVManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = new GameObject(typeof(OVManager).Name).AddComponent<OVManager>();
                DontDestroyOnLoad(instance.gameObject);
            }
            return instance;
        }
    }


    //private static string ADIDReward;
    private static List<string> ADIDRewards = new List<string>();
    private static int ADIDRewardsIndex = 0;
    private static List<string> ADIDCustoms = new List<string>();
    private static int ADIDCustomsIndex = 0;

    private static string ADIDBanner;
    private static string ADIDBoxPortal;

    private static List<QGRewardedVideoAd> ADReward = new List<QGRewardedVideoAd>();
    private static QGBannerAd ADBanner = null;
    private static QGBoxPortalAd ADBoxPortal = null;
    private static List<QGCustomAd> ADCustom = new List<QGCustomAd>();


    private static Action rewardCallBack;
    private static Action rewardFailCallBack;

    public static bool isCreatedBanner = false;

    public static int CountInstallShorCut = 0;

    private void Awake()
    {
        DontDestroyOnLoad(this.gameObject);
    }

    public static void Init()
    {

        //ADIDReward = SDK_Common_COSData.Instance.GetADIDData("Reward").ToString();
        ADIDBanner = SDK_Common_COSData.Instance.GetADIDData("Banner").ToString();
        ADIDBoxPortal = SDK_Common_COSData.Instance.GetADIDData("Box").ToString();

        var ridsobj = SDK_Common_COSData.Instance.GetADIDData("Rewards") as Array;
        for (var i = 0; i < ridsobj.Length; i++)
            ADIDRewards.Add(ridsobj.GetValue(i).ToString());

        var ridsobj2 = SDK_Common_COSData.Instance.GetADIDData("Customs") as Array;
        for (var i = 0; i < ridsobj2.Length; i++)
            ADIDCustoms.Add(ridsobj2.GetValue(i).ToString());

        QGMiniGameManager.Instance.Init();



        CreateRewardAD();
        CreateBoxProtalAd();
       // CreateCustomAD();

    }

    #region AD


    public static void CreateBannerAD()
    {
#if !UNITY_EDITOR
        if (!CheckVersionAPI(1051, 1031))
            return;

        QGCreateBannerAdParam adp = new QGCreateBannerAdParam();
        adp.posId = ADIDBanner;
        if (ADBanner != null)
            ADBanner.Destroy();
        ADBanner = QGMiniGameManager.Instance.CreateBannerAd(adp);
        ADBanner.Show(null, onBannerErrorAction);
#endif
    }

    public static void HideBanner()
    {
        if (!CheckVersionAPI(1051, 1031))
            return;

        if (ADBanner != null)
            ADBanner.Destroy();
    }

    private static void CreateRewardAD()
    {
        if (!CheckVersionAPI(1051, 1041))
            return;

        if (ADReward.Count > 0)
        {
            for (var i = 0; i < ADReward.Count; i++)
                if (ADReward[i] != null)
                    ADReward[i].Destroy();
        }
        ADReward = new List<QGRewardedVideoAd>();

        for(var i = 0; i< ADIDRewards.Count;i++)
        {
            QGCommonAdParam adp = new QGCommonAdParam();
            adp.posId = ADIDRewards[i];
            var ad = QGMiniGameManager.Instance.CreateRewardedVideoAd(adp);
            ad.onCloseRewardedVideoAction += onRewardCloseAction;
            ADReward.Add(ad);
        }


    }

    private static void CreateBoxProtalAd()
    {
        if (!CheckVersionAPI(1076, 1020))
            return;

        QGCreateBoxPortalAdParam adp = new QGCreateBoxPortalAdParam();
        adp.posId = ADIDBoxPortal;
        if (ADBoxPortal != null)
            ADBoxPortal.Destroy();
        ADBoxPortal = QGMiniGameManager.Instance.CreateBoxPortalAd(adp);


        ADBoxPortal.onCloseAction += onBoxPortalCloseAction;
        

    }

    private static void CreateCustomAD()
    {
        if (!CheckVersionAPI(1094, 1091))
            return;


        Debug.LogError("111111111111111111111111111");

        if (ADCustom.Count > 0)
        {
            for (var i = 0; i < ADCustom.Count; i++)
                if (ADCustom[i] != null)
                    ADCustom[i].Destroy();
        }
        ADCustom = new List<QGCustomAd>();

        for (var i = 0; i < ADIDCustoms.Count; i++)
        {
            QGCreateCustomAdParam adp = new QGCreateCustomAdParam();
            adp.posId = ADIDCustoms[i];
            var ad = QGMiniGameManager.Instance.CreateCustomAd(adp);
            ADCustom.Add(ad);
        }
    }



    private static void onRewardCloseAction(QGRewardedVideoResponse cb)
    {
        // Debug.LogError("onRewardCloseAction");

        if(cb.isEnded)
        {
            rewardCallBack?.Invoke();
            rewardCallBack = null;
        }
        else
        {
            rewardFailCallBack?.Invoke();
            rewardFailCallBack = null;
        }


        ADReward[ADIDRewardsIndex++].Load();
        if (ADIDRewardsIndex == ADIDRewards.Count)
            ADIDRewardsIndex = 0;


    }

    private static void onBoxPortalCloseAction()
    {
        //  Debug.LogError("onBoxPortalCloseAction");
        CreateBoxProtalAd();
    }

    public static void ShowBoxPortal()
    {
        if (!CheckVersionAPI(1076, 1020))
            return;

        if (!IsEditor())
        {
            ADBoxPortal.Show(null, onBoxPortalErrorAction);
        }

    }

    public static void ShowReward(Action callback,Action failback)
    {

        if (!CheckVersionAPI(1051, 1041))
            return;

        if (!IsEditor())
        {
            rewardCallBack = callback;
            rewardFailCallBack = failback;
            ADReward[ADIDRewardsIndex].Show(null, onRewardErrorAction);
        }
        else
        {
            callback();
        }


    }

    public static void ShowCustom()
    {
        //if (!CheckVersionAPI(1094, 1091))
        //    return;

        //if (!IsEditor())
        //{
        //    QGCreateCustomAdParam adp = new QGCreateCustomAdParam();
        //    adp.posId = ADIDCustoms[ADIDCustomsIndex++];
        //    var ad = QGMiniGameManager.Instance.CreateCustomAd(adp);
        //    if (ADIDCustomsIndex == ADIDCustoms.Count)
        //       ADIDCustomsIndex = 0;
        //}

    }

    public static void CreateAndShowCustom(int adidIndex,int leftPer, int topPer, int widthPer)
    {
        if (!CheckVersionAPI(1094, 1091))
            return;

        if (!IsEditor())
        {
            QGMiniGameManager.Instance.CreateAndShowCustomAd(ADIDCustoms[adidIndex], leftPer, topPer, widthPer);
            //if (ADIDCustomsIndex == ADIDCustoms.Count)
            //    ADIDCustomsIndex = 0;
        }

    }

    private static void onRewardErrorAction(QGBaseResponse cb)
    {
        QGMiniGameManager.Instance.ShowADisReading();
        CreateRewardAD();
    }

    private static void onBannerErrorAction(QGBaseResponse cb)
    {
        CreateBannerAD();
    }

    private static void onBoxPortalErrorAction(QGBaseResponse cb)
    {
        QGMiniGameManager.Instance.ShowADisReading();
        CreateBoxProtalAd();
    }

    private static void onCustomErrorAction(QGBaseResponse cb)
    {
       // QGMiniGameManager.Instance.ShowADisReading();
        CreateCustomAD();
    }


#endregion

    #region Shortcut

    public static void InstallShortCut()
    {
        if (!CheckVersionAPI(1040, 1041))
            return;
        QGMiniGameManager.Instance.HasShortcutInstalled(onHasShortcutInstalled);
    }

    private static void onHasShortcutInstalled(QGCommonResponse<QGShortcutBean> obj)
    {
        Debug.LogError("onHasShortcutInstalled" + obj.data.hasShortcutInstalled);
        if (obj.data.hasShortcutInstalled)
            return;
        QGMiniGameManager.Instance.InstallShortcut("添加到桌面吧！");
    }

    #endregion

    #region Others

    public static void SetLoadingProgress(int progress)
    {
#if OPPO
        QGMiniGameManager.Instance.SetToLoadingProgress(progress);
#endif
    }

    public static void VibShort()
    {
        if (!CheckVersionAPI(1099, 1040))
            return;

        int isOpenVib = SDK_Common_Playerprefs.GetInt("isOpenVib", 1);

        if (isOpenVib == 1)
        {
            QGMiniGameManager.Instance.VibShort();
        }
   
    }

    public static void ExitApplication()
    {
        QGMiniGameManager.Instance.ExitApplication();
    }

 

    #endregion

    #region Tool
    public static bool IsEditor()
    {
#if UNITY_EDITOR
        return true;
#endif
        return false;
    }

    private static bool CheckVersionAPI(int oppoversion, int vivoversion)
    {
        if (QGMiniGameManager.PlatFormVersion == -1)
            return true;

#if OPPO
        if (QGMiniGameManager.PlatFormVersion < oppoversion)
                return false;
            else
                return true;
#endif
#if VIVO
            if (QGMiniGameManager.PlatFormVersion < vivoversion)
                return false;
            else
                return true;
#endif

        return false;

    }
#endregion

    #region PUSHUI

    public static void PushLoadingUI(string rznumber)
    {

        GameObject imgYeadQue = GameObject.Find("PLAT_OV_Load_Canvas").transform.Find("imgYeadQue").gameObject;              
        GameObject txtRZ = GameObject.Find("PLAT_OV_Load_Canvas").transform.Find("txtRZ").gameObject; 

        txtRZ.gameObject.SetActive(true);
        imgYeadQue.gameObject.SetActive(true);

        var ruanzuName = "广州猫仙人游戏科技有限公司";
#if OPPO
        ruanzuName = SDK_Common_COSData.Instance.OPPO_Ruanzu_Other_Company == "" ? "广州猫仙人游戏科技有限公司" : SDK_Common_COSData.Instance.OPPO_Ruanzu_Other_Company;
#endif
#if VIVO
     
             ruanzuName = SDK_Common_COSData.Instance.VIVO_Ruanzu_Other_Company == "" ? "广州猫仙人游戏科技有限公司" : SDK_Common_COSData.Instance.VIVO_Ruanzu_Other_Company;
#endif
        txtRZ.GetComponent<Text>().text = "著作权人：" + ruanzuName + "\n登记号：" + rznumber;

    }

    public static void CreatePirvatePanel()
    {
        PLAT_OV_Start_Panel.Instance.transform.Find("PrivatePanel").gameObject.SetActive(true);
    }

    public static void CreatePrivateTextPanel()
    {
        PLAT_OV_Start_Panel.Instance.transform.Find("PrivateTextPanel").gameObject.SetActive(true);
    }


    public static void CreateBtnPrivate()
    {
        var gameobj = PLAT_OV_Start_Panel.Instance.transform.Find("btnPrivate").gameObject;
        var rect = gameobj.GetComponent<RectTransform>();

        var d = SDK_Common_COSData.Instance.GetTrapDataSe("OV_Private", "BtnPrivatePos") as Array;

        rect.localPosition = new Vector3(float.Parse(d.GetValue(0).ToString()), float.Parse(d.GetValue(1).ToString()), float.Parse(d.GetValue(2).ToString()));
        gameobj.SetActive(true);
    }

    public static void CreateBtnVib()
    {
        var gameobj = PLAT_OV_Start_Panel.Instance.transform.Find("btnVib").gameObject;
        var rect = gameobj.GetComponent<RectTransform>();

        var d = SDK_Common_COSData.Instance.GetTrapDataSe("OV_Private", "BtnVibPos") as Array;
        rect.localPosition = new Vector3(float.Parse(d.GetValue(0).ToString()), float.Parse(d.GetValue(1).ToString()), float.Parse(d.GetValue(2).ToString()));

        gameobj.SetActive(true);
    }

#endregion


}
