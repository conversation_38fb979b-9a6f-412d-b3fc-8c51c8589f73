<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>latest</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{3F7306CE-2AC1-0089-B33B-4F0622EFBE54}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>KinoBloom.Runtime</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE;UNITY_2019_4_10;UNITY_2019_4;UNITY_2019;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNET;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_MONO_BDWGC;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;PLATFORM_WEBGL;UNITY_WEBGL;UNITY_WEBGL_API;UNITY_DISABLE_WEB_VERIFICATION;UNITY_GFX_USE_PLATFORM_VSYNC;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_SPATIALTRACKING;ENABLE_MONO;NET_4_6;ENABLE_PROFILER;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;UNITY_PRO_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;OPPO;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>D:\unity\2019.4.10f1\Editor\Data\Managed/UnityEngine/UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>D:\unity\2019.4.10f1\Editor\Data\Managed/UnityEditor.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
     <Compile Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\Demo Assets\Kino Bloom\Bloom.cs" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\Demo Assets\Kino Bloom\KinoBloom.Runtime.asmdef" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\Demo Assets\Kino Bloom\Shader\Bloom.shader" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\Demo Assets\Kino Bloom\Kino Bloom License.txt" />
     <None Include="Assets\0_GAME\RES_Effect_JMO Assets\Cartoon FX Remaster\Demo Assets\Kino Bloom\Shader\Bloom.cginc" />
 <Reference Include="UnityEditor.UI">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Library/ScriptAssemblies/UnityEditor.UI.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UI">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Library/ScriptAssemblies/UnityEngine.UI.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AIModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AccessibilityModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AndroidJNIModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AnimationModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AssetBundleModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AudioModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClothModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CoreModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CrashReportingModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.DSPGraphModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.DirectorModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GameCenterModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GridModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.HotReloadModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.IMGUIModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ImageConversionModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.InputModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.InputLegacyModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.JSONSerializeModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.LocalizationModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ParticleSystemModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.PerformanceReportingModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.PhysicsModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.Physics2DModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ProfilerModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ScreenCaptureModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SharedInternalsModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpriteMaskModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpriteShapeModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.StreamingModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SubstanceModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SubsystemsModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TLSModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TerrainModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TerrainPhysicsModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TextCoreModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TextRenderingModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TilemapModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIElementsModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UNETModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UmbraModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityAnalyticsModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityConnectModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityTestProtocolModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestAudioModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestTextureModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestWWWModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VFXModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VRModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VehiclesModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VideoModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.WebGLModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/PlaybackEngines/WebGLSupport/Managed/UnityEngine.WebGLModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.WindModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.XRModule">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.VR">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/UnityExtensions/Unity/UnityVR/Editor/UnityEditor.VR.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.Graphs">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/Managed/UnityEditor.Graphs.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.WindowsStandalone.Extensions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.WebGL.Extensions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.Android.Extensions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.iOS.Extensions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="LitJson">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Assets/Plugins/LitJson.dll</HintPath>
 </Reference>
 <Reference Include="Optimize">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Assets/VIVO-GAME-SDK/Editor/Optimize.dll</HintPath>
 </Reference>
 <Reference Include="Compress.Info">
 <HintPath>E:/Project/Unity/2025/BlastCannon_OV/Assets/VIVO-GAME-SDK/ICSharpCode.SharpZipLib/Compress.Info.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.iOS.Extensions.Xcode">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.iOS.Extensions.Common">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll</HintPath>
 </Reference>
 <Reference Include="mscorlib">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll</HintPath>
 </Reference>
 <Reference Include="System">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll</HintPath>
 </Reference>
 <Reference Include="System.Core">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.Linq">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics.Vectors">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Http">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.CSharp">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll</HintPath>
 </Reference>
 <Reference Include="System.Data">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.Win32.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="netstandard">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll</HintPath>
 </Reference>
 <Reference Include="System.AppContext">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.Concurrent">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.NonGeneric">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.Specialized">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.Annotations">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.EventBasedAsync">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.TypeConverter">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll</HintPath>
 </Reference>
 <Reference Include="System.Console">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll</HintPath>
 </Reference>
 <Reference Include="System.Data.Common">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Contracts">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Debug">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.FileVersionInfo">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Process">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.StackTrace">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.TextWriterTraceListener">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Tools">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.TraceSource">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll</HintPath>
 </Reference>
 <Reference Include="System.Drawing.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Dynamic.Runtime">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization.Calendars">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization.Extensions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Compression.ZipFile">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll</HintPath>
 </Reference>
 <Reference Include="System.IO">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.DriveInfo">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.Watcher">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.IsolatedStorage">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.MemoryMappedFiles">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Pipes">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.UnmanagedMemoryStream">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Expressions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Parallel">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Queryable">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Http.Rtc">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.NameResolution">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.NetworkInformation">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Ping">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Requests">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Security">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Sockets">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebHeaderCollection">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebSockets.Client">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebSockets">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll</HintPath>
 </Reference>
 <Reference Include="System.ObjectModel">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit.ILGeneration">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit.Lightweight">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Extensions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.Reader">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.ResourceManager">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.Writer">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.CompilerServices.VisualC">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Extensions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Handles">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Numerics">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Formatters">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Json">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Xml">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Claims">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Algorithms">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Csp">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Encoding">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.X509Certificates">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Principal">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.SecureString">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Duplex">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Http">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.NetTcp">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Primitives">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Security">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.Encoding">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.Encoding.Extensions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.RegularExpressions">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Overlapped">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks.Parallel">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Thread">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.ThreadPool">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Timer">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll</HintPath>
 </Reference>
 <Reference Include="System.ValueTuple">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.ReaderWriter">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XDocument">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XmlDocument">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XmlSerializer">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XPath">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XPath.XDocument">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll</HintPath>
 </Reference>
 <Reference Include="UnityScript">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/UnityScript.dll</HintPath>
 </Reference>
 <Reference Include="UnityScript.Lang">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/UnityScript.Lang.dll</HintPath>
 </Reference>
 <Reference Include="Boo.Lang">
 <HintPath>D:/unity/2019.4.10f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/Boo.Lang.dll</HintPath>
 </Reference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
